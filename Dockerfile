FROM --platform=linux/amd64 python:3.10-bullseye

# Install system dependencies
RUN apt-get update -y && apt-get install -y --no-install-recommends \
    build-essential \
    libcurl4-openssl-dev \
    libssl-dev \
    libpq-dev \
    binutils \
    libproj-dev \
    gdal-bin \
    wkhtmltopdf \
    netcat-traditional \
    && rm -rf /var/lib/apt/lists/*

# Upgrade pip
RUN pip install --upgrade pip

# Set environment variables
ENV PYTHONUNBUFFERED 1

# Create and set the working directory
RUN mkdir /code
WORKDIR /code

# Install Python dependencies
COPY requirements.txt /code/
RUN pip install --no-cache-dir -r requirements.txt

# Copy project files
COPY . /code/

# Expose the ports for Gun<PERSON> (8000) and Daphne (8001)
EXPOSE 8000 8001 