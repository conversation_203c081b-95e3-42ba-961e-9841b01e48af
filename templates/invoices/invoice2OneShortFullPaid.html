<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Voucher</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Lora:ital,wght@0,400;0,500;0,600;0,700;1,400;1,500;1,600;1,700&family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap"
      rel="stylesheet"
    />

    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        color: #333333;
        font-family: "Roboto", sans-serif;
      }

      .font-lora {
        font-family: "Lora", serif !important;
      }

      .font-roboto {
        font-family: "Roboto", sans-serif !important;
      }

      .color-primary {
        color: #1e3a8a !important;
      }

      .color-secondary {
        color: #333333 !important;
      }

      .color-white {
        color: #ffffff !important;
      }

      .color-black {
        color: #000000 !important;
      }

      .color-red {
        color: #ff3951 !important;
      }

      .color-sky {
        color: #0083e7 !important;
      }

      .color-gray {
        color: #706e75 !important;
      }

      .color-green {
        color: #00AF31 !important;
      }

      h2 {
        font-size: 20px;
        font-weight: 600;
        line-height: 26px;
        letter-spacing: 0.05em;
      }

      h3 {
        font-size: 18px;
        font-weight: 600;
        line-height: 24px;
        letter-spacing: 0.05em;
      }

      h5 {
        font-size: 14px;
        font-weight: 500;
        line-height: 16px;
        letter-spacing: 0.05em;
      }

      h6 {
        font-size: 12px;
        font-weight: 500;
        line-height: 14px;
        letter-spacing: 0.05em;
      }
      .font-400 {
        font-weight: 400;
      }
      .font-500 {
        font-weight: 500;
      }
      .font-600 {
        font-weight: 600;
      }
      .font-700 {
        font-weight: 700;
      }

      .main-container {
        width: 595px;
        margin: 0 auto;
        box-sizing: border-box;
        background-color: #fbfcff;
        position: relative;
        min-height: 100dvh;
        height: auto;
      }

      .container-bg {
        background-image: url("https://static-zuumm-public.s3.ap-south-1.amazonaws.com/templates/invoice-bg/bg-section1.png");
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        height: 290px;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 0;
      }

      .container-main-1 {
        position: relative;
        z-index: 1;
        padding: 16px;
      }

      /* section 1 header */

      .logo-header {
        width: 88px;
        height: 20px;
      }

      .container-section-1-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .header-holiday-voucher {
        width: 97px;
        font-family: Lora;
        font-weight: 600;
        font-style: italic;
        font-size: 24px;
        line-height: 100%;
        letter-spacing: 5%;
        text-align: right;
        text-transform: capitalize;
        color: #1e3a8a;
      }

      /* section 2 */
      .container-section-2 {
        padding: 16px;
        width: 100%;
        background-image: url("https://static-zuumm-public.s3.ap-south-1.amazonaws.com/templates/invoice-bg/bg-user-info.png");
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        border-radius: 12px;
        margin-top: 20px;
        padding: 16px 12px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
      }

      .container-logo-section-2 {
        display: flex;
        align-items: center;
        justify-content: center;
        width: fit-content;
        width: 73px;
        min-height: 1px;
        height: 100%;
        padding-right: 9px;
        position: relative;
      }
      .boarding-pass-container{
        position: absolute;
        transform: rotate(-90deg);
        display: flex;
        flex-direction: column;
        gap: 8px;
        align-items: center;
        justify-content: center;

      }
      .boarding-pass{
        text-transform: uppercase;
        width: max-content;
      }
      .boarding-pass-sec{
        width: max-content;
        line-height: 20px;
        background: linear-gradient(90deg, #FF3951 0%, #5530F9 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        letter-spacing: 0.05em;
      }

      .logo-section-2 {
        width: 64px;
        height: 13px;
      }

      .container-text-section-2 {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 20px;
        padding-left: 12px;
      }

      .container-section-2-user-details {
        display: flex;
        gap: 10px;
        justify-content: space-between;
      }

      .container-section-2-user-details-item {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }

      .container-section-2-user-details-item-2 {
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        gap: 8px;
      }

      .container-section-2-emergency-details {
        display: flex;
        flex-direction: column;
        gap: 8px;
        padding: 10px;
        background-color: #ffffff;
        border-radius: 4px;
      }

      .container-section-2-emergency-details-item {
        display: flex;
        gap: 12px;
      }

      /* section 3 */
      .container-section-3 {
        margin-top: 28px;
        padding: 16px;
        width: 100%;
        background-color: #ffffff;
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        gap: 16px;
        box-shadow: 0px 4px 4px 0px #0000000A;
      }

      .container-section-3-details {
        display: flex;
        gap: 20px;
      }

      .container-section-3-details-item {
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .package-icon {
        width: 28px;
        height: 28px;
      }

      /* section 4 */
      .container-section-4 {
        margin-top: 28px;
        padding: 16px;
        width: 100%;
        background-color: #ffffff;
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        gap: 16px;
      }
      .container-section-4-highlights-container {
        display: flex;
        flex-direction: column;
        gap: 12px;
      }

      .container-section-4-highlights {
        display: flex;
        flex-direction: column;
        gap: 2px;
      }

      .container-section-4-highlights-item {
        display: flex;
        gap: 12px;
      }

      .container-section-4-highlights-item img {
        width: 24px;
        height: 24px;
      }

      .container-section-4-highlights-item h6 {
        line-height: 28px;
      }

      .container-section-4-exclusions {
        display: inline-flex;
        column-gap: 20px;
        row-gap: 12px;
        flex-wrap: wrap;
      }

      .container-section-4-exclusions-item {
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .container-section-4-exclusions-item img {
        width: 21px;
        height: 21px;
      }

      .container-section-4-exclusions-item h6 {
        width: max-content;
        line-height: 28px;
      }

      /* section 5 */
      .container-section-5 {
        margin-top: 28px;
        width: 100%;
      }

      .container-section-5-inner {
        background-color: #ffffff;
        margin-top: 12px;
      }

      .container-section-5-inner-header {
        display: flex;
      }

      .iti-day-heading {
        display: flex;
        flex-direction: column;
        gap: 2px;
        padding: 4px 12px;
        background-color: rgba(255, 57, 81, 0.05);
        justify-content: center;
        align-items: center;
      }

      .iti-day-desc {
        display: flex;
        flex: 1;
        padding: 15px 12px;
        background-color: #fff8f9;
      }

      .container-section-5-inner-content {
        display: flex;
        flex-direction: column;
        margin-top: 20px;
      }

      .iti-flight-details {
        border-left: 3px solid #ff3951;
        display: flex;
        gap: 12px;
        padding: 4px 10px 4px 8px;
        justify-content: space-between;
      }

      .iti-flight-details-item {
        flex: 1;
        gap: 8px;
        display: flex;
        align-items: center;
      }

      .iti-flight-details-item img {
        width: 24px;
        height: 24px;
      }

      .hr-line-flight {
        width: 1px;
        height: 100%;
        background-color: #333333;
        margin: 0px 8px;
      }

      .iti-flight-terminal {
        display: flex;
        flex-direction: column;
        gap: 12px;
      }

      .iti-flight-routes {
        margin-top: 12px;
        display: flex;
        gap: 12px;
        padding: 0px 16px;
        align-items: center;
      }

      .iti-flight-routes-image {
        display: flex;
        flex-direction: column;
        gap: 6px;
        justify-content: center;
        align-items: center;
      }

      .iti-flight-routes-time-start {
        display: flex;
        flex-direction: column;
        gap: 8px;
        justify-content: flex-start;
      }

      .hr-line-flight-container {
        position: relative;
        width: 180px;
        height: 1px;
        background-color: rgba(255, 57, 81, 0.5);
        margin: 8px;
      }

      .iti-flight-routes-airplane {
        width: 18px;
        height: 18px;
        position: absolute;
        top: -8.5px;
        left: calc(50% - 9px);
      }

      .iti-flight-routes-time-end {
        display: flex;
        flex-direction: column;
        gap: 8px;
        justify-content: flex-end;
      }

      .iti-transfer-details {
        margin-top: 12px;
        display: flex;
        gap: 10px;
        align-items: flex-start;
        padding: 0px 16px;
      }

      .iti-transfer-details-image {
        width: 52px;
        height: 52px;
      }

      .iti-transfer-details-content {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }

      .iti-hotel-details {
        margin-top: 12px;
        display: flex;
        gap: 10px;
        align-items: flex-start;
        padding: 0px 16px;
      }

      .iti-hotel-details-image {
        width: 47px;
        height: 47px;
      }

      .iti-hotel-details-content {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }

      /* container 2 */

      .container-section-6 {
        padding: 0px 16px;
        margin-bottom: 16px;
      }

      .container-section-6-payment-heading {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
      }

      .container-section-6-payment-heading-left {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 8px;
      }

      .container-section-6-payment-heading-left h1 {
        font-size: 24px;
      }

      /* section 7 */
      .container-section-7{
        margin-top: 20px;
        padding: 0px;
        width: 100%;
      }

      .container-section-7-header{
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 8px 16px;
        background-color: #1E3A8A;
      }

      .container-section-7-body{
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 10px 16px;
        background-color: #FFF5F5;
      }

      .container-section-7-body-h{
        display: flex;
        align-items: flex-start;
        padding: 10px 16px;
      }

      .container-section-7-body2{
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        padding: 10px 16px;
        background-color: #FFF5F5;
      }

      .container-section-7-body2-item{
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
      }

      .container-section-7-body3{
        width: 100%;
        display: flex;
        justify-content: flex-end;
        padding: 8px 16px;
      }

      /* section 8 */
      .container-section-8{
        margin-top: 20px;
        padding: 0px 16px;
        width: 100%;
      }

      .container-section-8-body{
        margin-top: 12px;
        display: flex;
        flex-direction: column;
        gap: 12px;
        padding: 14px 10px;
        background-color: #FFFFFF;
        list-style: disc;

      }

      .container-section-8-body-item{
        display: flex;
        align-items: flex-start;
        gap: 8px;
      }

      .dot-item{
        margin-top: 5px;
        width: 11px;
        height: 6px;
        background: linear-gradient(90deg, #FF3951 0%, #5530F9 100%);

        border-radius: 50%;
      }

      /* section 9 */
      .container-section-9{
        margin-top: 20px;
        padding: 0px 16px;
        width: 100%;
      }

      .container-section-9-body{
        background: linear-gradient(90deg, #FFF0F2 0%, #FFFFFF 100%);
        padding: 14px 10px; 
        border-radius: 4px;
      }

      .container-section-9-body-content{
        margin-top: 12px;
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 14px;
      }

      .container-section-10{
        margin-top: 20px;
        background-color: #FFFFFF;
        padding: 6px 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 2px;
      }

      .container-section-10 img{
        width: 20px;
        height: 20px;
      }

      /* section 11 */
      .container-section-11{
        margin-top: 20px;
        padding: 0px 16px 30px 16px;
        display: grid;
        grid-template-columns: 1fr 1fr;
        row-gap: 12px;
        column-gap: 18px;
        width: 100%;
      }

      .container-section-11-item{
        width: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid  #FF39510D;
        border-radius: 4px;
        padding: 12px;
        gap: 12px;
        background: #FFFFFF;

      }

      .container-section-11-item-content{
        display: flex;
        align-items: center;
        gap: 4px;
      }

      .container-section-11-item-content img{
        width: 12px;
        height: 10px;
      }

      .span-dual{
        grid-column: span 2;
      }

    </style>
  </head>
  <body>
    <main class="main-container">
      <div class="container-bg"></div>

      <div class="container-main-1">
        <div class="container-section-1">
          <div class="container-section-1-header">
            <div
              style="flex: 1; display: flex; flex-direction: column; gap: 16px"
            >
              <img src="https://static-zuumm-public.s3.ap-south-1.amazonaws.com/templates/brand-logo/logo.png" class="logo-header" alt="logo" />
              <h5 class="color-black" style="font-style: italic;">
                Travel Smarter With AI-For Business And Leisure
              </h5>
            </div>
            <div class="header-holiday-voucher">Holiday Voucher</div>
          </div>
          <hr style="border: 1px dashed #cbcbcb; margin: 12px 0" />
        </div>

        <!-- section 2 -->
        <div class="container-section-2">
           <div class="container-logo-section-2">
            <div class="boarding-pass-container">
              <h5 class="color-red boarding-pass">Boarding Pass</h5>
              <h5 class="color-red font-500 boarding-pass-sec"> To New Adventures</h5>
            </div>
          </div>
          <div class="container-text-section-2">
            <h3
              class="font-lora color-primary"
              style="text-align: center; font-style: italic"
            >
              A personalized trip for Prateek Singh
            </h3>
            <div class="container-section-2-user-details">
              <div class="container-section-2-user-details-item">
                <h6 class="color-sky"><EMAIL></h6>
                <h6>+917654321323</h6>
              </div>
              <div class="container-section-2-user-details-item-2">
                <h6 style="font-weight: 500">
                  Voucher no:
                  <span class="color-red" style="font-weight: 600"
                    >ZM-2024-001</span
                  >
                </h6>
                <h6>Booking Date:<span style="font-weight: 600">12-Dec-2024</span></h6>
              </div>
            </div>
            <div class="container-section-2-emergency-details">
              <h6 class="color-black">Emergency Contact Details</h6>
              <div class="container-section-2-emergency-details-item">
                <h6>Name: <span class="color-sky">John Doe</span></h6>
                <h6>
                  Phone:
                  <a
                    href="tel:+917654321323"
                    style="text-decoration: none"
                    class="color-sky"
                    >+917654321323</a
                  >
                </h6>
                <h6>
                  Email:
                  <a
                    href="mailto:<EMAIL>"
                    style="text-decoration: none"
                    class="color-sky"
                    ><EMAIL></a
                  >
                </h6>
              </div>
            </div>
          </div>
        </div>

        <!-- section 3 -->
        <div class="container-section-3">
          <h4 class="font-lora" style="font-weight: 600">
            Barcelona Adventure Package
          </h4>
          <div class="container-section-3-details">
            <div class="container-section-3-details-item">
              <img
                src="https://static-zuumm-public.s3.ap-south-1.amazonaws.com/templates/package-feature/duration.png"
                class="package-icon"
                alt="duration"
              />
              <div>
                <h6 class="" style="font-weight: 400; margin-bottom: 4px;">Duration</h6>
                <h6 style="font-weight: 500">4N/5D</h6>
              </div>
            </div>
            <div class="container-section-3-details-item">
              <img src="https://static-zuumm-public.s3.ap-south-1.amazonaws.com/templates/package-feature/person.png" class="package-icon" alt="person" />
              <div>
                <h6 class="" style="font-weight: 400; margin-bottom: 4px;">No.of guests</h6>
                <h6 style="font-weight: 500">2 Adults</h6>
              </div>
            </div>
            <div class="container-section-3-details-item">
              <img src="https://static-zuumm-public.s3.ap-south-1.amazonaws.com/templates/package-feature/cost.png" class="package-icon" alt="cost" />
              <div>
                <h6 class="" style="font-weight: 400; margin-bottom: 4px;">Package cost</h6>
                <h6 style="font-weight: 500">4500</h6>
              </div>
            </div>
            <div class="container-section-3-details-item">
              <img src="https://static-zuumm-public.s3.ap-south-1.amazonaws.com/templates/package-feature/date.png" class="package-icon" alt="date" />
              <div>
                <h6 style="font-weight: 400; margin-bottom: 4px;">Travel Dates</h6>
                <h6 style="font-weight: 500">Dec 15-17, 2024</h6>
              </div>
            </div>
          </div>
        </div>

        <!-- section 4 -->
        <div class="container-section-4">
          <div class="container-section-4-highlights-container">
            <h4>Tour Highlights</h4>
            <div class="container-section-4-highlights">
              <div class="container-section-4-highlights-item">
                <img src="https://static-zuumm-public.s3.ap-south-1.amazonaws.com/templates/package-highlights/common.png" alt="highlight" />
                <h6 class="font-400">
                  Experience the thrill of a speedboat to the stunning Phi Phi
                  Islands
                </h6>
              </div>
              <div class="container-section-4-highlights-item">
                <img src="https://static-zuumm-public.s3.ap-south-1.amazonaws.com/templates/package-highlights/common.png" alt="highlight" />
                <h6 class="font-400">
                  Be amazed by the variety of marine life in the archepelago
                </h6>
              </div>
              <div class="container-section-4-highlights-item">
                <img src="https://static-zuumm-public.s3.ap-south-1.amazonaws.com/templates/package-highlights/common.png" alt="highlight" />
                <h6 class="font-400">
                  Enjoy relaxing in paradise with white sand beaches and azure
                  turquoise water
                </h6>
              </div>
              <div class="container-section-4-highlights-item">
                <img src="https://static-zuumm-public.s3.ap-south-1.amazonaws.com/templates/package-highlights/common.png" alt="highlight" />
                <h6 class="font-400">
                  Feel the comfort of a tour limited to 35 passengers
                </h6>
              </div>
              <div class="container-section-4-highlights-item">
                <img src="https://static-zuumm-public.s3.ap-south-1.amazonaws.com/templates/package-highlights/common.png" alt="highlight" />
                <h6 class="font-400">
                  Catch a glimpse of the wild monkeys around Monkey Beach
                </h6>
              </div>
            </div>
          </div>

          <hr style="border: 1px dashed #cbcbcb; margin: 0px 0" />

          <div class="container-section-4-highlights-container">
            <h4>Inclusions</h4>
            <div class="container-section-4-highlights">
              <div class="container-section-4-highlights-item">
                <img src="https://static-zuumm-public.s3.ap-south-1.amazonaws.com/templates/package-highlights/common.png" alt="highlight" />
                <h6 class="font-400">
                  Experience the thrill of a speedboat to the stunning Phi Phi
                  Islands
                </h6>
              </div>
              <div class="container-section-4-highlights-item">
                <img src="https://static-zuumm-public.s3.ap-south-1.amazonaws.com/templates/package-highlights/common.png" alt="highlight" />
                <h6 class="font-400">
                  Be amazed by the variety of marine life in the archepelago
                </h6>
              </div>
              <div class="container-section-4-highlights-item">
                <img src="https://static-zuumm-public.s3.ap-south-1.amazonaws.com/templates/package-highlights/common.png" alt="highlight" />
                <h6 class="font-400">
                  Enjoy relaxing in paradise with white sand beaches and azure
                  turquoise water
                </h6>
              </div>
              <div class="container-section-4-highlights-item">
                <img src="https://static-zuumm-public.s3.ap-south-1.amazonaws.com/templates/package-highlights/common.png" alt="highlight" />
                <h6 class="font-400">
                  Feel the comfort of a tour limited to 35 passengers
                </h6>
              </div>
              <div class="container-section-4-highlights-item">
                <img src="https://static-zuumm-public.s3.ap-south-1.amazonaws.com/templates/package-highlights/common.png" alt="highlight" />
                <h6 class="font-400">
                  Catch a glimpse of the wild monkeys around Monkey Beach
                </h6>
              </div>
            </div>
          </div>

          <hr style="border: 1px dashed #cbcbcb; margin: 0px 0" />

          <div class="container-section-4-highlights-container">
            <h4>Exclusions</h4>
            <div class="container-section-4-exclusions">
              <div class="container-section-4-exclusions-item">
                <img src="https://static-zuumm-public.s3.ap-south-1.amazonaws.com/templates/package-highlights/exclusions.png" alt="highlight" />
                <h6 class="font-400">Experience the thrill</h6>
              </div>
              <div class="container-section-4-exclusions-item">
                <img src="https://static-zuumm-public.s3.ap-south-1.amazonaws.com/templates/package-highlights/exclusions.png" alt="highlight" />
                <h6 class="font-400">Experience the thrill</h6>
              </div>
              <div class="container-section-4-exclusions-item">
                <img src="https://static-zuumm-public.s3.ap-south-1.amazonaws.com/templates/package-highlights/exclusions.png" alt="highlight" />
                <h6 class="font-400">Experience the thrill</h6>
              </div>
              <div class="container-section-4-exclusions-item">
                <img src="https://static-zuumm-public.s3.ap-south-1.amazonaws.com/templates/package-highlights/exclusions.png" alt="highlight" />
                <h6 class="font-400">Experience the thrill</h6>
              </div>
              <div class="container-section-4-exclusions-item">
                <img src="https://static-zuumm-public.s3.ap-south-1.amazonaws.com/templates/package-highlights/exclusions.png" alt="highlight" />
                <h6 class="font-400">Experience the thrill</h6>
              </div>
            </div>
          </div>
        </div>

        <div class="container-section-5">
          <h2 class="font-600 font-lora">Travel Itinerary</h2>
          <div class="container-section-5-inner">
            <div class="container-section-5-inner-header">
              <div class="iti-day-heading">
                 <h5 class="font-lora">Day</h5>
                <h4 class="font-600 font-lora">1</h4>
              </div>
              <div class="iti-day-desc">
                <h4 class="font-600 font-lora">Arrive at goa</h4>
              </div>
            </div>
            <div class="container-section-5-inner-content">
              <div class="iti-flight-details">
                <div class="iti-flight-details-item">
                  <img src="https://static-zuumm-public.s3.ap-south-1.amazonaws.com/templates/package-highlights/flight.png" alt="flight" />
                  <h5>Flight</h5>
                  <div class="hr-line-flight"></div>
                  <h6 class="color-gray">Flight ZM101 - London to Barcelona</h6>
                </div>
                <div class="iti-flight-terminal">
                  <h6>Terminal 1</h6>
                </div>
              </div>
              <div class="iti-flight-routes">
                <div class="iti-flight-routes-image">
                  <img
                    src="https://static-zuumm-public.s3.ap-south-1.amazonaws.com/templates/package-highlights/common.png"
                    style="width: 46px; height: 42px"
                    alt="flight"
                  />
                  <h6 class="font-400">6E-1782</h6>
                </div>
                <div class="iti-flight-routes-time-start">
                  <h5 class="font-600">10:15</h5>
                  <h6 class="font-400 color-gray">Wed, 23 July</h6>
                  <h6 class="font-400 color-gray">London</h6>
                </div>
                <div class="hr-line-flight-container">
                  <img
                    src="https://static-zuumm-public.s3.ap-south-1.amazonaws.com/templates/itinerary/airplane.png"
                    class="iti-flight-routes-airplane"
                    alt="airplane"
                  />
                </div>
                <div class="iti-flight-routes-time-end">
                  <h5 class="font-600">10:15</h5>
                  <h6 class="font-400 color-gray">Wed, 23 July</h6>
                  <h6 class="font-400 color-gray">London</h6>
                </div>
              </div>

              <hr style="border: 1px dashed #cbcbcb; margin: 20px 16px" />
            </div>

            <div
              class="container-section-5-inner-content"
              style="margin-top: 0px !important"
            >
              <div class="iti-flight-details">
                <div class="iti-flight-details-item">
                  <img src="https://static-zuumm-public.s3.ap-south-1.amazonaws.com/templates/package-highlights/taxi.png" alt="flight" />
                  <h5>Transfer</h5>
                  <div class="hr-line-flight"></div>
                  <h6 class="color-gray">Airport to hotel in Bangkok</h6>
                </div>
              </div>
              <div class="iti-transfer-details">
                <img
                  src="https://static-zuumm-public.s3.ap-south-1.amazonaws.com/templates/itinerary/cab-transfer.png"
                  class="iti-transfer-details-image"
                  alt="cab"
                />
                <div class="iti-transfer-details-content">
                  <h5>Private Transfer</h5>
                  <h6 class="font-400 color-gray">
                    After your arrival at Bangkok Airport, you will be directly
                    transferred to your hotel in Pattaya by a private vehicle.
                    You should exit from Gate No...
                  </h6>
                </div>
              </div>

              <hr style="border: 1px dashed #cbcbcb; margin: 20px 16px" />
            </div>

            <div
              class="container-section-5-inner-content"
              style="margin-top: 0px !important"
            >
              <div class="iti-flight-details">
                <div class="iti-flight-details-item">
                  <img src="https://static-zuumm-public.s3.ap-south-1.amazonaws.com/templates/package-highlights/hotel.png" alt="flight" />
                  <h5>Hotel</h5>
                  <div class="hr-line-flight"></div>
                  <h6 class="color-gray">The Hawthorn Boutique Hotel</h6>
                </div>
                <div
                  class="iti-flight-terminal"
                  style="
                    display: flex;
                    align-items: center;
                    gap: 2px;
                    flex-direction: row;
                  "
                >
                  <h5 class="font-600">4</h5>
                  <img
                    src="https://static-zuumm-public.s3.ap-south-1.amazonaws.com/templates/itinerary/rating.png"
                    style="width: 16px; height: 16px"
                    alt="star"
                  />
                </div>
              </div>
              <div class="iti-hotel-details">
                <img
                  src="https://static-zuumm-public.s3.ap-south-1.amazonaws.com/templates/itinerary/cab-transfer.png"
                  class="iti-hotel-details-image"
                  alt="cab"
                />
                <div class="iti-hotel-details-content">
                  <h5>The Grand Hotel</h5>
                  <h6 class="font-400 color-gray">
                    Centrally located with a relaxed, modern vibe near beaches
                    and adventure activities.
                  </h6>
                </div>
              </div>
              <h6
                class="font-400 color-gray"
                style="padding: 8px 16px 0px 16px"
              >
                The air-conditioned rooms at the Grand Hotel Barcelona have a
                modern design. They are equipped with a 4K high-definition TV´s
                with Chromecast . Each one has a bathroom with a large,
                rain-effect shower and luxury toiletries.
              </h6>

              <hr style="border: 1px dashed #cbcbcb; margin: 20px 16px" />
            </div>

            <div
              class="container-section-5-inner-content"
              style="margin-top: 0px !important"
            >
              <div class="iti-flight-details">
                <div class="iti-flight-details-item">
                  <img src="https://static-zuumm-public.s3.ap-south-1.amazonaws.com/templates/package-highlights/beach.png" alt="flight" />
                  <h5>Activity</h5>
                  <div class="hr-line-flight"></div>
                  <h6 class="color-gray">SIGHTSEEING - Paro and Punakha </h6>
                </div>
              </div>
              <div class="iti-transfer-details">
                <img
                  src="https://static-zuumm-public.s3.ap-south-1.amazonaws.com/templates/itinerary/cab-transfer.png"
                  class="iti-transfer-details-image"
                  alt="cab"
                />
                <div class="iti-transfer-details-content">
                  <h6 class="font-400 color-gray">
                    After your arrival at Bangkok Airport, you will be directly
                    transferred to your hotel in Pattaya by a private vehicle.
                    You should exit from Gate No...
                  </h6>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="container-main-2">
        <div class="container-section-6">
          
          <div class="container-section-6-payment-heading">
            <div class="container-section-6-payment-heading-left">
              <h1 class="font-600 font-lora ">Payment Schedule</h1>
              <h6 class="font-400 color-gray">
                inclusive of all taxes, fees, GST, TCS as applicable
              </h6>
            </div>
            
          </div>
        </div>

         <div class="container-section-7">
         <div class="container-section-7-header">
            <h6 class="font-lora color-white" style="width: 140px; text-align: left;">
                Payment Detail
            </h6>
            <h6 class="font-lora color-white" style="flex: 1; text-align: center;">
                Date
            </h6>
            <h6 class="font-lora color-white" style="width: 120px;  text-align: left;">
                Amount
            </h6>
         </div>
         <div class="container-section-7-body">
            <h5 class="color-black" style="width: 140px; text-align: left;">
                Total Package Cost
            </h5>
            <h5 class="font-400"  style="flex: 1; text-align: center;">
                
            </h5>
            <h5  style="min-width: 120px; text-align: left;">
                3,000
            </h5>
         </div>
         <div class="container-section-7-body-h">
            <h5 class="color-primary" style="text-align: left;">
                Full Payment - pay in One go
            </h5>
            
         </div>
         <div class="container-section-7-body2">
            <div class="container-section-7-body2-item">
                <h5 class="color-black" style="width: 140px; text-align: left;">
                Full
            </h5>
            <h5 class="color-green"  style="flex: 1; text-align: center;">
                Paid on Dec 16, 2024 
            </h5>
            <h5 class="color-green"  style="min-width: 120px; text-align: left;">
                3,000
            </h5>
            </div>
            
            </div>
           
         </div>
         <div class="container-section-7-body3">
            <h6 class="color-sky" style="line-height: 1.5; text-align: right;">
                <a target="_blank" style="text-decoration: none;" href="https://www.zuumm.ai/privacy" class="color-sky">View Policies</a>, <a target="_blank" style="text-decoration: none;" href="https://www.zuumm.ai/terms" class="color-sky">Terms & Conditions</a>
            </h6>

         </div>
         </div>


         <hr style="border: 1px dashed #FF395180; margin: 20px 0px; width: 100%;" />


         <div class="container-section-8">
            <h4 class="font-600 font-lora" style="text-transform: uppercase;">
                Cancellation Policy
            </h4>
           
            <div class="container-section-8-body">
                <div class="container-section-8-body-item">
                    <div class="dot-item">
                        
                    </div>
                    <h6 class="font-400" style="line-height: 1.5;">
                       These are non-refundable amounts as per the current components attached. In the case of component change/modifications, the policy will change accordingly.
                    </h6>
                </div>
                <div class="container-section-8-body-item">
                    <div class="dot-item">
                        
                    </div>
                    <h6 class="font-400" style="line-height: 1.5;">
                       These are non-refundable amounts as per the current components attached. In the case of component change/modifications, the policy will change accordingly.
                    </h6>
                </div>
                <div class="container-section-8-body-item">
                    <div class="dot-item">
                        
                    </div>
                    <h6 class="font-400" style="line-height: 1.5;">
                       These are non-refundable amounts as per the current components attached. In the case of component change/modifications, the policy will change accordingly.
                    </h6>
                </div>
            </div>
            <hr style="border: 1px dashed #FF395180; margin: 20px 0px; width: 100%;" />

            
         </div>
         <div class="container-section-9">
                <div class="container-section-9-body">
                    <h5>
                        Get 100% Credit of TCS Amount
                    </h5>

                    <div class="container-section-9-body-content">
                        <div>
                            <h6 >
                            Receiving credit
                        </h6>
                        <h6 class="font-400" style="line-height: 1.5; font-size: 10px; margin-top: 5px;">
                            TCS credit would reflect in your Form 26AS on quarterly basis. You may also request TCS certificate from Zuumm.
                        </h6>
                        </div>
                        <div>
                            <h6 >
                            Claiming your credit
                        </h6>
                        <h6 class="font-400" style="line-height: 1.5; font-size: 10px; margin-top: 5px;">
                           Charged TCS can be claimed against the tax payable at the time of filing the return.
                        </h6>
                        </div>
                        <div>
                            <h6 >
                           TCS is collected via Zuumm
                        </h6>
                        <h6 class="font-400" style="line-height: 1.5; font-size: 10px; margin-top: 5px;">
                            In case there is no tax payable, you can claim the refund of TCS amount at the time of filing income tax return.
                        </h6>
                        </div>
                    </div>
                    

                </div>
                
            </div>

            <div class="container-section-10">
                <img src="https://static-zuumm-public.s3.ap-south-1.amazonaws.com/templates/travel-info/info.png" alt="info" />
                <h5 class="font-600 font-lora color-red">
                    Important Travel Information
                </h5>
            </div>

            <div class="container-section-11">

                <div class="container-section-11-item">
                   <div class="container-section-11-item-content">
                    <img src="https://static-zuumm-public.s3.ap-south-1.amazonaws.com/templates/travel-info/weather.png" alt="weather" />
                     <h6 class="color-secondary">
                        Weather & Climate
                    </h6>
                    
                   </div>
                   <h6 class="color-black">
                    December Weather: <span>Mild winter, 8-15 C</span>
                   </h6>
                   <h6 class="font-400">
                    Pack layers, light jackets, comfortable walking shoes, and umbrella for occasional rain.
                   </h6>
                </div>
                 <div class="container-section-11-item">
                   <div class="container-section-11-item-content">
                    <img src="https://static-zuumm-public.s3.ap-south-1.amazonaws.com/templates/travel-info/emergency.png" alt="emergency" />
                     <h6 class="color-secondary">
                        Emergency Contacts
                    </h6>
                    
                   </div>
                   <h6 class="color-black">
                    December Weather: <span>Mild winter, 8-15 C</span>
                   </h6>
                   <h6 class="font-400">
                    Pack layers, light jackets, comfortable walking shoes, and umbrella for occasional rain.
                   </h6>
                </div>

                 <div class="container-section-11-item">
                   <div class="container-section-11-item-content">
                    <img src="https://static-zuumm-public.s3.ap-south-1.amazonaws.com/templates/travel-info/currency.png" alt="currency" />
                     <h6 class="color-secondary">
                       Currency & Payment
                    </h6>
                    
                   </div>
                   <h6 class="color-black">
                    December Weather: <span>Mild winter, 8-15 C</span>
                   </h6>
                   <h6 class="font-400">
                    Pack layers, light jackets, comfortable walking shoes, and umbrella for occasional rain.
                   </h6>
                </div>

                 <div class="container-section-11-item">
                   <div class="container-section-11-item-content">
                    <img src="https://static-zuumm-public.s3.ap-south-1.amazonaws.com/templates/travel-info/pack.png" alt="pack" />
                     <h6 class="color-secondary">
                       What to Pack
                    </h6>
                    
                   </div>
                   <h6 class="color-black">
                    December Weather: <span>Mild winter, 8-15 C</span>
                   </h6>
                   <h6 class="font-400">
                    Pack layers, light jackets, comfortable walking shoes, and umbrella for occasional rain.
                   </h6>
                </div>

                 <div class="container-section-11-item span-dual">
                   <div class="container-section-11-item-content">
                    <img src="https://static-zuumm-public.s3.ap-south-1.amazonaws.com/templates/travel-info/recommend.png" alt="recommend" />
                     <h6 class="color-secondary">
                        Recommended Restaurants 
                    </h6>
                    
                   </div>
                   
                   <h6 class="font-400">
                    Pack layers, light jackets, comfortable walking shoes, and umbrella for occasional rain.Pack layers, light jackets, comfortable walking shoes, and umbrella for occasional rain.
                   </h6>
                </div>

            </div>
         
        </div>
      </div>
    </main>
  </body>
</html>
