<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transfer Voucher - {{ booking_reference }}</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Lora:ital,wght@0,400;0,500;0,600;0,700;1,400;1,500;1,600;1,700&family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap" rel="stylesheet" />
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            color: #333333;
            font-family: "Roboto", sans-serif;
        }
        
        .font-lora {
            font-family: "Lora", serif !important;
        }
        
        .color-primary {
            color: #FF3951 !important;
        }
        
        .color-red {
            color: #FF3951 !important;
        }
        
        .color-sky {
            color: #0083e7 !important;
        }
        
        .color-gray {
            color: #706e75 !important;
        }
        
        body {
            font-family: 'Roboto', Arial, sans-serif;
            font-size: 14px;
            line-height: 1.6;
            color: #333333;
            background: linear-gradient(135deg, #FEEBEE 0%, #F2EBFA 100%);
            min-height: 100vh;
        }
        
        .invoice-container {
            max-width: 800px;
            margin: 20px auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(255, 57, 81, 0.1);
        }
        
        /* Header Section */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #FF3951;
            padding-bottom: 20px;
        }
        
        .logo-section {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .logo {
            width: 88px;
            height: 20px;
        }
        
        .tagline {
            font-style: italic;
            color: #333333;
            font-size: 12px;
        }
        
        .voucher-title {
            font-size: 28px;
            font-weight: bold;
            color: #FF3951;
            font-style: italic;
            letter-spacing: 2px;
            font-family: "Lora", serif;
        }
        
        /* Booking Info Section */
        .booking-info {
            background: linear-gradient(135deg, #FEEBEE 0%, #F2EBFA 100%);
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 25px;
            border-left: 5px solid #FF3951;
            display: flex;
            justify-content: space-between;
        }
        
        .booking-left .info-row {
            margin-bottom: 10px;
        }
        
        .info-label {
            font-weight: 600;
            color: #FF3951;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .booking-ref {
            font-weight: bold;
            color: #333333;
            font-size: 16px;
            font-family: "Lora", serif;
        }
        
        .confirmation-section {
            text-align: right;
        }
        
        .transfer-status {
            background: #00AF31;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 14px;
            display: inline-block;
        }
        
        /* Route Information */
        .route-section {
            background: white;
            border: 2px solid #FF3951;
            border-radius: 12px;
            margin-bottom: 25px;
            overflow: hidden;
        }
        
        .route-header {
            background: linear-gradient(135deg, #FF3951 0%, #ff6b7a 100%);
            color: white;
            padding: 15px 20px;
            font-weight: bold;
            font-size: 16px;
        }
        
        .route-details {
            padding: 20px;
        }
        
        .route-points {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .route-point {
            text-align: center;
            flex: 1;
        }
        
        .location-name {
            font-size: 18px;
            font-weight: bold;
            color: #FF3951;
            margin-bottom: 5px;
            font-family: "Lora", serif;
        }
        
        .location-details {
            color: #706e75;
            font-size: 12px;
        }
        
        .route-arrow {
            flex: 0 0 100px;
            text-align: center;
            color: #FF3951;
            font-size: 24px;
            font-weight: bold;
        }
        
        .timing-info {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            background: #FEEBEE;
            padding: 15px;
            border-radius: 8px;
        }
        
        .timing-item {
            text-align: center;
        }
        
        .timing-label {
            font-size: 12px;
            color: #706e75;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .timing-value {
            font-size: 16px;
            font-weight: bold;
            color: #333333;
            margin-top: 5px;
        }
        
        /* Vehicle Information */
        .vehicle-section {
            background: linear-gradient(135deg, #FEEBEE 0%, #F2EBFA 100%);
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 25px;
            border-left: 5px solid #FF3951;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #FF3951;
            margin-bottom: 15px;
            font-family: "Lora", serif;
        }
        
        .vehicle-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255, 57, 81, 0.2);
        }
        
        .detail-label {
            font-weight: 600;
            color: #FF3951;
        }
        
        .detail-value {
            color: #333333;
            font-weight: 500;
        }
        
        /* Passenger Information */
        .passenger-section {
            background: white;
            border: 1px solid #FEEBEE;
            border-radius: 8px;
            margin-bottom: 25px;
            padding: 20px;
        }
        
        /* Pricing Section */
        .pricing-section {
            background: white;
            border: 2px solid #FF3951;
            border-radius: 12px;
            margin-bottom: 25px;
            overflow: hidden;
        }
        
        .pricing-header {
            background: linear-gradient(135deg, #FF3951 0%, #ff6b7a 100%);
            color: white;
            padding: 15px 20px;
            font-weight: bold;
            font-size: 16px;
        }
        
        .pricing-details {
            padding: 20px;
        }
        
        .price-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #FEEBEE;
        }
        
        .price-row:last-child {
            border-bottom: none;
            font-weight: bold;
            font-size: 18px;
            color: #FF3951;
        }
        
        /* Important Information */
        .important-info {
            background: linear-gradient(135deg, #FEEBEE 0%, #F2EBFA 100%);
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #FF3951;
        }
        
        .info-list {
            list-style: none;
            margin: 0;
            padding: 0;
        }
        
        .info-list li {
            margin-bottom: 10px;
            padding-left: 20px;
            position: relative;
            color: #706e75;
            line-height: 1.7;
        }
        
        .info-list li:before {
            content: '•';
            color: #FF3951;
            position: absolute;
            left: 0;
            font-weight: bold;
            font-size: 16px;
        }
        
        /* Customer Support */
        .contact-info {
            background: linear-gradient(135deg, #FF3951 0%, #ff6b7a 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            margin-top: 30px;
        }
        
        .contact-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            font-family: "Lora", serif;
        }
        
        .contact-details {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 15px;
        }
        
        /* Terms Section */
        .terms-section {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #FEEBEE;
        }
        
        .terms-list {
            list-style: decimal;
            margin-left: 20px;
        }
        
        .terms-list li {
            margin-bottom: 10px;
            color: #706e75;
            line-height: 1.7;
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .invoice-container {
                margin: 10px;
                padding: 20px;
            }
            
            .header {
                flex-direction: column;
                text-align: center;
                gap: 15px;
            }
            
            .booking-info {
                flex-direction: column;
                gap: 15px;
            }
            
            .route-points {
                flex-direction: column;
                gap: 20px;
            }
            
            .timing-info {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            
            .vehicle-details {
                grid-template-columns: 1fr;
            }
        }
        
        /* Print Styles */
        @media print {
            body {
                background: white;
            }
            
            .invoice-container {
                margin: 0;
                box-shadow: none;
                border-radius: 0;
            }
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <!-- Header Section -->
        <div class="header">
            <div class="logo-section">
                <img src="https://static-zuumm-public.s3.ap-south-1.amazonaws.com/templates/brand-logo/logo.png" class="logo" alt="Zuumm" />
                <div class="tagline">Travel Smarter With AI-For Business And Leisure</div>
            </div>
            <div class="voucher-title">TRANSFER VOUCHER</div>
        </div>

        <!-- Booking Reference Information -->
        <div class="booking-info">
            <div class="booking-left">
                <div class="info-row">
                    <span class="info-label">BOOKING REFERENCE:</span>
                    <span class="booking-ref">{{ booking_reference }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Date of Issue:</span>
                    <span>{{ date_of_issue }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Confirmation Number:</span>
                    <span>{{ confirmation_number }}</span>
                </div>
            </div>
            <div class="booking-right">
                <div class="booking-status">{{ transfer_status }}</div>
                <div class="booking-id">{{ transferz_booking_id }}</div>
            </div>
        </div>

        <!-- Transfer Details Section -->
        <div class="transfer-details">
            <div class="section-title">Transfer Details</div>
            
            <div class="transfer-route">
                <div class="location-section">
                    <div class="location pickup">
                        <div class="location-header">PICKUP LOCATION</div>
                        <div class="location-name">{{ pickup_location }}</div>
                        <div class="location-time">
                            <span class="date">{{ pickup_date }}</span>
                            <span class="time">{{ pickup_time }}</span>
                        </div>
                        {% if flight_number %}
                        <div class="flight-info">Flight: {{ flight_number }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="route-arrow">→</div>
                    
                    <div class="location dropoff">
                        <div class="location-header">DROP-OFF LOCATION</div>
                        <div class="location-name">{{ dropoff_location }}</div>
                        <div class="estimated-time">
                            <span>Estimated Duration: {{ estimated_duration }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Vehicle and Service Information -->
        <div class="service-details">
            <div class="section-title">Service Information</div>
            
            <table class="service-table">
                <thead>
                    <tr>
                        <th>Vehicle Type</th>
                        <th>Service Type</th>
                        <th>Passenger Count</th>
                        <th>Total Amount</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>{{ vehicle_type }}</td>
                        <td>{{ service_type }}</td>
                        <td>{{ passenger_count }}</td>
                        <td><strong>{{ currency }} {{ total_amount }}</strong></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Passenger Information -->
        <div class="passenger-section">
            <div class="section-title">Passenger Information</div>
            
            <div class="passenger-details">
                <div class="passenger-info">
                    <div class="info-row">
                        <span class="info-label">Primary Passenger:</span>
                        <span>{{ passenger_name }}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Contact Email:</span>
                        <span>{{ passenger_email }}</span>
                    </div>
                    {% if passenger_phone %}
                    <div class="info-row">
                        <span class="info-label">Contact Phone:</span>
                        <span>{{ passenger_phone }}</span>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Special Requirements -->
        {% if special_requirements %}
        <div class="special-requirements">
            <div class="section-title">Special Requirements</div>
            <div class="requirements-text">{{ special_requirements }}</div>
        </div>
        {% endif %}

        <!-- Pricing Breakdown -->
        <div class="pricing-section">
            <div class="section-title">Pricing Details</div>
            
            <table class="pricing-table">
                <tbody>
                    <tr>
                        <td class="pricing-label">Base Fare:</td>
                        <td class="pricing-value">{{ currency }} {{ base_fare }}</td>
                    </tr>
                    <tr>
                        <td class="pricing-label">Service Charges:</td>
                        <td class="pricing-value">{{ currency }} {{ service_charges }}</td>
                    </tr>
                    <tr>
                        <td class="pricing-label">Taxes & Fees:</td>
                        <td class="pricing-value">{{ currency }} {{ taxes_fees }}</td>
                    </tr>
                    <tr class="total-row">
                        <td class="pricing-label"><strong>Total Amount:</strong></td>
                        <td class="pricing-value"><strong>{{ currency }} {{ total_amount }}</strong></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Important Information -->
        <div class="important-info">
            <div class="section-title">Important Information</div>
            
            <div class="info-grid">
                <div class="info-item">
                    <h4>🕒 Timing Instructions</h4>
                    <ul>
                        <li>Please be ready at pickup location 15 minutes before scheduled time</li>
                        <li>Driver will wait for maximum 15 minutes beyond pickup time</li>
                        <li>Contact support if there are any delays</li>
                    </ul>
                </div>
                
                <div class="info-item">
                    <h4>📱 Contact Driver</h4>
                    <ul>
                        <li>Driver contact details will be shared 2 hours before pickup</li>
                        <li>You will receive SMS/WhatsApp with driver details</li>
                        <li>Show this voucher to the driver for verification</li>
                    </ul>
                </div>
                
                <div class="info-item">
                    <h4>🧳 Luggage Policy</h4>
                    <ul>
                        <li>Standard luggage allowance as per vehicle capacity</li>
                        <li>Extra luggage may incur additional charges</li>
                        <li>Inform support in advance for oversized items</li>
                    </ul>
                </div>
                
                <div class="info-item">
                    <h4>❌ Cancellation Policy</h4>
                    <ul>
                        <li>Free cancellation up to 24 hours before pickup</li>
                        <li>50% charges for cancellation within 24 hours</li>
                        <li>No refund for no-show or same-day cancellation</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Customer Support -->
        <div class="contact-info">
            <div class="contact-title">Customer Support</div>
            <div class="contact-details">Email: <EMAIL> | +91 9591767328</div>
            <div style="margin-top: 10px; font-size: 14px;">
                For any queries or assistance, contact our support team. 
                We're here to ensure your transfer experience is smooth and comfortable.
            </div>
        </div>

        <!-- Terms and Conditions -->
        <div class="terms-section">
            <div class="section-title">Terms & Conditions</div>
            <div class="terms-grid">
                <div class="terms-column">
                    <ul>
                        <li>This voucher is valid only for the specified date and time</li>
                        <li>Driver will verify passenger identity before service</li>
                        <li>Service is subject to traffic and weather conditions</li>
                        <li>Additional charges apply for route changes</li>
                        <li>Passengers must comply with local transportation regulations</li>
                        <li>Company is not liable for delays due to external factors</li>
                    </ul>
                </div>
                <div class="terms-column">
                    <ul>
                        <li>Smoking and alcohol consumption prohibited in vehicle</li>
                        <li>Damage to vehicle will be charged to passenger</li>
                        <li>Pets allowed only with prior notification</li>
                        <li>Children must be accompanied by adults</li>
                        <li>Valid ID required for airport transfers</li>
                        <li>Service provider reserves right to refuse service</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

</body>
</html>
