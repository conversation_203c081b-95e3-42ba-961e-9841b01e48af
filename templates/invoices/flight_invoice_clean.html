<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flight E-Ticket - {{ booking_reference }}</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Lora:ital,wght@0,400;0,500;0,600;0,700;1,400;1,500;1,600;1,700&family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap" rel="stylesheet" />
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            color: #333333;
            font-family: "Roboto", sans-serif;
        }
        
        .font-lora {
            font-family: "Lora", serif !important;
        }
        
        .color-primary {
            color: #FF3951 !important;
        }
        
        .color-red {
            color: #FF3951 !important;
        }
        
        .color-sky {
            color: #0083e7 !important;
        }
        
        .color-gray {
            color: #706e75 !important;
        }
        
        body {
            font-family: 'Roboto', Arial, sans-serif;
            font-size: 14px;
            line-height: 1.6;
            color: #333333;
            background: linear-gradient(135deg, #FEEBEE 0%, #F2EBFA 100%);
            min-height: 100vh;
        }
        
        .invoice-container {
            max-width: 800px;
            margin: 20px auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(255, 57, 81, 0.1);
        }
        
        /* Header Section */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #FF3951;
            padding-bottom: 20px;
        }
        
        .logo-section {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .logo {
            width: 88px;
            height: 20px;
        }
        
        .tagline {
            font-style: italic;
            color: #333333;
            font-size: 12px;
        }
        
        .eticket-title {
            font-size: 32px;
            font-weight: bold;
            color: #FF3951;
            font-style: italic;
            letter-spacing: 2px;
            font-family: "Lora", serif;
        }
        
        /* Booking Info Section */
        .booking-info {
            background: linear-gradient(135deg, #FEEBEE 0%, #F2EBFA 100%);
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 25px;
            border-left: 5px solid #FF3951;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .booking-left .info-row {
            margin-bottom: 10px;
        }
        
        .info-label {
            font-weight: 600;
            color: #FF3951;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .booking-ref {
            font-weight: bold;
            color: #333333;
            font-size: 16px;
            font-family: "Lora", serif;
        }
        
        .pnr-code {
            font-size: 20px;
            font-weight: bold;
            color: #FF3951;
            text-align: center;
            margin-bottom: 10px;
            font-family: monospace;
        }
        
        .barcode {
            text-align: center;
        }
        
        .barcode-lines {
            font-family: monospace;
            font-size: 12px;
            color: #333333;
            letter-spacing: 1px;
        }
        
        /* Flight Details */
        .flight-details {
            background: white;
            border: 2px solid #FF3951;
            border-radius: 12px;
            margin-bottom: 25px;
            overflow: hidden;
        }
        
        .flight-header {
            background: linear-gradient(135deg, #FF3951 0%, #ff6b7a 100%);
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .segment-number {
            font-weight: bold;
            font-size: 16px;
        }
        
        .airline-info {
            font-weight: 500;
        }
        
        .class-type {
            background: white;
            color: #FF3951;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .flight-timing {
            background: #FEEBEE;
            padding: 10px 20px;
            font-weight: 500;
            color: #706e75;
        }
        
        .flight-route {
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .route-point {
            text-align: center;
            flex: 1;
        }
        
        .route-time {
            font-size: 24px;
            font-weight: bold;
            color: #FF3951;
            margin-bottom: 5px;
        }
        
        .route-date {
            color: #706e75;
            font-size: 12px;
        }
        
        .route-city {
            font-weight: bold;
            color: #333333;
            margin-top: 5px;
        }
        
        .route-connection {
            flex: 0 0 200px;
            text-align: center;
            position: relative;
        }
        
        .route-line {
            height: 2px;
            background: #FF3951;
            position: relative;
            margin: 20px 0;
        }
        
        .route-line::after {
            content: '✈';
            position: absolute;
            top: -8px;
            left: calc(50% - 8px);
            background: white;
            color: #FF3951;
            font-size: 16px;
        }
        
        .flight-number {
            color: #FF3951;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        /* Status and Passengers */
        .status-section {
            background: linear-gradient(135deg, #FEEBEE 0%, #F2EBFA 100%);
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 25px;
            text-align: center;
        }
        
        .status-confirmed {
            color: #00AF31;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .passengers-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 25px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(255, 57, 81, 0.1);
        }
        
        .passengers-table th {
            background: linear-gradient(135deg, #FF3951 0%, #ff6b7a 100%);
            color: white;
            padding: 15px 12px;
            text-align: left;
            font-weight: 600;
            font-size: 13px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .passengers-table td {
            padding: 15px 12px;
            border-bottom: 1px solid #FEEBEE;
            vertical-align: top;
        }
        
        .passengers-table tbody tr:hover {
            background: #FEEBEE;
        }
        
        /* Passenger Details Cards */
        .passenger-card {
            background: white;
            border: 1px solid #FEEBEE;
            border-radius: 8px;
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .passenger-header {
            background: linear-gradient(135deg, #FEEBEE 0%, #F2EBFA 100%);
            padding: 15px 20px;
            border-bottom: 1px solid #FF3951;
        }
        
        .passenger-name {
            font-weight: bold;
            color: #FF3951;
            font-size: 16px;
            font-family: "Lora", serif;
        }
        
        .passenger-details {
            padding: 20px;
        }
        
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #FEEBEE;
        }
        
        .detail-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .detail-label {
            font-weight: 600;
            color: #FF3951;
            font-size: 12px;
            text-transform: uppercase;
        }
        
        .detail-value {
            color: #333333;
            font-weight: 500;
        }
        
        /* Pricing Section */
        .pricing-section {
            background: white;
            border: 2px solid #FF3951;
            border-radius: 12px;
            margin-bottom: 25px;
            overflow: hidden;
        }
        
        .pricing-header {
            background: linear-gradient(135deg, #FF3951 0%, #ff6b7a 100%);
            color: white;
            padding: 15px 20px;
            font-weight: bold;
            font-size: 16px;
        }
        
        .pricing-details {
            padding: 20px;
        }
        
        .price-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #FEEBEE;
        }
        
        .price-row:last-child {
            border-bottom: none;
            font-weight: bold;
            font-size: 16px;
            color: #FF3951;
        }
        
        /* Customer Support */
        .contact-info {
            background: linear-gradient(135deg, #FF3951 0%, #ff6b7a 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            margin-top: 30px;
        }
        
        .contact-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            font-family: "Lora", serif;
        }
        
        .contact-details {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 15px;
        }
        
        /* Visa Information */
        .visa-info {
            background: linear-gradient(135deg, #FEEBEE 0%, #F2EBFA 100%);
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #FF3951;
        }
        
        .visa-title {
            font-size: 16px;
            font-weight: bold;
            color: #FF3951;
            margin-bottom: 10px;
            font-family: "Lora", serif;
        }
        
        .visa-text {
            color: #706e75;
            line-height: 1.7;
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .invoice-container {
                margin: 10px;
                padding: 20px;
            }
            
            .header {
                flex-direction: column;
                text-align: center;
                gap: 15px;
            }
            
            .booking-info {
                flex-direction: column;
                gap: 15px;
            }
            
            .flight-route {
                flex-direction: column;
                gap: 20px;
            }
            
            .route-connection {
                order: 1;
            }
            
            .passengers-table th,
            .passengers-table td {
                padding: 10px 8px;
                font-size: 12px;
            }
        }
        
        /* Print Styles */
        @media print {
            body {
                background: white;
            }
            
            .invoice-container {
                margin: 0;
                box-shadow: none;
                border-radius: 0;
            }
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <!-- Header Section -->
        <div class="header">
            <div class="logo-section">
                <img src="https://static-zuumm-public.s3.ap-south-1.amazonaws.com/templates/brand-logo/logo.png" class="logo" alt="Zuumm" />
                <div class="tagline">Travel Smarter With AI-For Business And Leisure</div>
            </div>
            <div class="eticket-title">E-Ticket</div>
        </div>

        <!-- Booking Reference and PNR Information -->
        <div class="booking-info">
            <div class="booking-left">
                <div class="info-row">
                    <span class="info-label">BOOKING REFERENCE:</span>
                    <span class="booking-ref">{{ booking_reference }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Date of Issue:</span>
                    <span>{{ date_of_issue }}</span>
                </div>
            </div>
            <div class="booking-right">
                <div class="pnr-code">{{ pnr_code }}</div>
                <div class="barcode">
                    <div class="barcode-lines">|||||||||||||||||||||||||||</div>
                </div>
            </div>
        </div>

        <!-- Flight Details Section -->
        <div class="flight-details">
            <div class="flight-header">
                <span class="segment-number">{{ segment_number }}. {{ departure_city }} • {{ departure_code }} to {{ arrival_code }}</span>
                <span class="airline-info">{{ airline_name }}</span>
                <span class="class-type">{{ class_type }}</span>
            </div>

            <div class="flight-timing">
                <span class="timing-label">{{ flight_type }} • {{ duration }}</span>
            </div>

            <div class="flight-route">
                <div class="departure">
                    <div class="time">{{ departure_time }}</div>
                    <div class="city">{{ departure_code }}</div>
                    <div class="date">{{ departure_date }}</div>
                    <div class="airport">{{ departure_airport }}</div>
                </div>
                <div class="route-arrow">→</div>
                <div class="arrival">
                    <div class="time">{{ arrival_time }}</div>
                    <div class="city">{{ arrival_code }}</div>
                    <div class="date">{{ arrival_date }}</div>
                    <div class="airport">{{ arrival_airport }}</div>
                </div>
                <div class="status">
                    <div class="status-label">STATUS: {{ flight_status }}</div>
                    <div class="airline-logo">{{ airline_code }}</div>
                    <div class="aircraft-info">{{ aircraft_type }} - {{ seat_count }}</div>
                    <div class="class-label">{{ class_type }}</div>
                    <div class="baggage-info">{{ baggage_allowance }}</div>
                </div>
            </div>
        </div>

        <!-- Passenger Details Table -->
        <div class="passenger-section">
            <div class="section-title">Passenger Details:</div>
            <table class="passenger-table">
                <thead>
                    <tr>
                        <th>Traveller(s)</th>
                        <th>Traveller Type</th>
                        <th>Frequent Flyer No.</th>
                    </tr>
                </thead>
                <tbody>
                    {% for passenger in passengers %}
                    <tr>
                        <td>{{ passenger.title }} {{ passenger.first_name }} {{ passenger.last_name }}</td>
                        <td>{{ passenger.type }}</td>
                        <td>{{ passenger.frequent_flyer|default:"-" }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Individual Passenger Sections -->
        {% for passenger in passengers %}
        <div class="passenger-details">
            <div class="passenger-name">{{ passenger.title }} {{ passenger.first_name }} {{ passenger.last_name }}</div>
            
            <table class="service-table">
                <thead>
                    <tr>
                        <th>Sector</th>
                        <th>Baggage</th>
                        <th>Meal</th>
                        <th>Seat</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>{{ departure_code }}-{{ arrival_code }}</td>
                        <td>{{ passenger.baggage|default:"-" }}</td>
                        <td>{{ passenger.meal|default:baggage_allowance }}</td>
                        <td>{{ passenger.seat|default:"-" }}</td>
                    </tr>
                </tbody>
            </table>
        </div>
        {% endfor %}

        <!-- Pricing Table -->
        <table class="pricing-table">
            <thead>
                <tr>
                    <th>Base Fare</th>
                    <th>Taxes Fees</th>
                    <th>Add Ons</th>
                    <th>Total</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>{{ base_fare }}</td>
                    <td>{{ taxes_fees }}</td>
                    <td>{{ add_ons }}</td>
                    <td><strong>{{ total_amount }}</strong></td>
                </tr>
            </tbody>
        </table>

        <!-- Customer Support -->
        <div class="contact-info">
            <div class="contact-title">Customer Support</div>
            <div class="contact-details">Email: <EMAIL> | +91 9591767328</div>
        </div>

        <!-- Visa Tips Section -->
        <div class="visa-tips">
            <div class="tips-header">
                <h2>Simple tips for an easy visa application!</h2>
            </div>
            <div class="tips-content">
                <div class="tips-left">
                    <div class="tip-item">
                        <span class="tip-icon">✓</span>
                        <span class="tip-text">Apply early</span>
                    </div>
                    <div class="tip-item">
                        <span class="tip-icon">✓</span>
                        <span class="tip-text">Check what documents you need</span>
                    </div>
                    <div class="tip-item">
                        <span class="tip-icon">✓</span>
                        <span class="tip-text">Have a clear purpose of travel</span>
                    </div>
                    <div class="tip-item">
                        <span class="tip-icon">✓</span>
                        <span class="tip-text">Don't book your travel in advance</span>
                    </div>
                    <div class="tip-item">
                        <span class="tip-icon">✓</span>
                        <span class="tip-text">Have sufficient financial proof</span>
                    </div>
                    <div class="tip-item">
                        <span class="tip-icon">✓</span>
                        <span class="tip-text">Provide proof of not overstaying</span>
                    </div>
                </div>
                <div class="tips-right">
                    <div class="tip-item">
                        <span class="tip-icon">✓</span>
                        <span class="tip-text">Provide a detailed travel itinerary.</span>
                    </div>
                    <div class="tip-item">
                        <span class="tip-icon">✓</span>
                        <span class="tip-text">Don't underestimate the importance of supporting documents</span>
                    </div>
                    <div class="tip-item">
                        <span class="tip-icon">✓</span>
                        <span class="tip-text">Demonstrate strong ties with your home country.</span>
                    </div>
                    <div class="tip-item">
                        <span class="tip-icon">✓</span>
                        <span class="tip-text">Look for help if you need it</span>
                    </div>
                </div>
            </div>
            <div class="contact-visa">
                <div class="contact-button">CONTACT US NOW!</div>
                <div class="contact-info">
                    <div>📞 +91 9591767328</div>
                    <div>✉️ <EMAIL></div>
                </div>
            </div>
        </div>
    </div>

</body>
</html>
