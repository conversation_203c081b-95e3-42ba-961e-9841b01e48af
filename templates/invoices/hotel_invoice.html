<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hotel Invoice - {{ booking_reference }}</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap" rel="stylesheet" />
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            color: #333333;
            font-family: "Roboto", sans-serif;
        }
        
        .font-lora {
            font-family: "Lora", serif !important;
        }
        
        .color-primary {
            color: #FF3951 !important;
        }
        
        .color-red {
            color: #FF3951 !important;
        }
        
        .color-sky {
            color: #0083e7 !important;
        }
        
        .color-gray {
            color: #706e75 !important;
        }
        
        body {
            font-family: 'Roboto', Arial, sans-serif;
            font-size: 14px;
            line-height: 1.6;
            color: #333333;
            background: linear-gradient(135deg, #FEEBEE 0%, #F2EBFA 100%);
            min-height: 100vh;
        }
        
        .invoice-container {
            max-width: 800px;
            margin: 20px auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(255, 57, 81, 0.1);
        }
        
        /* Header Section */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #FF3951;
            padding-bottom: 20px;
        }
        
        .logo-section {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .logo {
            width: 88px;
            height: 20px;
        }
        
        .tagline {
            font-style: italic;
            color: #333333;
            font-size: 12px;
        }
        
        .voucher-title {
            font-size: 32px;
            font-weight: bold;
            color: #FF3951;
            font-style: italic;
            letter-spacing: 2px;
            font-family: "Lora", serif;
        }
        
        /* Booking Info Section */
        .booking-info {
            background: linear-gradient(135deg, #FEEBEE 0%, #F2EBFA 100%);
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 25px;
            border-left: 5px solid #FF3951;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            align-items: center;
        }
        
        .info-row:last-child {
            margin-bottom: 0;
        }
        
        .info-label {
            font-weight: 600;
            color: #FF3951;
            font-size: 14px;
        }
        
        .info-row span:last-child {
            font-weight: 500;
            color: #333333;
        }
        
        /* Hotel Details */
        .hotel-details {
            background: linear-gradient(135deg, #FEEBEE 0%, #F2EBFA 100%);
            padding: 20px;
            border: 2px solid #FF3951;
            border-radius: 12px;
            margin-bottom: 25px;
            text-align: center;
        }
        
        .hotel-name {
            font-size: 24px;
            font-weight: bold;
            color: #FF3951;
            margin-bottom: 8px;
            font-family: "Lora", serif;
        }
        
        .hotel-rating {
            font-size: 20px;
            color: #fbbf24;
            margin-bottom: 10px;
        }
        
        .hotel-address {
            color: #706e75;
            font-style: italic;
            margin-bottom: 8px;
        }
        
        .hotel-phone {
            color: #333333;
            font-weight: 500;
        }
        
        /* Tables */
        .table, .pricing-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 25px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(255, 57, 81, 0.1);
        }
        
        .table th, .pricing-table th {
            background: linear-gradient(135deg, #FF3951 0%, #ff6b7a 100%);
            color: white;
            padding: 15px 12px;
            text-align: left;
            font-weight: 600;
            font-size: 13px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .table td, .pricing-table td {
            padding: 15px 12px;
            border-bottom: 1px solid #FEEBEE;
            vertical-align: top;
        }
        
        .table tbody tr:hover, .pricing-table tbody tr:hover {
            background: #FEEBEE;
        }
        
        .pricing-table {
            border: 2px solid #FF3951;
        }
        
        .pricing-table td strong {
            color: #FF3951;
            font-size: 16px;
        }
        
        /* Policy Sections */
        .policy-section {
            background: linear-gradient(135deg, #FEEBEE 0%, #F2EBFA 100%);
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #FF3951;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #FF3951;
            margin-bottom: 15px;
            border-bottom: 2px solid #FF3951;
            padding-bottom: 8px;
            font-family: "Lora", serif;
        }
        
        .notes-section, .terms-section {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #FEEBEE;
        }
        
        .notes-list, .terms-list {
            margin-left: 20px;
        }
        
        .notes-list li, .terms-list li {
            margin-bottom: 15px;
            line-height: 1.7;
            color: #706e75;
        }
        
        .notes-list li strong, .terms-list li strong {
            color: #FF3951;
        }
        
        /* Contact Info */
        .contact-info {
            background: linear-gradient(135deg, #FF3951 0%, #ff6b7a 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            margin-top: 30px;
        }
        
        .contact-info .section-title {
            color: white;
            border-bottom: 1px solid rgba(255,255,255,0.3);
            margin-bottom: 15px;
        }
        
        .contact-info p {
            font-size: 16px;
            font-weight: 500;
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .invoice-container {
                margin: 10px;
                padding: 20px;
            }
            
            .header {
                flex-direction: column;
                text-align: center;
                gap: 15px;
            }
            
            .voucher-title {
                font-size: 24px;
            }
            
            .info-row {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }
            
            .table th, .table td,
            .pricing-table th, .pricing-table td {
                padding: 10px 8px;
                font-size: 12px;
            }
        }
        
        /* Print Styles */
        @media print {
            body {
                background: white;
            }
            
            .invoice-container {
                margin: 0;
                box-shadow: none;
                border-radius: 0;
            }
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <!-- Header Section -->
        <div class="header">
            <div class="logo-section">
                <img src="https://static-zuumm-public.s3.ap-south-1.amazonaws.com/templates/brand-logo/logo.png" class="logo" alt="Zuumm" />
                <div class="tagline">Travel Smarter With AI-For Business And Leisure</div>
            </div>
            <div class="voucher-title">VOUCHER</div>
        </div>

        <!-- Booking Reference Information -->
        <div class="booking-info">
            <div class="info-row">
                <span class="info-label">Booking Reference:</span>
                <span>{{ booking_reference }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">Confirmation No:</span>
                <span>{{ confirmation_no }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">Date of Issue:</span>
                <span>{{ date_of_issue }}</span>
            </div>
        </div>

        <!-- Hotel Details Section -->
        <div class="hotel-details">
            <div class="hotel-name">{{ hotel_name }}</div>
            <div class="hotel-rating">
                {% for i in "12345"|slice:":"|slice:hotel_rating %}★{% endfor %}
                {% for i in "12345"|slice:hotel_rating %}☆{% endfor %}
            </div>
            <div class="hotel-address">{{ hotel_address }}</div>
            {% if hotel_phone != 'Phone Not Available' %}
            <div class="hotel-phone">Phone No: {{ hotel_phone }}</div>
            {% endif %}
        </div>

        <!-- Check-in/Check-out Details Table -->
        <table class="table">
            <thead>
                <tr>
                    <th>Check In Date</th>
                    <th>Check Out Date</th>
                    <th>Total Rooms</th>
                    <th>Total Stay</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>{{ check_in_date }} {{ check_in_time }}</td>
                    <td>{{ check_out_date }} {{ check_out_time }}</td>
                    <td>{{ total_rooms }}</td>
                    <td>{{ total_nights }}</td>
                </tr>
            </tbody>
        </table>

        <!-- Guest Details Table -->
        <table class="table">
            <thead>
                <tr>
                    <th>Guest Name</th>
                    <th>Room Type</th>
                    <th>Inclusions</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>{{ guest_name }}</td>
                    <td>{{ room_type }}</td>
                    <td>{{ inclusions }}</td>
                </tr>
            </tbody>
        </table>

        <!-- Pricing Table -->
        <table class="pricing-table">
            <thead>
                <tr>
                    <th>Base Fare</th>
                    <th>Taxes & Fees</th>
                    <th>Total</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>{{ base_fare }}</td>
                    <td>{{ taxes_fees }}</td>
                    <td><strong>{{ total_amount }}</strong></td>
                </tr>
            </tbody>
        </table>

        <!-- Special Requests Section -->
        <div class="policy-section">
            <div class="section-title">Special Request(s):</div>
            <p>{{ special_requests }}</p>
        </div>

        <!-- Cancellation Policy Section -->
        <div class="policy-section">
            <div class="section-title">Cancellation Policy:</div>
            <p>{{ cancellation_policy }}</p>
        </div>

        <!-- Booking Notes Section -->
        <div class="section-title">Booking Notes:</div>
        <div class="notes-section">
            <ol class="notes-list">
                <li><strong>Booking Notes</strong><br>
                    Bookings confirmed through Dynamic Rates note there is a mandatory service/resort fee payable directly to the hotel by the guest. Depending on the hotel's policy extra charge for children's Stay and Breakfast is applicable and payable at hotel directly.
                </li>
                <li><strong>Policies</strong><br>
                    Know Before You Go: Pool access available from 6 AM to 7 PM. Reservations are required for golf tee times, massage services, and spa treatments. Reservations can be made by contacting the hotel prior to arrival, using the contact information on the booking confirmation. One child 10 years old or younger stays free when occupying the parent or guardian's room, using existing bedding. No pets and no service animals are allowed at this property. Contactless check-out is available. This property welcomes guests of all sexual orientations and gender identities (LGBTQ+ friendly).
                </li>
                <li><strong>Check-in Instructions</strong><br>
                    Special Instructions: Front desk staff will greet guests on arrival. To register at this property, guests who are Indian citizens must present a valid photo identity card.
                </li>
            </ol>
        </div>

        <!-- General Terms & Conditions -->
        <div class="section-title">General Terms & Conditions</div>
        <div class="terms-section">
            <ol class="terms-list">
                <li>Each country/state may have its own set of COVID-19 guidelines and restrictions. Please check with the hotel or visit the country's/state's website for the same.</li>
                <li>Your booking is confirmed. However, your name will be listed in the hotel's reservation system closer to your arrival date.</li>
                <li>Guest Photo Id must be presented at the time of check-in.</li>
                <li>Credit card or cash deposit may be required for extra services at the time of check-in.</li>
                <li>All extra charges will be borne by the guest directly prior to departure.</li>
                <li>Extra-person and/or Extra-bed charges may apply and vary depending on property policy.</li>
                <li>In case of the guest arrival delayed or postponed due to any unforeseen occurrences, additional charges will be borne by the guest.</li>
                <li>In case of incorrect residency and nationality chosen by the user at the time of booking, additional charges may be applicable which will be borne by the guest and paid to the hotel at the time of check-in/check-out.</li>
                <li>Any special requests are all subject to availability at the time of check-in and are not guaranteed at the time of booking (bed type, smoking room, early check-in, late check-out etc.).</li>
                <li>Full cancellation charges are applicable on early check-out unless otherwise specified.</li>
                <li>Hotels do not permit unmarried or unrelated couples and it is at the hotel management's discretion to allow or cancel the booking. In such case no refund is applicable if the hotel disallows check-in.</li>
                <li>City tax and resort fee (if any) are to be paid directly to the hotel.</li>
                <li>If your booking offers complimentary car transfer you need to inform the hotel of your travel details 24 hours prior to check-in.</li>
                <li>Additional GST Payment (if any) to be paid to the hotel directly by the guest.</li>
                <li>As per RBI guidelines: in case of foreign nationals, it is mandatory to submit a passport copy of the guest. Please send a scanned copy of the guest's passport to us. Failure to comply may result in the cancellation of the booking without notice.</li>
                <li>Guests must be over 18 years of age to check in to this hotel.</li>
                <li>As per Government regulations, it is mandatory for all guests above 18 years of age to carry a valid photo identity card & address proof at the time of check-in. Please note that failure to abide by this can result in the hotel denying a check-in. Hotels charge a compulsory fee for such cancellations.</li>
                <li>The standard check-in and check-out times are 12 noon. "In some cases, the check-in and check-out time may vary as per the hotel policy". Early check-in or late check-out is subject to hotel availability and may also be chargeable by the hotel. Any early check-in or late check-out request must be directed to and reconfirmed with the hotel directly prior to arrival.</li>
                <li>Failure to check-in to the hotel will attract the full cost of stay or a penalty as per the hotel cancellation policy.</li>
                <li>Hotels charge a compulsory Gala Dinner Supplement during Christmas, New Year's Eve or other special events and festivals like Diwali or Dusshera. These additional charges are not included in the booking amount and will be collected directly at the hotel.</li>
                <li>There might be seasonal variations in hotel tariff rates during Peak days, for example, URS period in Ajmer or Lord Jagannath Rath Yatra in Puri, the room tariff differences if any will have to be borne and paid by the customer directly at the hotel for the booking stays period falls during such dates.</li>
                <li>All additional charges other than the room charges and inclusions as mentioned in the booking voucher are to be borne and paid separately during check-out. Please make sure that you are aware of all such charges that may come as extras. Some of them can be WiFi costs, Mini Bar, Laundry Expenses, Telephone calls, Room Service, Snacks, etc.</li>
                <li>Some hotels may have policies that do not allow unmarried/unrelated couples or certain foreign nationalities to check-in without the correct documentation. No refund will be applicable in case the hotel denies check-in under such circumstances. If you have any doubts about this, do call us for any assistance.</li>
                <li>Any changes or booking modifications are subject to availability and charges may apply as per the hotel policies.</li>
            </ol>
        </div>

        <!-- Customer Support -->
        <div class="contact-info">
            <div class="section-title" style="color: white; border-bottom: 1px solid white;">Customer Support</div>
            <p>Email: <EMAIL> | +91 9591767328</p>
        </div>
    </div>
</body>
</html>
