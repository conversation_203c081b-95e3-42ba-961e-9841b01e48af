<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link
      href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100..900;1,100..900&display=swap"
      rel="stylesheet"
    />
    <title>{{ package.title|default:"Package Details" }}</title>
    <style>
      /* Page margins and padding for PDF */
      @page {
        margin: 20px;
        padding: 0;
      }
      
      body {
        margin: 20px;
        padding: 20px;
      }
      
      /* Ensure all major sections have proper spacing */
      .section-container {
        margin-top: 20px;
        margin-bottom: 20px;
        padding-top: 8px;
        padding-bottom: 8px;
      }

      /* Itinerary-specific styling */
      .itinerary-content h3 {
        font-size: 11px !important;
        font-weight: 500 !important;
        color: #333333 !important;
        margin-bottom: 12px !important;
        margin-top: 8px !important;
        padding-left: 12px !important;
        border-left: 3px solid #ff3366 !important;
        position: relative !important;
        line-height: 11px !important;
      }
      
      .itinerary-content h3:first-child {
        margin-top: 0 !important;
      }
      
      .itinerary-content h3:not(:first-child) {
        border-top: 1px solid #e5e5e5 !important;
        padding-top: 8px !important;
        margin-top: 8px !important;
      }
      
      .itinerary-content p,
      .itinerary-content ul,
      .itinerary-content li {
        font-size: 9px !important;
        font-weight: 400 !important;
        color: #555555 !important;
        line-height: 13px !important;
        margin-bottom: 4px !important;
      }
      
      .itinerary-content ul {
        padding-left: 16px !important;
        margin-left: 12px !important;
      }
      
      .itinerary-content li {
        margin-bottom: 3px !important;
      }
      
      /* Remove default margins for better control */
      .itinerary-content * {
        margin-top: 0 !important;
      }

      /* Destination content styling for What to Pack rich text */
      .destination-content p,
      .destination-content ul,
      .destination-content li,
      .destination-content h3,
      .destination-content h4,
      .destination-content h5 {
        font-size: 9px !important;
        font-weight: 400 !important;
        color: #555555 !important;
        line-height: 13px !important;
        margin-bottom: 4px !important;
        margin-top: 0 !important;
      }
      
      .destination-content ul {
        padding-left: 16px !important;
        margin-left: 12px !important;
      }
      
      .destination-content li {
        margin-bottom: 3px !important;
      }
      
      .destination-content h3,
      .destination-content h4,
      .destination-content h5 {
        font-weight: 500 !important;
        color: #333333 !important;
        font-size: 10px !important;
        margin-bottom: 6px !important;
        margin-top: 8px !important;
      }
      
      .destination-content strong {
        font-weight: 500 !important;
        color: #333333 !important;
      }

      /* Page break control */
      .section-container {
        page-break-inside: avoid;
        break-inside: avoid;
      }
      
      .itinerary-content h3 {
        page-break-after: avoid;
        break-after: avoid;
      }
      
      .activity-grid {
        page-break-inside: avoid;
        break-inside: avoid;
      }
      
      .addon-grid {
        page-break-inside: avoid;
        break-inside: avoid;
      }
      
      /* Keep section headers with their content */
      h3 {
        page-break-after: avoid;
        break-after: avoid;
      }
      
      /* Prevent orphans and widows */
      * {
        orphans: 3;
        widows: 3;
      }
      
      /* Better page breaks */
      @media print {
        .page-break-avoid {
          page-break-inside: avoid;
          break-inside: avoid;
        }
        
        .page-break-before {
          page-break-before: always;
          break-before: page;
        }
        
        /* Ensure content doesn't touch page edges */
        body {
          margin: 20px !important;
          padding: 20px !important;
        }
        
        .section-container {
          margin-top: 20px !important;
          margin-bottom: 20px !important;
        }
      }

      /* Star rating styles for half stars */
      .star-rating {
        display: inline-flex;
        align-items: center;
        font-size: 9px;
        line-height: 1;
      }
      
      .star {
        position: relative;
        display: inline-block;
        width: 10px;
        height: 10px;
        margin-right: 1px;
        overflow: hidden;
      }
      
      .star:before {
        content: '★';
        position: absolute;
        left: 0;
        top: 0;
        color: #e5e5e5;
        font-size: 9px;
      }
      
      .star.full:before {
        color: #00af31;
      }
      
      .star.half:before {
        color: #e5e5e5;
      }
      
      .star.half:after {
        content: '★';
        position: absolute;
        left: 0;
        top: 0;
        color: #00af31;
        font-size: 9px;
        width: 50%;
        overflow: hidden;
      }
    </style>
  </head>
  <body
    style="
      background: #f5f6fa;
      font-family: 'Roboto', sans-serif;
    "
  >
    <div
      style="
        max-width: 595px;
        margin: 0 auto;
        background: #ffffff;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        position: relative;
      "
    >
      <div style="padding: 32px 32px 24px 32px">
        <!-- ZUUMM Header -->
        <h1
          style="
            color: #ff3366;
            font-weight: 700;
            letter-spacing: 2px;
            margin-bottom: 32px;
            text-align: center;
            margin-top: 0px;
          "
        >
          ZUUMM
        </h1>

        <!-- Header Section -->
        <div style="background-color: #ff39510d; padding: 16px; margin-bottom: 16px; border-radius: 8px;">
          <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 4px;">
            <button
              style="
                border: 0.5px solid #1e3a8a;
                background: #fff;
                color: #1e3a8a;
                border-radius: 3px;
                padding: 3px 8px;
                font-size: 9px;
                font-weight: 400;
                display: inline-block;
                line-height: 1.2;
                text-align: center;
                width: auto;
                white-space: nowrap;
              "
            >
              {{ package.type|default:"Customizable" }}
            </button>
            {% if package.rating %}
            <div style="text-align: right; line-height: 1;">
              <div style="font-size: 8px; font-weight: 500; color: #333333; margin-bottom: 1px;">
                Rating Stars: {{ package.rating }}
              </div>
              <div class="star-rating">
                {% for i in "12345" %}
                  {% if forloop.counter0 < package.rating_stars.full %}
                    <span class="star full"></span>
                  {% elif forloop.counter0 == package.rating_stars.full and package.rating_stars.half > 0 %}
                    <span class="star half"></span>
                  {% else %}
                    <span class="star"></span>
                  {% endif %}
                {% endfor %}
              </div>
            </div>
            {% endif %}
          </div>
          
          <h1 style="margin: 8px 0; font-size: 16px; font-weight: 600; color: #333333;">
            {{ package.title }}
          </h1>
          
          <div style="color: #1e3a8a; font-size: 8px; font-weight: 500; margin-bottom: 8px;">
            <span>• {{ package.owner|default:"Zuumm" }}</span>
            {% if package.visa_type_display %}
            <span style="margin: 0 8px">•</span>
            <span>{{ package.visa_type_display }}</span>
            {% endif %}
            {% if package.duration_display %}
            <span style="margin: 0 8px">•</span>
            <span>{{ package.duration_display|default:package.duration }}</span>
            {% endif %}
            {% if package.price_display %}
            <span style="margin: 0 8px">•</span>
            <span>{{ package.price_display|default:package.price_per_person }}</span>
            {% endif %}
          </div>
        </div>

        <!-- Tour Overview Section -->
        {% if package.about_this_tour_cleaned %}
        <div style="margin-bottom: 18px;">
          <h3 style="font-weight: 500; font-size: 12px; margin-bottom: 8px; color: #1e3a8a;">
            Tour Overview
          </h3>
          <div style="color: #333333; font-size: 10px; font-weight: 400; line-height: 14px;">
            {{ package.about_this_tour_cleaned }}
          </div>
        </div>
        {% endif %}

        <!-- Tour Highlights Section -->
        {% if package.highlights %}
        <div style="margin-bottom: 18px;">
          <h3 style="font-weight: 500; font-size: 12px; margin-bottom: 8px; color: #1e3a8a;">
            Tour Highlights
          </h3>
          <ul style="color: #333333; font-size: 10px; font-weight: 400; line-height: 14px; padding-left: 16px; margin: 0;">
            {% for highlight in package.highlights %}
            <li style="margin-bottom: 4px;">{{ highlight.value|default:highlight }}</li>
            {% endfor %}
          </ul>
        </div>
        {% endif %}

        <!-- Inclusions Section -->
        {% if package.inclusions %}
        <div style="margin-bottom: 18px;">
          <h3 style="font-weight: 500; font-size: 12px; margin-bottom: 8px; color: #1e3a8a;">
            Inclusions
          </h3>
          <div style="color: #333333; font-size: 9px; font-weight: 400; line-height: 12px;">
            {% for inclusion in package.inclusions %}
            <div style="margin-bottom: 4px;">✓ {{ inclusion.value|default:inclusion }}</div>
            {% endfor %}
          </div>
        </div>
        {% endif %}

        <!-- Exclusions Section -->
        {% if package.exclusions %}
        <div style="margin-bottom: 18px;">
          <h3 style="font-weight: 500; font-size: 12px; margin-bottom: 8px; color: #1e3a8a;">
            Exclusions
          </h3>
          <div style="font-size: 9px; font-weight: 400; line-height: 12px;">
            {% for exclusion in package.exclusions %}
            <div style="display: flex; align-items: flex-start; margin-bottom: 6px;">
              <span style="display: flex; align-items: center; justify-content: center; width: 12px; height: 12px; background: #bebebe; border-radius: 50%; color: #fff; font-size: 8px; font-weight: 700; margin-right: 6px; flex-shrink: 0;">×</span>
              <span style="color: #333333;">{{ exclusion }}</span>
            </div>
            {% endfor %}
          </div>
        </div>
        {% endif %}

        <!-- Itinerary Section -->
        {% if package.itinerary %}
        <div style="margin-bottom: 28px;">
          <h3 style="font-size: 12px; color: #1e3a8a; font-weight: 500; margin-bottom: 16px;">
            Itinerary
          </h3>
          <div class="itinerary-content" style="position: relative;">
            {{ package.itinerary|safe }}
          </div>
        </div>
        {% endif %}

        <!-- Hotels Section -->
        {% if package.hotels %}
        <div style="margin-bottom: 18px;">
          <h3 style="font-weight: 500; font-size: 12px; margin-bottom: 8px; color: #1e3a8a;">
            Where You'll Be Staying
          </h3>
          {% for hotel in package.hotels %}
          <div style="margin-bottom: 6px;">
            <div style="font-size: 10px; font-weight: 500; color: #333333;">
              {{ hotel }}
            </div>
          </div>
          {% endfor %}
        </div>
        {% endif %}

        <!-- Add-ons Section -->
        {% if package.addons %}
        <div class="section-container" style="margin-bottom: 28px;">
          <h3 style="font-weight: 500; font-size: 12px; margin-bottom: 12px; color: #1e3a8a;">
            Add-ons
          </h3>
          <div class="addon-grid" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 12px; margin-bottom: 12px;">
            {% for addon in package.addons %}
            <div style="
              background: #fef7f7; 
              border-radius: 8px; 
              padding: 12px 8px; 
              border: 1px solid #f5e6e6;
              text-align: center;
              box-shadow: 0 1px 3px rgba(0,0,0,0.05);
              min-height: 80px;
            ">
              <div style="margin-bottom: 8px;">
                <span style="
                  font-size: 18px; 
                  color: #6b46c1;
                  display: block;
                  line-height: 1;
                ">✈</span>
              </div>
              <div style="
                font-weight: 500; 
                font-size: 8px; 
                color: #374151; 
                line-height: 10px;
                word-wrap: break-word;
              ">
                {{ addon.value|default:addon }}
              </div>
            </div>
            {% endfor %}
          </div>
        </div>
        {% endif %}

        <!-- Meet your destination Section -->
        <div class="section-container" style="margin-bottom: 28px;">
          <h3 style="font-size: 12px; color: #1e3a8a; font-weight: 600; margin-bottom: 16px;">
            Meet your destination
          </h3>
          <div class="destination-content" style="position: relative;">
            
            <!-- Best time to visit -->
            {% if package.best_time_to_visit %}
            <div style="
              font-size: 11px !important;
              font-weight: 500 !important;
              color: #333333 !important;
              margin-bottom: 12px !important;
              margin-top: 0 !important;
              padding-left: 12px !important;
              border-left: 3px solid #ff3366 !important;
              position: relative !important;
              line-height: 11px !important;
            ">
              Best time to visit
            </div>
            <div style="
              font-size: 9px !important;
              font-weight: 400 !important;
              color: #555555 !important;
              line-height: 13px !important;
              margin-bottom: 4px !important;
              margin-left: 12px !important;
            ">
              {{ package.best_time_to_visit }}
            </div>
            {% endif %}

            <!-- Destination Safety -->
            {% if package.destination_safety %}
            <div style="
              font-size: 11px !important;
              font-weight: 500 !important;
              color: #333333 !important;
              margin-bottom: 12px !important;
              margin-top: 8px !important;
              padding-left: 12px !important;
              border-left: 3px solid #ff3366 !important;
              border-top: 1px solid #e5e5e5 !important;
              padding-top: 8px !important;
              position: relative !important;
              line-height: 11px !important;
            ">
              Destination Safety
            </div>
            <div style="
              font-size: 9px !important;
              font-weight: 400 !important;
              color: #555555 !important;
              line-height: 13px !important;
              margin-bottom: 4px !important;
              margin-left: 12px !important;
            ">
              {{ package.destination_safety }}
            </div>
            {% endif %}

            <!-- Cultural Information -->
            {% if package.cultural_info %}
            <div style="
              font-size: 11px !important;
              font-weight: 500 !important;
              color: #333333 !important;
              margin-bottom: 12px !important;
              margin-top: 8px !important;
              padding-left: 12px !important;
              border-left: 3px solid #ff3366 !important;
              border-top: 1px solid #e5e5e5 !important;
              padding-top: 8px !important;
              position: relative !important;
              line-height: 11px !important;
            ">
              Cultural Information
            </div>
            <div style="
              font-size: 9px !important;
              font-weight: 400 !important;
              color: #555555 !important;
              line-height: 13px !important;
              margin-bottom: 4px !important;
              margin-left: 12px !important;
            ">
              {{ package.cultural_info }}
            </div>
            {% endif %}

            <!-- What To Shop -->
            {% if package.what_to_shop %}
            <div style="
              font-size: 11px !important;
              font-weight: 500 !important;
              color: #333333 !important;
              margin-bottom: 12px !important;
              margin-top: 8px !important;
              padding-left: 12px !important;
              border-left: 3px solid #ff3366 !important;
              border-top: 1px solid #e5e5e5 !important;
              padding-top: 8px !important;
              position: relative !important;
              line-height: 11px !important;
            ">
              What To Shop
            </div>
            <div style="
              font-size: 9px !important;
              font-weight: 400 !important;
              color: #555555 !important;
              line-height: 13px !important;
              margin-bottom: 4px !important;
              margin-left: 12px !important;
            ">
              {{ package.what_to_shop }}
            </div>
            {% endif %}

            <!-- What To Pack -->
            {% if package.what_to_pack %}
            <div style="
              font-size: 11px !important;
              font-weight: 500 !important;
              color: #333333 !important;
              margin-bottom: 12px !important;
              margin-top: 8px !important;
              padding-left: 12px !important;
              border-left: 3px solid #ff3366 !important;
              border-top: 1px solid #e5e5e5 !important;
              padding-top: 8px !important;
              position: relative !important;
              line-height: 11px !important;
            ">
              What To Pack
            </div>
            <div style="
              font-size: 9px !important;
              font-weight: 400 !important;
              color: #555555 !important;
              line-height: 13px !important;
              margin-left: 12px !important;
            " class="destination-content">
              {{ package.what_to_pack|safe }}
            </div>
            {% endif %}

          </div>
        </div>

        <!-- Rating Section -->
        {% if package.rating %}
        <div class="section-container" style="margin-bottom: 18px; background: #fff0f3; border-radius: 10px; padding: 12px; display: flex; align-items: center; gap: 18px;">
          <div>
            <div style="font-size: 10px; color: #000000; font-weight: 500; min-width: 70px;">
              Rating
            </div>
            <div style="color: #00af31; font-size: 12px; min-width: 80px;">
              <div class="star-rating">
                {% for i in "12345" %}
                  {% if forloop.counter0 < package.rating_stars.full %}
                    <span class="star full"></span>
                  {% elif forloop.counter0 == package.rating_stars.full and package.rating_stars.half > 0 %}
                    <span class="star half"></span>
                  {% else %}
                    <span class="star"></span>
                  {% endif %}
                {% endfor %}
              </div>
              <span style="color: #000000; font-size: 10px; font-weight: 500;">{{ package.rating }}/5</span>
            </div>
          </div>
          <div style="font-size: 8px; color: #444; line-height: 12px; font-weight: 400;">
            {{ package.rating_description|default:"Customer rating and feedback will be updated soon." }}
          </div>
        </div>
        {% endif %}

        <!-- Popular Activities Section -->
        {% if package.activities %}
        <div class="section-container" style="margin-bottom: 18px;">
          <h3 style="font-size: 12px; color: #1e3a8a; font-weight: 600; margin-bottom: 12px;">
            Popular Activities
          </h3>
          <div class="activity-grid" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 8px; margin-bottom: 12px;">
            {% for activity in package.activities %}
            <div style="
              position: relative;
              height: 120px;
              border-radius: 8px;
              overflow: hidden;
              box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            ">
              {% if activity.media and activity.media.0 %}
              <img src="{{ activity.media.0.media }}" style="
                width: 100%;
                height: 100%;
                object-fit: cover;
              " alt="{{ activity.title }}" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
              <div style="
                width: 100%;
                height: 100%;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                display: none;
              "></div>
              {% else %}
              <div style="
                width: 100%;
                height: 100%;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              "></div>
              {% endif %}
              <div style="
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                background: rgba(0,0,0,0.6);
                padding: 8px;
              ">
                <div style="
                  color: white;
                  font-size: 9px;
                  font-weight: 600;
                  text-align: center;
                  text-transform: capitalize;
                  line-height: 11px;
                ">
                  {{ activity.title }}
                </div>
              </div>
            </div>
            {% endfor %}
          </div>
        </div>
        {% endif %}

        <!-- Nearby Restaurants Section -->
        {% if package.popular_restaurants %}
        <div style="margin-bottom: 18px;">
          <h3 style="font-size: 12px; color: #1e3a8a; font-weight: 600; margin-bottom: 8px;">
            Nearby Restaurants
          </h3>
          <ul style="color: #333333; font-size: 10px; font-weight: 400; line-height: 16px; padding-left: 16px; margin: 0;">
            {% for restaurant in package.popular_restaurants %}
            <li style="margin-bottom: 6px;">{{ restaurant }}</li>
            {% endfor %}
          </ul>
        </div>
        {% endif %}

      </div>
    </div>
  </body>
</html>
