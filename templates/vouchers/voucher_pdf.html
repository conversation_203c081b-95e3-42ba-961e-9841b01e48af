<!DOCTYPE html>
<!-- Updated template - December 2024 - Footer fixed -->
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Travel Voucher - {{ voucher.booking_id }}</title>
</head>
<body>
    <!-- Header Section -->
    <div class="header">
        <div class="logo">
            <img src="{{ logo_url }}" alt="Zuumm Logo" style="max-height: 60px; max-width: 200px;" />
        </div>
        <div class="voucher-title">TRAVEL VOUCHER</div>
        <div style="font-size: 14px; color: #666; margin-top: 5px;">
            Booking Confirmation & Travel Details
        </div>
    </div>

    <!-- Booking Information -->
    <div class="booking-info">
        <div class="info-row">
            <span class="info-label">Booking ID:</span>
            <span class="info-value" style="font-weight: bold; color: #FF6B35;">{{ voucher.booking_id }}</span>
        </div>
        <div class="info-row">
            <span class="info-label">Booking Date:</span>
            <span class="info-value">{{ formatted_booking_date }}</span>
        </div>
        <div class="info-row">
            <span class="info-label">Travel Dates:</span>
            <span class="info-value">{{ formatted_start_date }} to {{ formatted_end_date }}</span>
        </div>
        <div class="info-row">
            <span class="info-label">Duration:</span>
            <span class="info-value">{{ duration }} Day{% if duration > 1 %}s{% endif %}</span>
        </div>
    </div>

    <!-- Customer Details -->
    <div class="section">
        <div class="section-title">Customer Details</div>
        <div class="info-row">
            <span class="info-label">Name:</span>
            <span class="info-value">{{ voucher.user_name }}</span>
        </div>
        <div class="info-row">
            <span class="info-label">Email:</span>
            <span class="info-value">{{ voucher.user_email }}</span>
        </div>
        <div class="info-row">
            <span class="info-label">Number of Guests:</span>
            <span class="info-value">{{ voucher.number_of_guests }}</span>
        </div>
    </div>

    <!-- Package Details -->
    <div class="section">
        <div class="section-title">Package Details</div>
        <div class="info-row">
            <span class="info-label">Package Name:</span>
            <span class="info-value">{{ voucher.package_name }}</span>
        </div>
        <div class="info-row">
            <span class="info-label">Package Type:</span>
            <span class="info-value">{{ voucher.get_package_type_display }}</span>
        </div>
        
        {% if inclusions_list %}
        <div style="margin-top: 15px;">
            <div style="font-weight: bold; margin-bottom: 8px;">Package Inclusions:</div>
            {% for inclusion in inclusions_list %}
            <div class="inclusion-item">{{ inclusion }}</div>
            {% endfor %}
        </div>
        {% endif %}
    </div>

    <!-- Payment Details -->
    <div class="section">
        <div class="section-title">Payment Information</div>
        <div class="amount-section">
            <div class="info-row">
                <span class="info-label">Total Amount:</span>
                <span class="info-value total-amount">₹{{ voucher.total_amount|floatformat:2 }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">Amount Paid:</span>
                <span class="info-value">₹{{ voucher.amount_paid|floatformat:2 }}</span>
            </div>
            {% if voucher.remaining_amount > 0 %}
            <div class="info-row">
                <span class="info-label">Remaining Amount:</span>
                <span class="info-value status-pending">₹{{ voucher.remaining_amount|floatformat:2 }}</span>
            </div>
            {% endif %}
            <div class="info-row">
                <span class="info-label">Payment Status:</span>
                <span class="info-value {% if voucher.is_fully_paid %}status-paid{% else %}status-pending{% endif %}">
                    {{ payment_status }}
                </span>
            </div>
            <div class="info-row">
                <span class="info-label">Payment Mode:</span>
                <span class="info-value">{{ voucher.get_payment_mode_display }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">Payment Date:</span>
                <span class="info-value">{{ voucher.payment_date|date:"d F Y" }}</span>
            </div>
        </div>
    </div>

    <!-- Itinerary Section -->
    <div class="section">
        <div class="section-title">Day-wise Itinerary</div>
        {% if has_itinerary %}
        {% for day in itinerary_days %}
        <div class="itinerary-day">
            <div class="day-header">
                Day {{ day.day_number }}: {{ day.day_title }}
                <span style="font-weight: normal; color: #666; font-size: 11px;">
                    ({{ day.date|date:"d F Y" }})
                </span>
            </div>
            
            {% if day.description %}
            <div style="margin: 8px 0; color: #555;">{{ day.description }}</div>
            {% endif %}
            
            {% if day.hotel_name %}
            <div style="margin: 5px 0;">
                <strong>Hotel:</strong> {{ day.hotel_name }}
                {% if day.room_type %}({{ day.room_type }}){% endif %}
            </div>
            {% endif %}
            
            {% if day.activities %}
            <div style="margin: 5px 0;">
                <strong>Activities:</strong> {{ day.activities }}
            </div>
            {% endif %}
            
            {% if day.meals_included %}
            <div style="margin: 5px 0;">
                <strong>Meals:</strong> {{ day.get_meals_included_display }}
            </div>
            {% endif %}
            
            {% if day.transportation %}
            <div style="margin: 5px 0;">
                <strong>Transportation:</strong> {{ day.transportation }}
            </div>
            {% endif %}
            
            {% if day.check_in_time or day.check_out_time %}
            <div style="margin: 5px 0; font-size: 11px; color: #666;">
                {% if day.check_in_time %}Check-in: {{ day.check_in_time|time:"H:i" }}{% endif %}
                {% if day.check_in_time and day.check_out_time %} | {% endif %}
                {% if day.check_out_time %}Check-out: {{ day.check_out_time|time:"H:i" }}{% endif %}
            </div>
            {% endif %}
        </div>
        {% endfor %}
        {% else %}
        <div style="padding: 15px; background-color: #f8f9fa; border-radius: 5px; color: #666; text-align: center;">
            <em>Detailed day-wise itinerary will be provided closer to your travel date.</em>
        </div>
        {% endif %}
    </div>

    <!-- Special Notes -->
    {% if special_notes_list %}
    <div class="section">
        <div class="section-title">Special Notes & Instructions</div>
        {% for note in special_notes_list %}
        <div class="note-item">{{ note }}</div>
        {% endfor %}
    </div>
    {% endif %}

    <!-- Important Information -->
    <div class="section">
        <div class="section-title">Important Information</div>
        <div style="font-size: 11px; color: #666; line-height: 1.6;">
            <ul style="margin: 0; padding-left: 20px;">
                <li>Please carry a valid photo ID proof during travel</li>
                <li>Report to the departure point 30 minutes before scheduled time</li>
                <li>This voucher is valid only for the dates mentioned above</li>
                <li>Any changes to the itinerary are subject to availability and additional charges</li>
                <li>For any queries or assistance, please contact your travel provider</li>
                {% if not voucher.is_fully_paid %}
                <li style="color: #dc3545; font-weight: bold;">
                    Please clear the remaining amount of ₹{{ voucher.remaining_amount|floatformat:2 }} before travel
                </li>
                {% endif %}
            </ul>
        </div>
    </div>

    <!-- Footer -->
    <div class="footer">
        <div style="margin-top: 5px;">
            This is a computer-generated voucher. No signature required.
        </div>
        <p>Address : 1005, Prestige Meridian 1,MG Road, Bangalore – 560001</p>
        <p>Email : <EMAIL></p>
        <div style="margin-top: 5px;">
            &copy; {% now "Y" %} {{ partner.entity_name }}. All rights reserved.
        </div>
    </div>
</body>
</html> 