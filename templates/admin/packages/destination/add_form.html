{% extends "admin/change_form.html" %}
{% load static %}

{% block extrahead %}
{{ block.super }}
<style>
    /* <PERSON>de remove button ONLY for destination media inline on add form */
    /* Target specifically the media field inline */
    .inline-group .inline-related .field-media {
        position: relative;
    }
    
    /* Hide delete elements only when they're in the same inline as media field */
    .inline-group .inline-related:has(.field-media) .delete-row,
    .inline-group .inline-related:has(.field-media) input[name*="DELETE"],
    .inline-group .inline-related:has(.field-media) .inline-deletelink,
    .inline-group .inline-related:has(.field-media) .delete {
        display: none !important;
    }
    
    /* Alternative approach for browsers that don't support :has() */
    .inline-group .inline-related .field-media + .delete-row,
    .inline-group .inline-related .field-media ~ .delete-row,
    .inline-group .inline-related .field-media ~ input[name*="DELETE"],
    .inline-group .inline-related .field-media ~ .inline-deletelink,
    .inline-group .inline-related .field-media ~ .delete {
        display: none !important;
    }
    
    /* More specific targeting for the media inline */
    .inline-group .inline-related .field-media {
        margin-right: 0 !important;
    }
    
    /* Hide the entire delete column for media inlines */
    .inline-group .inline-related:has(.field-media) th:last-child,
    .inline-group .inline-related:has(.field-media) td:last-child {
        display: none !important;
    }
</style>
{% endblock %}
