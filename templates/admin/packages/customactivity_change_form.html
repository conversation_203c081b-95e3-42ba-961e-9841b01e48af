{% extends "admin/change_form.html" %}

{% block object-tools-items %}
    {{ block.super }}
    
    {% if original %}
    <!-- Add custom Reframe Highlights With AI button only for existing activities -->
    <li>
        <a href="#" class="btn btn-primary hey-custom-button" 
           onclick="submitAIRequest('highlights'); return false;">
            Reframe Highlights With AI
        </a>
    </li>
    <!-- Add custom Reframe Description With AI button only for existing activities -->
    <li>
        <a href="#" class="btn btn-primary hey-custom-button" 
           onclick="submitAIRequest('description'); return false;">
            Reframe Description With AI
        </a>
    </li>
    {% endif %}
{% endblock %}

{% block extrahead %}
{{ block.super }}
<style>
    /* Loading Overlay Styles for AI Processing */
    .ai-loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.7);
        z-index: 9999;
        display: none;
        align-items: center;
        justify-content: center;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    }
    
    .ai-loading-content {
        background: white;
        padding: 40px 30px;
        border-radius: 12px;
        text-align: center;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        max-width: 90vw;
        max-height: 90vh;
    }
    
    .ai-loading-spinner {
        width: 60px;
        height: 60px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #007bff;
        border-radius: 50%;
        animation: ai-spin 1s linear infinite;
        margin: 0 auto 20px;
    }
    
    @keyframes ai-spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    .ai-loading-title {
        font-size: 24px;
        font-weight: 600;
        color: #333;
        margin-bottom: 15px;
    }
    
    .ai-loading-message {
        font-size: 16px;
        color: #666;
        line-height: 1.5;
    }
    
    /* Disable pointer events when loading */
    body.ai-processing {
        pointer-events: none;
        overflow: hidden;
    }
    
    body.ai-processing .ai-loading-overlay {
        pointer-events: all;
    }

    /* Force proper spacing and sizing for the AI buttons */
    .object-tools ul {
        display: flex !important;
        gap: 12px !important;
        align-items: center !important;
    }
    
    .object-tools li {
        margin: 0 !important;
        margin-bottom: 12px !important; /* Add bottom spacing between buttons */
        list-style: none !important;
    }
    
    /* Style the AI buttons to match other admin buttons exactly */
    .hey-custom-button {
        background-color: #007bff !important;
        border: 1px solid #007bff !important;
        color: #fff !important;
        padding: 8px 16px !important;
        font-size: 14px !important; /* Increased from 13px to 14px to match Close button */
        font-weight: 500 !important; /* Increased font weight for better visibility */
        line-height: 1.5 !important;
        border-radius: 0.25rem !important;
        text-decoration: none !important;
        display: inline-block !important;
        text-align: center !important;
        vertical-align: middle !important;
        cursor: pointer !important;
        white-space: nowrap !important;
        user-select: none !important;
        min-height: 36px !important;
        box-sizing: border-box !important;
        transition: all 0.15s ease-in-out !important;
        width: 100% !important; /* Make button full width like others */
    }
    
    .hey-custom-button:hover,
    .hey-custom-button:focus {
        background-color: #0056b3 !important;
        border-color: #0056b3 !important;
        color: #fff !important;
        text-decoration: none !important;
        outline: 0 !important;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
    }
    
    .hey-custom-button:active {
        background-color: #004085 !important;
        border-color: #004085 !important;
        transform: translateY(1px) !important;
    }
    
    /* Ensure the button matches the height and style of other buttons */
    .object-tools a.btn {
        min-height: 36px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        margin-bottom: 12px !important; /* Add spacing between all buttons */
    }
    
    /* Override any Jazzmin theme conflicts */
    .btn.btn-primary.hey-custom-button {
        background: #007bff !important;
        border-color: #007bff !important;
        color: white !important;
        font-size: 14px !important; /* Ensure font size override */
        font-weight: 500 !important; /* Ensure font weight override */
    }
    
    /* Add spacing between all object-tools buttons */
    .object-tools {
        margin-bottom: 0 !important;
    }
    
    .object-tools li:not(:last-child) {
        margin-bottom: 12px !important;
    }
    
    /* Responsive adjustments */
    @media (max-width: 768px) {
        .object-tools ul {
            flex-direction: column !important;
            gap: 6px !important;
        }
        
        .hey-custom-button {
            width: 100% !important;
        }
    }
</style>
{% endblock %}

{% block content %}
    <!-- AI Loading Overlay -->
    <div id="aiLoadingOverlay" class="ai-loading-overlay">
        <div class="ai-loading-content">
            <div class="ai-loading-spinner"></div>
            <div class="ai-loading-title" id="aiLoadingTitle">🤖 AI Processing...</div>
            <div class="ai-loading-message" id="aiLoadingMessage">
                Please wait while AI processes your content.
            </div>
        </div>
    </div>
    
    {{ block.super }}
    
    {% if original %}
    <!-- Hidden form for reframe highlights AI action -->
    <form id="reframe-highlights-ai-form" method="post" style="display: none;">
        {% csrf_token %}
        <input type="hidden" name="_reframe_highlights_ai" value="1">
    </form>
    <!-- Hidden form for reframe description AI action -->
    <form id="reframe-description-ai-form" method="post" style="display: none;">
        {% csrf_token %}
        <input type="hidden" name="_reframe_description_ai" value="1">
    </form>
    {% endif %}
{% endblock %}

{% block footer %}
    {{ block.super }}
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const aiLoadingOverlay = document.getElementById('aiLoadingOverlay');
            const aiLoadingTitle = document.getElementById('aiLoadingTitle');
            const aiLoadingMessage = document.getElementById('aiLoadingMessage');
            
            // Global function to show AI loading based on type
            window.showAILoading = function(type) {
                if (!aiLoadingOverlay) return;
                
                let title = '🤖 AI Processing...';
                let message = 'Please wait while AI processes your content.';
                
                if (type === 'highlights') {
                    title = '🤖 AI Reframing Highlights...';
                    message = 'Please wait while AI reframes your activity highlights to make them more engaging.';
                } else if (type === 'description') {
                    title = '🤖 AI Reframing Description...';
                    message = 'Please wait while AI reframes your activity description to make it more compelling.';
                }
                
                if (aiLoadingTitle) aiLoadingTitle.textContent = title;
                if (aiLoadingMessage) aiLoadingMessage.textContent = message;
                
                aiLoadingOverlay.style.display = 'flex';
                document.body.classList.add('ai-processing');
            };
            
            // Function to submit AI request via AJAX
            window.submitAIRequest = function(type) {
                showAILoading(type);
                
                // Get CSRF token
                const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
                
                // Prepare form data
                const formData = new FormData();
                formData.append('csrfmiddlewaretoken', csrfToken);
                
                if (type === 'highlights') {
                    formData.append('_reframe_highlights_ai', '1');
                } else if (type === 'description') {
                    formData.append('_reframe_description_ai', '1');
                }
                
                // Submit via AJAX
                fetch(window.location.href, {
                    method: 'POST',
                    body: formData,
                    credentials: 'same-origin'
                })
                .then(response => {
                    if (response.redirected) {
                        // Follow the redirect to reload the page with results
                        window.location.href = response.url;
                    } else {
                        return response.text();
                    }
                })
                .catch(error => {
                    console.error('AI request failed:', error);
                    hideAILoading();
                    alert('An error occurred while processing your AI request. Please try again.');
                });
            };
            
            function hideAILoading() {
                if (aiLoadingOverlay) {
                    aiLoadingOverlay.style.display = 'none';
                    document.body.classList.remove('ai-processing');
                }
            }
            
            // Hide loading on page navigation
            window.addEventListener('beforeunload', hideAILoading);
            window.addEventListener('pageshow', function(event) {
                if (event.persisted) hideAILoading();
            });
        });
    </script>
{% endblock %} 