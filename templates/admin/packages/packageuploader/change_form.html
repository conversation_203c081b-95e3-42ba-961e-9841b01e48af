{% extends "admin/change_form.html" %}
{% load i18n admin_urls %}

{% block extrahead %}
    {{ block.super }}
    <style>
        /* Loading Overlay Styles - simplified for clean display */
        .package-loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            z-index: 9999;
            display: none;
            align-items: center;
            justify-content: center;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        }
        
        .package-loading-content {
            background: white;
            padding: 40px 30px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            max-width: 90vw;
            max-height: 90vh;
        }
        
        .package-loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007cba;
            border-radius: 50%;
            animation: package-spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes package-spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .package-loading-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
        }
        
        .package-loading-message {
            font-size: 16px;
            color: #666;
            line-height: 1.5;
        }
        
        /* Disable pointer events when loading */
        body.package-processing {
            pointer-events: none;
            overflow: hidden;
        }
        
        body.package-processing .package-loading-overlay {
            pointer-events: all;
        }
        
        /* File upload specific styling */
        .file-upload-info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .file-upload-info h4 {
            margin: 0 0 10px 0;
            color: #0066cc;
        }
    </style>
{% endblock %}

{% block content %}
    <!-- Loading Overlay -->
    <div id="packageLoadingOverlay" class="package-loading-overlay">
        <div class="package-loading-content">
            <div class="package-loading-spinner"></div>
            <div class="package-loading-title" id="loadingTitle">Processing Package Upload...</div>
            <div class="package-loading-message" id="loadingMessage">
                Please wait while we process your uploaded package file.
            </div>
        </div>
    </div>
    
    {{ block.super }}
{% endblock %}

{% block footer %}
    {{ block.super }}
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const loadingOverlay = document.getElementById('packageLoadingOverlay');
            const loadingTitle = document.getElementById('loadingTitle');
            const loadingMessage = document.getElementById('loadingMessage');
            
            function showLoading() {
                if (!loadingOverlay) return;
                
                if (loadingTitle) loadingTitle.textContent = 'Processing Package Upload...';
                if (loadingMessage) loadingMessage.textContent = 'Please wait while we process your uploaded package file.';
                
                loadingOverlay.style.display = 'flex';
                document.body.classList.add('package-processing');
            }
            
            function hideLoading() {
                if (loadingOverlay) {
                    loadingOverlay.style.display = 'none';
                    document.body.classList.remove('package-processing');
                }
            }
            
            // Handle form submissions
            const forms = document.querySelectorAll('form');
            forms.forEach((form) => {
                form.addEventListener('submit', function(e) {
                    const fileInput = form.querySelector('input[type="file"]');
                    if (fileInput && fileInput.files && fileInput.files.length > 0) {
                        showLoading();
                    }
                });
            });
            
            // Document-level listener as backup
            document.addEventListener('submit', function(e) {
                const form = e.target;
                const fileInput = form.querySelector('input[type="file"]');
                if (fileInput && fileInput.files && fileInput.files.length > 0) {
                    showLoading();
                }
            });
            
            // Button click listener as backup
            const saveButtons = document.querySelectorAll('input[type="submit"], button[type="submit"], .submit-row input');
            saveButtons.forEach((button) => {
                button.addEventListener('click', function(e) {
                    setTimeout(() => {
                        const fileInput = document.querySelector('input[type="file"]');
                        if (fileInput && fileInput.files && fileInput.files.length > 0) {
                            showLoading();
                        }
                    }, 10);
                });
            });
            
            // Hide loading on page navigation
            window.addEventListener('beforeunload', hideLoading);
            window.addEventListener('pageshow', function(event) {
                if (event.persisted) hideLoading();
            });
            
            // Auto-hide after 3 minutes as failsafe
            let failsafeTimeout;
            const originalShow = showLoading;
            showLoading = function() {
                originalShow();
                clearTimeout(failsafeTimeout);
                failsafeTimeout = setTimeout(() => {
                    hideLoading();
                    alert('Processing is taking longer than expected. Please check if the upload completed successfully.');
                }, 180000);
            };
        });
    </script>
{% endblock %} 