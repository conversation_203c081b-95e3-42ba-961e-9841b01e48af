{% extends "admin/change_list.html" %}
{% load i18n admin_urls %}

{% block object-tools-items %}
    {{ block.super }}
    
    <!-- Upload Package File Button - Redirects to PackageUploader add form -->
    <li style="list-style: none; display: inline-block;">
        <a href="{% url 'admin:packages_packageuploader_add' %}" 
           class="btn btn-success package-action-btn" 
           data-action="upload">
            <i class="fa fa-plus-circle"></i> Upload Package File
        </a>
    </li>
{% endblock %}

{% block extrahead %}
    {{ block.super }}
    <style>
        /* Loading Overlay Styles */
        .package-loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            z-index: 9999;
            display: none;
            align-items: center;
            justify-content: center;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, sans-serif;
        }
        
        .package-loading-content {
            background: white;
            padding: 40px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            max-width: 500px;
            min-width: 400px;
        }
        
        .package-loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007cba;
            border-radius: 50%;
            animation: package-spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes package-spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .package-loading-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
        }
        
        .package-loading-message {
            font-size: 16px;
            color: #666;
            margin-bottom: 20px;
            line-height: 1.5;
        }
        
        .package-loading-details {
            font-size: 14px;
            color: #888;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            text-align: left;
        }
        
        .package-loading-steps {
            list-style: none;
            padding: 0;
            margin: 10px 0;
        }
        
        .package-loading-steps li {
            padding: 5px 0;
            position: relative;
            padding-left: 25px;
        }
        
        .package-loading-steps li:before {
            content: "⏳";
            position: absolute;
            left: 0;
        }
        
        .package-loading-steps li.completed:before {
            content: "✅";
        }
        
        .package-progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin: 15px 0;
        }
        
        .package-progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #007cba, #004d75);
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 4px;
        }
        
        /* Disable pointer events when loading */
        body.package-processing {
            pointer-events: none;
        }
        
        body.package-processing .package-loading-overlay {
            pointer-events: all;
        }
    </style>
{% endblock %}

{% block content %}
    {{ block.super }}
    
    <!-- Loading Overlay -->
    <div id="packageLoadingOverlay" class="package-loading-overlay">
        <div class="package-loading-content">
            <div class="package-loading-spinner"></div>
            <div class="package-loading-title" id="loadingTitle">Processing Package...</div>
            <div class="package-loading-message" id="loadingMessage">
                Please wait while we process your package. This may take a few moments.
            </div>
            
            <div class="package-progress-bar">
                <div class="package-progress-fill" id="progressFill"></div>
            </div>
            
            <div class="package-loading-details">
                <strong>Processing Steps:</strong>
                <ul class="package-loading-steps" id="loadingSteps">
                    <li id="step-upload">📁 File Upload</li>
                    <li id="step-validation">✅ Initial Validation</li>
                    <li id="step-ai">🤖 AI Processing (OpenAI)</li>
                    <li id="step-data-validation">🔍 Data Validation</li>
                    <li id="step-creation">📦 Package Creation</li>
                    <li id="step-completion">🎉 Finalizing</li>
                </ul>
                
                <div style="margin-top: 15px; font-style: italic;">
                    <strong>Note:</strong> AI processing may take 30-60 seconds depending on file complexity.
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block footer %}
    {{ block.super }}
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const loadingOverlay = document.getElementById('packageLoadingOverlay');
            const loadingTitle = document.getElementById('loadingTitle');
            const loadingMessage = document.getElementById('loadingMessage');
            const progressFill = document.getElementById('progressFill');
            const loadingSteps = document.getElementById('loadingSteps');
            
            let currentStep = 0;
            let progressInterval;
            
            const steps = [
                { id: 'step-upload', title: 'Uploading File...', message: 'Uploading your package file to the server.' },
                { id: 'step-validation', title: 'Validating File...', message: 'Checking file format and required fields.' },
                { id: 'step-ai', title: 'AI Processing...', message: 'OpenAI is analyzing and enhancing your package data.' },
                { id: 'step-data-validation', title: 'Validating Data...', message: 'Ensuring all data meets our quality standards.' },
                { id: 'step-creation', title: 'Creating Package...', message: 'Saving your package to the database.' },
                { id: 'step-completion', title: 'Almost Done...', message: 'Finalizing and preparing your package.' }
            ];
            
            function showLoading(actionType = 'upload') {
                // Set appropriate title and message based on action type
                if (actionType === 'upload') {
                    loadingTitle.textContent = 'Processing Package Upload...';
                    loadingMessage.textContent = 'Please wait while we process your uploaded package file.';
                } else if (actionType === 'manual') {
                    loadingTitle.textContent = 'Creating Package...';
                    loadingMessage.textContent = 'Please wait while we process and create your package.';
                } else if (actionType === 'edit') {
                    loadingTitle.textContent = 'Updating Package...';
                    loadingMessage.textContent = 'Please wait while we process your package updates.';
                }
                
                // Show overlay and disable interactions
                loadingOverlay.style.display = 'flex';
                document.body.classList.add('package-processing');
                
                // Reset progress
                currentStep = 0;
                progressFill.style.width = '0%';
                resetSteps();
                
                // Start progress simulation
                startProgress();
            }
            
            function hideLoading() {
                loadingOverlay.style.display = 'none';
                document.body.classList.remove('package-processing');
                clearInterval(progressInterval);
            }
            
            function resetSteps() {
                const stepElements = loadingSteps.querySelectorAll('li');
                stepElements.forEach(step => {
                    step.classList.remove('completed');
                });
            }
            
            function markStepCompleted(stepIndex) {
                if (stepIndex < steps.length) {
                    const stepElement = document.getElementById(steps[stepIndex].id);
                    if (stepElement) {
                        stepElement.classList.add('completed');
                    }
                    
                    // Update title and message
                    if (stepIndex + 1 < steps.length) {
                        loadingTitle.textContent = steps[stepIndex + 1].title;
                        loadingMessage.textContent = steps[stepIndex + 1].message;
                    }
                }
            }
            
            function startProgress() {
                let progress = 0;
                currentStep = 0;
                
                progressInterval = setInterval(() => {
                    // AI processing step (step 2) takes longer
                    const isAIStep = currentStep === 2;
                    const increment = isAIStep ? 0.5 : 2; // Slower progress during AI step
                    
                    progress += increment;
                    progressFill.style.width = Math.min(progress, 95) + '%';
                    
                    // Mark steps as completed based on progress
                    const stepProgress = Math.floor((progress / 100) * steps.length);
                    if (stepProgress > currentStep) {
                        markStepCompleted(currentStep);
                        currentStep = stepProgress;
                    }
                    
                    // Don't complete automatically - wait for actual completion
                    if (progress >= 95) {
                        clearInterval(progressInterval);
                    }
                }, isAIStep ? 1000 : 500); // Slower updates during AI step
            }
            
            // Handle upload button clicks
            document.addEventListener('click', function(e) {
                const target = e.target.closest('a[href*="packageuploader_add"]');
                if (target) {
                    showLoading('upload');
                    // Let the normal navigation happen
                }
            });
            
            // Handle form submissions for package creation/editing
            document.addEventListener('submit', function(e) {
                const form = e.target;
                
                // Check if this is a package-related form
                if (form.action && (
                    form.action.includes('/packages/package/') || 
                    form.action.includes('/packages/packageuploader/')
                )) {
                    let actionType = 'upload';
                    
                    // Determine action type based on URL and form content
                    if (form.action.includes('/add/')) {
                        actionType = form.action.includes('packageuploader') ? 'upload' : 'manual';
                    } else if (form.action.includes('/change/')) {
                        actionType = 'edit';
                    }
                    
                    showLoading(actionType);
                    // Let the form submit normally
                }
            });
            
            // Hide loading on page unload or when coming back
            window.addEventListener('beforeunload', hideLoading);
            window.addEventListener('pageshow', function(event) {
                if (event.persisted) {
                    hideLoading();
                }
            });
            
            // Auto-hide loading after 2 minutes as failsafe
            let failsafeTimeout;
            const originalShow = showLoading;
            showLoading = function(...args) {
                originalShow.apply(this, args);
                clearTimeout(failsafeTimeout);
                failsafeTimeout = setTimeout(() => {
                    hideLoading();
                    alert('Processing is taking longer than expected. Please check if the operation completed successfully.');
                }, 120000); // 2 minutes
            };
            
            // Clear failsafe when hiding
            const originalHide = hideLoading;
            hideLoading = function(...args) {
                clearTimeout(failsafeTimeout);
                originalHide.apply(this, args);
            };
        });
    </script>
{% endblock %} 