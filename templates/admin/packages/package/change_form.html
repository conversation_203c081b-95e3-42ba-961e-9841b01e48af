{% extends "admin/change_form.html" %}
{% load i18n admin_urls %}

{% block extrahead %}
    {{ block.super }}
    <style>
        /* Loading Overlay Styles - simplified for clean display */
        .package-loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            z-index: 9999;
            display: none;
            align-items: center;
            justify-content: center;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        }
        
        .package-loading-content {
            background: white;
            padding: 40px 30px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            max-width: 90vw;
            max-height: 90vh;
        }
        
        .package-loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007cba;
            border-radius: 50%;
            animation: package-spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes package-spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .package-loading-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
        }
        
        .package-loading-message {
            font-size: 16px;
            color: #666;
            line-height: 1.5;
        }
        
        /* Disable pointer events when loading */
        body.package-processing {
            pointer-events: none;
            overflow: hidden;
        }
        
        body.package-processing .package-loading-overlay {
            pointer-events: all;
        }
    </style>
{% endblock %}

{% block content %}
    <!-- Loading Overlay -->
    <div id="packageLoadingOverlay" class="package-loading-overlay">
        <div class="package-loading-content">
            <div class="package-loading-spinner"></div>
            <div class="package-loading-title" id="loadingTitle">Creating Package...</div>
            <div class="package-loading-message" id="loadingMessage">
                Please wait while we process and create your package.
            </div>
        </div>
    </div>
    
    {{ block.super }}
{% endblock %}

{% block footer %}
    {{ block.super }}
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const loadingOverlay = document.getElementById('packageLoadingOverlay');
            const loadingTitle = document.getElementById('loadingTitle');
            const loadingMessage = document.getElementById('loadingMessage');
            
            function showLoading() {
                if (!loadingOverlay) return;
                
                // Determine if this is add or edit based on URL
                const isEdit = window.location.href.includes('/change/');
                
                if (isEdit) {
                    if (loadingTitle) loadingTitle.textContent = 'Updating Package...';
                    if (loadingMessage) loadingMessage.textContent = 'Please wait while we process your package updates.';
                } else {
                    if (loadingTitle) loadingTitle.textContent = 'Creating Package...';
                    if (loadingMessage) loadingMessage.textContent = 'Please wait while we process and create your package.';
                }
                
                loadingOverlay.style.display = 'flex';
                document.body.classList.add('package-processing');
            }
            
            function hideLoading() {
                if (loadingOverlay) {
                    loadingOverlay.style.display = 'none';
                    document.body.classList.remove('package-processing');
                }
            }
            
            // Handle form submissions
            const packageForm = document.querySelector('form');
            if (packageForm) {
                packageForm.addEventListener('submit', function(e) {
                    // Check if this is the main package form
                    const titleField = packageForm.querySelector('input[name="title"]');
                    const aboutField = packageForm.querySelector('textarea[name="about_this_tour"]');
                    const packageNoField = packageForm.querySelector('input[name="package_no"]');
                    
                    const isMainForm = titleField || aboutField || packageNoField;
                    
                    if (isMainForm) {
                        showLoading();
                    }
                });
            }
            
            // Backup: Handle all forms
            const allForms = document.querySelectorAll('form');
            allForms.forEach((form) => {
                form.addEventListener('submit', function(e) {
                    // Check if this form contains package fields
                    const hasPackageFields = form.querySelector('input[name="title"]') || 
                                           form.querySelector('input[name="package_no"]') ||
                                           form.querySelector('textarea[name="about_this_tour"]');
                    
                    if (hasPackageFields) {
                        showLoading();
                    }
                });
            });
            
            // Backup: Button click detection
            const submitButtons = document.querySelectorAll('input[type="submit"], button[type="submit"], .submit-row input');
            submitButtons.forEach((button) => {
                button.addEventListener('click', function(e) {
                    setTimeout(() => {
                        // Check if we're on a package form page
                        if (window.location.href.includes('/packages/package/')) {
                            showLoading();
                        }
                    }, 10);
                });
            });
            
            // Hide loading on page navigation
            window.addEventListener('beforeunload', hideLoading);
            window.addEventListener('pageshow', function(event) {
                if (event.persisted) hideLoading();
            });
            
            // Auto-hide after 2 minutes as failsafe
            let failsafeTimeout;
            const originalShow = showLoading;
            showLoading = function() {
                originalShow();
                clearTimeout(failsafeTimeout);
                failsafeTimeout = setTimeout(() => {
                    hideLoading();
                    alert('Processing is taking longer than expected. Please check if the operation completed successfully.');
                }, 120000);
            };
        });
    </script>
{% endblock %} 