{% extends "admin/change_form.html" %}

{% block object-tools-items %}
    {{ block.super }}
    
    {% if show_fetch_images_button %}
    <li>
        <a href="#" class="btn btn-primary fetch-images-button" 
           data-url="{{ fetch_images_url }}"
           id="fetch-images-btn">
            Fetch Images from SerpAPI
        </a>
    </li>
    <li>
        <a href="#" class="btn btn-success generate-description-button" 
           data-url="{{ generate_description_url }}"
           id="generate-description-btn">
            Generate/Rephrase Description with AI
        </a>
    </li>
    {% endif %}
{% endblock %}

{% block extrahead %}
{{ block.super }}
{% if show_fetch_images_button %}
<style>
    /* Match existing admin button styling */
    .object-tools ul {
        display: flex !important;
        gap: 8px !important;
        align-items: center !important;
    }
    
    .object-tools li {
        margin: 0 !important;
        margin-bottom: 8px !important;
        list-style: none !important;
    }
    
    .fetch-images-button,
    .generate-description-button {
        padding: 8px 16px !important;
        font-size: 14px !important;
        font-weight: 500 !important;
        line-height: 1.5 !important;
        border-radius: 0.25rem !important;
        text-decoration: none !important;
        display: inline-block !important;
        text-align: center !important;
        vertical-align: middle !important;
        cursor: pointer !important;
        white-space: nowrap !important;
        user-select: none !important;
        min-height: 36px !important;
        box-sizing: border-box !important;
        transition: all 0.15s ease-in-out !important;
        width: 100% !important;
        margin-top: 15px !important; /* Add spacing from History button */
    }
    
    .fetch-images-button {
        background-color: #417690 !important;
        border: 1px solid #417690 !important;
        color: #fff !important;
    }
    
    .generate-description-button {
        background-color: #28a745 !important;
        border: 1px solid #28a745 !important;
        color: #fff !important;
    }
    
    .fetch-images-button:hover,
    .fetch-images-button:focus {
        background-color: #205067 !important;
        border-color: #205067 !important;
        color: #fff !important;
        text-decoration: none !important;
    }
    
    .generate-description-button:hover,
    .generate-description-button:focus {
        background-color: #218838 !important;
        border-color: #218838 !important;
        color: #fff !important;
        text-decoration: none !important;
    }
    
    .fetch-images-button:disabled,
    .generate-description-button:disabled {
        background-color: #6c757d !important;
        border-color: #6c757d !important;
        cursor: not-allowed !important;
    }
    
    /* Loading overlay */
    .fetch-images-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.7);
        z-index: 9999;
        display: none;
        justify-content: center;
        align-items: center;
    }
    
    .fetch-images-loader {
        background: white;
        padding: 30px;
        border-radius: 8px;
        text-align: center;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        max-width: 300px;
    }
    
    .spinner {
        border: 4px solid #f3f3f3;
        border-top: 4px solid #0073aa;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        animation: spin 1s linear infinite;
        margin: 0 auto 15px;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>

<script>
(function() {
    'use strict';
    
    document.addEventListener('DOMContentLoaded', function() {
        var fetchBtn = document.getElementById('fetch-images-btn');
        var generateBtn = document.getElementById('generate-description-btn');
        
        if (fetchBtn) {
            fetchBtn.addEventListener('click', handleFetchImages);
        }
        
        if (generateBtn) {
            generateBtn.addEventListener('click', handleGenerateDescription);
        }
        
        function handleFetchImages(e) {
            e.preventDefault();
            
            var fetchUrl = this.getAttribute('data-url');
            if (!fetchUrl) {
                alert('Fetch URL not found');
                return;
            }
            
            if (!confirm('This will fetch up to 5 new images from SerpAPI and add them to existing images. Continue?')) {
                return;
            }
            
            // Disable button and show loading
            fetchBtn.style.pointerEvents = 'none';
            fetchBtn.style.opacity = '0.6';
            fetchBtn.textContent = 'Fetching...';
            
            showLoading();
            
            // Get CSRF token
            var csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
            if (!csrfToken) {
                alert('CSRF token not found');
                resetButton();
                return;
            }
            
            // Make request using XMLHttpRequest for better compatibility
            var xhr = new XMLHttpRequest();
            xhr.open('POST', fetchUrl, true);
            xhr.setRequestHeader('X-CSRFToken', csrfToken.value);
            xhr.setRequestHeader('Content-Type', 'application/json');
            xhr.timeout = 300000; // 5 minutes
            
            xhr.onload = function() {
                hideLoading();
                resetButton();
                
                if (xhr.status === 200) {
                    try {
                        var data = JSON.parse(xhr.responseText);
                        if (data.success) {
                            alert('Success: ' + data.message);
                            window.location.reload();
                        } else {
                            alert('Error: ' + (data.error || 'Unknown error occurred'));
                        }
                    } catch (e) {
                        alert('Error: Invalid response from server');
                    }
                } else {
                    alert('Error: Server returned status ' + xhr.status);
                }
            };
            
            xhr.onerror = function() {
                hideLoading();
                resetButton();
                alert('Error: Network error occurred');
            };
            
            xhr.ontimeout = function() {
                hideLoading();
                resetButton();
                alert('Error: Request timed out. Please try again.');
            };
            
            xhr.send();
        }
        
        function handleGenerateDescription(e) {
            e.preventDefault();
            
            var generateUrl = this.getAttribute('data-url');
            if (!generateUrl) {
                alert('Generate URL not found');
                return;
            }
            
            if (!confirm('This will generate or rephrase the hotel description using AI. If a description exists, it will be rephrased to improve it. Continue?')) {
                return;
            }
            
            // Disable button and show loading
            generateBtn.style.pointerEvents = 'none';
            generateBtn.style.opacity = '0.6';
            generateBtn.textContent = 'Generating...';
            
            showLoadingOverlay('Generating hotel description using AI...', 'This may take up to 2 minutes');
            
            // Get CSRF token
            var csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
            if (!csrfToken) {
                alert('CSRF token not found');
                resetGenerateButton();
                return;
            }
            
            // Make request using XMLHttpRequest
            var xhr = new XMLHttpRequest();
            xhr.open('POST', generateUrl, true);
            xhr.setRequestHeader('X-CSRFToken', csrfToken.value);
            xhr.setRequestHeader('Content-Type', 'application/json');
            xhr.timeout = 120000; // 2 minutes
            
            xhr.onload = function() {
                hideLoading();
                resetGenerateButton();
                
                if (xhr.status === 200) {
                    try {
                        var data = JSON.parse(xhr.responseText);
                        if (data.success) {
                            alert('Success: ' + data.message);
                            window.location.reload();
                        } else {
                            alert('Error: ' + (data.error || 'Unknown error occurred'));
                        }
                    } catch (e) {
                        alert('Error: Invalid response from server');
                    }
                } else {
                    alert('Error: Server returned status ' + xhr.status);
                }
            };
            
            xhr.onerror = function() {
                hideLoading();
                resetGenerateButton();
                alert('Error: Network error occurred');
            };
            
            xhr.ontimeout = function() {
                hideLoading();
                resetGenerateButton();
                alert('Error: Request timed out. Please try again.');
            };
            
            xhr.send();
        }
        
        function resetButton() {
            var fetchBtn = document.getElementById('fetch-images-btn');
            if (fetchBtn) {
                fetchBtn.style.pointerEvents = '';
                fetchBtn.style.opacity = '';
                fetchBtn.textContent = 'Fetch Images from SerpAPI';
            }
        }
        
        function resetGenerateButton() {
            var generateBtn = document.getElementById('generate-description-btn');
            if (generateBtn) {
                generateBtn.style.pointerEvents = '';
                generateBtn.style.opacity = '';
                generateBtn.textContent = 'Generate/Rephrase Description with AI';
            }
        }
        
        function showLoading() {
            showLoadingOverlay('Fetching images from SerpAPI...', 'Please do not refresh the page<br>This may take up to 5 minutes');
        }
        
        function showLoadingOverlay(message, subtitle) {
            var overlay = document.createElement('div');
            overlay.className = 'fetch-images-overlay';
            overlay.id = 'loading-overlay';
            overlay.style.display = 'flex';
            overlay.innerHTML = 
                '<div class="fetch-images-loader">' +
                    '<div class="spinner"></div>' +
                    '<p><strong>' + message + '</strong></p>' +
                    '<small>' + subtitle + '</small>' +
                '</div>';
            document.body.appendChild(overlay);
        }
        
        function hideLoading() {
            var overlay = document.getElementById('loading-overlay');
            if (overlay) {
                overlay.remove();
            }
        }
    });
})();
</script>
{% endif %}
{% endblock %}