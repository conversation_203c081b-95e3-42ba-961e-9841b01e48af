{% extends "admin/change_form.html" %}

{% block object-tools-items %}
    {{ block.super }}
    
    {% if original %}
    <!-- Add custom Send Voucher Email button only for existing vouchers -->
    <li>
        <a href="#" class="btn btn-success hey-custom-button" 
           onclick="document.getElementById('send-voucher-email-form').submit(); return false;">
            Send Voucher Email
        </a>
    </li>
    {% endif %}
{% endblock %}

{% block extrahead %}
{{ block.super }}
<style>
    /* Force proper spacing and sizing for the Send Voucher Email button */
    .object-tools ul {
        display: flex !important;
        gap: 8px !important;
        align-items: center !important;
    }
    
    .object-tools li {
        margin: 0 !important;
        margin-bottom: 8px !important; /* Add bottom spacing between buttons */
        list-style: none !important;
    }
    
    /* Style the Send Voucher Email button to match other admin buttons exactly */
    .hey-custom-button {
        background-color: #28a745 !important;
        border: 1px solid #28a745 !important;
        color: #fff !important;
        padding: 8px 16px !important;
        font-size: 14px !important; /* Increased from 13px to 14px to match Close button */
        font-weight: 500 !important; /* Increased font weight for better visibility */
        line-height: 1.5 !important;
        border-radius: 0.25rem !important;
        text-decoration: none !important;
        display: inline-block !important;
        text-align: center !important;
        vertical-align: middle !important;
        cursor: pointer !important;
        white-space: nowrap !important;
        user-select: none !important;
        min-height: 36px !important;
        box-sizing: border-box !important;
        transition: all 0.15s ease-in-out !important;
        width: 100% !important; /* Make button full width like others */
    }
    
    .hey-custom-button:hover,
    .hey-custom-button:focus {
        background-color: #218838 !important;
        border-color: #1e7e34 !important;
        color: #fff !important;
        text-decoration: none !important;
        outline: 0 !important;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
    }
    
    .hey-custom-button:active {
        background-color: #1e7e34 !important;
        border-color: #1a6727 !important;
        transform: translateY(1px) !important;
    }
    
    /* Ensure the button matches the height and style of other buttons */
    .object-tools a.btn {
        min-height: 36px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        margin-bottom: 8px !important; /* Add spacing between all buttons */
    }
    
    /* Override any Jazzmin theme conflicts */
    .btn.btn-success.hey-custom-button {
        background: #28a745 !important;
        border-color: #28a745 !important;
        color: white !important;
        font-size: 14px !important; /* Ensure font size override */
        font-weight: 500 !important; /* Ensure font weight override */
    }
    
    /* Add spacing between all object-tools buttons */
    .object-tools {
        margin-bottom: 0 !important;
    }
    
    .object-tools li:not(:last-child) {
        margin-bottom: 8px !important;
    }
    
    /* Responsive adjustments */
    @media (max-width: 768px) {
        .object-tools ul {
            flex-direction: column !important;
            gap: 4px !important;
        }
        
        .hey-custom-button {
            width: 100% !important;
        }
    }
</style>
{% endblock %}

{% block content %}
    {{ block.super }}
    
    {% if original %}
    <!-- Hidden form for send voucher email action -->
    <form id="send-voucher-email-form" method="post" style="display: none;">
        {% csrf_token %}
        <input type="hidden" name="_send_voucher_email" value="1">
    </form>
    {% endif %}
{% endblock %} 