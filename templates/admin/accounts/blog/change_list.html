{% extends "admin/change_list.html" %}
{% load i18n admin_urls %}

{% block extrahead %}
    {{ block.super }}
    <style>
        /* Loading Overlay Styles - simplified for clean display */
        .blog-loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            z-index: 9999;
            display: none;
            align-items: center;
            justify-content: center;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        }
        
        .blog-loading-content {
            background: white;
            padding: 40px 30px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            max-width: 90vw;
            max-height: 90vh;
        }
        
        .blog-loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007cba;
            border-radius: 50%;
            animation: blog-spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes blog-spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .blog-loading-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
        }
        
        .blog-loading-message {
            font-size: 16px;
            color: #666;
            line-height: 1.5;
        }
        
        /* Disable pointer events when loading */
        body.blog-processing {
            pointer-events: none;
        }
        
        body.blog-processing .blog-loading-overlay {
            pointer-events: all;
        }
        
        /* Blog list enhancements */
        .addlink {
            transition: all 0.3s ease;
        }
        
        .addlink:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 123, 186, 0.3);
        }
    </style>
{% endblock %}

{% block content %}
    {{ block.super }}
    
    <!-- Loading Overlay -->
    <div id="blogLoadingOverlay" class="blog-loading-overlay">
        <div class="blog-loading-content">
            <div class="blog-loading-spinner"></div>
            <div class="blog-loading-title" id="loadingTitle">Preparing Blog Form...</div>
            <div class="blog-loading-message" id="loadingMessage">
                Please wait while we prepare the blog creation form.
            </div>
        </div>
    </div>
{% endblock %}

{% block footer %}
    {{ block.super }}
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const loadingOverlay = document.getElementById('blogLoadingOverlay');
            const loadingTitle = document.getElementById('loadingTitle');
            const loadingMessage = document.getElementById('loadingMessage');
            
            function showLoading(actionType = 'add') {
                if (!loadingOverlay) return;
                
                // Set appropriate title and message based on action type
                if (actionType === 'add') {
                    if (loadingTitle) loadingTitle.textContent = 'Preparing Blog Form...';
                    if (loadingMessage) loadingMessage.textContent = 'Please wait while we prepare the blog creation form.';
                } else if (actionType === 'edit') {
                    if (loadingTitle) loadingTitle.textContent = 'Loading Blog...';
                    if (loadingMessage) loadingMessage.textContent = 'Please wait while we load the blog for editing.';
                }
                
                loadingOverlay.style.display = 'flex';
                document.body.classList.add('blog-processing');
            }
            
            function hideLoading() {
                if (loadingOverlay) {
                    loadingOverlay.style.display = 'none';
                    document.body.classList.remove('blog-processing');
                }
            }
            
            // Handle "Add Blog" button clicks
            document.addEventListener('click', function(e) {
                // Check for "Add Blog" link
                const addBlogLink = e.target.closest('a[href*="/accounts/blog/add/"]');
                if (addBlogLink) {
                    showLoading('add');
                    return; // Let the navigation happen
                }
                
                // Check for edit links (blog title or change links)
                const editBlogLink = e.target.closest('a[href*="/accounts/blog/"][href*="/change/"]');
                if (editBlogLink) {
                    showLoading('edit');
                    return; // Let the navigation happen
                }
                
                // Check for any blog-related actions
                const blogActionLink = e.target.closest('a[href*="/accounts/blog/"]');
                if (blogActionLink && !blogActionLink.href.includes('delete')) {
                    showLoading('edit');
                    return; // Let the navigation happen
                }
            });
            
            // Handle form submissions (like bulk actions)
            document.addEventListener('submit', function(e) {
                const form = e.target;
                
                // Check if this is a blog-related form
                if (form.action && form.action.includes('/accounts/blog/')) {
                    showLoading('edit');
                    // Let the form submit normally
                }
            });
            
            // Hide loading on page unload or when coming back
            window.addEventListener('beforeunload', hideLoading);
            window.addEventListener('pageshow', function(event) {
                if (event.persisted) {
                    hideLoading();
                }
            });
            
            // Auto-hide loading after 30 seconds as failsafe for list actions
            let failsafeTimeout;
            const originalShow = showLoading;
            showLoading = function(...args) {
                originalShow.apply(this, args);
                clearTimeout(failsafeTimeout);
                failsafeTimeout = setTimeout(() => {
                    hideLoading();
                    // No alert for list actions as they should be quick
                }, 30000);
            };
        });
    </script>
{% endblock %} 