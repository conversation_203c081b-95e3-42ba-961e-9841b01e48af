{% extends "admin/change_form.html" %}
{% load i18n admin_urls %}

{% block extrahead %}
    {{ block.super }}
    <style>
        /* Loading Overlay Styles - simplified for clean display */
        .blog-loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            z-index: 9999;
            display: none;
            align-items: center;
            justify-content: center;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        }
        
        .blog-loading-content {
            background: white;
            padding: 40px 30px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            max-width: 90vw;
            max-height: 90vh;
        }
        
        .blog-loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007cba;
            border-radius: 50%;
            animation: blog-spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes blog-spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .blog-loading-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
        }
        
        .blog-loading-message {
            font-size: 16px;
            color: #666;
            line-height: 1.5;
        }
        
        /* Disable pointer events when loading */
        body.blog-processing {
            pointer-events: none;
            overflow: hidden;
        }
        
        body.blog-processing .blog-loading-overlay {
            pointer-events: all;
        }
        
        /* Blog form specific enhancements */
        .field-type select {
            padding: 8px;
            border-radius: 4px;
        }
        
        .field-banner input[type="file"] {
            padding: 8px;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }
        
        .field-banner input[type="file"]:hover {
            border-color: #007cba;
            background: #e7f3ff;
        }
    </style>
{% endblock %}

{% block content %}
    <!-- Loading Overlay -->
    <div id="blogLoadingOverlay" class="blog-loading-overlay">
        <div class="blog-loading-content">
            <div class="blog-loading-spinner"></div>
            <div class="blog-loading-title" id="loadingTitle">Creating Blog...</div>
            <div class="blog-loading-message" id="loadingMessage">
                Please wait while we process and enhance your blog with AI.
            </div>
        </div>
    </div>
    
    {{ block.super }}
{% endblock %}

{% block footer %}
    {{ block.super }}
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const loadingOverlay = document.getElementById('blogLoadingOverlay');
            const loadingTitle = document.getElementById('loadingTitle');
            const loadingMessage = document.getElementById('loadingMessage');
            
            function showLoading() {
                if (!loadingOverlay) return;
                
                // Determine if this is add or edit based on URL
                const isEdit = window.location.href.includes('/change/');
                
                if (isEdit) {
                    if (loadingTitle) loadingTitle.textContent = 'Updating Blog...';
                    if (loadingMessage) loadingMessage.textContent = 'Please wait while we process your blog updates with AI enhancement.';
                } else {
                    if (loadingTitle) loadingTitle.textContent = 'Creating Blog...';
                    if (loadingMessage) loadingMessage.textContent = 'Please wait while we process and enhance your blog with AI.';
                }
                
                loadingOverlay.style.display = 'flex';
                document.body.classList.add('blog-processing');
            }
            
            function hideLoading() {
                if (loadingOverlay) {
                    loadingOverlay.style.display = 'none';
                    document.body.classList.remove('blog-processing');
                }
            }
            
            // Handle form submissions
            const blogForm = document.querySelector('form');
            if (blogForm) {
                blogForm.addEventListener('submit', function(e) {
                    // Check if this is the main blog form
                    const titleField = blogForm.querySelector('input[name="title"]');
                    const contentField = blogForm.querySelector('textarea[name="content"]');
                    const typeField = blogForm.querySelector('select[name="type"]');
                    
                    const isMainForm = titleField || contentField || typeField;
                    
                    if (isMainForm) {
                        showLoading();
                    }
                });
            }
            
            // Backup: Handle all forms
            const allForms = document.querySelectorAll('form');
            allForms.forEach((form) => {
                form.addEventListener('submit', function(e) {
                    // Check if this form contains blog fields
                    const hasBlogFields = form.querySelector('input[name="title"]') || 
                                         form.querySelector('textarea[name="content"]') ||
                                         form.querySelector('select[name="type"]');
                    
                    if (hasBlogFields) {
                        showLoading();
                    }
                });
            });
            
            // Backup: Button click detection
            const submitButtons = document.querySelectorAll('input[type="submit"], button[type="submit"], .submit-row input');
            submitButtons.forEach((button) => {
                button.addEventListener('click', function(e) {
                    setTimeout(() => {
                        // Check if we're on a blog form page
                        if (window.location.href.includes('/accounts/blog/')) {
                            showLoading();
                        }
                    }, 10);
                });
            });
            
            // Hide loading on page navigation
            window.addEventListener('beforeunload', hideLoading);
            window.addEventListener('pageshow', function(event) {
                if (event.persisted) hideLoading();
            });
            
            // Auto-hide after 3 minutes as failsafe
            let failsafeTimeout;
            const originalShow = showLoading;
            showLoading = function() {
                originalShow();
                clearTimeout(failsafeTimeout);
                failsafeTimeout = setTimeout(() => {
                    hideLoading();
                    alert('Blog processing is taking longer than expected. Please check if the operation completed successfully.');
                }, 180000);
            };
        });
    </script>
{% endblock %} 