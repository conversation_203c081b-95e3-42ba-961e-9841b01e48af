"""
Utility functions for the zuumm project.
"""
import os
import json
import boto3
from pathlib import Path
from dotenv import load_dotenv
from functools import lru_cache
from typing import Dict, Any, Optional
from base.settings_utils import SettingsManager, init_settings

# Environment types
ENV_LOCAL = 'local'
ENV_DEV = 'dev'
ENV_UAT = 'uat'
ENV_PROD = 'prod'

# Initialize settings manager
settings = init_settings(Path(__file__).resolve().parent.parent)

def get_environment() -> str:
    """Get the current environment."""
    return settings.environment

def load_env_file() -> Dict[str, Any]:
    """Load all variables from .env file into a dictionary."""
    load_dotenv()
    return {key: value for key, value in os.environ.items()}

def get_aws_secrets(env: str) -> Dict[str, Any]:
    """Get secrets from AWS Secrets Manager."""
    secret_name = f"zuumm/{env}/secrets"
    region_name = "ap-south-1" # Can be overridden by environment
    
    session = boto3.session.Session()
    client = session.client(
        service_name='secretsmanager',
        region_name=region_name
    )
    
    try:
        response = client.get_secret_value(SecretId=secret_name)
        return json.loads(response['SecretString'])
    except Exception as e:
        raise Exception(f"Error fetching secrets from AWS: {str(e)}")

@lru_cache()
def get_secrets() -> Dict[str, Any]:
    """
    Get secrets based on environment.
    - Local: Uses .env file
    - Others: Uses AWS Secrets Manager
    """
    env = get_environment()
    
    if env == ENV_LOCAL:
        return load_env_file()
    else:
        return get_aws_secrets(env)

def get_secret(key: str, default: Any = None) -> Any:
    """
    Get a specific secret value.
    Args:
        key: The key to look up
        default: Default value if key is not found
    Returns:
        The secret value or default if not found
    """
    return settings.get(key, default)

def get_required_secret(key: str) -> Any:
    """
    Get a required secret value. Raises an error if not found.
    Args:
        key: The key to look up
    Returns:
        The secret value
    Raises:
        KeyError: If the secret is not found
    """
    return settings.get_required(key) 