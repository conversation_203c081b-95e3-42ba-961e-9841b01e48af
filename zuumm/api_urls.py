from django.contrib import admin
from django.urls import path, include
from rest_framework_simplejwt.views import (
    TokenRefreshView,
    TokenVerifyView,
)

from accounts.api.v1.views import ZuummTokenObtainPairView


urlpatterns = [
    # Account URLs
    path("accounts/", include("accounts.urls")),

    # Module URLs
    path("modules/", include("modules.urls")),

    # Package URLs
    path("packages/", include("packages.urls")),

    # Booking URLs
    path("bookings/", include("bookings.urls")),

    # Chat URLs
    path("chat/", include("chat.urls")),

    # JWT Token endpoints
    path('token/', ZuummTokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('token/verify/', TokenVerifyView.as_view(), name='token_verify'),
]
