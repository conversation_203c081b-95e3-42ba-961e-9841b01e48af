"""
Django settings for zuumm project.

Generated by 'django-admin startproject' using Django 5.1.5.

For more information on this file, see
https://docs.djangoproject.com/en/5.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.1/ref/settings/
"""

import os
from pathlib import Path
from datetime import timed<PERSON><PERSON>
from base.settings_utils import init_settings, SettingsManager

from base.custom_admin_jazzmin import JAZZMIN_SETTINGS_CONFIGURATION
import sentry_sdk
from sentry_sdk.integrations.django import DjangoIntegration

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Initialize settings manager
settings = init_settings(BASE_DIR)

# Environment
ENVIRONMENT = settings.environment
ENV_LOCAL = SettingsManager.ENV_LOCAL
ENV_DEV = SettingsManager.ENV_DEV
ENV_UAT = SettingsManager.ENV_UAT
ENV_PROD = SettingsManager.ENV_PROD

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = settings.get_required('SECRET_KEY')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = settings.get_bool('DEBUG', False)

ALLOWED_HOSTS = settings.get_list('ALLOWED_HOSTS', ['*'])


# Application definition

INSTALLED_APPS = [
    # Jazzmin
    "jazzmin",

    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    # "django.contrib.sites",
    
    # DB
    'django.contrib.gis',
    'django.contrib.postgres',

    # Rest Framework
    "rest_framework",
    "django_filters",
    
    # JWT
    "rest_framework_simplejwt",
    "rest_framework_simplejwt.token_blacklist",

    # Third Party
    "corsheaders",
    "import_export",
    "django_better_admin_arrayfield",
    "ckeditor",
    "ckeditor_uploader",
    
    # Django Apps
    "accounts",
    "dynamic_packages",
    "modules",
    "packages",
    "bookings",
    "chat",
]

# Custom user model
AUTH_USER_MODEL = 'accounts.User'

MIDDLEWARE = [
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    'base.middleware.PartnerMiddleware',
]

ROOT_URLCONF = "zuumm.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [str(BASE_DIR / "templates/")],  # Simple path to templates
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "zuumm.wsgi.application"


# Database
# https://docs.djangoproject.com/en/5.1/ref/settings/#databases

# Primary Database Configuration (Writer)
PRIMARY_DATABASE_CONFIG = {
    "ENGINE": "django.contrib.gis.db.backends.postgis",
    "NAME": settings.get_required('DATABASE_NAME'),
    "USER": settings.get_required('DATABASE_USER'),
    "PASSWORD": settings.get_required('DATABASE_PASSWORD'),
    "HOST": settings.get_required('DATABASE_HOST'),
    "PORT": 5432,
}

# # Production Database Routing Setup
# if ENVIRONMENT == ENV_PROD:
#     # Only enable replica routing in production
#     REPLICA_HOST = settings.get('DATABASE_REPLICA_HOST')
    
#     if REPLICA_HOST:
#         # Production setup with separate replica host
#         REPLICA_DATABASE_CONFIG = {
#             "ENGINE": "django.contrib.gis.db.backends.postgis",
#             "NAME": settings.get_required('DATABASE_NAME'),
#             "USER": settings.get_required('DATABASE_USER'),
#             "PASSWORD": settings.get_required('DATABASE_PASSWORD'),
#             "HOST": REPLICA_HOST,
#             "PORT": 5432,
#         }
        
#         # Optional Database Settings
#         if db_options := settings.get_json('DATABASE_OPTIONS'):
#             PRIMARY_DATABASE_CONFIG['OPTIONS'] = db_options
#             REPLICA_DATABASE_CONFIG['OPTIONS'] = db_options

#         # Enable routing only in production with replica
#         DATABASE_ROUTERS = ['zuumm.db_router.ProductionDBRouter']
        
#         DATABASES = {
#             "default": PRIMARY_DATABASE_CONFIG,  # Primary (Writer)
#             "replica": REPLICA_DATABASE_CONFIG,  # Replica (Reader)
#         }
#     else:
#         # Production but no replica configured - use single database
#         if db_options := settings.get_json('DATABASE_OPTIONS'):
#             PRIMARY_DATABASE_CONFIG['OPTIONS'] = db_options
        
#         DATABASE_ROUTERS = []
#         DATABASES = {
#             "default": PRIMARY_DATABASE_CONFIG,
#         }
# else:
#     # Development/UAT - always use single database
#     if db_options := settings.get_json('DATABASE_OPTIONS'):
#         PRIMARY_DATABASE_CONFIG['OPTIONS'] = db_options
    
#     DATABASE_ROUTERS = []
#     DATABASES = {
#         "default": PRIMARY_DATABASE_CONFIG,
#     }


if db_options := settings.get_json('DATABASE_OPTIONS'):
    PRIMARY_DATABASE_CONFIG['OPTIONS'] = db_options

DATABASE_ROUTERS = []
DATABASES = {
    "default": PRIMARY_DATABASE_CONFIG,
}


# Password validation
# https://docs.djangoproject.com/en/5.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# AWS Settings
AWS_ACCESS_KEY_ID = settings.get_required('AWS_ACCESS_KEY_ID')
AWS_SECRET_ACCESS_KEY = settings.get_required('AWS_SECRET_ACCESS_KEY')
AWS_S3_REGION_NAME = settings.get_required('AWS_S3_REGION_NAME')
AWS_STORAGE_BUCKET_NAME = settings.get_required('AWS_STORAGE_BUCKET_NAME')
AWS_CLOUDFRONT_DOMAIN = settings.get_required('AWS_CLOUDFRONT_DOMAIN')

# AWS Static Files Configuration
AWS_STATIC_LOCATION = 'static'

AWS_S3_CUSTOM_DOMAIN = f"{AWS_STORAGE_BUCKET_NAME}.s3.{AWS_S3_REGION_NAME}.amazonaws.com"
STORAGES = {
    "staticfiles": {
        "BACKEND": "storages.backends.s3boto3.S3Boto3Storage",
        "OPTIONS": {
            "location": AWS_STATIC_LOCATION,
        },
    },
    "default": {
        "BACKEND": "storages.backends.s3boto3.S3Boto3Storage",
        "OPTIONS": {
            "location": "",  # No prefix, let the model's upload_to handle the path
        },
    },
}
STATIC_URL = f'https://{AWS_S3_CUSTOM_DOMAIN}/{AWS_STATIC_LOCATION}/'

# For collecting static files locally before uploading to S3
STATICFILES_DIRS = [
    os.path.join(BASE_DIR, 'static'),
]

# Email Configuration (Sendgrid)
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
DEFAULT_FROM_EMAIL = "<EMAIL>"
EMAIL_HOST = "smtp.sendgrid.net"
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = "apikey"
EMAIL_HOST_PASSWORD = settings.get_required("SENDGRID_API_KEY")

# SMS Configuration (MSG91)
MSG91_API_KEY = settings.get('MSG91_API_KEY') 
MSG91_TEMPLATE_ID = settings.get('MSG91_TEMPLATE_ID')


# Internationalization
# https://docs.djangoproject.com/en/5.1/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "UTC"

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.1/howto/static-files/


# Default primary key field type
# https://docs.djangoproject.com/en/5.1/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# Authentication settings
AUTHENTICATION_BACKENDS = [
    'accounts.admin_auth.auth.PartnerAdminAuthBackend',  # For partner admin and superadmin login
]

# Rest Framework Settings
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    ),
    'DEFAULT_PERMISSION_CLASSES': (
        'rest_framework.permissions.IsAuthenticated',
    ),
    'DEFAULT_RENDERER_CLASSES': (
        'base.custom_renderer.CustomJSONRenderer',
    ),
    'DEFAULT_FILTER_BACKENDS': (
        'django_filters.rest_framework.DjangoFilterBackend',
        'rest_framework.filters.SearchFilter',
        'rest_framework.filters.OrderingFilter',
    ),
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 10,
}

# JWT Settings
SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=settings.get_int('JWT_ACCESS_TOKEN_LIFETIME', 60)),
    'REFRESH_TOKEN_LIFETIME': timedelta(minutes=settings.get_int('JWT_REFRESH_TOKEN_LIFETIME', 1440)),
    'ROTATE_REFRESH_TOKENS': settings.get_bool('JWT_ROTATE_REFRESH_TOKENS', False),
    'BLACKLIST_AFTER_ROTATION': settings.get_bool('JWT_BLACKLIST_AFTER_ROTATION', True),
    'UPDATE_LAST_LOGIN': settings.get_bool('JWT_UPDATE_LAST_LOGIN', False),

    'ALGORITHM': settings.get('JWT_ALGORITHM', 'HS256'),
    'SIGNING_KEY': SECRET_KEY,
    'VERIFYING_KEY': SECRET_KEY,
    'AUDIENCE': settings.get('JWT_AUDIENCE'),
    'ISSUER': settings.get('JWT_ISSUER'),
    'LEEWAY': timedelta(seconds=settings.get_int('JWT_LEEWAY', 0)),

    'AUTH_HEADER_TYPES': tuple(settings.get_list('JWT_AUTH_HEADER_TYPES', ['Bearer'])),
    'AUTH_HEADER_NAME': settings.get('JWT_AUTH_HEADER_NAME', 'HTTP_AUTHORIZATION'),
    'USER_ID_FIELD': settings.get('JWT_USER_ID_FIELD', 'id'),
    'USER_ID_CLAIM': settings.get('JWT_USER_ID_CLAIM', 'user_id'),

    'AUTH_TOKEN_CLASSES': tuple(settings.get_list('JWT_AUTH_TOKEN_CLASSES', ['rest_framework_simplejwt.tokens.AccessToken'])),
    'TOKEN_TYPE_CLAIM': settings.get('JWT_TOKEN_TYPE_CLAIM', 'token_type'),

    'JTI_CLAIM': settings.get('JWT_JTI_CLAIM', 'jti'),
    
    'TOKEN_OBTAIN_SERIALIZER': 'accounts.api.v1.token_serializers.ZuummTokenObtainPairSerializer',
}


# Logging Configuration
# Create logs directory if it doesn't exist
LOGS_DIR = BASE_DIR / 'logs'
LOGS_DIR.mkdir(exist_ok=True)

# Define log levels based on environment
LOG_LEVEL = {
    ENV_LOCAL: 'DEBUG',
    ENV_DEV: 'DEBUG',
    ENV_UAT: 'INFO',
    ENV_PROD: 'WARNING'
}.get(ENVIRONMENT, 'INFO')

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '[{levelname}] {asctime} {name} {module} {process:d} {thread:d} {message}',
            'style': '{',
            'datefmt': '%Y-%m-%d %H:%M:%S'
        },
        'simple': {
            'format': '[{levelname}] {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': True,
        },
        'accounts': {
            'handlers': ['console'],
            'level': 'DEBUG',
            'propagate': True,
        },
        'bookings': {
            'handlers': ['console'],
            'level': 'DEBUG',
            'propagate': True,
        },
        'packages': {
            'handlers': ['console'],
            'level': 'DEBUG',
            'propagate': True,
        },
    },
}

JAZZMIN_SETTINGS = JAZZMIN_SETTINGS_CONFIGURATION

# Redis settings
REDIS_URL = settings.get('REDIS_URL', 'redis://redis:6379/0')

# Celery settings
CELERY_BROKER_URL = REDIS_URL
CELERY_RESULT_BACKEND = REDIS_URL
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = TIME_ZONE

# Environment-specific settings
ENV = settings.get('DJANGO_ENV', 'dev')
if ENV == 'prod':
    BE_BASE_URL = 'app.zuumm.ai'
elif ENV == 'uat':
    BE_BASE_URL = 'uat-api.zuumm.ai'
else:
    BE_BASE_URL = 'dev-api.zuumm.ai'

# CSRF Settings
CSRF_TRUSTED_ORIGINS = [
    'https://*.dev-api.zuumm.ai',  # For dev partner domains
    'https://*.uat-api.zuumm.ai',  # For uat partner domains
    'https://*.app.zuumm.ai',  # For prod partner domains
    'https://dev-api.zuumm.ai',  # For dev superadmin
    'https://uat-api.zuumm.ai',  # For uat superadmin
    'https://app.zuumm.ai',  # For prod superadmin (BE domain)
    'https://*.zuumm.ai',    # For all zuumm.ai subdomains (partner websites)
    'https://zuumm.ai',      # Frontend domain
    'https://www.zuumm.ai',  # WWW frontend domain
    'http://localhost:8000',  # For local development
    'http://127.0.0.1:8000'  # For local development
]

# Session Security Settings
SESSION_COOKIE_SECURE = not DEBUG  # True in production, False in development
SESSION_COOKIE_HTTPONLY = True
SESSION_COOKIE_SAMESITE = 'Lax'
SESSION_COOKIE_DOMAIN = None  # Allow cookies to work across subdomains
CSRF_COOKIE_SECURE = not DEBUG  # True in production, False in development
CSRF_COOKIE_HTTPONLY = True
CSRF_COOKIE_SAMESITE = 'Lax'
CSRF_COOKIE_DOMAIN = None  # Allow CSRF cookies to work across subdomains

# CORS Configuration
# Set to False so that regex patterns work
CORS_ALLOW_ALL_ORIGINS = True
CORS_ALLOW_CREDENTIALS = True

# Allow all zuumm.ai domains and subdomains
# CORS_ALLOWED_ORIGIN_REGEXES = [
#     r"^https://.*\.zuumm\.ai$",  # All subdomains of zuumm.ai
#     r"^https://zuumm\.ai$",      # Main domain zuumm.ai
# ]

CORS_ALLOW_HEADERS = [
    'accept',
    'accept-encoding',
    'authorization',
    'content-type',
    'domain',  # Custom header for partner domain detection
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
    'cache-control',
    'pragma',
    'sec-fetch-dest',
    'sec-fetch-mode',
    'sec-fetch-site',
]

CORS_ALLOW_METHODS = [
    'DELETE',
    'GET',
    'OPTIONS',
    'PATCH',
    'POST',
    'PUT',
]

# CKEditor Configuration
CKEDITOR_UPLOAD_PATH = "ckeditor_uploads/"
CKEDITOR_IMAGE_BACKEND = "pillow"

# CKEditor Configuration
CKEDITOR_CONFIGS = {
    'default': {
        'toolbar': 'Full',
        'height': 300,
        'width': '100%',
        'toolbar_Full': [
            ['Source', '-', 'Save', 'NewPage', 'Preview', '-', 'Templates'],
            ['Cut', 'Copy', 'Paste', 'PasteText', 'PasteFromWord', '-', 'Print', 'SpellChecker', 'Scayt'],
            ['Undo', 'Redo', '-', 'Find', 'Replace', '-', 'SelectAll', 'RemoveFormat'],
            ['Form', 'Checkbox', 'Radio', 'TextField', 'Textarea', 'Select', 'Button', 'ImageButton', 'HiddenField'],
            '/',
            ['Bold', 'Italic', 'Underline', 'Strike', '-', 'Subscript', 'Superscript'],
            ['NumberedList', 'BulletedList', '-', 'Outdent', 'Indent', 'Blockquote'],
            ['JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock'],
            ['Link', 'Unlink', 'Anchor'],
            ['Image', 'Flash', 'Table', 'HorizontalRule', 'Smiley', 'SpecialChar', 'PageBreak'],
            '/',
            ['Styles', 'Format', 'Font', 'FontSize'],
            ['TextColor', 'BGColor'],
            ['Maximize', 'ShowBlocks', '-', 'About'],
        ],
        'extraPlugins': 'codesnippet',
    },
    'terms_and_conditions': {
        'toolbar': 'Custom',
        'height': 400,
        'width': '100%',
        'toolbar_Custom': [
            ['Bold', 'Italic', 'Underline', 'Strike'],
            ['NumberedList', 'BulletedList', '-', 'Outdent', 'Indent'],
            ['JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock'],
            ['Link', 'Unlink'],
            ['RemoveFormat', 'Source'],
            ['Styles', 'Format'],
        ],
        'format_tags': 'p;h1;h2;h3;h4;h5;h6;pre',
        'removeDialogTabs': 'image:advanced;link:advanced',
    },
}

# DeepSeek AI Configuration  
DEEPSEEK_API_KEY = settings.get_required('DEEPSEEK_API_KEY')
DEEPSEEK_API_URL = settings.get('DEEPSEEK_API_URL', 'https://api.deepseek.com/v1/chat/completions')

# OpenAI Configuration
OPENAI_API_KEY = settings.get_required('OPENAI_API_KEY')
OPENAI_API_URL = settings.get('OPENAI_API_URL', 'https://api.openai.com/v1/responses')

# Gemini Configuration
GOOGLE_API_KEY = settings.get_required('GOOGLE_API_KEY')

# Pinecone Configuration
PINECONE_API_KEY = settings.get_required('PINECONE_API_KEY')
os.environ['PINECONE_API_KEY'] = PINECONE_API_KEY

# SerpAPI Configuration
SERP_API_KEY = settings.get_required("SERP_API_KEY")

UNSPLASH_ACCESS_KEY = settings.get_required('UNSPLASH_ACCESS_KEY')

GYG_AUTHORIZATION_TOKEN = settings.get('GYG_AUTHORIZATION_TOKEN', "1234567890")

GOOGLE_CLIENT_ID = settings.get_required("GOOGLE_CLIENT_ID")
GOOGLE_CLIENT_SECRET = settings.get_required("GOOGLE_CLIENT_SECRET")

# Tripjack Configuration
TRIPJACK_API_KEY = settings.get_required('TRIPJACK_API_KEY')
TRIPJACK_API_URL = settings.get('TRIPJACK_API_URL', 'https://apitest.tripjack.com')  # Changed to test environment
MAX_CONCURRENT_HOTELS = settings.get_int('MAX_CONCURRENT_HOTELS', 30)
MAX_CONCURRENT_CITIES = settings.get_int('MAX_CONCURRENT_CITIES', 3)

# Transferz Configuration
TRANSFERZ_EMAIL = settings.get('TRANSFERZ_EMAIL', '<EMAIL>')
TRANSFERZ_PASSWORD = settings.get('TRANSFERZ_PASSWORD', 'sXaM8eE8fNqE')
TRANSFERZ_AUTH_URL = settings.get('TRANSFERZ_AUTH_URL', 'https://gateway.staging.transferz.com/auth')
TRANSFERZ_API_URL = settings.get('TRANSFERZ_API_URL', 'https://warpdrive.staging.transferz.com')

FE_BASE_URL = settings.get('FE_BASE_URL', 'https://zuumm.ai')
FE_AFFILIATE_SECTION_URL = settings.get('FE_AFFILIATE_SECTION_URL', 'https://zuumm.ai/explore/profile/affiliate')
FE_AFFILIATE_BASE_URL = settings.get('FE_AFFILIATE_BASE_URL', 'https://zuumm.ai/?ref=')


# sentry integrations
if ENVIRONMENT != ENV_LOCAL:
    sentry_sdk.init(
        dsn=settings.get_required("BE_SENTRY_DSN"),
        integrations=[DjangoIntegration()],
        environment=ENVIRONMENT,
        traces_sample_rate=1.0,
        send_default_pii=True
    )
