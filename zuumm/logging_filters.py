"""
Logging filters for the zuumm project.
"""
import logging
from base.settings_utils import init_settings
from pathlib import Path

# Initialize settings manager
settings = init_settings(Path(__file__).resolve().parent.parent)

class EnvironmentFilter(logging.Filter):
    """
    Filter to add environment information to log records
    and control logging based on environment.
    """
    def filter(self, record):
        record.environment = settings.environment
        return True 