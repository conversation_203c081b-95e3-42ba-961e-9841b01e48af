"""
Database router for production primary-replica configuration.
"""

class ProductionDBRouter:
    """
    Routes read operations to replica and write operations to primary in production.
    """

    def db_for_read(self, model, **hints):
        """Route reads to replica database."""
        return 'replica'

    def db_for_write(self, model, **hints):
        """Route writes to primary database."""
        return 'default'

    def allow_relation(self, obj1, obj2, **hints):
        """Allow relations between primary and replica."""
        db_set = {'default', 'replica'}
        if obj1._state.db in db_set and obj2._state.db in db_set:
            return True
        return None

    def allow_migrate(self, db, app_label, model_name=None, **hints):
        """Only run migrations on primary database."""
        return db == 'default' 