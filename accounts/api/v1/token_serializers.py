from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from django.conf import settings

class ZuummTokenObtainPairSerializer(TokenObtainPairSerializer):
    @classmethod
    def get_token(cls, user):
        token = super().get_token(user)

        # Add custom claims
        token['iss'] = settings.SIMPLE_JWT['ISSUER']
        token['aud'] = settings.SIMPLE_JWT['AUDIENCE']
        token['partner_id'] = user.partner.id

        return token