from rest_framework import status
from django.shortcuts import get_object_or_404
from rest_framework.generics import ListAPIView, RetrieveAPIView, CreateAPIView, UpdateAPIView
from rest_framework.response import Response
from rest_framework_simplejwt.tokens import RefreshToken
from django.conf import settings
from django_filters.rest_framework import DjangoFilterBackend
from .serializers import (
    PartnerRegistrationSerializer,
    OTPSerializer,
    VerifyOTPSerializer,
    BlogPostSerializer,
    BlogPodcastSerializer,
    BlogDescriptionSerializer,
    UserSignupSerializer,
    SocialLoginSerializer,
    UserProfileUpdateSerializer,
    UserProfileSerializer,
    AffiliateProfileSerializer,
    AffiliateRequestSerializer,
    AffiliateReferralUsersSerializer,
    GlobalDashboardSerializer,
)
from .token_serializers import ZuummTokenObtainPairSerializer
from base.email_utils import send_mail_task, EmailContentManager
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.views import APIView
from rest_framework_simplejwt.views import TokenObtainPairView
from base.storage_utils import CustomS3Boto3Storage
from base.utils import get_file_and_extension, get_file_meta_data
from django.db import transaction
from rest_framework.viewsets import ModelViewSet
from accounts.models import User, Blog, GlobalDashboard
from accounts.choices import BlogTypeChoices, AffiliateStatusChoices
from accounts.filters import BlogTagFilter
from accounts.utils.helper_methods import get_user_data
from base.pagination import BlogPostPagination, GenericPagination

class ZuummTokenObtainPairView(TokenObtainPairView):
    serializer_class = ZuummTokenObtainPairSerializer

class PartnerRegistrationView(CreateAPIView):
    serializer_class = PartnerRegistrationSerializer
    permission_classes = [AllowAny]  # Allow anonymous access

    @transaction.atomic
    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        partner = serializer.save()

        # Get admin credentials and referral flag from context
        admin_password = serializer.context['admin_password']
        admin_user = serializer.context['admin_user']
        is_referral_partner = serializer.context['is_referral_partner']

        # Prepare email context
        email_context = {
            'partner_name': partner.entity_name,
            'admin_name': admin_user.full_name,
            'admin_email': admin_user.email,
            'gst_number': partner.gst_number or 'Not provided',
            'company_registration_number': partner.company_registration_number or 'Not provided',
            'monthly_transaction_volume': partner.get_monthly_transaction_volume_display(),
            'package_type': partner.get_package_type_display(),
            'registration_date': partner.created_at.strftime('%Y-%m-%d'),
        }

        # Send different emails based on partner type
        if is_referral_partner:
            # For referral partners, don't include admin credentials
            subject, body = EmailContentManager.get_email_content('partner_referral_registration', email_context)
            response_message = "Your Referral Partner Registration is Complete!"
            response_data = {
                "message": response_message,
                "referral_program": {
                    "welcome_email_sent": True,
                    "email_sent_to": admin_user.email,
                    "next_steps": "Our team will contact you soon with referral program details"
                }
            }
        else:
            # For regular partners, include admin credentials and panel URL
            # Construct admin panel URL
            protocol = 'https'
            subdomain = f"{partner.subdomain}"
            domain = settings.BE_BASE_URL
            admin_url = f"{protocol}://{subdomain}.{domain}/admin/login/"
            
            email_context.update({
                'admin_password': admin_password,
                'admin_url': admin_url,
                'partner_domain': partner.subdomain,
            })
            
            subject, body = EmailContentManager.get_email_content('partner_registration', email_context)
            response_message = "Your Partner is Now Live on Zuumm!"
            response_data = {
                "message": response_message,
                "admin_access": {
                    "admin_url": admin_url,
                    "temporary_password_sent": True,
                    "email_sent_to": admin_user.email
                }
            }

        # Send welcome email
        send_mail_task(subject, body, [admin_user.email])

        return Response(response_data, status=status.HTTP_201_CREATED)


class PresignedURLView(APIView):
    permission_classes = [AllowAny]

    def get(self, request, file_type, file):
        """
        Generate a presigned URL for direct file upload to S3
        """
        if file_type == 'document':
            return Response({
                'message': 'This API is not for private documents upload',
                'errors': 'Invalid file type'
            }, status=status.HTTP_400_BAD_REQUEST)

        file_name, extension = get_file_and_extension(file)
        path, allowed_extensions, size_limit, error = get_file_meta_data(
            file_type
        )
        # Check if file type is valid
        if error:
            return Response({
                'message': error,
                'errors': error
            }, status=status.HTTP_400_BAD_REQUEST)

        file_path = f"{path}"

        # Validate extension
        if extension not in allowed_extensions:
            return Response({
                'message': f'Invalid file extension. Allowed extensions: {", ".join(allowed_extensions)}',
                'errors': 'Invalid file extension'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Generate presigned URL
        storage = CustomS3Boto3Storage()
        response, error = storage.generate_presigned_url(
            file_path=file_path,
            file_name=file_name,
            extension=extension,
            limit=size_limit
        )
        
        if not response:
            return Response({
                'message': 'Failed to generate presigned URL',
                'errors': str(error)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        return Response({
            'message': 'Presigned URL generated successfully',
            'data': response
        })


class EncryptedPresignedURLView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, file_type, file):
        """
        Generate a presigned URL for encrypted-bucket 
        Direct file upload to S3 for caregiver doocuments
        """
        if file_type != 'document':
            return Response({
                'message': 'This API is only for private documents',
                'errors': 'Invalid file type'
            }, status=status.HTTP_400_BAD_REQUEST)

        file_name, extension = get_file_and_extension(file)
        path, allowed_extensions, size_limit, error = get_file_meta_data(
            file_type, 
            request.user
        )
        # Check if file type is valid
        if error:
            return Response({
                'message': error,
                'errors': error
            }, status=status.HTTP_400_BAD_REQUEST)

        file_path = f"{path}"

        # Validate extension
        if extension not in allowed_extensions:
            return Response({
                'message': f'Invalid file extension. Allowed extensions: {", ".join(allowed_extensions)}',
                'errors': 'Invalid file extension'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Generate presigned URL
        storage = CustomS3Boto3Storage(is_public=False)
        response, error = storage.generate_presigned_url(
            file_path=file_path,
            file_name=file_name,
            extension=extension,
            limit=size_limit
        )
        
        if not response:
            return Response({
                'message': 'Failed to generate presigned URL',
                'errors': str(error)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        return Response({
            'message': 'Presigned URL generated successfully',
            'data': response
        })


class OTPViewSet(ModelViewSet):
    """
    OTP ViewSet to Generate and Verify OTP
    POST/Create - Generate OTP for User
    GET/List - To Verify OTP
    """

    serializer_class = OTPSerializer
    permission_classes = (AllowAny, )

    def get_serializer_class(self):
        if self.request.method == "GET":
            return VerifyOTPSerializer

        return super().get_serializer_class()

    def get_serializer_context(self):
        """Add partner to serializer context"""
        context = super().get_serializer_context()
        partner = getattr(self.request, 'domain', None)
        if not partner:
            # Return empty context if no domain resolved
            return context
        context['partner'] = partner
        return context

    def send_otp(self, request, *args, **kwargs):
        partner = getattr(self.request, 'domain', None)
        if not partner:
            # Return error if no domain resolved
            return Response({
                'status': 'error',
                'message': 'Invalid domain'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        return super().create(request, *args, **kwargs)

    def verify_otp(self, request, *args, **kwargs):
        """GET method to verify OTP using VerifyOTPSerializer"""
        partner = getattr(self.request, 'domain', None)
        if not partner:
            # Return error if no domain resolved
            return Response({
                'status': 'error',
                'message': 'Invalid domain'
            }, status=status.HTTP_400_BAD_REQUEST)

        serializer = VerifyOTPSerializer(
            data=request.query_params, 
            context={"request": request, "partner": partner}
        )
        serializer.is_valid(raise_exception=True)
        return Response(serializer.validated_data)


class SignupView(CreateAPIView):
    """
    User Signup API with OTP validation
    POST - Create user after validating email and phone number OTPs
    """
    serializer_class = UserSignupSerializer
    permission_classes = [AllowAny]

    @transaction.atomic
    def create(self, request, *args, **kwargs):
        """Create user with OTP validation"""
        partner = getattr(self.request, 'domain', None)
        context = {}
        context['partner'] = partner
        # Get serializer with partner context
        serializer = self.get_serializer(data=request.data, context=context)
        serializer.is_valid(raise_exception=True)
        user = serializer.save()
        
        # Generate token for the user
        token = user.token

        return Response({
            'status': 'success',
            'message': 'User registered successfully',
            'data': {
                'user': get_user_data(user),
                'token': token,
            }
        }, status=status.HTTP_201_CREATED)


class LogoutView(APIView):
    permission_classes = [IsAuthenticated,]

    def post(self, request, *args, **kwargs):
        refresh_token = request.data.get("refresh_token")
        if refresh_token:
            try:
                token = RefreshToken(refresh_token)
                token.blacklist()
                return Response({
                    'status': 'success',
                    'message': 'Logged out successfully',
                }, status=status.HTTP_200_OK)
            except Exception as e:
                return Response({
                    'status': 'error',
                    'message': 'Invalid refresh token',
                }, status=status.HTTP_400_BAD_REQUEST)


class BlogPostView(ListAPIView):
    """
    API to get all blog posts with optional tag filtering
    - Paginated with 50 records per page
    - Open API (no authentication required)
    - Supports multiple tags with OR filter (if any tag matches, include the blog)
    - Filter values come from Tag model's name field
    
    Query Parameters:
        - page: Page number (optional, default: 1)
        - page_size: Number of records per page (optional, default: 50, max: 100)
        - tags: Comma-separated tag names for filtering (optional)
    """
    serializer_class = BlogPostSerializer
    permission_classes = [AllowAny]
    filter_backends = [DjangoFilterBackend]
    filterset_class = BlogTagFilter
    pagination_class = BlogPostPagination

    def get_queryset(self):
        queryset = Blog.objects.filter(
            is_active=True,
            type=BlogTypeChoices.POST.value
        ).select_related('writer').prefetch_related('tags').order_by('-created_at')

        exclude_slug = self.request.query_params.get('exclude')
        if exclude_slug:
            queryset = queryset.exclude(slug=exclude_slug)

        return queryset


class BlogPodcastView(ListAPIView):
    """
    API to get all podcast blogs with optional tag filtering
    - Paginated with 50 records per page
    - Open API (no authentication required)
    - Supports multiple tags with OR filter
    
    Query Parameters:
        - page: Page number (optional, default: 1)
        - page_size: Number of records per page (optional, default: 50, max: 100)
        - tags: Comma-separated tag names for filtering (optional)
    """
    serializer_class = BlogPodcastSerializer
    permission_classes = [AllowAny]
    filter_backends = [DjangoFilterBackend]
    filterset_class = BlogTagFilter
    pagination_class = GenericPagination

    def get_queryset(self):
        queryset = Blog.objects.filter(
            is_active=True,
            type=BlogTypeChoices.PODCAST.value
        ).select_related('writer').prefetch_related('tags')        
        return queryset.order_by('-created_at')


class BlogDescriptionView(RetrieveAPIView):
    """
    API to get individual blog details by ID or slug
    - Open API (no authentication required)
    """
    serializer_class = BlogDescriptionSerializer
    permission_classes = [AllowAny]
    queryset = Blog.objects.filter(is_active=True).select_related('writer').prefetch_related('tags')

    def retrieve(self, request, *args, **kwargs):
        """
        Override list method to provide consistent API response format
        """
        instance = get_object_or_404(self.get_queryset(), slug=kwargs['slug'])
        serializer = self.get_serializer(instance)

        return Response({
            'status': 'success',
            'message': 'Blog description retrieved successfully',
            'data': serializer.data
        }, status=status.HTTP_200_OK)


class SocialLoginView(CreateAPIView):
    """
    Social Login API for handling social authentication
    POST - Login/Signup user with social provider tokens
    """
    serializer_class = SocialLoginSerializer
    permission_classes = [AllowAny]

    @transaction.atomic
    def create(self, request, *args, **kwargs):
        """Handle social login request"""
        partner = getattr(self.request, 'domain', None)
        if not partner:
            return Response({
                'status': 'error',
                'message': 'Invalid domain'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Get serializer with partner context
        serializer = self.get_serializer(data=request.data, context={'partner': partner})
        serializer.is_valid(raise_exception=True)
        result = serializer.save()
        
        # Prepare response message
        if result['is_signup']:
            message = 'User registered successfully via social login'
        else:
            message = 'User logged in successfully via social login'

        return Response({
            'status': 'success',
            'message': message,
            'data': {
                'user': result['user_data'],
                'token': result['token'],
                'provider': result['provider'],
                'is_login': result['is_login'],
                'is_signup': result['is_signup']
            }
        }, status=status.HTTP_200_OK)


class UserProfileViewSet(ModelViewSet):
    """
    ViewSet for user profile operations
    GET - Retrieve user profile
    PATCH - Update user profile (partial updates)
    """
    permission_classes = [IsAuthenticated]
    http_method_names = ['get', 'patch']  # Only allow GET and PATCH

    def get_object(self):
        return self.request.user

    def get_serializer_class(self):
        if self.request.method == 'GET':
            return UserProfileSerializer

        elif self.request.method == 'PATCH':
            return UserProfileUpdateSerializer
        return UserProfileSerializer

    def retrieve(self, request, *args, **kwargs):
        """GET - Retrieve user profile"""
        partner = getattr(self.request, 'domain', None)
        if not partner:
            return Response({
                'status': 'error',
                'message': 'Invalid domain'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Response({
            'status': 'success',
            'message': 'Profile retrieved successfully',
            'data': serializer.data
        }, status=status.HTTP_200_OK)

    def partial_update(self, request, *args, **kwargs):
        """PATCH - Update user profile"""
        partner = getattr(self.request, 'domain', None)
        if not partner:
            return Response({
                'status': 'error',
                'message': 'Invalid domain'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        instance = self.get_object()
        serializer = self.get_serializer(
            instance, 
            data=request.data,
            context={'partner': partner},
            partial=True
        )
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)
        return Response({
            'status': 'success',
            'message': 'Profile updated successfully',
            'user': get_user_data(instance)
        }, status=status.HTTP_200_OK)


class AffiliateProfileView(ModelViewSet):
    """
    API to get affiliate profile details
    """
    permission_classes = [IsAuthenticated]
    serializer_class = AffiliateProfileSerializer

    def get_object(self):
        return self.request.user

    def fetch_profile(self, request, *args, **kwargs):
        """GET - Retrieve affiliate profile"""
        partner = getattr(self.request, 'domain', None)
        if not partner:
            # Return error if no domain resolved
            return Response({
                'status': 'error',
                'message': 'Invalid domain'
            }, status=status.HTTP_400_BAD_REQUEST)

        instance = self.get_object()
        serializer = self.get_serializer(instance=instance)
        
        return Response({
            'status': 'success',
            'message': 'Affiliate profile retrieved successfully',
            'data': serializer.data
        }, status=status.HTTP_200_OK)


class AffiliateRequestView(ModelViewSet):
    """
    API to request affiliate
    """
    permission_classes = [IsAuthenticated]
    serializer_class = AffiliateRequestSerializer

    def request_affiliate(self, request, *args, **kwargs):
        """POST - Request affiliate"""
        partner = getattr(self.request, 'domain', None)
        if not partner:
            # Return error if no domain resolved
            return Response({
                'status': 'error',
                'message': 'Invalid domain'
            }, status=status.HTTP_400_BAD_REQUEST)

        response = super().create(request, *args, **kwargs)
        return Response({
            'status': 'success',
            'message': 'Affiliate request sent successfully',
            'data': response.data
        }, status=status.HTTP_201_CREATED)


class AffiliateReferralUsersView(ModelViewSet):
    """
    API to get affiliate referral users
    """
    permission_classes = [IsAuthenticated]
    serializer_class = AffiliateReferralUsersSerializer
    queryset = User.objects.all()
    pagination_class = GenericPagination

    def get_queryset(self):
        user = self.request.user
        affiliate = user.affiliate
        referred_users_id = affiliate.referred_users.filter().values_list('user', flat=True)

        queryset = super().get_queryset()
        return queryset.filter(id__in=referred_users_id)

    def fetch_referral_users(self, request, *args, **kwargs):
        """GET - Retrieve affiliate referral users"""
        partner = getattr(self.request, 'domain', None)
        if not partner:
            # Return error if no domain resolved
            return Response({
                'status': 'error',
                'message': 'Invalid domain'
            }, status=status.HTTP_400_BAD_REQUEST)

        response = super().list(request, *args, **kwargs)
        return Response({
            'status': 'success',
            'message': 'Affiliate referral users retrieved successfully',
            'data': response.data
        }, status=status.HTTP_200_OK)


class GlobalDashboardView(ListAPIView):
    """
    API view for getting global dashboard values
    
    URL: /api/accounts/v1/global-dashboard/
    """
    serializer_class = GlobalDashboardSerializer
    permission_classes = [AllowAny]
    
    def get_queryset(self):
        """Get all active global dashboard entries"""
        return GlobalDashboard.objects.filter(is_active=True).order_by('type')
    
    def list(self, request, *args, **kwargs):
        """
        Override list method to provide consistent API response format
        """
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        
        return Response({
            'status': 'success',
            'message': 'Global dashboard values retrieved successfully',
            'data': {
                'dashboard_values': serializer.data,
                'count': len(serializer.data)
            }
        }, status=status.HTTP_200_OK)
