from django.contrib import admin, messages
from django.contrib.auth.admin import UserAdmin
from django.utils.translation import gettext_lazy as _
from django.core.mail import send_mail # For sending email
from django.urls import reverse # For generating URLs
from django.conf import settings # To build full URLs if needed
from django.contrib.sites.shortcuts import get_current_site # To get current site for URL
from django.contrib.admin.models import LogEntry, CHANGE
from django.contrib.contenttypes.models import ContentType
from django.utils.safestring import mark_safe
from django import forms
from django.http import HttpResponseRedirect

from import_export.admin import ExportActionMixin
from import_export.formats.base_formats import XLSX, CSV
from ckeditor.widgets import CKEditorWidget

from .models import User, Partner, SuperAdminUser, PartnerAdminUser, Blog, BlogTag, Tag, Affiliate, AffiliateReferralUser, GlobalDashboard
from .choices import UserTypeChoices, PartnerTypeChoices, BlogTypeChoices, AffiliateStatusChoices
from .forms import SuperAdminCreationForm, PartnerActivationForm, BlogForm, AffiliateForm, GlobalDashboardForm
from base.admin_filters import ActiveStatusFilter, EmailVerificationFilter, PrimarySuperAdminFilter, BlogStatusFilter, ApprovedAffiliateFilter


# Custom ExportActionMixin with specific formats
class CustomExportActionMixin(ExportActionMixin):
    # Specify the formats you want to allow
    formats = [XLSX, CSV]

    def get_export_formats(self):
        return [XLSX, CSV]
    
    @property
    def media(self):
        super_media = super().media
        return forms.Media(
            js = super_media._js,
            css = super_media._css
        )

    def can_actually_save(self, request, obj=None):
        """
        Determine if the user can actually save changes to this object.
        This is different from has_change_permission which might return True
        but all fields are disabled/readonly.
        """
        if not self.has_change_permission(request, obj):
            return False
            
        # Check if there are any editable fields
        form = self.get_form(request, obj)()
        readonly_fields = self.get_readonly_fields(request, obj)
        
        # Count non-disabled and non-readonly fields
        editable_fields = 0
        for field_name, field in form.base_fields.items():
            if (not getattr(field, 'disabled', False) and 
                field_name not in readonly_fields):
                editable_fields += 1
                
        return editable_fields > 0

    def change_view(self, request, object_id, form_url='', extra_context=None):
        """Override change_view to add can_actually_save to context"""
        if extra_context is None:
            extra_context = {}
            
        obj = self.get_object(request, object_id)
        extra_context['can_actually_save'] = self.can_actually_save(request, obj)
        
        return super().change_view(request, object_id, form_url, extra_context)

# Custom admin site sections
admin.site.site_header = "Zuumm Admin"
admin.site.site_title = "Zuumm Admin Portal"
admin.site.index_title = "Welcome to Zuumm Admin Portal"

# Commenting out custom ordering code to prevent hyperlink issues
'''
# Custom admin ordering
admin.site.app_index_template = 'admin/app_index.html'

# Create admin section ordering
class AdminAppConfig:
    app_label = 'admin'
    verbose_name = 'Admin'

class PartnersAppConfig:
    app_label = 'partners'
    verbose_name = 'Partners'

class UsersAppConfig:
    app_label = 'users'
    verbose_name = 'Users'

# Apply section configurations
original_get_app_list = admin.AdminSite.get_app_list

def get_app_list(self, request, app_label=None):
    """
    Return a sorted list of all the installed apps that have been
    registered in this site.
    """
    app_dict = self._build_app_dict(request, app_label)
    
    # Create our custom app sections
    custom_app_dict = {}
    
    # Admin section
    admin_dict = {
        'name': 'Admin',
        'app_label': 'admin',
        'app_url': '#',
        'has_module_perms': True,
        'models': []
    }
    
    # Partners section  
    partners_dict = {
        'name': 'Partners',
        'app_label': 'partners',
        'app_url': '#',
        'has_module_perms': True,
        'models': []
    }
    
    # Users section
    users_dict = {
        'name': 'Users',
        'app_label': 'users',
        'app_url': '#',
        'has_module_perms': True,
        'models': []
    }
    
    # Map models to the appropriate section
    for app in app_dict.values():
        for model in app['models']:
            if model['object_name'] == 'SuperAdminUser':
                admin_dict['models'].append(model)
            elif model['object_name'] in ['Partner', 'PartnerAdminUser']:
                partners_dict['models'].append(model)
            elif model['object_name'] == 'User':
                users_dict['models'].append(model)
    
    # Only add non-empty sections
    result = []
    if admin_dict['models'] and request.user.is_superuser:
        result.append(admin_dict)
    if partners_dict['models']:
        result.append(partners_dict)
    if users_dict['models']:
        result.append(users_dict)
    
    return result

# Patch the admin site
admin.AdminSite.get_app_list = get_app_list
'''


@admin.register(SuperAdminUser)
class SuperAdminUserAdmin(CustomExportActionMixin, UserAdmin):
    # Use the custom form for adding new SuperAdminUser instances
    add_form = SuperAdminCreationForm

    list_display = ('email', 'display_full_name', 'user_type', 'is_primary_superadmin', 'is_active', 'is_email_verified', 'last_login')
    list_filter = (ActiveStatusFilter, PrimarySuperAdminFilter, EmailVerificationFilter, 'created_at')
    search_fields = ('email', 'full_name')
    ordering = ('-date_joined',)
    
    # Fieldsets for the CHANGE form (editing an existing user)
    fieldsets = (
        (None, {'fields': ('email', 'password')}), # 'password' here shows the "change password" link
        (_('Personal info'), {'fields': ('full_name', 'phone_number_country_code', 'phone_number')}),
        (_('Permissions'), {
            'fields': ('is_active', 'is_staff', 'is_superuser', 'is_email_verified', 'user_type', 'is_primary_superadmin'),
        }),
        (_('Important dates'), {'fields': ('last_login', 'date_joined')}),
    )
    
    # add_fieldsets defines the fields and layout for the ADD form (creating a new user)
    # It should match the fields in SuperAdminCreationForm
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('email', 'full_name', 'password', 'confirm_password'),
        }),
    )

    readonly_fields = ('last_login', 'date_joined', 'username')

    def display_full_name(self, obj):
        """Display full name or '-' if empty"""
        return obj.full_name or "-"
    display_full_name.short_description = _("Full Name")

    def display_phone_number(self, obj):
        """Display phone number or '-' if empty"""
        return obj.phone_number or "-"
    display_phone_number.short_description = _("Phone Number")

    def display_phone_number_country_code(self, obj):
        """Display phone country code or '-' if empty"""
        return obj.phone_number_country_code or "-"
    display_phone_number_country_code.short_description = _("Country Code")

    def get_queryset(self, request):
        return super().get_queryset(request).filter(
            user_type=UserTypeChoices.SUPER_ADMIN.value,
            is_superuser=True,
        )

    def has_module_permission(self, request):
        # Only Super Admins should see this section in the admin
        return request.user.is_active and \
               request.user.is_superuser and \
               request.user.user_type == UserTypeChoices.SUPER_ADMIN.value
    
    def has_view_permission(self, request, obj=None):
        # Only Super Admins should be able to view the list or individual objects
        return request.user.is_active and \
               request.user.is_superuser and \
               request.user.user_type == UserTypeChoices.SUPER_ADMIN.value

    def has_add_permission(self, request):
        return request.user.is_active and request.user.is_superuser and request.user.user_type == UserTypeChoices.SUPER_ADMIN.value

    def has_change_permission(self, request, obj=None):
        return request.user.is_active and request.user.is_superuser and request.user.user_type == UserTypeChoices.SUPER_ADMIN.value

    def has_delete_permission(self, request, obj=None):
        return request.user.is_active and request.user.is_superuser and request.user.user_type == UserTypeChoices.SUPER_ADMIN.value
    
    def save_model(self, request, obj, form, change):
        """
        Save the model and ensure it's treated as a SuperAdminUser
        """
        # Check if user is being deactivated
        invalidate_tokens = False
        if change and obj.pk:
            old_obj = User.objects.get(pk=obj.pk)
            if old_obj.is_active and not obj.is_active:
                invalidate_tokens = True
        
        obj.user_type = UserTypeChoices.SUPER_ADMIN.value
        obj.is_staff = True
        obj.is_superuser = True
        
        # Get a partner for this user if none is assigned
        if not obj.partner_id:
            partner = Partner.objects.filter(partner_type=PartnerTypeChoices.ZUUMM.value).first()
            if partner:
                obj.partner = partner
                
        super().save_model(request, obj, form, change)
        
        # Invalidate tokens if user was deactivated
        if invalidate_tokens:
            obj.invalidate_all_tokens()
        
    def _response_post_save(self, request, obj):
        """
        Override this method to handle redirection after saving
        """
        opts = self.model._meta
        if self.has_change_permission(request, obj):
            # If we have appropriate permissions, redirect to the change page
            post_url = reverse(
                'admin:accounts_superadminuser_change',
                args=[obj.pk],
                current_app=self.admin_site.name
            )
        else:
            # Otherwise redirect to the changelist
            post_url = reverse(
                'admin:accounts_superadminuser_changelist',
                current_app=self.admin_site.name
            )
        return HttpResponseRedirect(post_url)
        
    def response_add(self, request, obj, post_url_continue=None):
        """
        Handle redirection after adding a SuperAdminUser
        """
        if '_continue' in request.POST or '_addanother' in request.POST:
            return super().response_add(request, obj, post_url_continue)
        return self._response_post_save(request, obj)

    # Add get_readonly_fields method to control which fields are editable
    def get_readonly_fields(self, request, obj=None):
        # Get base readonly fields
        fields = list(self.readonly_fields)
        
        # If editing an existing SuperAdmin
        if obj:
            # is_primary_superadmin is always readonly after creation
            fields.append('is_primary_superadmin')
            
            # If the object is a primary superadmin, make is_active readonly
            if obj.is_primary_superadmin:
                fields.append('is_active')
            # If the object is the current user (and not primary), make is_active readonly
            elif obj.id == request.user.id:
                fields.append('is_active')
                
            # Make all fields readonly except editable ones for SuperAdmins
            # Allow editing: full_name, phone_number_country_code, phone_number, is_active (unless restricted above)
            editable_fields = ['full_name', 'phone_number_country_code', 'phone_number', 'is_active']
            all_fields = [f.name for f in self.model._meta.fields 
                         if f.name not in editable_fields and f.name not in fields]
            fields = tuple(set(fields + all_fields))
            
        return fields
    
    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        
        # Remove password help text for add form
        if not obj:  # Adding a new user
            if 'email' in form.base_fields:
                form.base_fields['email'].widget = forms.TextInput(
                    attrs={'size': 80}  # increase input box length (default is ~30)
                )
            if 'password' in form.base_fields:
                form.base_fields['password'].help_text = ''
            if 'confirm_password' in form.base_fields:
                form.base_fields['confirm_password'].help_text = ''
        
        # If editing an existing SuperAdmin
        if obj:
            # is_primary_superadmin is always disabled after creation
            if 'is_primary_superadmin' in form.base_fields:
                form.base_fields['is_primary_superadmin'].disabled = True
                
            # If object is primary superadmin or current user, disable is_active
            if obj.is_primary_superadmin or obj.id == request.user.id:
                if 'is_active' in form.base_fields:
                    form.base_fields['is_active'].disabled = True
                    
            # Disable other fields except allowed ones for SuperAdmins
            # Allow editing: full_name, phone_number_country_code, phone_number, is_active (unless restricted above)
            editable_fields = ['full_name', 'phone_number_country_code', 'phone_number', 'is_active']
            for field_name in form.base_fields:
                if field_name not in editable_fields:
                    form.base_fields[field_name].disabled = True
                    
        return form

    def get_fieldsets(self, request, obj=None):
        """Customize fieldsets based on whether we're adding or changing a user"""
        if obj:  # Editing an existing user
            return (
                (None, {'fields': ('email', 'full_name', 'phone_number_country_code', 'phone_number', 'is_active', 'is_staff', 'is_superuser', 'is_email_verified', 'user_type', 'is_primary_superadmin', 'last_login', 'date_joined')}),
            )
        else:  # Adding a new user
            return self.add_fieldsets


@admin.register(PartnerAdminUser)
class PartnerAdminUserAdmin(CustomExportActionMixin, UserAdmin):
    list_display = ('email', 'display_partner', 'display_full_name', 'user_type', 'is_active', 'is_email_verified', 'last_login')
    list_filter = (ActiveStatusFilter, EmailVerificationFilter, 'created_at')
    search_fields = ('email', 'full_name', 'partner__entity_name')
    ordering = ('-date_joined',)
    
    fieldsets = (
        (None, {'fields': ('email', 'password')}),
        (_('Personal info'), {'fields': ('full_name', 'phone_number')}),
        (_('Partner info'), {'fields': ('partner',)}),
        (_('Permissions'), {
            'fields': ('is_active', 'is_email_verified', 'user_type', 'is_staff'),
        }),
        (_('Important dates'), {'fields': ('last_login', 'date_joined')}),
    )
    
    # Basic read-only fields for all users
    readonly_fields = ('last_login', 'date_joined', 'username', 'is_superuser')

    def display_partner(self, obj):
        """Display partner name or '-' if empty"""
        return obj.partner.entity_name if obj.partner else "-"
    display_partner.short_description = _("Partner")

    def display_full_name(self, obj):
        """Display full name or '-' if empty"""
        return obj.full_name or "-"
    display_full_name.short_description = _("Full Name")

    def display_phone_number(self, obj):
        """Display phone number or '-' if empty"""
        return obj.phone_number or "-"
    display_phone_number.short_description = _("Phone Number")

    def display_phone_number_country_code(self, obj):
        """Display phone country code or '-' if empty"""
        return obj.phone_number_country_code or "-"
    display_phone_number_country_code.short_description = _("Country Code")

    def get_readonly_fields(self, request, obj=None):
        # Get base readonly fields
        fields = list(self.readonly_fields)
        
        if obj and request.user.is_superuser:
            # For SuperAdmin, make everything read-only except is_active
            all_fields = [f.name for f in self.model._meta.fields if f.name != 'is_active']
            fields = tuple(set(fields + all_fields))
            
        return fields

    def get_queryset(self, request):
        qs = super().get_queryset(request).filter(user_type__in=UserTypeChoices.partner_admin_types())
        if request.user.user_type == UserTypeChoices.PARTNER_ADMIN.value:
            # PartnerAdmins can only see other PartnerAdmins within their own partner
            return qs.filter(partner=request.user.partner)
        # SuperAdmins can see all PartnerAdmins
        return qs
        
    def get_list_filter(self, request):
        """Only show partner filter to superadmins"""
        if request.user.is_superuser:
            return ('partner', ActiveStatusFilter, EmailVerificationFilter, 'created_at')
        return (ActiveStatusFilter, EmailVerificationFilter, 'created_at')

    def has_module_permission(self, request):
        # Hide this module from dashboard and side panel
        # The model will still be accessible via direct URL for the "View Admin Details" button
        return False

    # PartnerAdmins should only be able to manage PartnerAdminUsers in their own partner.
    # SuperAdmins can manage any PartnerAdminUser.
    def has_view_permission(self, request, obj=None):
        if not (request.user.is_active and (request.user.is_superuser or request.user.user_type == UserTypeChoices.PARTNER_ADMIN.value)):
            return False
        if obj and request.user.user_type == UserTypeChoices.PARTNER_ADMIN.value and obj.partner != request.user.partner:
            return False
        return True

    def has_add_permission(self, request):
        # No one can add partner admins through admin
        return False

    def has_change_permission(self, request, obj=None):
        if not (request.user.is_active and (request.user.is_superuser or request.user.user_type == UserTypeChoices.PARTNER_ADMIN.value)):
            return False
        if obj and request.user.user_type == UserTypeChoices.PARTNER_ADMIN.value and obj.partner != request.user.partner:
            return False
        return True

    def has_delete_permission(self, request, obj=None):
        return False
        
    def get_form(self, request, obj=None, **kwargs):
        """
        Customize form based on user type
        """
        form = super().get_form(request, obj, **kwargs)
        
        # For SuperAdmin, make everything read-only except is_active
        if request.user.is_superuser and obj:
            for field_name in form.base_fields:
                if field_name != 'is_active':
                    form.base_fields[field_name].disabled = True
                    
        # For PartnerAdmins, allow editing their own full_name only if it's their own record
        if request.user.user_type == UserTypeChoices.PARTNER_ADMIN.value:
            for field_name in form.base_fields:
                # Allow editing full_name only if it's their own record
                if obj and obj.id == request.user.id and field_name == 'full_name':
                    continue  # Keep this field editable
                else:
                    form.base_fields[field_name].disabled = True
        
        return form

    def get_fieldsets(self, request, obj=None):
        """Customize fieldsets based on whether we're adding or changing a user"""
        if obj:  # Editing an existing user
            if request.user.user_type == UserTypeChoices.PARTNER_ADMIN.value:
                # For PartnerAdmin, show limited fields in single section
                return (
                    (None, {'fields': ('email', 'full_name', 'phone_number_country_code', 'phone_number', 'is_active', 'is_email_verified', 'user_type', 'is_staff', 'last_login', 'date_joined')}),
                )
            else:
                # For SuperAdmin, show all fields in single section
                return (
                    (None, {'fields': ('email', 'full_name', 'phone_number_country_code', 'phone_number', 'partner', 'is_active', 'is_email_verified', 'user_type', 'is_staff', 'last_login', 'date_joined')}),
                )
        else:  # Adding a new user
            return self.fieldsets
    
    def save_model(self, request, obj, form, change):
        """Handle token invalidation when partner admin users are deactivated"""
        # Check if user is being deactivated
        invalidate_tokens = False
        if change and obj.pk:
            old_obj = User.objects.get(pk=obj.pk)
            if old_obj.is_active and not obj.is_active:
                invalidate_tokens = True
        
        super().save_model(request, obj, form, change)
        
        # Invalidate tokens if user was deactivated
        if invalidate_tokens:
            obj.invalidate_all_tokens()


# Admin for regular users (ZuummUsers), managed by PartnerAdmins and SuperAdmins
@admin.register(User)
class ZuummUserAdmin(CustomExportActionMixin, UserAdmin):
    list_display = ('email', 'display_partner', 'display_full_name', 'user_type', 'is_active', 'is_email_verified', 'last_login')
    list_filter = (ActiveStatusFilter, EmailVerificationFilter, 'user_type', 'created_at')
    search_fields = ('email', 'full_name', 'partner__entity_name')
    ordering = ('-date_joined',)
    fieldsets = (
        (None, {'fields': ('email', 'password')}),
        (_('Personal info'), {'fields': ('full_name', 'phone_number')}),
        (_('Partner info'), {'fields': ('partner',)}),
        (_('Permissions'), {
            'fields': ('is_active', 'is_email_verified', 'user_type'),
        }),
        (_('Important dates'), {'fields': ('last_login', 'date_joined')}),
    )
    
    # Basic read-only fields for all users
    readonly_fields = ('last_login', 'date_joined', 'username', 'is_staff', 'is_superuser')

    def display_partner(self, obj):
        """Display partner name or '-' if empty"""
        return obj.partner.entity_name if obj.partner else "-"
    display_partner.short_description = _("Partner")

    def display_full_name(self, obj):
        """Display full name or '-' if empty"""
        return obj.full_name or "-"
    display_full_name.short_description = _("Full Name")

    def display_phone_number(self, obj):
        """Display phone number or '-' if empty"""
        return obj.phone_number or "-"
    display_phone_number.short_description = _("Phone Number")

    def get_readonly_fields(self, request, obj=None):
        # Get base readonly fields
        fields = list(self.readonly_fields)
        
        # For superadmins, add user_type and partner as readonly for security
        if obj and request.user.is_superuser:
            fields = fields + ['user_type', 'partner']
        
        # For PartnerAdmins, make everything read-only
        if obj and request.user.user_type == UserTypeChoices.PARTNER_ADMIN.value:
            all_fields = [f.name for f in self.model._meta.fields]
            fields = tuple(set(fields + all_fields))
            
        return fields

    def get_queryset(self, request):
        # Show only ZUUMM_USER type users.
        qs = super().get_queryset(request).exclude(
            user_type__in=UserTypeChoices.admin_types(),
        )
        if request.user.user_type == UserTypeChoices.PARTNER_ADMIN.value:
            # PartnerAdmins can only see ZuummUsers within their own partner
            return qs.filter(
                partner=request.user.partner,
                user_type=UserTypeChoices.PARTNER_USER.value,
            )
        # SuperAdmins can see all ZuummUsers
        return qs
        
    def get_list_filter(self, request):
        """Only show partner filter to superadmins"""
        if request.user.is_superuser:
            return ('partner', ActiveStatusFilter, EmailVerificationFilter, 
                    ('user_type', admin.ChoicesFieldListFilter), 
                    'created_at')
        return (ActiveStatusFilter, EmailVerificationFilter, 'user_type', 'created_at')

    def has_module_permission(self, request):
        # Only SuperAdmins can see this module (hide from PartnerAdmins)
        return request.user.is_active and request.user.is_superuser

    def has_view_permission(self, request, obj=None):
        if not (request.user.is_active and (request.user.is_superuser or request.user.user_type == UserTypeChoices.PARTNER_ADMIN.value)):
            return False
        if obj and request.user.user_type == UserTypeChoices.PARTNER_ADMIN.value and obj.partner != request.user.partner:
            return False
        return True

    def has_add_permission(self, request):
        # No one can add users through admin
        return False

    def has_change_permission(self, request, obj=None):
        if not request.user.has_perm('accounts.change_user'):
            return False
        if not (request.user.is_active and (request.user.is_superuser or request.user.user_type == UserTypeChoices.PARTNER_ADMIN.value)):
            return False
        if obj and request.user.user_type == UserTypeChoices.PARTNER_ADMIN.value and obj.partner != request.user.partner:
            return False
        return True

    def has_delete_permission(self, request, obj=None):
        return False

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        
        # For PartnerAdmins, don't allow editing at all
        if request.user.user_type == UserTypeChoices.PARTNER_ADMIN.value:
            for field_name in form.base_fields:
                form.base_fields[field_name].disabled = True
                    
        return form

    def get_fieldsets(self, request, obj=None):
        """Customize fieldsets based on whether we're adding or changing a user"""
        if obj:  # Editing an existing user
            return (
                (None, {'fields': ('email', 'full_name', 'phone_number_country_code', 'phone_number', 'partner', 'is_active', 'is_email_verified', 'user_type', 'last_login', 'date_joined')}),
            )
        else:  # Adding a new user
            return self.fieldsets
    
    def change_view(self, request, object_id, form_url='', extra_context=None):
        """Redirect to the appropriate proxy admin for admin-type users.

        Clicking the user shortcut in the Jazzmin sidebar attempts to open
        this model's change view for the current user. Since this admin
        excludes admin-type users from its queryset, Django would normally
        show a "doesn't exist" warning. To provide a smooth UX, detect that
        the requested object is an admin-type user and redirect to the
        corresponding proxy admin change page instead.
        """
        user_obj = User.objects.filter(pk=object_id).first()
        if user_obj and user_obj.user_type in UserTypeChoices.admin_types():
            if user_obj.user_type == UserTypeChoices.SUPER_ADMIN.value:
                url = reverse('admin:accounts_superadminuser_change', args=[object_id], current_app=self.admin_site.name)
            else:
                url = reverse('admin:accounts_partneradminuser_change', args=[object_id], current_app=self.admin_site.name)
            return HttpResponseRedirect(url)
        return super().change_view(request, object_id, form_url, extra_context)

    def save_model(self, request, obj, form, change):
        """Handle token invalidation when regular users are deactivated"""
        # Check if user is being deactivated
        invalidate_tokens = False
        if change and obj.pk:
            old_obj = User.objects.get(pk=obj.pk)
            if old_obj.is_active and not obj.is_active:
                invalidate_tokens = True
        
        super().save_model(request, obj, form, change)
        
        # Invalidate tokens if user was deactivated
        if invalidate_tokens:
            obj.invalidate_all_tokens()


# Partner (formerly Partner) admin interface (managed by SuperAdmins)
@admin.register(Partner)
class PartnerAdmin(CustomExportActionMixin, admin.ModelAdmin):
    list_display = ('entity_name', 'subdomain', 'display_logo', 'package_type', 'partner_admin_email', 'active_status', 'created_on')
    search_fields = ('entity_name', 'subdomain', 'vanity_domain')
    list_display_links = ('entity_name',)
    ordering = ('-created_at', 'entity_name')
    readonly_fields = ('created_at', 'updated_at', 'admin_log_entries', 'partner_admin_details', 'display_logo', 'display_address_details', 'display_entity_type', 'display_subdomain', 'display_vanity_domain', 'display_preference', 'display_package_type', 'display_primary_theme', 'display_secondary_theme', 'display_gst_number', 'display_company_registration_number', 'display_monthly_transaction_volume', 'display_referral_code', 'display_organization_url', 'display_facebook_media_link', 'display_instagram_media_link', 'display_twitter_media_link')
    form = PartnerActivationForm

    def created_on(self, obj):
        return obj.created_at
    created_on.short_description = "Created On"
    
    def get_list_filter(self, request):
        """Only show package_type filter to superadmins"""
        if request.user.is_superuser:
            return (ActiveStatusFilter, ('package_type', admin.ChoicesFieldListFilter), 'created_at')
        return (ActiveStatusFilter, 'created_at')
    
    def get_queryset(self, request):
        qs = super().get_queryset(request).exclude(partner_type=PartnerTypeChoices.ZUUMM.value)
        if request.user.user_type == UserTypeChoices.PARTNER_ADMIN.value:
            # PartnerAdmins can only see their own partner
            return qs.filter(id=request.user.partner_id)
        return qs
    
    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        # Inject the user into the form for logging purposes
        form.user = request.user
        
        # For SuperAdmin, allow editing specific fields (excluding entity_type)
        if request.user.is_superuser:
            superadmin_editable_fields = [
                'is_active', 'organization_url', 'gst_number', 
                'company_registration_number', 'monthly_transaction_volume', 
                'facebook_media_link', 'instagram_media_link', 'twitter_media_link', 
                'update_notification_enabled'
            ]
            for field_name in form.base_fields:
                if field_name not in superadmin_editable_fields:
                    form.base_fields[field_name].disabled = True
        
        # For PartnerAdmin, make only specific fields editable (excluding entity_type)
        elif request.user.user_type == UserTypeChoices.PARTNER_ADMIN.value:
            for field_name in form.base_fields:
                if field_name not in ['entity_name', 'logo', 'primary_theme', 'secondary_theme', 
                                      'gst_number', 'company_registration_number', 
                                      'monthly_transaction_volume', 'update_notification_enabled']:
                    form.base_fields[field_name].disabled = True
                    
        return form
    
    def display_logo(self, obj):
        """Show partner logo as an image"""
        if obj.logo:
            return mark_safe(f'<img src="{obj.logo.url}" alt="Logo" style="max-height: 50px; max-width: 100px;" />')
        return "-"
    
    display_logo.short_description = _("Partner Logo")

    def display_entity_type(self, obj):
        """Display entity type or '-' if empty"""
        return obj.get_entity_type_display() if obj.entity_type else "-"
    display_entity_type.short_description = _("Entity Type")

    def display_subdomain(self, obj):
        """Display subdomain or '-' if empty"""
        return obj.subdomain or "-"
    display_subdomain.short_description = _("Subdomain")

    def display_vanity_domain(self, obj):
        """Display vanity domain or '-' if empty"""
        return obj.vanity_domain or "-"
    display_vanity_domain.short_description = _("Vanity Domain")

    def display_preference(self, obj):
        """Display preference or '-' if empty"""
        return obj.get_preference_display() if obj.preference else "-"
    display_preference.short_description = _("Preference")

    def display_package_type(self, obj):
        """Display package type or '-' if empty"""
        return obj.get_package_type_display() if obj.package_type else "-"
    display_package_type.short_description = _("Package Type")

    def display_primary_theme(self, obj):
        """Display primary theme or '-' if empty"""
        return obj.primary_theme or "-"
    display_primary_theme.short_description = _("Primary Theme")

    def display_secondary_theme(self, obj):
        """Display secondary theme or '-' if empty"""
        return obj.secondary_theme or "-"
    display_secondary_theme.short_description = _("Secondary Theme")

    def display_gst_number(self, obj):
        """Display GST number or '-' if empty"""
        return obj.gst_number or "-"
    display_gst_number.short_description = _("GST Number")

    def display_company_registration_number(self, obj):
        """Display company registration number or '-' if empty"""
        return obj.company_registration_number or "-"
    display_company_registration_number.short_description = _("Company Registration Number")

    def display_monthly_transaction_volume(self, obj):
        """Display monthly transaction volume or '-' if empty"""
        return obj.get_monthly_transaction_volume_display() if obj.monthly_transaction_volume else "-"
    display_monthly_transaction_volume.short_description = _("Monthly Transaction Volume")

    def display_referral_code(self, obj):
        """Display referral code or '-' if empty"""
        return obj.referral_code or "-"
    display_referral_code.short_description = _("Referral Code")

    def display_organization_url(self, obj):
        """Display organization URL or '-' if empty"""
        return obj.organization_url or "-"
    display_organization_url.short_description = _("Organization URL")

    def display_facebook_media_link(self, obj):
        """Display Facebook media link or '-' if empty"""
        return obj.facebook_media_link or "-"
    display_facebook_media_link.short_description = _("Facebook Link")

    def display_instagram_media_link(self, obj):
        """Display Instagram media link or '-' if empty"""
        return obj.instagram_media_link or "-"
    display_instagram_media_link.short_description = _("Instagram Link")

    def display_twitter_media_link(self, obj):
        """Display Twitter media link or '-' if empty"""
        return obj.twitter_media_link or "-"
    display_twitter_media_link.short_description = _("Twitter Link")

    def display_address_details(self, obj):
        """Show detailed address in change form"""
        if not obj.address:
            return _("No address information available")
        
        address = obj.address
        html = ['<div style="padding: 10px; background-color: #f8f9fa; border-radius: 4px;">']
        
        # if address.title:
        #     html.append(f'<p><strong>Title:</strong> {address.title}</p>')
        
        # address_parts = []
        # if address.house_no:
        #     address_parts.append(f'<strong>House/Building:</strong> {address.house_no}')
        # if address.address:
        #     address_parts.append(f'<strong>Street/Area:</strong> {address.address}')
        
        # if address_parts:
        #     html.append(f'<p>{", ".join(address_parts)}</p>')
        
        location_parts = []
        if address.city:
            location_parts.append(f'<strong>City:</strong> {address.city}')
        # if address.state:
        #     location_parts.append(f'<strong>State:</strong> {address.state}')
        if address.country:
            location_parts.append(f'<strong>Country:</strong> {address.country}')
        # if address.pincode:
        #     location_parts.append(f'<strong>PIN:</strong> {address.pincode}')
        
        if location_parts:
            html.append(f'<p>{", ".join(location_parts)}</p>')
        
        # Show map link if coordinates are available
        # if address.location:
        #     lat = address.location.y
        #     lng = address.location.x
        #     html.append(f'<p><strong>Coordinates:</strong> {lat}, {lng}</p>')
        #     html.append(f'<p><a href="https://www.google.com/maps/search/?api=1&query={lat},{lng}" target="_blank" class="button" style="display: inline-block; padding: 5px 10px; background-color: #417690; color: white; text-decoration: none; border-radius: 4px;">View on Google Maps</a></p>')
        
        html.append('</div>')
        return mark_safe(''.join(html))
    
    display_address_details.short_description = _("Address Details")
    
    def partner_admin_email(self, obj):
        admin = User.objects.filter(
            partner=obj,
            user_type__in=UserTypeChoices.partner_admin_types(),
        ).first()
        
        if admin:
            return admin.email
        return "-"
    
    partner_admin_email.short_description = _("Admin Email")

    def get_fieldsets(self, request, obj=None):
        if request.user.is_superuser:
            return [
                (None, {'fields': ('entity_name', 'display_entity_type', 'display_subdomain', 'display_vanity_domain', 'is_active', 'display_preference', 'display_package_type', 'display_logo', 'display_primary_theme', 'display_secondary_theme', 'gst_number', 'company_registration_number', 'monthly_transaction_volume', 'display_address_details', 'partner_admin_details', 'update_notification_enabled', 'display_referral_code', 'organization_url', 'facebook_media_link', 'instagram_media_link', 'twitter_media_link', 'created_at', 'updated_at', 'admin_log_entries')}),
            ]
        else:
            # For partner admins, show editable fields as actual fields and read-only fields as display methods
            return [
                (None, {'fields': ('entity_name', 'display_entity_type', 'display_subdomain', 'display_vanity_domain', 'display_preference', 'display_package_type', 'logo', 'primary_theme', 'secondary_theme', 'gst_number', 'company_registration_number', 'monthly_transaction_volume', 'display_address_details', 'partner_admin_details', 'update_notification_enabled', 'display_referral_code', 'display_organization_url', 'display_facebook_media_link', 'display_instagram_media_link', 'display_twitter_media_link', 'created_at', 'updated_at')}),
            ]
    
    def partner_admin_details(self, obj):
        """Show partner admin details with link to admin page"""
        admin = User.objects.filter(
            partner=obj,
            user_type__in=UserTypeChoices.partner_admin_types(),
        ).first()
        
        if not admin:
            return _("No admin found for this partner.")
        
        # Build URL to the PartnerAdminUserAdmin change page
        admin_url = reverse(
            "admin:accounts_partneradminuser_change",
            args=[admin.pk]
        )
        
        return mark_safe(f"""
            <div>
                <p><strong>Admin Email:</strong> {admin.email}</p>
                <p><strong>Full Name:</strong> {admin.full_name or '-'}</p>
                <p><strong>Phone:</strong> {admin.phone_number or '-'}</p>
                <p><a href="{admin_url}" class="button" style="display: inline-block; padding: 8px 16px; 
                    background-color: #0d6efd !important; color: #ffffff !important; text-decoration: none; border-radius: 4px; 
                    font-weight: bold; border: 2px solid #0d6efd;">
                    View Admin Details</a></p>
            </div>
        """)
    
    partner_admin_details.short_description = _("Partner Admin")
    
    def admin_log_entries(self, obj):
        """
        Display log entries for this partner as a read-only field
        """
        if not obj.pk:
            return _("Log entries will be available once the partner is saved.")
            
        # Get all log entries for this partner
        log_entries = LogEntry.objects.filter(
            content_type_id=ContentType.objects.get_for_model(obj).pk,
            object_id=obj.pk
        ).order_by('-action_time')[:10]  # Limited to last 10 for performance
        
        if not log_entries:
            return _("No log entries found.")
            
        html = ['<table style="width:100%">']
        html.append('<tr><th>Date</th><th>User</th><th>Action</th></tr>')
        
        for entry in log_entries:
            timestamp = entry.action_time.strftime('%Y-%m-%d %H:%M:%S')
            user = entry.user.email if entry.user else 'Unknown'
            html.append(f'<tr><td>{timestamp}</td><td>{user}</td><td>{entry.change_message}</td></tr>')
            
        html.append('</table>')
        return mark_safe(''.join(html))
    
    admin_log_entries.short_description = _("History Log")
    
    def active_status(self, obj):
        if obj.is_active:
            return mark_safe('<span style="color:green; font-weight:bold;">✓ Active</span>')
        return mark_safe('<span style="color:red; font-weight:bold;">✗ Inactive</span>')
    
    active_status.short_description = _('Status')
    active_status.admin_order_field = 'is_active'
    
    def save_model(self, request, obj, form, change):
        """
        Override save_model to ensure the form's full save logic is executed
        This ensures the partner's related users are properly activated/deactivated
        and that appropriate logs and emails are sent
        """
        # The form will handle updating related users and sending notifications
        form.save()

    def get_readonly_fields(self, request, obj=None):
        if obj:  # Editing an existing object
            if request.user.is_superuser:
                # For SuperAdmin, allow editing specific fields while keeping others read-only (excluding entity_type)
                superadmin_editable_fields = [
                    'is_active', 'organization_url', 'gst_number', 
                    'company_registration_number', 'monthly_transaction_volume', 
                    'facebook_media_link', 'instagram_media_link', 'twitter_media_link', 
                    'update_notification_enabled'
                ]
                model_fields = [f.name for f in self.model._meta.fields if f.name not in superadmin_editable_fields]
                return tuple(set(list(self.readonly_fields) + model_fields))
            elif request.user.user_type == UserTypeChoices.PARTNER_ADMIN.value:
                # For PartnerAdmin, allow editing only specific fields (excluding entity_type)
                # Display methods are always read-only, plus non-editable model fields
                editable_fields = [
                    'entity_name', 'logo', 'primary_theme', 'secondary_theme', 
                    'gst_number', 'company_registration_number', 'monthly_transaction_volume', 
                    'update_notification_enabled'
                ]
                non_editable_model_fields = [f.name for f in self.model._meta.fields if f.name not in editable_fields]
                return tuple(set(list(self.readonly_fields) + non_editable_model_fields))
        return self.readonly_fields
    
    def has_add_permission(self, request):
        # Only superadmins can add partners
        return False

    def has_module_permission(self, request):
        # Both Super Admins and Partner Admins can see this module
        return request.user.is_active and (request.user.is_superuser or request.user.user_type == UserTypeChoices.PARTNER_ADMIN.value)

    def has_view_permission(self, request, obj=None):
        # Both Super Admins and Partner Admins can view partners
        if not request.user.is_active:
            return False
        if request.user.is_superuser:
            return True
        if request.user.user_type == UserTypeChoices.PARTNER_ADMIN.value:
            # Partner Admins can only view their own partner
            return obj is None or obj.id == request.user.partner_id
        return False

    def has_change_permission(self, request, obj=None):
        # Both Super Admins and Partner Admins can change partners
        if not request.user.is_active:
            return False
        if request.user.is_superuser:
            return True
        if request.user.user_type == UserTypeChoices.PARTNER_ADMIN.value:
            # Partner Admins can only change their own partner
            return obj is None or obj.id == request.user.partner_id
        return False

    def has_delete_permission(self, request, obj=None):
        return False

    def active_status(self, obj):
        if obj.is_active:
            return mark_safe('<span style="color:green; font-weight:bold;">✓ Active</span>')
        return mark_safe('<span style="color:red; font-weight:bold;">✗ Inactive</span>')

    def get_search_fields(self, request):
        """Override search_fields to add a custom behavior for admin email search"""
        return ('entity_name', 'subdomain', 'vanity_domain')

    def get_search_results(self, request, queryset, search_term):
        """Extend search to include searching by admin email"""
        queryset, use_distinct = super().get_search_results(request, queryset, search_term)
        
        # If there's a search term that looks like an email
        if search_term:
            # Find partners with admin emails matching the search term
            admin_users = User.objects.filter(
                email__icontains=search_term,
                user_type__in=UserTypeChoices.partner_admin_types(),
            )
            # Get their partner IDs
            partner_ids = admin_users.values_list('partner_id', flat=True)
            # Add these partners to the queryset
            queryset |= self.model.objects.filter(id__in=partner_ids)
        
        return queryset, use_distinct


# Inline admin for BlogTag through model
class BlogTagInline(admin.TabularInline):
    """Inline to add tags to blogs"""
    model = BlogTag
    extra = 1
    show_change_link = False
    fields = ['tag']
    exclude = ['deleted_at', 'restored_at', 'is_active', 'external_id', 'created_at', 'updated_at']
    
    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """Filter tags to only active ones"""
        if db_field.name == "tag":
            kwargs["queryset"] = Tag.objects.filter(is_active=True)
        return super().formfield_for_foreignkey(db_field, request, **kwargs)

    def has_add_permission(self, request, obj=None):
        """Only superadmins can add blog tags"""
        return request.user.is_superuser
    
    def has_change_permission(self, request, obj=None):
        """Only superadmins can change blog tags"""
        return request.user.is_superuser
    
    def has_delete_permission(self, request, obj=None):
        """Only superadmins can delete blog tags"""
        return request.user.is_superuser
    
    def has_view_permission(self, request, obj=None):
        """Only superadmins can view blog tags"""
        return request.user.is_superuser


@admin.register(Blog)
class BlogAdmin(CustomExportActionMixin, admin.ModelAdmin):
    """
    Admin interface for Blog model with single form for create and update
    """
    form = BlogForm
    list_display = ('title', 'display_writer', 'type', 'blog_status', 'display_published_at', 'display_tags', 'created_at')
    list_filter = (BlogStatusFilter, 'type', 'writer', 'created_at')
    search_fields = ('title', 'slug', 'writer__name', 'content')
    ordering = ('-created_at',)
    list_display_links = ('title',)
    inlines = [BlogTagInline]
    readonly_fields = ('created_at', 'updated_at', 'published_at')
    
    def get_fieldsets(self, request, obj=None):
        """Control field visibility: limited fields for creation, all fields for update/view"""
        if obj is None:  # Creating new blog - show only essential fields with auto-generation controls
            return [
                (_('General'), {
                    'fields': (
                        'title', 
                        'content', 'auto_generate_content',
                        'slug', 'auto_generate_slug', 
                        'banner', 'type', 'is_active', 'writer', 
                        'audio_file', 'auto_generate_audio_file'
                    )
                }),
            ]
        else:  # Updating existing blog - show all fields
            if obj.type == BlogTypeChoices.PODCAST.value:
                return [
                    (_('General'), {
                        'fields': (
                            'title', 
                            'content', 'auto_generate_content',
                            'slug', 'auto_generate_slug',
                            'banner', 'read_time', 'type', 'is_active', 'writer', 
                            'audio_file', 'auto_generate_audio_file',
                            'seo_title', 'meta_description', 'meta_keywords', 
                            'canonical_url', 'featured_image_alt_text', 'robots_directive', 
                            'focus_keyword', 'meta_information', 
                            'created_at', 'updated_at', 'published_at'
                        )
                    }),
                ]
            else:
                return [
                    (_('General'), {
                        'fields': (
                            'title', 
                            'content', 'auto_generate_content',
                            'slug', 'auto_generate_slug',
                            'banner', 'read_time', 'type', 'is_active', 'writer', 
                            'seo_title', 'meta_description', 'meta_keywords', 
                            'canonical_url', 'featured_image_alt_text', 'robots_directive', 
                            'focus_keyword', 'meta_information',
                            'created_at', 'updated_at', 'published_at'
                        )
                    }),
                ]

    def formfield_for_dbfield(self, db_field, request, **kwargs):
        """Use CKEditor for content field"""
        if db_field.name == 'content':
            kwargs['widget'] = CKEditorWidget()
        return super().formfield_for_dbfield(db_field, request, **kwargs)

    def display_writer(self, obj):
        """Display writer name"""
        return obj.writer.name if obj.writer else "-"
    display_writer.short_description = _("Writer")
    display_writer.admin_order_field = 'writer__name'

    def blog_status(self, obj):
        """Display blog status with colors"""
        if obj.is_active:
            return mark_safe('<span style="color:green; font-weight:bold;">✓ Published</span>')
        else:
            return mark_safe('<span style="color:red; font-weight:bold;">✗ Unpublished</span>')
    blog_status.short_description = _('Status')
    blog_status.admin_order_field = 'is_active'

    def display_published_at(self, obj):
        """Display published date formatted"""
        if obj.published_at:
            return obj.published_at.strftime('%Y-%m-%d %H:%M')
        return "-"
    display_published_at.short_description = _("Published At")
    display_published_at.admin_order_field = 'published_at'

    def display_tags(self, obj):
        """Display associated tags"""
        tags = obj.tags.filter(is_active=True)
        if tags.exists():
            tag_names = [tag.name for tag in tags[:3]]  # Show max 3 tags
            display = ", ".join(tag_names)
            if tags.count() > 3:
                display += f" (+{tags.count() - 3} more)"
            return display
        return "-"
    display_tags.short_description = _("Tags")

    def get_queryset(self, request):
        """Include related objects to optimize queries"""
        return super().get_queryset(request).select_related('writer').prefetch_related('tags')

    def get_readonly_fields(self, request, obj=None):
        """Make type field readonly for existing blogs"""
        readonly_fields = list(self.readonly_fields)  # Start with base readonly fields
        
        # For existing blogs, make type field readonly to prevent changes
        if obj and obj.pk:
            readonly_fields.append('type')
            print(f"[DEBUG] Added 'type' to readonly fields for existing blog (ID: {obj.pk})")
        else:
            print(f"[DEBUG] 'type' field remains editable for new blog creation")
            
        return readonly_fields

    def has_module_permission(self, request):
        """Only superadmins can access blog module"""
        return request.user.is_superuser

    def has_view_permission(self, request, obj=None):
        """Only superadmins can view blogs"""
        return request.user.is_superuser

    def has_add_permission(self, request):
        """Only superadmins can add blogs"""
        return request.user.is_superuser

    def has_change_permission(self, request, obj=None):
        """Only superadmins can change blogs"""
        return request.user.is_superuser

    def has_delete_permission(self, request, obj=None):
        """Only superadmins can delete blogs"""
        return request.user.is_superuser

    def response_add(self, request, obj, post_url_continue=None):
        """Custom message after adding a Blog"""
        if obj.type == BlogTypeChoices.PODCAST:
            self.message_user(request, f"🎙️ Podcast '{obj.title}' was created successfully!", level=messages.SUCCESS)
        else:
            self.message_user(request, f"✍️ Blog Post '{obj.title}' was created successfully!", level=messages.SUCCESS)
        
        return super().response_add(request, obj, post_url_continue)

    def response_change(self, request, obj):
        """Custom message after changing a Blog"""
        if obj.type == BlogTypeChoices.PODCAST:
            self.message_user(request, f"🎙️ Podcast '{obj.title}' was updated successfully!", level=messages.SUCCESS)
        else:
            self.message_user(request, f"✍️ Blog Post '{obj.title}' was updated successfully!", level=messages.SUCCESS)
        
        return super().response_change(request, obj)

    def save_model(self, request, obj, form, change):
        """Override save to handle OpenAI processing during blog creation"""
        print(f"[DEBUG] BlogAdmin.save_model called - change: {change}, obj.pk: {obj.pk}")
        
        # If this is a new blog creation, trigger OpenAI processing manually
        if not change:
            print(f"[DEBUG] New blog creation detected - triggering OpenAI processing")
            
            try:
                # Manually trigger the form's OpenAI processing by calling clean()
                cleaned_data = form.clean()
                print(f"[DEBUG] Form clean() completed, updating model fields")
                
                # Update the model instance with the enhanced data from clean()
                enhanced_fields = [
                    'content', 'slug', 'read_time', 'seo_title', 'meta_description', 
                    'meta_keywords', 'canonical_url', 'featured_image_alt_text', 
                    'robots_directive', 'focus_keyword', 'meta_information'
                ]
                
                for field in enhanced_fields:
                    if field in cleaned_data and cleaned_data[field]:
                        setattr(obj, field, cleaned_data[field])
                        print(f"[DEBUG] Set {field} from cleaned_data: {cleaned_data[field][:50] if isinstance(cleaned_data[field], str) else cleaned_data[field]}")
                
                print(f"[DEBUG] OpenAI processing completed successfully for new blog")
                
            except Exception as e:
                print(f"[DEBUG] OpenAI processing failed during creation: {str(e)}")
                # Continue with save even if OpenAI processing fails
        else:
            print(f"[DEBUG] Blog update detected - using standard form processing")
        
        # Call the parent save_model method which handles the form.save() logic correctly
        super().save_model(request, obj, form, change)
        print(f"[DEBUG] Blog saved successfully")


@admin.register(Affiliate)
class AffiliateAdmin(admin.ModelAdmin):
    form = AffiliateForm
    list_display = ('user', 'status', 'affiliate_url', 'users_referred_count', 'referred_users_link', 'requested_at', 'created_at')
    list_filter = ('status', 'created_at')
    search_fields = ('user__email', 'user__full_name', 'user__phone_number')
    readonly_fields = ('user', 'created_at', 'affiliate_url', 'requested_at', 'users_referred_count', 'approved_at', 'rejected_at', 'referred_users_link')
    ordering = ('-created_at',)
    exclude = ('affiliate_code',)
    list_display_links = ('user',)

    def get_readonly_fields(self, request, obj=None):
        """Only make status and rejection_reason readonly if saved with approved/rejected status"""
        readonly_fields = list(self.readonly_fields)
        
        if obj and obj.pk:
            # Get the object from database to check its actual saved status
            try:
                saved_obj = Affiliate.objects.get(pk=obj.pk)
                if saved_obj.status in [AffiliateStatusChoices.APPROVED.value, AffiliateStatusChoices.REJECTED.value]:
                    readonly_fields.extend(['status', 'rejection_reason'])
            except Affiliate.DoesNotExist:
                pass
                
        return readonly_fields

    def affiliate_url(self, obj):
        """Return affiliate URL if approved"""
        if obj and obj.status == AffiliateStatusChoices.APPROVED.value and obj.affiliate_code:
            return f"{settings.FE_AFFILIATE_BASE_URL}{obj.affiliate_code}"
        return "-"
    affiliate_url.short_description = "Affiliate URL"

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return request.user.is_superuser

    def has_delete_permission(self, request, obj=None):
        return False

    def referred_users_link(self, obj):
        """Return link to view referred users"""
        if obj and obj.users_referred_count > 0:
            url = reverse(
                'admin:accounts_affiliatereferraluser_changelist',
                current_app=self.admin_site.name
            )
            url = f"{url}?affiliate__id__exact={obj.id}"
            return mark_safe(f'<a href="{url}" class="button" target="_blank">👥 View Referred Users</a>')
        return "-"
    referred_users_link.short_description = "Referred Users"


@admin.register(AffiliateReferralUser)
class AffiliateReferralUserAdmin(admin.ModelAdmin):
    list_display = ('user', 'created_at')
    list_filter = (ApprovedAffiliateFilter, 'created_at')
    search_fields = ('user__email', 'user__full_name', 'user__phone_number', 'affiliate__user__email')
    readonly_fields = ('user', 'affiliate', 'created_at',)
    ordering = ('-created_at',)
    list_display_links = ('user',)
    exclude = ('deleted_at', 'restored_at', 'transaction_id')

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

    def affiliate_link(self, obj):
        """Return link to view affiliate details"""
        if obj and obj.affiliate:
            url = reverse(
                'admin:accounts_affiliate_change',
                args=[obj.affiliate.id],
                current_app=self.admin_site.name
            )
            return mark_safe(f'<a href="{url}" class="button" target="_blank">🔗 View Affiliate</a>')
        return "-"
    affiliate_link.short_description = "Affiliate"


@admin.register(GlobalDashboard)
class GlobalDashboardAdmin(CustomExportActionMixin, admin.ModelAdmin):
    """
    Admin interface for Global Dashboard with hard delete functionality
    """
    form = GlobalDashboardForm
    list_display = ('type', 'value', 'created_at', 'updated_at')
    list_filter = ('type', 'created_at')
    search_fields = ('type', 'value')
    ordering = ('type', '-created_at')
    readonly_fields = ('created_at', 'updated_at')
    
    def get_readonly_fields(self, request, obj=None):
        """Make type field readonly when editing existing objects"""
        readonly_fields = list(self.readonly_fields)
        
        # If editing an existing object, make type readonly
        if obj and obj.pk:
            readonly_fields.append('type')
            
        return readonly_fields
    
    fieldsets = (
        ('Dashboard Configuration', {
            'fields': ('type', 'value'),
            'description': 'Configure global dashboard values. Only one value is allowed per type.'
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        """Use default queryset (no partner filtering needed for global dashboard)"""
        return super().get_queryset(request)

    def has_add_permission(self, request):
        """Allow adding global dashboard entries for superusers only"""
        return request.user.is_superuser

    def has_change_permission(self, request, obj=None):
        """Allow changing global dashboard entries for superusers only"""
        return request.user.is_superuser

    def has_delete_permission(self, request, obj=None):
        """Allow deleting global dashboard entries for superusers only"""
        return request.user.is_superuser

    def has_view_permission(self, request, obj=None):
        """Allow viewing for superusers and partner admins"""
        return request.user.is_active and (
            request.user.is_superuser or 
            request.user.user_type == UserTypeChoices.PARTNER_ADMIN.value
        )

    def delete_model(self, request, obj):
        """Use hard_delete instead of soft delete"""
        obj.hard_delete()

    def delete_queryset(self, request, queryset):
        """Hard delete multiple objects"""
        for obj in queryset:
            obj.hard_delete()

    def save_model(self, request, obj, form, change):
        """Override save to add logging"""
        action = 'Updated' if change else 'Created'
        super().save_model(request, obj, form, change)
        
        # Log the action
        LogEntry.objects.log_action(
            user_id=request.user.pk,
            content_type_id=ContentType.objects.get_for_model(obj).pk,
            object_id=obj.pk,
            object_repr=str(obj),
            action_flag=CHANGE if change else 1,  # 1 = ADDITION
            change_message=f'{action} global dashboard entry: {obj.type} = {obj.value}'
        )
