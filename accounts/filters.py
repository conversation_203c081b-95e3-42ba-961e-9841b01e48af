import django_filters
from django.db.models import Q
from accounts.models import Blog


class BlogTagFilter(django_filters.FilterSet):
    """
    Filter class for Blog model with tag filtering support
    """
    tags = django_filters.CharFilter(method='filter_tags', help_text="Comma-separated tag external_ids for OR filtering")
    
    class Meta:
        model = Blog
        fields = ['tags']
    

    def filter_tags(self, queryset, name, value):
        """
        Filter blogs by multiple tag external_ids with OR logic.
        """
        if not value:
            return queryset

        tag_external_ids = [v.strip() for v in value.split(',') if v.strip()]
        if not tag_external_ids:
            return queryset

        return queryset.filter(
            blog_tags__tag__external_id__in=tag_external_ids
        ).distinct()
