"""
Admin login views for handling partner admin and superadmin authentication.
"""
from django.contrib.auth.views import LoginView
from django.contrib.auth import login, logout
from django.shortcuts import redirect
from django.urls import reverse
from django.contrib import messages, admin
from django.core.exceptions import ValidationError
from django.http import HttpResponseBadRequest
from django.utils.http import url_has_allowed_host_and_scheme
from django.utils.translation import gettext_lazy as _
from accounts.models import Partner
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
import json
import logging

from .forms import PartnerAwareAdminAuthenticationForm
from .auth import PartnerAdminAuthBackend

logger = logging.getLogger(__name__)

@method_decorator(csrf_exempt, name='dispatch')
class CustomAdminLoginView(LoginView):
    """
    Custom admin login view that handles partner validation before CSRF check
    """
    form_class = PartnerAwareAdminAuthenticationForm
    template_name = 'admin/login.html'
    redirect_authenticated_user = True

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.auth_backend = PartnerAdminAuthBackend()

    def dispatch(self, request, *args, **kwargs):
        logger.info(f"[AdminLogin] Starting login process for host: {request.get_host()}")
        
        # Extract partner domain and environment using the auth backend
        partner_domain, environment = self.auth_backend._extract_partner_domain(request.get_host())
        logger.info(f"[AdminLogin] Extracted partner_domain: {partner_domain}, environment: {environment}")
        
        # If no environment is found, it's an invalid domain
        if environment is None:
            logger.warning(f"[AdminLogin] Invalid domain detected: {request.get_host()}")
            request.partner_validation_error = _("Invalid domain. Please use the correct admin portal URL.")
            self.form_class = self.get_invalid_partner_form()
            return super().dispatch(request, *args, **kwargs)

        if partner_domain is None:
            # This is a base domain (superadmin login)
            logger.info(f"[AdminLogin] Attempting superadmin login on host: {request.get_host()}")
            # Verify it's actually a base domain
            if request.get_host().lower() not in self.auth_backend.env_map:
                logger.warning(f"[AdminLogin] Invalid superadmin portal URL: {request.get_host()}")
                request.partner_validation_error = _("Invalid superadmin portal URL.")
                self.form_class = self.get_invalid_partner_form()
                return super().dispatch(request, *args, **kwargs)

            request.is_superadmin_login = True
            logger.info("[AdminLogin] Valid superadmin portal, proceeding with login")
            return super().dispatch(request, *args, **kwargs)

        # This is a partner domain - validate partner before allowing any login attempt
        logger.info(f"[AdminLogin] Attempting partner admin login for partner: {partner_domain}")
        try:
            # Check if partner exists and is active
            partner = Partner.objects.get(subdomain=partner_domain)
            logger.info(f"[AdminLogin] Found partner: {partner.subdomain}, active: {partner.is_active}")
            
            if not partner.is_active:
                logger.warning(f"[AdminLogin] Inactive partner detected: {partner.subdomain}")
                
                # Check if user is already logged in - log them out
                if request.user.is_authenticated and hasattr(request.user, "partner") and request.user.partner == partner:
                    logger.warning(f"[AdminLogin] Inactive partner with logged in user: {request.user.email}, logging out")
                    logout(request)
                
                # Store partner in request for error display during form validation
                request.partner = partner
                request.partner_inactive = True
                request.is_superadmin_login = False
                messages.error(request, _("Your partner has been deactivated by Zuumm. Please contact support."))
                
                # Continue to the login page where the error will be displayed
                return super().dispatch(request, *args, **kwargs)
                
            # Only set these properties for active partners
            request.partner = partner
            request.is_superadmin_login = False
            logger.info(f"[AdminLogin] Valid partner portal, proceeding with login for partner: {partner.subdomain}")
            return super().dispatch(request, *args, **kwargs)
        except Partner.DoesNotExist:
            logger.warning(f"[AdminLogin] Non-existent partner detected: {partner_domain}")
            request.partner_validation_error = _("This partner's admin portal is not available. Please contact support.")
            self.form_class = self.get_invalid_partner_form()
            return super().dispatch(request, *args, **kwargs)

    def get_invalid_partner_form(self):
        """Return a form class that always shows the partner validation error"""
        class InvalidPartnerForm(PartnerAwareAdminAuthenticationForm):
            def clean(self):
                # Always raise validation error with the partner validation message
                raise ValidationError(self.request.partner_validation_error)
        return InvalidPartnerForm

    def get_form_class(self):
        """Get the appropriate form class based on request state"""
        if hasattr(self.request, 'partner_validation_error'):
            return self.get_invalid_partner_form()
        return self.form_class

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Add partner info to context if available
        partner = getattr(self.request, 'partner', None)
        is_superadmin_login = getattr(self.request, 'is_superadmin_login', False)
        
        if partner and not is_superadmin_login:
            # For partner admin login
            context.update({
                'partner': partner,
                'site_header': f"{partner.entity_name} Admin",
                'site_title': f"{partner.entity_name} Admin",
                'is_superadmin_login': False
            })
        else:
            # For superadmin login
            context.update({
                'site_header': admin.site.site_header,
                'site_title': admin.site.site_title,
                'is_superadmin_login': True
            })

        # Add partner validation error if exists
        if hasattr(self.request, 'partner_validation_error'):
            context['partner_validation_error'] = self.request.partner_validation_error

        # Common context for both
        context.update({
            'title': _('Log in'),
            'app_path': self.request.get_full_path(),
            'username': self.request.user.get_username(),
            'admin_url': reverse('admin:index'),
            'available_apps': admin.site.get_app_list(self.request)
        })
        
        return context

    def form_valid(self, form):
        logger.info("[AdminLogin] Form validation started")
        
        # If there's a partner validation error, show it
        if hasattr(self.request, 'partner_validation_error'):
            logger.warning(f"[AdminLogin] Partner validation error: {self.request.partner_validation_error}")
            messages.error(self.request, self.request.partner_validation_error)
            return self.form_invalid(form)
            
        # Check if partner is inactive - don't allow login
        if hasattr(self.request, 'partner_inactive') and self.request.partner_inactive:
            logger.warning(f"[AdminLogin] Login attempt for inactive partner: {self.request.partner.subdomain}")
            messages.error(self.request, _("Your partner has been deactivated by Zuumm. Please contact support."))
            return self.form_invalid(form)

        try:
            # Authenticate user
            user = form.get_user()
            logger.info(f"[AdminLogin] User authenticated: {user.email}, partner: {getattr(user.partner, 'subdomain', 'None')}")
            
            # Login user
            login(self.request, user)
            logger.info(f"[AdminLogin] User logged in successfully: {user.email}")
            
            # Get success URL
            success_url = self.get_success_url()
            logger.info(f"[AdminLogin] Redirecting to: {success_url}")
            return redirect(success_url)
                
        except ValidationError as e:
            logger.error(f"[AdminLogin] Validation error during login: {str(e)}")
            messages.error(self.request, str(e))
            return self.form_invalid(form)
        except Exception as e:
            logger.error(f"[AdminLogin] Unexpected error during login: {str(e)}", exc_info=True)
            messages.error(self.request, "An error occurred during login.")
            return self.form_invalid(form)

    def form_invalid(self, form):
        # For API requests, return JSON response
        if self.request.headers.get('x-requested-with') == 'XMLHttpRequest':
            # Get the first error message if it's a list, otherwise use the error directly
            error_message = form.errors.as_data().get('__all__', [])[0].messages[0] if form.errors.get('__all__') else str(form.errors)
            return HttpResponseBadRequest(
                json.dumps({
                    'status': 'error',
                    'message': error_message
                }),
                content_type='application/json'
            )
        # For regular requests, use default behavior
        return super().form_invalid(form)

    def get_success_url(self):
        """
        Get the success URL for the login redirect.
        For superadmin logins, redirects to the main admin portal.
        For partner admin logins, redirects to their specific partner admin portal.
        """
        logger.info("[AdminLogin] Determining success URL")
        
        # For superadmin logins, always redirect to the main admin portal
        if getattr(self.request, 'is_superadmin_login', False):
            logger.info("[AdminLogin] Superadmin login - redirecting to main admin portal")
            return reverse('admin:index')

        # For partner admin logins
        if hasattr(self.request, 'partner'):
            partner = self.request.partner
            success_url = f"/admin/?partner={partner.subdomain}"
            logger.info(f"[AdminLogin] Partner admin login - redirecting to partner portal: {success_url}")
            return success_url

        # Fallback to main admin portal
        logger.warning("[AdminLogin] No partner or superadmin flag found - falling back to main admin portal")
        return reverse('admin:index')

# View for /admin/login/
superadmin_login_view = CustomAdminLoginView.as_view()

# View for /admin/login/partner/<partner_subdomain>/
partner_specific_login_view = CustomAdminLoginView.as_view()

# This admin_login is now an alias for superadmin_login_view if still imported elsewhere
admin_login = superadmin_login_view 


from django.contrib.auth import views as auth_views
from django.contrib import messages

class CustomPasswordChangeDoneView(auth_views.PasswordChangeDoneView):
    def dispatch(self, request, *args, **kwargs):
        messages.success(request, "Password changed successfully.")
        return super().dispatch(request, *args, **kwargs)
    
