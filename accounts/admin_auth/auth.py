"""
Admin authentication backend for handling partner admin and superadmin authentication.
"""
from django.contrib.auth.backends import ModelBackend
from django.contrib.auth import get_user_model
from django.conf import settings
from django.db.models import Q
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
import re
import logging

from accounts.models import Partner
from accounts.choices import UserTypeChoices

User = get_user_model()
logger = logging.getLogger(__name__)

class PartnerAdminAuthBackend(ModelBackend):
    """
    Custom authentication backend for admin login that handles both partner admin and superadmin authentication.
    This backend enforces proper domain-based access control and user type validation.
    """
    def __init__(self):
        # Use the same patterns as PartnerMiddleware
        self.domain_patterns = {
            'dev': r'^(?P<partner_domain>[^.]+)\.dev-api\.zuumm\.ai$',
            'uat': r'^(?P<partner_domain>[^.]+)\.uat-api\.zuumm\.ai$',
            'prod': r'^(?P<partner_domain>[^.]+)\.app\.zuumm\.ai$'
        }
        # Map hostnames to environments
        self.env_map = {
            'dev-api.zuumm.ai': 'dev',
            'uat-api.zuumm.ai': 'uat',
            'app.zuumm.ai': 'prod',
            '127.0.0.1:8000': 'local',
            'localhost:8000': 'local',
            'localhost': 'local',
            '127.0.0.1': 'local',
            '0.0.0.0': 'local'
        }
        logger.info("[AuthBackend] Initialized with domain patterns and environment map")

    def _extract_partner_domain(self, host):
        """
        Extract partner domain from host using the same logic as PartnerMiddleware
        Returns tuple of (partner_domain, environment)
        """
        host = host.lower()
        logger.info(f"[AuthBackend] Extracting partner domain from host: {host}")
        
        # First check if it's a base domain (superadmin login)
        if host in self.env_map:
            logger.info(f"[AuthBackend] Found base domain for environment: {self.env_map[host]}")
            return None, self.env_map[host]
            
        # Try to match partner subdomain pattern
        for env, pattern in self.domain_patterns.items():
            match = re.match(pattern, host)
            if match:
                partner_domain = match.group('partner_domain')
                logger.info(f"[AuthBackend] Partner domain extracted: {partner_domain} (env: {env})")
                return partner_domain, env
                
        logger.warning(f"[AuthBackend] No matching pattern found for host: {host}")
        return None, None

    def authenticate(self, request, username=None, password=None, **kwargs):
        logger.info(f"[AuthBackend] Starting authentication for user: {username}")
        
        if username is None or password is None:
            logger.warning("[AuthBackend] Missing username or password")
            return None

        # Get host from request
        host = request.get_host().lower()
        logger.info(f"[AuthBackend] Authenticating on host: {host}")
        
        # Extract partner domain and environment using the same logic as middleware
        partner_domain, environment = self._extract_partner_domain(host)
        logger.info(f"[AuthBackend] Extracted partner_domain: {partner_domain}, environment: {environment}")

        # Store domain info in request for later use
        request.partner_domain = partner_domain
        request.environment = environment

        try:
            if partner_domain is None:
                # Main domain - only superadmins allowed
                logger.info(f"[AuthBackend] Main domain login attempt for: {username}")
                user = User.objects.get(
                    email__iexact=username,
                    user_type=UserTypeChoices.SUPER_ADMIN.value,
                    is_superuser=True,
                    is_staff=True,
                    is_active=True
                )
                logger.info(f"[AuthBackend] Found superadmin user: {user.email}")
                request.is_superadmin_login = True
                request.partner = None

            else:
                # Subdomain - only partner admins for this specific partner allowed
                logger.info(f"[AuthBackend] Partner domain login attempt for: {username} on domain: {partner_domain}")
                
                # First, find the partner
                try:
                    partner = Partner.objects.get(subdomain=partner_domain, is_active=True)
                    logger.info(f"[AuthBackend] Found active partner: {partner.entity_name}")
                except Partner.DoesNotExist:
                    logger.warning(f"[AuthBackend] No active partner found for domain: {partner_domain}")
                    request.auth_error = _("Invalid partner domain or partner is inactive.")
                    return None

                # Look for partner admin user - ensure they belong to this specific partner
                user = User.objects.get(
                    email__iexact=username,
                    user_type__in=UserTypeChoices.partner_admin_types(),
                    is_staff=True,
                    is_active=True,
                    partner=partner
                )
                logger.info(f"[AuthBackend] Found partner admin user: {user.email} for partner: {partner.subdomain}")
                request.is_superadmin_login = False
                request.partner = partner

        except User.DoesNotExist:
            if partner_domain is None:
                logger.warning(f"[AuthBackend] No superadmin account found for email: {username}")
                request.auth_error = _("No superadmin account found with this email.")
            else:
                logger.warning(f"[AuthBackend] No partner admin account found for email: {username} in partner: {partner_domain}")
                request.auth_error = _("No partner admin account found with this email for this partner.")
            return None
        except User.MultipleObjectsReturned as e:
            # Log the actual users that were found
            users = User.objects.filter(
                email__iexact=username,
                user_type=UserTypeChoices.SUPER_ADMIN.value if partner_domain is None else UserTypeChoices.PARTNER_ADMIN.value,
                is_staff=True
            ).values('id', 'email', 'user_type', 'is_superuser', 'partner_id')
            logger.error(f"[AuthBackend] Multiple users found: {list(users)}")
            request.auth_error = _("Multiple admin accounts found with this email. Please contact support.")
            return None
        except Exception as e:
            logger.error(f"[AuthBackend] Unexpected error during authentication: {str(e)}")
            request.auth_error = _("An error occurred during authentication. Please try again.")
            return None

        # Verify password
        if user.check_password(password):
            logger.info(f"[AuthBackend] Password verified for user: {user.email}")
            return user
        else:
            logger.warning(f"[AuthBackend] Invalid password for user: {user.email}")
            request.auth_error = _("Invalid password.")
            return None

    def get_user(self, user_id):
        """
        Get user by ID for session management
        """
        try:
            return User.objects.get(pk=user_id)
        except User.DoesNotExist:
            return None 