"""
Admin authentication forms for handling partner admin and superadmin login.
"""
from django import forms
from django.contrib.auth.forms import AuthenticationForm
from django.contrib.auth import authenticate
from django.utils.translation import gettext_lazy as _
from django.db import transaction
from django.core.exceptions import ValidationError

from accounts.models import User, Partner
from accounts.choices import UserTypeChoices, PartnerTypeChoices

from .auth import PartnerAdminAuthBackend

class PartnerAwareAdminAuthenticationForm(AuthenticationForm):
    """
    Custom authentication form for admin login that handles both partner admin and superadmin authentication.
    """
    username = forms.EmailField(
        label=_("Email"),
        widget=forms.EmailInput(attrs={
            'autofocus': True,
            'class': 'vTextField',
            'placeholder': 'Enter your email'
        })
    )
    password = forms.CharField(
        label=_("Password"),
        strip=False,
        widget=forms.PasswordInput(attrs={
            'class': 'vPassword<PERSON>ield',
            'placeholder': 'Enter your password',
            'autocomplete': 'current-password'
        }),
    )

    error_messages = {
        'invalid_login': _("Invalid email or password."),
        'inactive': _("This account is inactive."),
        'invalid_partner': _("Invalid partner specified."),
        'not_staff': _("This account does not have admin access."),
        'not_superuser': _("This account does not have superadmin access."),
        'not_partner_admin': _("This account does not have partner admin access."),
        'partner_admin_on_superadmin': _("Partner admin accounts can only log in through their partner's admin URL."),
        'superadmin_on_partner': _("Superadmin accounts can only log in through the main admin URL."),
    }

    def clean(self):
        username = self.cleaned_data.get('username')
        password = self.cleaned_data.get('password')
        request = self.request

        if username is not None and password:
            # Try to authenticate using PartnerAdminAuthBackend
            self.user_cache = authenticate(
                request=request,
                username=username,
                password=password,
                backend=PartnerAdminAuthBackend
            )

            if self.user_cache is None:
                # Check if there was an auth error message set by the backend
                if hasattr(request, 'auth_error'):
                    self.add_error(None, request.auth_error)
                else:
                    self.add_error(None, self.error_messages['invalid_login'])
                return self.cleaned_data

            # Check if user is active
            if not self.user_cache.is_active:
                self.add_error(None, self.error_messages['inactive'])
                return self.cleaned_data

            # Check if user is staff
            if not self.user_cache.is_staff:
                self.add_error(None, self.error_messages['not_staff'])
                return self.cleaned_data

            # Additional checks are handled by PartnerAdminAuthBackend
            # which sets appropriate error messages on the request object

        return self.cleaned_data

class SuperAdminCreationForm(forms.ModelForm):
    """
    A form for creating new Super Admin users.
    Uses email as the username field.
    """
    email = forms.EmailField(
        label=_("Email"),
        max_length=254,
        widget=forms.EmailInput(attrs={'autocomplete': 'email'})
    )
    password = forms.CharField(
        label=_("Password"),
        strip=False,
        widget=forms.PasswordInput(attrs={'autocomplete': 'new-password'}),
        help_text=_("Raw passwords are not stored, so there is no way to see this user's password, but you can change the password using <a href=\"../password/\">this form</a>.")
    )
    confirm_password = forms.CharField(
        label=_("Confirm password"),
        strip=False,
        widget=forms.PasswordInput(attrs={'autocomplete': 'new-password'})
    )
    full_name = forms.CharField(label=_("Full name"), max_length=150, required=False)

    class Meta:
        model = User
        fields = ("email", "full_name") # Fields from the model we directly map

    def clean_email(self):
        email = self.cleaned_data.get("email")
        if User.objects.filter(
            email__iexact=email,
            user_type=UserTypeChoices.SUPER_ADMIN.value
        ).exists():
            raise forms.ValidationError(_("A user with that email already exists."))
        return email

    def clean_confirm_password(self):
        password = self.cleaned_data.get("password")
        confirm_password = self.cleaned_data.get("confirm_password")
        if password and confirm_password and password != confirm_password:
            raise forms.ValidationError(_("The two password fields didn't match."))
        return confirm_password

    @transaction.atomic
    def save(self, commit=True):
        # This form's save method is responsible for creating the user via the manager
        # and then calling the email utility.
        # Note: super().save(commit=False) is not called as we use the manager directly.
        extra_fields = {}

        email = self.cleaned_data["email"]
        # Use the first password field for sending, as confirm_password might be cleared or changed by other logic
        # if clean_confirm_password is called multiple times or form is re-validated.
        raw_password_for_email = self.cleaned_data["password"] 
        password_for_creation = self.cleaned_data["confirm_password"] # Use confirmed password for creation
        full_name = self.cleaned_data.get("full_name")
        extra_fields['user_type'] = UserTypeChoices.SUPER_ADMIN.value

        partner = Partner.objects.filter(partner_type=PartnerTypeChoices.ZUUMM.value).first()
        if partner:
            extra_fields['partner'] = partner.id

        if full_name:
            extra_fields['full_name'] = full_name
        
        # Use the UserManager's create_superuser method
        self.instance = User.objects.create_superuser(
            email=email,
            password=password_for_creation, 
            **extra_fields
        )

        # Send email using the helper utility (pass the original raw password)
        # send_new_superadmin_email(user, raw_password_for_email)
        return self.instance

    def save_m2m(self, *args, **kwargs):
        """
        Admin calls this to save M2M data. For superuser creation,
        M2M (groups/permissions) are implicitly handled by is_superuser=True.
        This form has no explicit M2M fields to save.
        This method is here to prevent AttributeError during admin save process.
        """
        pass 