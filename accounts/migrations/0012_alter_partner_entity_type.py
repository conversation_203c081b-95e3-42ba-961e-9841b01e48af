# Generated by Django 4.2 on 2025-06-16 11:16

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("accounts", "0011_alter_partner_entity_type"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="partner",
            name="entity_type",
            field=models.CharField(
                choices=[
                    ("TA", "Travel Agent & Tour Operators"),
                    ("BP", "Banks & Financial Institutions"),
                    ("RP", "Retail chains and loyalty platforms"),
                    ("IC", "Insurance Companies"),
                    ("EI", "Educational Institutions"),
                    ("RSP", "Religious and Spiritual Platform"),
                    ("HEBP", "HR Employee Benefit Portal"),
                    ("ECP", "Ecommerce Platforms"),
                    ("FG", "Freelancer Gig Worker"),
                    ("SBL", "Small Business Local Shop"),
                    ("WCC", "Wellness Center Clinic"),
                    ("TO", "Transport Operations"),
                    ("OT", "Others"),
                ],
                default="OT",
                max_length=255,
            ),
        ),
    ]
