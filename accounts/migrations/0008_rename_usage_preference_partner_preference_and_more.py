# Generated by Django 4.2 on 2025-06-03 12:44

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("accounts", "0007_alter_otp_code"),
    ]

    operations = [
        migrations.RenameField(
            model_name="partner",
            old_name="usage_preference",
            new_name="preference",
        ),
        migrations.AddField(
            model_name="partner",
            name="facebook_media_link",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name="partner",
            name="instagram_media_link",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name="partner",
            name="organization_url",
            field=models.Char<PERSON>ield(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name="partner",
            name="twitter_media_link",
            field=models.<PERSON>r<PERSON>ield(blank=True, max_length=255, null=True),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name="partner",
            name="package_type",
            field=models.Char<PERSON><PERSON>(
                choices=[("ZP", "Zuumm Package"), ("OP", "Own Package")],
                default="ZP",
                max_length=255,
            ),
        ),
    ]
