# Generated by Django 4.2 on 2025-06-16 14:28

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("accounts", "0012_alter_partner_entity_type"),
    ]

    operations = [
        migrations.<PERSON>er<PERSON>ield(
            model_name="partner",
            name="entity_type",
            field=models.CharField(
                choices=[
                    ("TA", "Travel Agent/Tour operators"),
                    ("BP", "Banks & financial Institutions"),
                    ("RP", "Retail chains and loyalty platforms"),
                    ("IC", "Insurance companies"),
                    ("EI", "Educational Institutions"),
                    ("RSP", "Religious and spiritual Organizations"),
                    ("HEBP", "HR/Employee benefit portal"),
                    ("ECP", "E-commerce platforms"),
                    ("FG", "Freelancer & Gig-workers"),
                    ("SBL", "Small Businesses & local shops"),
                    ("WCC", "Wellness Centres & clinics"),
                    ("TO", "Transport operations (Rail, Bus, Cab)"),
                    ("OT", "Others"),
                ],
                default="OT",
                max_length=255,
            ),
        ),
    ]
