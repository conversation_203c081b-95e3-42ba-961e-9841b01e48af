# Generated by Django 4.2 on 2025-05-29 08:37

import base.storage_utils
from django.db import migrations, models
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("accounts", "0002_alter_brandadminuser_options_alter_tenant_options_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="OTP",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("restored_at", models.DateTimeField(blank=True, null=True)),
                ("transaction_id", models.UUIDField(blank=True, null=True)),
                ("external_id", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.Boolean<PERSON>ield(default=True)),
                ("email", models.EmailField(blank=True, max_length=255, null=True)),
                (
                    "phone_number",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                (
                    "source",
                    models.CharField(
                        choices=[("PARTNER_REGISTRATION", "Partner Registration")],
                        default="PARTNER_REGISTRATION",
                        max_length=255,
                    ),
                ),
                ("code", models.CharField(max_length=6)),
            ],
            options={
                "ordering": ["-created_at"],
                "abstract": False,
            },
        ),
        migrations.RenameField(
            model_name="tenant",
            old_name="name",
            new_name="entity_name",
        ),
        migrations.RemoveField(
            model_name="tenant",
            name="custom_domain",
        ),
        migrations.RemoveField(
            model_name="tenant",
            name="theme",
        ),
        migrations.AddField(
            model_name="tenant",
            name="company_registration_number",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name="tenant",
            name="entity_type",
            field=models.CharField(
                choices=[
                    ("TA", "Travel Agent"),
                    ("RP", "Retail Platform"),
                    ("IP", "Institutional Platform"),
                    ("BP", "Bank Platform"),
                    ("OT", "Others"),
                ],
                default="OT",
                max_length=255,
            ),
        ),
        migrations.AddField(
            model_name="tenant",
            name="monthly_transaction_volume",
            field=models.CharField(
                choices=[
                    ("<1L", "< 1 L"),
                    ("1-5L", "1-5L"),
                    ("5-20L", "5-20L"),
                    (">20L", "> 20L"),
                ],
                default="<1L",
                max_length=255,
            ),
        ),
        migrations.AddField(
            model_name="tenant",
            name="package_type",
            field=models.CharField(
                choices=[("ZP", "Zuumm"), ("OP", "Own")], default="ZP", max_length=255
            ),
        ),
        migrations.AddField(
            model_name="tenant",
            name="primary_theme",
            field=models.CharField(
                default="light", help_text="Brand primary theme color", max_length=50
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="tenant",
            name="referral_code",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name="tenant",
            name="secondary_theme",
            field=models.CharField(
                blank=True,
                help_text="Brand secondary theme color",
                max_length=50,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="tenant",
            name="update_notification_enabled",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="tenant",
            name="usage_preference",
            field=models.CharField(
                choices=[
                    ("OW", "Own Website Whitelabel"),
                    ("IA", "Integrate with Mobile App"),
                    ("IW", "Integrate with Existing Website"),
                    ("RL", "Referral Link"),
                ],
                default="OW",
                max_length=255,
            ),
        ),
        migrations.AddField(
            model_name="tenant",
            name="vanity_domain",
            field=models.CharField(blank=True, max_length=255, null=True, unique=True),
        ),
        migrations.AddField(
            model_name="user",
            name="is_primary_superadmin",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="user",
            name="phone_number_country_code",
            field=models.CharField(blank=True, default="+91", max_length=10, null=True),
        ),
        migrations.AlterField(
            model_name="tenant",
            name="brand_type",
            field=models.CharField(
                choices=[("ZUUMM", "Zuumm"), ("TENANT", "Tenant")],
                default="TENANT",
                max_length=25,
            ),
        ),
        migrations.AlterField(
            model_name="tenant",
            name="logo",
            field=models.FileField(
                default="logo-path",
                help_text="Brand logo image",
                upload_to=base.storage_utils.get_logo_path,
            ),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="tenant",
            name="subdomain",
            field=models.CharField(blank=True, max_length=255, null=True, unique=True),
        ),
    ]
