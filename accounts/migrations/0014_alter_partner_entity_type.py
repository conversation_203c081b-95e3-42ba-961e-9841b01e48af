# Generated by Django 4.2 on 2025-06-18 12:06

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("accounts", "0013_alter_partner_entity_type"),
    ]

    operations = [
        migrations.<PERSON>er<PERSON>ield(
            model_name="partner",
            name="entity_type",
            field=models.CharField(
                choices=[
                    ("TA", "Travel Agent/Tour Operators"),
                    ("BP", "Banks & Financial Institutions"),
                    ("RP", "Retail Chains and Loyalty Platforms"),
                    ("IC", "Insurance Companies"),
                    ("EI", "Educational Institutions"),
                    ("RSP", "Religious & Spiritual Organizations"),
                    ("HEBP", "HR/Employee Benefit Portal"),
                    ("ECP", "E-commerce Platforms"),
                    ("FG", "Freelancer & Gig-workers"),
                    ("SBL", "Small Businesses & Local Shops"),
                    ("WCC", "Wellness Centres & Clinics"),
                    ("TO", "Transport Operations (Rail, Bus, Cab)"),
                    ("OT", "Others"),
                ],
                default="OT",
                max_length=255,
            ),
        ),
    ]
