# Generated by Django 4.2 on 2025-05-21 07:18

import base.storage_utils
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("accounts", "0001_initial"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="brandadminuser",
            options={
                "verbose_name": "Partner Admin",
                "verbose_name_plural": "Partner Admins",
            },
        ),
        migrations.AlterModelOptions(
            name="tenant",
            options={
                "ordering": ["-created_at"],
                "verbose_name": "Partner",
                "verbose_name_plural": "Partners",
            },
        ),
        migrations.RemoveField(
            model_name="tenant",
            name="branding_settings",
        ),
        migrations.AddField(
            model_name="tenant",
            name="logo",
            field=models.FileField(
                blank=True,
                help_text="Brand logo image",
                null=True,
                upload_to=base.storage_utils.get_logo_path,
            ),
        ),
        migrations.AddField(
            model_name="tenant",
            name="theme",
            field=models.CharField(
                blank=True,
                help_text="Brand theme color (hex code or theme name)",
                max_length=50,
                null=True,
            ),
        ),
    ]
