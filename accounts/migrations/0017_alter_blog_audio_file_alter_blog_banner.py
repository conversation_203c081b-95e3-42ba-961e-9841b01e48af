# Generated by Django 4.2 on 2025-06-25 06:34

import base.storage_utils
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("accounts", "0016_blog_tag_writer_blogtag_blog_tags_blog_writer"),
    ]

    operations = [
        migrations.AlterField(
            model_name="blog",
            name="audio_file",
            field=models.FileField(
                blank=True,
                max_length=500,
                null=True,
                upload_to=base.storage_utils.get_blog_audio_file_path,
            ),
        ),
        migrations.AlterField(
            model_name="blog",
            name="banner",
            field=models.FileField(
                max_length=500, upload_to=base.storage_utils.get_blog_banner_image_path
            ),
        ),
    ]
