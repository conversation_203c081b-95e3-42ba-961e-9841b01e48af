# Generated by Django 4.2 on 2025-06-25 04:45

import base.storage_utils
import ckeditor.fields
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("accounts", "0015_alter_partner_logo"),
    ]

    operations = [
        migrations.CreateModel(
            name="Blog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("restored_at", models.DateTimeField(blank=True, null=True)),
                ("transaction_id", models.UUIDField(blank=True, null=True)),
                ("external_id", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.<PERSON>oleanField(default=True)),
                ("title", models.CharField(max_length=255)),
                ("content", ckeditor.fields.RichTextField()),
                ("slug", models.TextField()),
                (
                    "banner",
                    models.FileField(
                        upload_to=base.storage_utils.get_blog_banner_image_path
                    ),
                ),
                ("read_time", models.IntegerField(blank=True, null=True)),
                (
                    "type",
                    models.CharField(
                        choices=[("POST", "Post"), ("PODCAST", "Podcast")],
                        default="POST",
                        max_length=255,
                    ),
                ),
                ("published_at", models.DateTimeField(blank=True, null=True)),
                (
                    "audio_file",
                    models.FileField(
                        blank=True,
                        null=True,
                        upload_to=base.storage_utils.get_blog_audio_file_path,
                    ),
                ),
                ("seo_title", models.CharField(blank=True, max_length=255, null=True)),
                ("meta_description", models.TextField(blank=True, null=True)),
                ("meta_keywords", models.TextField(blank=True, null=True)),
                (
                    "canonical_url",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "featured_image_alt_text",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("robots_directive", models.TextField(blank=True, null=True)),
                (
                    "focus_keyword",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("meta_information", models.TextField(blank=True, null=True)),
            ],
            options={
                "verbose_name": "Blog",
                "verbose_name_plural": "Blogs",
            },
        ),
        migrations.CreateModel(
            name="Tag",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("restored_at", models.DateTimeField(blank=True, null=True)),
                ("transaction_id", models.UUIDField(blank=True, null=True)),
                ("external_id", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "name",
                    models.CharField(
                        choices=[
                            ("SMART_TRAVEL", "Smart Travel"),
                            ("DESTINATIONS", "Destinations"),
                            ("TRIP_TYPES", "Trip Types"),
                            ("TRAVEL_TOOLS", "Travel Tools"),
                            ("STAY_AND_EXPERIENCE", "Stay and Experience"),
                            ("TRAVEL_BYTES", "Travel Bytes"),
                            ("VOICES", "Voices"),
                        ],
                        max_length=255,
                        unique=True,
                    ),
                ),
                ("slug", models.TextField(blank=True, null=True)),
            ],
            options={
                "verbose_name": "Tag",
                "verbose_name_plural": "Tags",
            },
        ),
        migrations.CreateModel(
            name="Writer",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("restored_at", models.DateTimeField(blank=True, null=True)),
                ("transaction_id", models.UUIDField(blank=True, null=True)),
                ("external_id", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                ("name", models.CharField(max_length=255)),
                (
                    "profile_picture",
                    models.FileField(
                        upload_to=base.storage_utils.get_writer_profile_picture_path
                    ),
                ),
                ("bio", models.TextField()),
                ("email", models.EmailField(blank=True, max_length=255, null=True)),
            ],
            options={
                "verbose_name": "Writer",
                "verbose_name_plural": "Writers",
            },
        ),
        migrations.CreateModel(
            name="BlogTag",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("restored_at", models.DateTimeField(blank=True, null=True)),
                ("transaction_id", models.UUIDField(blank=True, null=True)),
                ("external_id", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "blog",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="blog_tags",
                        to="accounts.blog",
                    ),
                ),
                (
                    "tag",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="blog_tags",
                        to="accounts.tag",
                    ),
                ),
            ],
            options={
                "verbose_name": "Blog Tag",
                "verbose_name_plural": "Blog Tags",
                "unique_together": {("blog", "tag")},
            },
        ),
        migrations.AddField(
            model_name="blog",
            name="tags",
            field=models.ManyToManyField(
                blank=True,
                related_name="blogs",
                through="accounts.BlogTag",
                to="accounts.tag",
            ),
        ),
        migrations.AddField(
            model_name="blog",
            name="writer",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="blogs",
                to="accounts.writer",
            ),
        ),
    ]
