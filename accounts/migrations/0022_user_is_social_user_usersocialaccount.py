# Generated by Django 4.2 on 2025-07-15 06:48

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("accounts", "0021_blog_auto_generate_audio_file_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="user",
            name="is_social_user",
            field=models.BooleanField(default=False),
        ),
        migrations.CreateModel(
            name="UserSocialAccount",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("restored_at", models.DateTimeField(blank=True, null=True)),
                ("transaction_id", models.UUIDField(blank=True, null=True)),
                ("external_id", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "provider",
                    models.CharField(choices=[("GOOGLE", "Google")], max_length=10),
                ),
                ("token", models.CharField(max_length=255)),
                ("uid", models.CharField(db_index=True, max_length=255)),
                ("meta_data", models.JSONField(default=dict)),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="social_account",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "User Social Account",
                "verbose_name_plural": "User Social Accounts",
                "unique_together": {("provider", "uid")},
            },
        ),
    ]
