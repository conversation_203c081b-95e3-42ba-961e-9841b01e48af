# Generated by Django 4.2 on 2025-05-30 06:14

import base.storage_utils
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("accounts", "0003_otp_rename_name_tenant_entity_name_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="Partner",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("restored_at", models.DateTimeField(blank=True, null=True)),
                ("transaction_id", models.UUIDField(blank=True, null=True)),
                ("external_id", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                ("entity_name", models.CharField(max_length=255)),
                (
                    "entity_type",
                    models.CharField(
                        choices=[
                            ("TA", "Travel Agent"),
                            ("RP", "Retail Platform"),
                            ("IP", "Institutional Platform"),
                            ("BP", "Bank Platform"),
                            ("OT", "Others"),
                        ],
                        default="OT",
                        max_length=255,
                    ),
                ),
                (
                    "partner_type",
                    models.CharField(
                        choices=[("ZUUMM", "Zuumm"), ("PARTNER", "Partner")],
                        default="PARTNER",
                        max_length=25,
                    ),
                ),
                (
                    "usage_preference",
                    models.CharField(
                        choices=[
                            ("OW", "Own Website Whitelabel"),
                            ("IA", "Integrate with Mobile App"),
                            ("IW", "Integrate with Existing Website"),
                            ("RL", "Referral Link"),
                        ],
                        default="OW",
                        max_length=255,
                    ),
                ),
                (
                    "subdomain",
                    models.CharField(
                        blank=True, max_length=255, null=True, unique=True
                    ),
                ),
                (
                    "vanity_domain",
                    models.CharField(
                        blank=True, max_length=255, null=True, unique=True
                    ),
                ),
                (
                    "logo",
                    models.FileField(
                        help_text="Partner logo image",
                        upload_to=base.storage_utils.get_logo_path,
                    ),
                ),
                (
                    "primary_theme",
                    models.CharField(
                        help_text="Partner primary theme color", max_length=50
                    ),
                ),
                (
                    "secondary_theme",
                    models.CharField(
                        blank=True,
                        help_text="Partner secondary theme color",
                        max_length=50,
                        null=True,
                    ),
                ),
                ("gst_number", models.CharField(blank=True, max_length=15, null=True)),
                (
                    "company_registration_number",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "monthly_transaction_volume",
                    models.CharField(
                        choices=[
                            ("<1L", "< 1 L"),
                            ("1-5L", "1-5L"),
                            ("5-20L", "5-20L"),
                            (">20L", "> 20L"),
                        ],
                        default="<1L",
                        max_length=255,
                    ),
                ),
                (
                    "package_type",
                    models.CharField(
                        choices=[("ZP", "Zuumm"), ("OP", "Own")],
                        default="ZP",
                        max_length=255,
                    ),
                ),
                (
                    "referral_code",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("update_notification_enabled", models.BooleanField(default=False)),
                (
                    "address",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="accounts.address",
                    ),
                ),
            ],
            options={
                "verbose_name": "Partner",
                "verbose_name_plural": "Partners",
                "ordering": ["-created_at"],
            },
        ),
        migrations.RemoveField(
            model_name="tenant",
            name="address",
        ),
        migrations.DeleteModel(
            name="BrandAdminUser",
        ),
        migrations.CreateModel(
            name="PartnerAdminUser",
            fields=[],
            options={
                "verbose_name": "Partner Admin",
                "verbose_name_plural": "Partner Admins",
                "proxy": True,
                "indexes": [],
                "constraints": [],
            },
            bases=("accounts.user",),
        ),
        migrations.AlterField(
            model_name="user",
            name="user_type",
            field=models.CharField(
                choices=[
                    ("SA", "Super Admin"),
                    ("PA", "Partner Admin"),
                    ("PU", "Partner User"),
                    ("ZU", "Zuumm User"),
                ],
                default="ZU",
                max_length=20,
            ),
        ),
        migrations.AlterUniqueTogether(
            name="user",
            unique_together=set(),
        ),
        migrations.AddField(
            model_name="user",
            name="partner",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="users",
                to="accounts.partner",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="user",
            unique_together={("partner", "email", "user_type")},
        ),
        migrations.RemoveField(
            model_name="user",
            name="tenant",
        ),
    ]
