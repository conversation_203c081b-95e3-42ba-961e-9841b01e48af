# Generated by Django 4.2 on 2025-08-11 09:03

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("accounts", "0026_alter_partner_entity_type"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="partner",
            name="entity_type",
            field=models.CharField(
                choices=[
                    ("TA", "Travel Agent/Tour Operators"),
                    ("BP", "Banks & Financial Institutions"),
                    ("RP", "Retail Chains and Loyalty Platforms"),
                    ("IC", "Insurance Companies"),
                    ("EI", "Educational Institutions"),
                    ("RSP", "Religious & Spiritual Organizations"),
                    ("HEBP", "HR/Employee Benefit Portal"),
                    ("ECP", "E-commerce Platforms"),
                    ("FG", "Influencers/Freelancers/Gig-workers"),
                    ("SBL", "Small Businesses & Local Shops"),
                    ("WCC", "Wellness Centres & Clinics"),
                    ("TO", "Transport Operations (Rail, Bus, Cab)"),
                    ("AF", "Affiliate"),
                    ("OT", "Others"),
                ],
                default="OT",
                max_length=255,
            ),
        ),
    ]
