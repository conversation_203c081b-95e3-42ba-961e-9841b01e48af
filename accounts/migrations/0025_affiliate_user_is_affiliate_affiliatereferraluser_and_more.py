# Generated by Django 4.2 on 2025-08-01 07:26

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("accounts", "0024_alter_partner_logo"),
    ]

    operations = [
        migrations.CreateModel(
            name="Affiliate",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("restored_at", models.DateTimeField(blank=True, null=True)),
                ("transaction_id", models.UUIDField(blank=True, null=True)),
                ("external_id", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.Boolean<PERSON>ield(default=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("Pending", "Pending"),
                            ("Approved", "Approved"),
                            ("Rejected", "Rejected"),
                        ],
                        default="Pending",
                        max_length=255,
                    ),
                ),
                (
                    "affiliate_code",
                    models.UUIDField(blank=True, null=True, unique=True),
                ),
                ("requested_at", models.DateTimeField(blank=True, null=True)),
                ("approved_at", models.DateTimeField(blank=True, null=True)),
                ("rejected_at", models.DateTimeField(blank=True, null=True)),
                (
                    "rejection_reason",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("users_referred_count", models.PositiveIntegerField(default=0)),
            ],
            options={
                "verbose_name": "Affiliate User",
                "verbose_name_plural": "Affiliate Users",
            },
        ),
        migrations.AddField(
            model_name="user",
            name="is_affiliate",
            field=models.BooleanField(default=False),
        ),
        migrations.CreateModel(
            name="AffiliateReferralUser",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("restored_at", models.DateTimeField(blank=True, null=True)),
                ("transaction_id", models.UUIDField(blank=True, null=True)),
                ("external_id", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "affiliate",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="referred_users",
                        to="accounts.affiliate",
                    ),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="referred_user",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Affiliate Referral",
                "verbose_name_plural": "Affiliate Referrals",
            },
        ),
        migrations.AddField(
            model_name="affiliate",
            name="user",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="affiliate",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]
