# Generated by Django 4.2 on 2025-08-12 05:57

from django.db import migrations, models
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("accounts", "0027_alter_partner_entity_type"),
    ]

    operations = [
        migrations.CreateModel(
            name="GlobalDashboard",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("restored_at", models.DateTimeField(blank=True, null=True)),
                ("transaction_id", models.UUIDField(blank=True, null=True)),
                ("external_id", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("Hours Saved", "Hours Saved"),
                            ("Travelers Assisted", "Tarvellers Assisted"),
                            (
                                "Flights, Hotels & Packages Booked",
                                "Flights Hotels Packages Booked",
                            ),
                            ("Money Saved for Travelers", "Money Saved Of Travellers"),
                        ],
                        max_length=255,
                    ),
                ),
                ("value", models.CharField(max_length=255)),
            ],
            options={
                "verbose_name": "Global Dashboard",
                "verbose_name_plural": "Global Dashboards",
            },
        ),
    ]
