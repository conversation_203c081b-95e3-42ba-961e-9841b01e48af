# Generated by Django 4.2 on 2025-07-11 12:44

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("accounts", "0020_alter_blog_options"),
    ]

    operations = [
        migrations.Add<PERSON>ield(
            model_name="blog",
            name="auto_generate_audio_file",
            field=models.BooleanField(
                default=False,
                help_text="Whether audio file was auto-generated by OpenAI",
                verbose_name="Auto-generate Audio File",
            ),
        ),
        migrations.AddField(
            model_name="blog",
            name="auto_generate_content",
            field=models.<PERSON>oleanField(
                default=False,
                help_text="Whether content was auto-generated by OpenAI",
                verbose_name="Auto-generate Content",
            ),
        ),
        migrations.AddField(
            model_name="blog",
            name="auto_generate_slug",
            field=models.BooleanField(
                default=False,
                help_text="Whether slug was auto-generated by OpenAI",
                verbose_name="Auto-generate Slug",
            ),
        ),
    ]
