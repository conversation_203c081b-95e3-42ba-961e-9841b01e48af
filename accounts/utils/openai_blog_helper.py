import json
import requests
import logging
from typing import Dict, Any, Optional
from django.conf import settings
from django.core.exceptions import ValidationError
from django.core.files.base import ContentFile
from django.core.files.storage import default_storage
from django.utils.text import slugify
import re
import uuid
from io import BytesIO
import base64
from base.static import Constants

logger = logging.getLogger(__name__)


class OpenAIBlogHelper:
    """
    OOP-based helper for blog creation and enhancement using OpenAI API.
    Generates missing fields and creates audio files for podcasts based on auto-generation flags.
    """
    
    def __init__(self, blog_data: Dict[str, Any], blog_type: str = 'POST', auto_flags: Dict[str, bool] = None):
        """
        Initialize the OpenAI blog helper
        
        Args:
            blog_data: Dictionary containing blog field data
            blog_type: Type of blog ('POST' or 'PODCAST')
            auto_flags: Dictionary with auto-generation flags
        """
        print(f"[DEBUG] OpenAIBlogHelper initialized with blog_type: {blog_type}")
        print(f"[DEBUG] Blog data keys: {list(blog_data.keys())}")
        print(f"[DEBUG] Auto-generation flags: {auto_flags}")
        logger.info(f"OpenAIBlogHelper initialized for {blog_type} blog")
        
        self.blog_data = blog_data.copy()
        self.blog_type = blog_type
        self.auto_flags = auto_flags or {}
        self.api_key = settings.OPENAI_API_KEY
        self.api_url = settings.OPENAI_API_URL
        self.errors = []
        self.enhanced_data = {}
        
        print(f"[DEBUG] OpenAI helper initialized successfully")
        logger.info("OpenAI helper initialized for blog processing")

    def process(self) -> Dict[str, Any]:
        """
        Process the blog data and generate missing fields based on auto-generation flags
        
        Returns:
            Enhanced blog data with requested fields populated
        """
        print(f"[DEBUG] Starting blog processing with OpenAI")
        logger.info("Starting blog enhancement process")
        
        try:
            # Start with provided data
            self.enhanced_data = self.blog_data.copy()
            
            # Generate fields based on auto-generation flags
            if any(self.auto_flags.values()):
                self._generate_requested_fields()
            
            # Generate content image only if needed
            self._generate_content_image_if_needed()
            
            # Generate banner image if not provided
            self._generate_banner_image_if_needed()
            
            # Handle audio file generation for podcasts if requested
            if (self.blog_type == 'PODCAST' and 
                self.auto_flags.get('auto_generate_audio_file', False)):
                self._generate_audio_file()
            
            print(f"[DEBUG] Blog processing completed successfully")
            print(f"[DEBUG] Enhanced data keys: {list(self.enhanced_data.keys())}")
            logger.info("Blog enhancement completed successfully")
            
            return self.enhanced_data
            
        except Exception as e:
            error_msg = f"Blog processing failed: {str(e)}"
            print(f"[DEBUG] {error_msg}")
            self.errors.append(error_msg)
            logger.error(error_msg)
            raise ValidationError(error_msg)

    def _generate_requested_fields(self) -> None:
        """
        Generate only the fields that have been requested via auto-generation flags
        """
        print(f"[DEBUG] Generating requested fields based on flags")
        logger.info("Generating requested fields using OpenAI")
        
        try:
            # Create prompt for only requested fields
            prompt = self._create_selective_field_generation_prompt()
            print(f"[DEBUG] Created selective prompt for requested fields")
            
            # Call OpenAI API - this returns a string response
            response_text = self._call_openai_api(prompt)
            print(f"[DEBUG] OpenAI API call completed")
            
            # Parse the JSON response into a dictionary
            response_data = self._extract_json_from_response(response_text)
            print(f"[DEBUG] JSON parsing completed")
            
            # Handle OpenAI field name inconsistencies and update enhanced data
            if response_data:
                # Fix field name mapping - OpenAI sometimes returns 'body' instead of 'content'
                if 'body' in response_data and 'content' not in response_data:
                    response_data['content'] = response_data.pop('body')
                    print(f"[DEBUG] Mapped 'body' field to 'content'")
                
                # Only store valid fields that should be generated
                for field, value in response_data.items():
                    if value and self._should_generate_field(field):
                        # Ensure we're not storing the entire JSON structure as content
                        if field == 'content' and isinstance(value, str):
                            self.enhanced_data[field] = value
                            print(f"[DEBUG] Generated {field}: {value[:50]}...")
                        elif field != 'content':
                            self.enhanced_data[field] = value
                            print(f"[DEBUG] Generated {field}: {value[:50] if isinstance(value, str) else value}")
                        else:
                            print(f"[DEBUG] Skipped invalid content field (not a string): {type(value)}")
            
            # Merge generated data with existing blog data
            self._merge_generated_data(response_data)
            
            print(f"[DEBUG] Requested fields generation completed")
            logger.info("Requested fields generated successfully")
            
        except Exception as e:
            error_msg = f"Field generation failed: {str(e)}"
            print(f"[DEBUG] {error_msg}")
            logger.error(error_msg)
            self.errors.append(error_msg)

    def _should_generate_field(self, field: str) -> bool:
        """
        Check if a field should be generated based on auto-generation flags
        """
        field_flag_mapping = {
            'content': 'auto_generate_content',
            'slug': 'auto_generate_slug',
        }
        
        # If field has a specific flag, check it
        if field in field_flag_mapping:
            return self.auto_flags.get(field_flag_mapping[field], False)
        
        # For other fields, always generate if they're empty
        return True

    def _create_selective_field_generation_prompt(self) -> str:
        """
        Create prompt for generating only the requested fields
        """
        title = self.blog_data.get('title', '')
        content = self.blog_data.get('content', '')
        blog_type = self.blog_type
        
        # Determine which fields to request
        requested_fields = []
        if self.auto_flags.get('auto_generate_content', False):
            requested_fields.append('content')
        if self.auto_flags.get('auto_generate_slug', False):
            requested_fields.append('slug')
        
        # Always include these if they're empty - ENSURE seo_title is ALWAYS generated
        always_generate = ['read_time', 'seo_title', 'meta_description', 'meta_keywords', 
                          'canonical_url', 'featured_image_alt_text', 'robots_directive', 
                          'focus_keyword', 'meta_information']
        
        for field in always_generate:
            if not self.blog_data.get(field) or field == 'seo_title':  # Force seo_title generation
                requested_fields.append(field)
        
        # Ensure seo_title is ALWAYS in requested fields
        if 'seo_title' not in requested_fields:
            requested_fields.append('seo_title')
        
        prompt_text = f"""
You are an expert blog content creator and SEO specialist. Generate the following specific fields for this {blog_type.lower()} blog:

Title: {title}
{"Current Content: " + content[:1000] + "..." if content else "No content provided yet."}

Please generate ONLY the following requested fields as a JSON object:
{', '.join(requested_fields)}

IMPORTANT FIELD NAMING REQUIREMENTS:
- Use "content" (NOT "body") for the main blog content
- Use "slug" for the URL slug
- Use exact field names as requested above

CRITICAL CONTENT FORMATTING REQUIREMENTS:
- Generate content in RICH HTML format (NOT Markdown)
- Use proper HTML tags: <h2>, <h3>, <p>, <strong>, <em>, <ul>, <li>, etc.
- Make headings bold using <h2><strong>Heading Text</strong></h2>
- Structure content with multiple sections and subheadings
- Include {{{{CONTENT_IMAGE}}}} placeholder EXACTLY ONCE in the middle of content for image insertion
- Use <p> tags for paragraphs, <strong> for bold text, <em> for emphasis
- Create engaging, well-structured HTML content with visual hierarchy

Requirements:
1. Only include the requested fields in your response
2. If generating content, make it comprehensive, engaging, and SEO-optimized (at least 800 words)
3. Content must be in HTML format with proper tags and structure
4. Include {{{{CONTENT_IMAGE}}}} placeholder for image insertion
5. Make content relevant to the title and blog type
6. Ensure slug is URL-friendly (lowercase, hyphens, no special chars)
7. Meta description should be 150-160 characters
8. Focus on {blog_type.lower()}
9. read_time should be an integer representing minutes only (e.g., 8, not "8 minutes")
10. ALWAYS generate seo_title - create an SEO-optimized version of the title (max 60 chars)

ALWAYS include these additional fields for image generation:
- content_image_prompt: A descriptive prompt for generating a relevant content image
- banner_image_prompt: A descriptive prompt for generating a blog banner image

Example JSON structure with HTML content:
{{
  "content": "<h2><strong>Main Heading</strong></h2><p>Introduction paragraph with <strong>bold text</strong> and engaging content.</p><h3><strong>Section Heading</strong></h3><p>More detailed content here...</p>{{{{CONTENT_IMAGE}}}}<h3><strong>Another Section</strong></h3><p>Additional content with proper HTML formatting...</p>",
  "slug": "your-url-friendly-slug",
  "read_time": 8,
  "seo_title": "SEO-Optimized Title (Max 60 Chars)",
  "meta_description": "Brief description...",
  "content_image_prompt": "Descriptive image prompt...",
  "banner_image_prompt": "Banner description..."
}}

Generate the JSON response now:
"""

        return prompt_text

    def _generate_audio_file(self) -> None:
        """
        Generate audio file for podcast type blogs using OpenAI TTS
        """
        if self.blog_type != 'PODCAST':
            print(f"[DEBUG] Skipping audio generation - not a podcast")
            return
            
        print(f"[DEBUG] Starting audio file generation for podcast")
        logger.info("Generating audio file for podcast")
        
        try:
            # Check if audio file already exists
            if self.blog_data.get('audio_file'):
                print(f"[DEBUG] Audio file already exists, skipping generation")
                logger.info("Audio file already exists, skipping generation")
                return
            
            # Prepare content for TTS
            content_for_audio = self._prepare_content_for_tts()
            print(f"[DEBUG] Prepared content for TTS (length: {len(content_for_audio)} chars)")
            
            # Generate audio using OpenAI TTS
            audio_content = self._call_openai_tts(content_for_audio)
            print(f"[DEBUG] Generated audio content (size: {len(audio_content)} bytes)")
            
            # Upload to S3 and get URL
            audio_url = self._upload_audio_to_s3(audio_content)
            print(f"[DEBUG] Uploaded audio to S3: {audio_url}")
            
            # Store audio URL in enhanced data
            self.enhanced_data['audio_file_url'] = audio_url
            
            print(f"[DEBUG] Audio file generation completed successfully")
            logger.info("Audio file generated and uploaded successfully")
            
        except Exception as e:
            error_msg = f"Audio generation failed: {str(e)}"
            print(f"[DEBUG] {error_msg}")
            logger.error(error_msg)
            # Don't raise error for audio generation failure - it's optional
            self.errors.append(error_msg)

    def _prepare_content_for_tts(self) -> str:
        """
        Prepare blog content for text-to-speech conversion
        """
        print(f"[DEBUG] Preparing content for TTS")
        
        title = self.enhanced_data.get('title', '')
        content = self.enhanced_data.get('content', '')
        
        # Strip HTML tags from content
        clean_content = re.sub(r'<[^>]+>', '', content)
        clean_content = re.sub(r'\s+', ' ', clean_content).strip()
        
        # OpenAI TTS has a strict limit of 4096 characters
        # We need to leave room for the podcast intro/outro
        intro_outro_length = 200  # Approximate length of intro and outro
        max_content_length = 4096 - intro_outro_length  # 3896 characters for content
        
        if len(clean_content) > max_content_length:
            clean_content = clean_content[:max_content_length] + "..."
            print(f"[DEBUG] Content truncated to {max_content_length} characters for TTS (total limit: 4096)")
        
        # Create podcast script
        podcast_script = f"""Welcome to our podcast. Today's episode is titled: {title}.

{clean_content}

Thank you for listening to this episode. Don't forget to subscribe and share with your friends."""
        
        # Final check to ensure we're within limits
        if len(podcast_script) > 4096:
            # If still too long, trim the content more aggressively
            excess = len(podcast_script) - 4096
            content_to_trim = len(clean_content) - excess - 10  # Extra buffer
            clean_content = clean_content[:content_to_trim] + "..."
            
            podcast_script = f"""Welcome to our podcast. Today's episode is titled: {title}.

{clean_content}

Thank you for listening to this episode. Don't forget to subscribe and share with your friends."""
        
        print(f"[DEBUG] Podcast script prepared (length: {len(podcast_script)} chars, limit: 4096)")
        return podcast_script.strip()

    def _call_openai_tts(self, text: str) -> bytes:
        """
        Call OpenAI Text-to-Speech API using requests (consistent with existing pattern)
        """
        print(f"[DEBUG] Calling OpenAI TTS API with text length: {len(text)} characters")
        logger.info("Calling OpenAI TTS API")
        
        # Validate text length before sending
        if len(text) > 4096:
            error_msg = f"Text too long for TTS API: {len(text)} characters (limit: 4096)"
            print(f"[DEBUG] {error_msg}")
            logger.error(error_msg)
            raise ValidationError(error_msg)
        
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
        
        payload = {
            'model': 'tts-1',
            'voice': 'alloy',  # Professional voice
            'input': text,
            'response_format': 'mp3'
        }
        
        try:
            print(f"[DEBUG] Sending TTS request to OpenAI API")
            print(f"[DEBUG] Text preview: {text[:200]}...")
            response = requests.post(
                'https://api.openai.com/v1/audio/speech',  # Direct TTS endpoint
                headers=headers,
                json=payload,
                timeout=60
            )
            
            print(f"[DEBUG] TTS API response status: {response.status_code}")
            
            # Get detailed error information if request failed
            if response.status_code != 200:
                error_response = response.text
                print(f"[DEBUG] TTS API error response: {error_response}")
                logger.error(f"TTS API error response: {error_response}")
                
                try:
                    error_json = response.json()
                    error_message = error_json.get('error', {}).get('message', 'Unknown error')
                    error_type = error_json.get('error', {}).get('type', 'Unknown type')
                    print(f"[DEBUG] Parsed error - Type: {error_type}, Message: {error_message}")
                    raise ValidationError(f"OpenAI TTS API error ({error_type}): {error_message}")
                except:
                    # If we can't parse the JSON error, use the raw response
                    raise ValidationError(f"OpenAI TTS API error (HTTP {response.status_code}): {error_response}")
            
            response.raise_for_status()
            
            # TTS returns raw audio bytes
            audio_content = response.content
            print(f"[DEBUG] TTS API returned audio content (size: {len(audio_content)} bytes)")
            
            return audio_content
            
        except requests.exceptions.RequestException as e:
            error_msg = f"OpenAI TTS API request failed: {str(e)}"
            print(f"[DEBUG] {error_msg}")
            logger.error(error_msg)
            raise ValidationError(error_msg)
        except Exception as e:
            error_msg = f"OpenAI TTS API call failed: {str(e)}"
            print(f"[DEBUG] {error_msg}")
            logger.error(error_msg)
            raise ValidationError(error_msg)

    def _upload_audio_to_s3(self, audio_content: bytes) -> str:
        """
        Upload audio content to S3 and return URL
        """
        print(f"[DEBUG] Uploading audio to S3")
        logger.info("Uploading audio file to S3")
        
        try:
            # Generate unique filename
            filename = f"blog/audio/podcast-{uuid.uuid4()}.mp3"
            print(f"[DEBUG] Generated filename: {filename}")
            
            # Upload to S3 using default storage
            audio_file = ContentFile(audio_content, name=filename)
            saved_path = default_storage.save(filename, audio_file)
            
            # Get public URL
            audio_url = default_storage.url(saved_path)
            print(f"[DEBUG] Audio uploaded successfully to: {audio_url}")
            
            return audio_url
            
        except Exception as e:
            error_msg = f"S3 upload failed: {str(e)}"
            print(f"[DEBUG] {error_msg}")
            logger.error(error_msg)
            raise ValidationError(error_msg)

    def _call_openai_api(self, prompt: str) -> str:
        """
        Make API call to OpenAI using the standard chat completions endpoint for better reliability
        """
        print(f"[DEBUG] Calling OpenAI API using chat completions endpoint")
        logger.info("Making OpenAI API call for field generation")
        
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
        
        # Use chat completions format for better JSON generation reliability
        payload = {
            'model': 'gpt-4o-mini',  # Use standard OpenAI model for complex JSON generation
            'messages': [
                {
                    'role': 'system',
                    'content': 'You are an AI blog content specialist. Always respond with valid JSON format as requested.'
                },
                {
                    'role': 'user', 
                    'content': prompt
                }
            ],
            'temperature': 0.3,  # Lower temperature for more consistent JSON output
            'max_tokens': 3000   # Enough tokens for comprehensive content and all fields
        }
        
        try:
            print(f"[DEBUG] Sending request to OpenAI chat completions endpoint")
            # Use standard chat completions endpoint instead of custom responses endpoint
            response = requests.post(
                'https://api.openai.com/v1/chat/completions',
                headers=headers,
                json=payload,
                timeout=60
            )
            
            print(f"[DEBUG] OpenAI API response status: {response.status_code}")
            response.raise_for_status()
            
            response_data = response.json()
            
            # Handle standard chat completions response structure
            content = None
            
            try:
                # Standard chat completions structure
                if 'choices' in response_data and response_data['choices']:
                    choice = response_data['choices'][0]
                    if 'message' in choice and 'content' in choice['message']:
                        content = choice['message']['content']
                        logger.info("Successfully extracted content from chat completions response")
                
                # If still no content found, log the structure and raise error
                if not content:
                    logger.error(f"Could not extract content from OpenAI chat completions response structure: {list(response_data.keys())}")
                    logger.error(f"Full response data: {response_data}")
                    raise ValidationError(f"OpenAI API returned unexpected response structure. Available keys: {list(response_data.keys())}")
                    
            except (KeyError, IndexError, TypeError) as e:
                logger.error(f"Error parsing OpenAI chat completions response structure: {str(e)}")
                logger.error(f"Response data: {response_data}")
                raise ValidationError(f"Failed to parse OpenAI response structure: {str(e)}")
            
            # Ensure content is a string
            if content is None:
                content = ""
            else:
                content = str(content)
            
            print(f"[DEBUG] OpenAI API response received (length: {len(content)} chars)")
            print(f"[DEBUG] Response preview: {content[:200]}...")
            return content.strip()
            
        except requests.exceptions.RequestException as e:
            error_msg = f"OpenAI API request failed: {str(e)}"
            print(f"[DEBUG] {error_msg}")
            logger.error(error_msg)
            raise ValidationError(error_msg)
        except Exception as e:
            error_msg = f"OpenAI API call failed: {str(e)}"
            print(f"[DEBUG] {error_msg}")
            logger.error(error_msg)
            raise ValidationError(error_msg)

    def _extract_json_from_response(self, response_text: str) -> Dict[str, Any]:
        """
        Extract JSON from OpenAI response - same logic as package creation
        """
        print(f"[DEBUG] Extracting JSON from OpenAI response")
        
        try:
            # Clean the response text
            cleaned_response = response_text.strip()
            
            # Remove markdown code blocks if present
            if cleaned_response.startswith('```json'):
                cleaned_response = cleaned_response[7:]
            if cleaned_response.startswith('```'):
                cleaned_response = cleaned_response[3:]
            if cleaned_response.endswith('```'):
                cleaned_response = cleaned_response[:-3]
            
            cleaned_response = cleaned_response.strip()
            
            # Try to parse the entire response as JSON first
            try:
                json_data = json.loads(cleaned_response)
                
                # Validate that we have a proper dictionary structure
                if not isinstance(json_data, dict):
                    raise ValidationError(f"OpenAI response is not a JSON object, got: {type(json_data)}")
                
                # Additional validation: check for suspicious content
                if 'content' in json_data:
                    content_value = json_data['content']
                    # Ensure content is a string and not a nested structure
                    if not isinstance(content_value, str):
                        print(f"[DEBUG] Warning: Content field is not a string: {type(content_value)}")
                        # Try to extract string content if it's a dict with 'content' or 'body'
                        if isinstance(content_value, dict):
                            if 'body' in content_value:
                                json_data['content'] = str(content_value['body'])
                                print(f"[DEBUG] Extracted content from nested 'body' field")
                            elif 'content' in content_value:
                                json_data['content'] = str(content_value['content'])
                                print(f"[DEBUG] Extracted content from nested 'content' field")
                            else:
                                # Convert the entire dict to string as last resort
                                json_data['content'] = str(content_value)
                                print(f"[DEBUG] Converted non-string content to string")
                    
                    # Check if content looks like it might be the entire JSON response
                    if isinstance(json_data['content'], str) and ('{' in json_data['content'] and '}' in json_data['content']):
                        try:
                            # If content parses as JSON, it's probably the wrong field
                            json.loads(json_data['content'])
                            print(f"[DEBUG] Warning: Content field appears to contain JSON data")
                            # Don't use this content field
                            del json_data['content']
                        except json.JSONDecodeError:
                            # Content is a string with braces but not valid JSON - that's fine
                            pass
                
                print(f"[DEBUG] Successfully extracted JSON with keys: {list(json_data.keys())}")
                return json_data
            except json.JSONDecodeError:
                pass
            
            # Look for JSON within curly braces
            if '{' in cleaned_response and '}' in cleaned_response:
                start = cleaned_response.find('{')
                end = cleaned_response.rfind('}') + 1
                json_text = cleaned_response[start:end]
                json_data = json.loads(json_text)
                
                # Apply the same validation as above
                if not isinstance(json_data, dict):
                    raise ValidationError(f"Extracted JSON is not a JSON object, got: {type(json_data)}")
                
                print(f"[DEBUG] Successfully extracted JSON with keys: {list(json_data.keys())}")
                return json_data
            
            # If all else fails, raise an error
            raise ValidationError("No valid JSON found in OpenAI response")
            
        except json.JSONDecodeError as e:
            error_msg = f"Failed to parse JSON from OpenAI response: {str(e)}"
            print(f"[DEBUG] {error_msg}")
            print(f"[DEBUG] Raw response: {response_text[:500]}...")
            logger.error(error_msg)
            raise ValidationError(error_msg)

    def _merge_generated_data(self, generated_data: Dict[str, Any]) -> None:
        """
        Merge generated data with existing blog data using simplified logic:
        - Only use generated content if original was less than minimum word count
        - Always use generated SEO fields if missing
        - Preserve existing data otherwise
        """
        print(f"[DEBUG] Merging generated data with simplified logic")
        
        # Check if content was meant to be generated
        existing_content = self.enhanced_data.get('content', '')
        existing_word_count = self._count_words_in_html(existing_content)
        should_use_generated_content = existing_word_count < Constants.BLOG_MIN_LENGTH
        
        # Handle content - only use generated if original was too short
        generated_content = generated_data.get('content', '')
        if should_use_generated_content and generated_content:
            # Ensure content is in HTML format
            html_content = self._ensure_html_format(generated_content)
            self.enhanced_data['content'] = html_content
            print(f"[DEBUG] Used generated content: {existing_word_count} words -> {self._count_words_in_html(html_content)} words")
        elif existing_content:
            print(f"[DEBUG] Preserved existing content: {existing_word_count} words (sufficient)")
        else:
            print(f"[DEBUG] No content available")
        
        # Handle slug - always use generated to ensure proper formatting
        generated_slug = generated_data.get('slug')
        if generated_slug:
            self.enhanced_data['slug'] = generated_slug
            print(f"[DEBUG] Set slug: {generated_slug}")
        
        # FORCE seo_title generation - always use generated seo_title if available
        generated_seo_title = generated_data.get('seo_title')
        if generated_seo_title:
            self.enhanced_data['seo_title'] = generated_seo_title
            print(f"[DEBUG] FORCED seo_title generation: {generated_seo_title}")
        elif not self.enhanced_data.get('seo_title'):
            # Fallback: use title as seo_title if both are missing
            title = self.enhanced_data.get('title', '')
            if title:
                fallback_seo_title = title[:60] if len(title) > 60 else title
                self.enhanced_data['seo_title'] = fallback_seo_title
                print(f"[DEBUG] Fallback seo_title from title: {fallback_seo_title}")
        
        # SEO fields - only set if missing or empty (except seo_title which is forced above)
        seo_fields = [
            'read_time', 'meta_description', 'meta_keywords',
            'canonical_url', 'featured_image_alt_text', 'robots_directive', 'focus_keyword',
            'meta_information'
        ]
        
        for field in seo_fields:
            existing_value = self.enhanced_data.get(field)
            generated_value = generated_data.get(field)
            
            # Only use generated value if existing is missing or empty
            if not existing_value and generated_value:
                self.enhanced_data[field] = generated_value
                print(f"[DEBUG] Set {field}: {generated_value}")
            elif existing_value:
                print(f"[DEBUG] Kept existing {field}: {existing_value}")
        
        # Store content image prompt for later use (don't merge into final data)
        if generated_data.get('content_image_prompt'):
            self.enhanced_data['content_image_prompt'] = generated_data['content_image_prompt']
            print(f"[DEBUG] Stored content image prompt: {generated_data['content_image_prompt']}")
        
        # Store banner image prompt for later use (don't merge into final data)
        if generated_data.get('banner_image_prompt'):
            self.enhanced_data['banner_image_prompt'] = generated_data['banner_image_prompt']
            print(f"[DEBUG] Stored banner image prompt: {generated_data['banner_image_prompt']}")
        
        print(f"[DEBUG] Simplified data merging completed")

    def _ensure_html_format(self, content: str) -> str:
        """
        Ensure content is in HTML format. Convert markdown to HTML if needed.
        """
        if not content:
            return content
        
        print(f"[DEBUG] Ensuring content is in HTML format")
        
        # Check if content looks like markdown (has # headers)
        if re.search(r'^#+\s', content, re.MULTILINE):
            print(f"[DEBUG] Content appears to be in markdown format, converting to HTML")
            # Convert markdown headers to HTML
            content = re.sub(r'^### (.+)$', r'<h3><strong>\1</strong></h3>', content, flags=re.MULTILINE)
            content = re.sub(r'^## (.+)$', r'<h2><strong>\1</strong></h2>', content, flags=re.MULTILINE)
            content = re.sub(r'^# (.+)$', r'<h1><strong>\1</strong></h1>', content, flags=re.MULTILINE)
            
            # Convert markdown bold/italic to HTML
            content = re.sub(r'\*\*(.+?)\*\*', r'<strong>\1</strong>', content)
            content = re.sub(r'\*(.+?)\*', r'<em>\1</em>', content)
            
            # Convert line breaks to paragraphs
            paragraphs = content.split('\n\n')
            html_paragraphs = []
            
            for paragraph in paragraphs:
                paragraph = paragraph.strip()
                if paragraph:
                    # Skip if already has HTML tags
                    if not re.search(r'<[^>]+>', paragraph):
                        paragraph = f'<p>{paragraph}</p>'
                    html_paragraphs.append(paragraph)
            
            content = '\n\n'.join(html_paragraphs)
            print(f"[DEBUG] Converted markdown to HTML format")
        
        # Ensure {{CONTENT_IMAGE}} placeholder is present if not already there
        if '{{CONTENT_IMAGE}}' not in content and not re.search(r'<img[^>]*>', content):
            # Insert placeholder after first paragraph or heading
            if '<p>' in content:
                first_p_end = content.find('</p>')
                if first_p_end != -1:
                    insertion_point = first_p_end + 4
                    content = content[:insertion_point] + '\n\n{{CONTENT_IMAGE}}\n\n' + content[insertion_point:]
                    print(f"[DEBUG] Added {{CONTENT_IMAGE}} placeholder after first paragraph")
            elif '<h2>' in content:
                first_h2_end = content.find('</h2>')
                if first_h2_end != -1:
                    insertion_point = first_h2_end + 5
                    content = content[:insertion_point] + '\n\n{{CONTENT_IMAGE}}\n\n' + content[insertion_point:]
                    print(f"[DEBUG] Added {{CONTENT_IMAGE}} placeholder after first heading")
        
        return content

    def get_errors(self) -> list:
        """Get list of errors encountered during processing"""
        return self.errors

    def has_errors(self) -> bool:
        """Check if any errors occurred during processing"""
        return len(self.errors) > 0

    def _count_words_in_html(self, html_content: str) -> int:
        """
        Count words in HTML content by stripping HTML tags
        
        Args:
            html_content: HTML string to count words from
            
        Returns:
            Number of words in the content
        """
        if not html_content:
            return 0
        
        # Strip HTML tags from content
        clean_content = re.sub(r'<[^>]+>', '', html_content)
        # Remove extra whitespace
        clean_content = re.sub(r'\s+', ' ', clean_content).strip()
        
        if not clean_content:
            return 0
        
        # Count words by splitting on whitespace
        words = clean_content.split()
        word_count = len(words)
        
        print(f"[DEBUG] Word count in content: {word_count}")
        return word_count

    def _generate_content_image_if_needed(self) -> None:
        """
        Generate content image only if no images exist in the content and blog type is POST
        """
        print(f"[DEBUG] Checking if content image generation is needed")
        
        try:
            # Check blog type first - only generate images for POST blogs
            if self.blog_type != 'POST':
                print(f"[DEBUG] Skipping image generation - blog type is {self.blog_type}, not POST")
                logger.info(f"Skipping image generation for {self.blog_type} blog type")
                return
            
            content = self.enhanced_data.get('content', '')
            
            # Check if we have an image prompt to generate
            image_prompt = self.enhanced_data.get('content_image_prompt', '')
            if not image_prompt:
                print(f"[DEBUG] No image prompt provided, skipping image generation")
                return
            
            print(f"[DEBUG] Content image prompt found: {image_prompt}")
            
            # Check if content already has real images (but allow {{CONTENT_IMAGE}} placeholder)
            if self._content_has_images(content):
                print(f"[DEBUG] Content already has real images, skipping image generation")
                logger.info("Content already has real images, skipping image generation")
                return
            
            # Generate image if:
            # 1. Content has {{CONTENT_IMAGE}} placeholder, OR
            # 2. Content has no images at all
            has_placeholder = '{{CONTENT_IMAGE}}' in content
            print(f"[DEBUG] Content has {{{{CONTENT_IMAGE}}}} placeholder: {has_placeholder}")
            
            if has_placeholder or not self._content_has_any_images(content):
                print(f"[DEBUG] Proceeding with image generation for POST blog")
                self._generate_content_image()
            else:
                print(f"[DEBUG] Content has images but no placeholder - skipping generation")
            
        except Exception as e:
            error_msg = f"Content image check failed: {str(e)}"
            print(f"[DEBUG] {error_msg}")
            logger.error(error_msg)
            # Don't raise error - this is just a check
            self.errors.append(error_msg)

    def _content_has_images(self, content: str) -> bool:
        """
        Check if content already contains real images (excluding {{CONTENT_IMAGE}} placeholder)
        The {{CONTENT_IMAGE}} placeholder should trigger image generation, not prevent it
        """
        print(f"[DEBUG] Checking if content has existing real images")
        
        if not content:
            print(f"[DEBUG] No content provided, assuming no images")
            return False
        
        # If content has {{CONTENT_IMAGE}} placeholder, we should generate an image
        if '{{CONTENT_IMAGE}}' in content:
            print(f"[DEBUG] Found {{CONTENT_IMAGE}} placeholder - need to generate image to replace it")
            return False  # Return False so image generation proceeds
        
        # Check for various real image patterns
        image_patterns = [
            r'<img[^>]*src[^>]*>',  # HTML img tags
            r'<div[^>]*class[^>]*blog-content-image[^>]*>',  # Our generated image divs
            r'!\[.*?\]\(.*?\)',  # Markdown images
            r'<figure[^>]*>.*?<img[^>]*>.*?</figure>',  # Figure with images
        ]
        
        for pattern in image_patterns:
            if re.search(pattern, content, re.IGNORECASE | re.DOTALL):
                print(f"[DEBUG] Found existing real image using pattern: {pattern}")
                return True
        
        print(f"[DEBUG] No existing real images found in content")
        return False

    def _content_has_any_images(self, content: str) -> bool:
        """
        Check if content already contains any images, regardless of placeholder.
        This is a helper to determine if image generation is truly needed.
        """
        print(f"[DEBUG] Checking if content has any real images")
        
        if not content:
            print(f"[DEBUG] No content provided, assuming no images")
            return False
        
        # Check for {{CONTENT_IMAGE}} placeholder first
        if '{{CONTENT_IMAGE}}' in content:
            print(f"[DEBUG] Found {{CONTENT_IMAGE}} placeholder - need to generate image to replace it")
            return False # Return False to indicate image generation is needed
        
        # Check for various real image patterns
        image_patterns = [
            r'<img[^>]*src[^>]*>',  # HTML img tags
            r'<div[^>]*class[^>]*blog-content-image[^>]*>',  # Our generated image divs
            r'!\[.*?\]\(.*?\)',  # Markdown images
            r'<figure[^>]*>.*?<img[^>]*>.*?</figure>',  # Figure with images
        ]
        
        for pattern in image_patterns:
            if re.search(pattern, content, re.IGNORECASE | re.DOTALL):
                print(f"[DEBUG] Found existing real image using pattern: {pattern}")
                return True
        
        print(f"[DEBUG] No existing real images found in content")
        return False

    def _generate_content_image(self) -> None:
        """
        Generate content image using Unsplash API and upload to S3
        """
        print(f"[DEBUG] Starting content image generation")
        logger.info("Generating content image using Unsplash API")
        
        try:
            image_prompt = self.enhanced_data.get('content_image_prompt', '')
            if not image_prompt:
                print(f"[DEBUG] No image prompt provided, skipping image generation")
                return
            
            print(f"[DEBUG] Using Unsplash prompt: {image_prompt}")
            
            # Generate image using Unsplash API (preferred over DALL-E for realistic photos)
            image_url = self._call_unsplash_api(image_prompt)
            print(f"[DEBUG] Unsplash generated image URL: {image_url}")
            
            # Download and upload to S3
            s3_image_url = self._download_and_upload_image(image_url)
            print(f"[DEBUG] Image uploaded to S3: {s3_image_url}")
            
            # Replace placeholder in content with actual image
            self._embed_image_in_content(s3_image_url)
            
            print(f"[DEBUG] Content image generation completed successfully")
            logger.info("Content image generated and embedded successfully")
            
        except Exception as e:
            error_msg = f"Content image generation failed: {str(e)}"
            print(f"[DEBUG] {error_msg}")
            logger.error(error_msg)
            # If Unsplash fails, try DALL-E as fallback
            try:
                print(f"[DEBUG] Unsplash failed, trying DALL-E as fallback")
                image_url = self._call_openai_dalle(image_prompt)
                print(f"[DEBUG] DALL-E fallback generated image URL: {image_url}")
                
                # Download and upload to S3
                s3_image_url = self._download_and_upload_image(image_url)
                print(f"[DEBUG] Fallback image uploaded to S3: {s3_image_url}")
                
                # Replace placeholder in content with actual image
                self._embed_image_in_content(s3_image_url)
                
                print(f"[DEBUG] Content image generation completed with DALL-E fallback")
                logger.info("Content image generated with DALL-E fallback")
                
            except Exception as fallback_error:
                error_msg = f"Both Unsplash and DALL-E image generation failed: {str(fallback_error)}"
                print(f"[DEBUG] {error_msg}")
                logger.error(error_msg)
                # Don't raise error for image generation failure - it's optional
                self.errors.append(error_msg)

    def _call_unsplash_api(self, prompt: str) -> str:
        """
        Call Unsplash API to fetch authentic, realistic photos
        """
        print(f"[DEBUG] Calling Unsplash API for authentic photography")
        logger.info("Calling Unsplash API for realistic photography")
        
        # Check if Unsplash API key is available
        if not hasattr(settings, 'UNSPLASH_ACCESS_KEY') or not settings.UNSPLASH_ACCESS_KEY:
            raise ValidationError("Unsplash API key not configured in settings")
        
        headers = {
            'Authorization': f'Client-ID {settings.UNSPLASH_ACCESS_KEY}'
        }
        
        # Clean prompt for better search results
        search_query = self._optimize_search_query(prompt)
        print(f"[DEBUG] Original prompt: {prompt}")
        print(f"[DEBUG] Optimized search query: {search_query}")
        
        params = {
            'query': search_query,
            'per_page': 3,  # Get more options to choose from
            'orientation': 'landscape',
            'content_filter': 'high'  # Get high quality images
        }
        
        try:
            print(f"[DEBUG] Sending Unsplash API request with query: '{search_query}'")
            response = requests.get(
                'https://api.unsplash.com/search/photos',
                headers=headers,
                params=params,
                timeout=30
            )
            
            print(f"[DEBUG] Unsplash API response status: {response.status_code}")
            
            if response.status_code != 200:
                error_response = response.text
                print(f"[DEBUG] Unsplash API error response: {error_response}")
                logger.error(f"Unsplash API error response: {error_response}")
                raise ValidationError(f"Unsplash API error (HTTP {response.status_code}): {error_response}")
            
            response.raise_for_status()
            response_data = response.json()
            
            print(f"[DEBUG] Unsplash API response keys: {list(response_data.keys())}")
            
            if 'results' not in response_data:
                raise ValidationError(f"Unsplash API returned unexpected response structure: {list(response_data.keys())}")
                
            if not response_data['results']:
                # Try with a more generic search term
                print(f"[DEBUG] No results for '{search_query}', trying fallback search")
                fallback_params = params.copy()
                fallback_params['query'] = 'travel landscape'
                
                fallback_response = requests.get(
                    'https://api.unsplash.com/search/photos',
                    headers=headers,
                    params=fallback_params,
                    timeout=30
                )
                
                if fallback_response.status_code == 200:
                    fallback_data = fallback_response.json()
                    if fallback_data.get('results'):
                        response_data = fallback_data
                        print(f"[DEBUG] Fallback search returned {len(response_data['results'])} results")
                    else:
                        raise ValidationError("Unsplash API returned no images even with fallback search")
                else:
                    raise ValidationError("Unsplash API returned no images")
            
            # Get the best quality image from results
            total_results = response_data.get('total', 0)
            results_count = len(response_data['results'])
            print(f"[DEBUG] Unsplash search returned {results_count} results (total available: {total_results})")
            
            # Choose the first high-quality result
            selected_image = response_data['results'][0]
            image_url = selected_image['urls']['regular']  # Use 'regular' size for good quality
            
            # Log image details for debugging
            image_id = selected_image.get('id', 'unknown')
            image_description = selected_image.get('description', 'No description')
            photographer = selected_image.get('user', {}).get('name', 'Unknown')
            
            print(f"[DEBUG] Selected image ID: {image_id}")
            print(f"[DEBUG] Image description: {image_description}")
            print(f"[DEBUG] Photographer: {photographer}")
            print(f"[DEBUG] Final image URL: {image_url}")
            
            logger.info(f"Unsplash image selected: {image_id} by {photographer}")
            return image_url
            
        except requests.exceptions.RequestException as e:
            error_msg = f"Unsplash API request failed: {str(e)}"
            print(f"[DEBUG] {error_msg}")
            logger.error(error_msg)
            raise ValidationError(error_msg)
        except Exception as e:
            error_msg = f"Unsplash API call failed: {str(e)}"
            print(f"[DEBUG] {error_msg}")
            logger.error(error_msg)
            raise ValidationError(error_msg)

    def _optimize_search_query(self, original_prompt: str) -> str:
        """
        Optimize the search query for better Unsplash results
        """
        # Clean up the prompt to extract key search terms
        prompt = original_prompt.lower().strip()
        
        # Remove common AI generation terms that don't help with search
        remove_terms = [
            'real photograph', 'authentic', 'candid photo', 'natural unposed',
            'taken with camera', 'genuine moment', 'natural lighting',
            'real life scene', 'documentary style', 'no artistic effects',
            'raw unfiltered', 'realistic everyday', 'smartphone', 'dslr camera'
        ]
        
        for term in remove_terms:
            prompt = prompt.replace(term, '').strip()
        
        # Clean up multiple spaces and commas
        prompt = ' '.join(prompt.split())
        prompt = prompt.replace(',', ' ').strip()
        
        # Take first few meaningful words (Unsplash works better with simple queries)
        words = prompt.split()[:3]
        search_query = ' '.join(words)
        
        return search_query if search_query else 'travel'

    def _call_openai_dalle(self, prompt: str) -> str:
        """
        Call OpenAI DALL-E API to generate authentic, realistic photos that look like real photography
        """
        print(f"[DEBUG] Calling OpenAI DALL-E API for authentic photography")
        logger.info("Calling OpenAI DALL-E API for realistic photography")
        
        # Enhance prompt for realistic, authentic photography
        enhanced_prompt = self._enhance_prompt_for_realism(prompt)
        print(f"[DEBUG] Original prompt: {prompt}")
        print(f"[DEBUG] Enhanced prompt: {enhanced_prompt}")
        
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
        
        # Updated payload for more realistic, authentic photography
        payload = {
            'model': 'dall-e-3',
            'prompt': enhanced_prompt,
            'size': '1024x1024',
            'quality': 'hd',  # Use HD quality for more realistic details
            'n': 1,
            'style': 'natural'  # Natural style produces more realistic, less artistic images
        }
        
        try:
            print(f"[DEBUG] Sending DALL-E request for authentic photography")
            response = requests.post(
                'https://api.openai.com/v1/images/generations',
                headers=headers,
                json=payload,
                timeout=60
            )
            
            print(f"[DEBUG] DALL-E API response status: {response.status_code}")
            response.raise_for_status()
            
            response_data = response.json()
            
            if 'data' not in response_data or not response_data['data']:
                raise ValidationError("DALL-E API returned empty response")
            
            image_url = response_data['data'][0]['url']
            print(f"[DEBUG] DALL-E generated authentic photo URL: {image_url}")
            
            return image_url
            
        except requests.exceptions.RequestException as e:
            error_msg = f"OpenAI DALL-E API request failed: {str(e)}"
            print(f"[DEBUG] {error_msg}")
            logger.error(error_msg)
            raise ValidationError(error_msg)
        except Exception as e:
            error_msg = f"OpenAI DALL-E API call failed: {str(e)}"
            print(f"[DEBUG] {error_msg}")
            logger.error(error_msg)
            raise ValidationError(error_msg)

    def _enhance_prompt_for_realism(self, original_prompt: str) -> str:
        """
        Enhance the DALL-E prompt to generate authentic, realistic photos that look like they were taken by a real person
        """
        print(f"[DEBUG] Enhancing prompt for authentic, realistic photo generation")
        
        # Clean up the original prompt
        prompt = original_prompt.lower().strip()
        
        # Remove any existing style modifiers that might make it artistic/painted
        artistic_terms = [
            'cartoon', 'animated', 'illustration', 'drawing', 'sketch', 
            'comic', 'anime', 'manga', 'digital art', 'artwork', 'painting',
            'artistic', 'stylized', 'rendered', 'concept art', 'fantasy art',
            'abstract', 'watercolor', 'oil painting', 'acrylic', 'brush strokes'
        ]
        
        for term in artistic_terms:
            prompt = prompt.replace(term, '').strip()
        
        # Build enhanced prompt focusing on authentic, candid photography
        # Use terms that specifically request real photography, not generated art
        enhanced_prompt = f"""Real photograph taken with a camera, {prompt.strip()}, authentic candid photo, natural unposed shot, real world photography, taken by smartphone or DSLR camera, genuine moment captured, natural lighting conditions, real life scene, documentary style photography, no artistic effects, raw unfiltered photo, realistic everyday scene"""
        
        # Ensure prompt doesn't exceed DALL-E length limits
        if len(enhanced_prompt) > 400:
            # Prioritize the most important realism terms
            enhanced_prompt = f"Real photograph, {prompt.strip()}, authentic candid photo, natural unposed shot, taken with camera, genuine moment, natural lighting, real life scene"
            if len(enhanced_prompt) > 400:
                enhanced_prompt = enhanced_prompt[:397] + "..."
        
        print(f"[DEBUG] Enhanced prompt for authentic photography: {enhanced_prompt}")
        print(f"[DEBUG] Enhanced prompt length: {len(enhanced_prompt)} characters")
        return enhanced_prompt

    def _download_and_upload_image(self, image_url: str) -> str:
        """
        Download image from OpenAI and upload to S3
        """
        print(f"[DEBUG] Downloading and uploading image to S3")
        logger.info("Downloading and uploading generated image to S3")
        
        try:
            # Download image from OpenAI URL
            print(f"[DEBUG] Downloading image from: {image_url}")
            response = requests.get(image_url, timeout=30)
            response.raise_for_status()
            
            image_content = response.content
            print(f"[DEBUG] Downloaded image (size: {len(image_content)} bytes)")
            
            # Generate unique filename
            filename = f"blog/content-images/blog-image-{uuid.uuid4()}.png"
            print(f"[DEBUG] Generated filename: {filename}")
            
            # Upload to S3 using default storage
            image_file = ContentFile(image_content, name=filename)
            saved_path = default_storage.save(filename, image_file)
            
            # Get public URL
            s3_image_url = default_storage.url(saved_path)
            print(f"[DEBUG] Image uploaded successfully to: {s3_image_url}")
            
            return s3_image_url
            
        except Exception as e:
            error_msg = f"Image download/upload failed: {str(e)}"
            print(f"[DEBUG] {error_msg}")
            logger.error(error_msg)
            raise ValidationError(error_msg)

    def _embed_image_in_content(self, image_url: str) -> None:
        """
        Replace image placeholder in content with actual image HTML, or insert strategically if no placeholder
        """
        print(f"[DEBUG] Embedding image in content")
        print(f"[DEBUG] Image URL: {image_url}")
        
        try:
            content = self.enhanced_data.get('content', '')
            title = self.enhanced_data.get('title', 'Blog Image')
            
            print(f"[DEBUG] Current content length: {len(content)} characters")
            print(f"[DEBUG] Content preview: {content[:200]}...")
            
            # Check if placeholder exists - be more specific about the search
            placeholder = '{{CONTENT_IMAGE}}'
            has_placeholder = placeholder in content
            print(f"[DEBUG] Content has {placeholder} placeholder: {has_placeholder}")
            
            if has_placeholder:
                # Count occurrences
                placeholder_count = content.count(placeholder)
                print(f"[DEBUG] Found {placeholder_count} occurrences of {placeholder}")
            
            # Create image HTML with proper styling
            image_html = f'''<div class="blog-content-image" style="text-align: center; margin: 20px 0;">
    <img src="{image_url}" alt="{title} - Illustration" style="max-width: 100%; height: auto; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);" />
</div>'''
            
            if has_placeholder:
                # Replace ALL occurrences of placeholder with actual image
                updated_content = content.replace(placeholder, image_html)
                self.enhanced_data['content'] = updated_content
                
                # Verify replacement worked
                placeholder_after = placeholder in updated_content
                print(f"[DEBUG] After replacement, {placeholder} still exists: {placeholder_after}")
                print(f"[DEBUG] Updated content length: {len(updated_content)} characters")
                print(f"[DEBUG] Updated content preview: {updated_content[:300]}...")
                
                if not placeholder_after:
                    print(f"[DEBUG] ✅ Image embedded successfully using placeholder replacement")
                else:
                    print(f"[DEBUG] ❌ WARNING: Placeholder still exists after replacement!")
                    
            else:
                # No placeholder found, insert image strategically
                updated_content = self._insert_image_strategically(content, image_html)
                self.enhanced_data['content'] = updated_content
                print(f"[DEBUG] Image inserted strategically in content (no placeholder found)")
                print(f"[DEBUG] Updated content length: {len(updated_content)} characters")
            
            logger.info("Image embedded in content successfully")
            
        except Exception as e:
            error_msg = f"Failed to embed image in content: {str(e)}"
            print(f"[DEBUG] {error_msg}")
            logger.error(error_msg)

    def _insert_image_strategically(self, content: str, image_html: str) -> str:
        """
        Insert image at an appropriate location in HTML content when no placeholder exists
        """
        print(f"[DEBUG] Inserting image strategically in content")
        
        if not content or not content.strip():
            print(f"[DEBUG] Empty content, returning just the image")
            return image_html
        
        # Strategy 1: Insert after first paragraph if content has paragraphs
        if '<p>' in content:
            # Find the end of the first paragraph
            first_p_end = content.find('</p>')
            if first_p_end != -1:
                insertion_point = first_p_end + 4  # After </p>
                result = content[:insertion_point] + '\n\n' + image_html + '\n\n' + content[insertion_point:]
                print(f"[DEBUG] Inserted image after first paragraph")
                return result
        
        # Strategy 2: Insert after first heading if content has headings
        for heading in ['</h1>', '</h2>', '</h3>']:
            if heading in content:
                heading_end = content.find(heading)
                if heading_end != -1:
                    insertion_point = heading_end + len(heading)
                    result = content[:insertion_point] + '\n\n' + image_html + '\n\n' + content[insertion_point:]
                    print(f"[DEBUG] Inserted image after first heading")
                    return result
        
        # Strategy 3: Insert after first 200 characters at a natural break point
        if len(content) > 200:
            # Look for natural break points after 200 characters
            search_start = 200
            search_end = min(400, len(content))
            break_points = ['</p>', '</div>', '<br>', '\n\n']
            
            for break_point in break_points:
                pos = content.find(break_point, search_start, search_end)
                if pos != -1:
                    insertion_point = pos + len(break_point)
                    result = content[:insertion_point] + '\n\n' + image_html + '\n\n' + content[insertion_point:]
                    print(f"[DEBUG] Inserted image at natural break point ({break_point})")
                    return result
        
        # Strategy 4: Insert in the middle of content (fallback)
        middle_point = len(content) // 2
        # Try to find a good break near the middle
        for i in range(middle_point, min(middle_point + 100, len(content))):
            if content[i:i+4] == '</p>':
                insertion_point = i + 4
                result = content[:insertion_point] + '\n\n' + image_html + '\n\n' + content[insertion_point:]
                print(f"[DEBUG] Inserted image near middle at paragraph break")
                return result
        
        # Final fallback: Insert at the middle
        result = content[:middle_point] + '\n\n' + image_html + '\n\n' + content[middle_point:]
        print(f"[DEBUG] Inserted image at middle of content (fallback)")
        return result 

    def _generate_banner_image_if_needed(self) -> None:
        """
        Generate banner image if not provided
        """
        print(f"[DEBUG] Checking if banner image generation is needed")
        
        try:
            # Check if banner image already exists
            if self.blog_data.get('banner'):
                print(f"[DEBUG] Banner image already exists, skipping generation")
                logger.info("Banner image already exists, skipping generation")
                return
            
            # Check if we have a banner image prompt to generate
            banner_prompt = self.enhanced_data.get('banner_image_prompt', '')
            if not banner_prompt:
                print(f"[DEBUG] No banner image prompt provided, skipping banner generation")
                return
            
            # Generate banner image
            self._generate_banner_image()
            
        except Exception as e:
            error_msg = f"Banner image generation failed: {str(e)}"
            print(f"[DEBUG] {error_msg}")
            logger.error(error_msg)
            # Don't raise error for banner image generation failure - it's optional
            self.errors.append(error_msg)

    def _generate_banner_image(self) -> None:
        """
        Generate banner image using OpenAI DALL-E and upload to S3
        """
        print(f"[DEBUG] Starting banner image generation")
        logger.info("Generating banner image using DALL-E")
        
        try:
            banner_prompt = self.enhanced_data.get('banner_image_prompt', '')
            if not banner_prompt:
                print(f"[DEBUG] No banner image prompt provided, skipping banner generation")
                return
            
            print(f"[DEBUG] Using DALL-E prompt for banner: {banner_prompt}")
            
            # Generate banner image using DALL-E
            image_url = self._call_openai_dalle_banner(banner_prompt)
            print(f"[DEBUG] DALL-E generated banner image URL: {image_url}")
            
            # Download and upload to S3
            s3_banner_url = self._download_and_upload_banner_image(image_url)
            print(f"[DEBUG] Banner image uploaded to S3: {s3_banner_url}")
            
            # Store banner image URL in enhanced data
            self.enhanced_data['banner_image_url'] = s3_banner_url
            
            print(f"[DEBUG] Banner image generation completed successfully")
            logger.info("Banner image generated and uploaded successfully")
            
        except Exception as e:
            error_msg = f"Banner image generation failed: {str(e)}"
            print(f"[DEBUG] {error_msg}")
            logger.error(error_msg)
            # Don't raise error for banner image generation failure - it's optional
            self.errors.append(error_msg)

    def _call_openai_dalle_banner(self, prompt: str) -> str:
        """
        Call OpenAI DALL-E API to generate realistic banner images optimized for blog headers
        """
        print(f"[DEBUG] Calling OpenAI DALL-E API for banner image generation")
        logger.info("Calling OpenAI DALL-E API for banner image")
        
        # Enhance prompt for realistic banner photography
        enhanced_prompt = self._enhance_banner_prompt_for_realism(prompt)
        print(f"[DEBUG] Original banner prompt: {prompt}")
        print(f"[DEBUG] Enhanced banner prompt: {enhanced_prompt}")
        
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
        
        # Banner-specific payload - wider aspect ratio for headers
        payload = {
            'model': 'dall-e-3',
            'prompt': enhanced_prompt,
            'size': '1792x1024',  # Wide banner format (16:9 aspect ratio)
            'quality': 'hd',
            'n': 1,
            'style': 'natural'
        }
        
        try:
            print(f"[DEBUG] Sending DALL-E request for banner image")
            response = requests.post(
                'https://api.openai.com/v1/images/generations',
                headers=headers,
                json=payload,
                timeout=60
            )
            
            print(f"[DEBUG] DALL-E banner API response status: {response.status_code}")
            response.raise_for_status()
            
            response_data = response.json()
            
            if 'data' not in response_data or not response_data['data']:
                raise ValidationError("DALL-E API returned empty response for banner")
            
            image_url = response_data['data'][0]['url']
            print(f"[DEBUG] DALL-E generated banner image URL: {image_url}")
            
            return image_url
            
        except requests.exceptions.RequestException as e:
            error_msg = f"OpenAI DALL-E banner API request failed: {str(e)}"
            print(f"[DEBUG] {error_msg}")
            logger.error(error_msg)
            raise ValidationError(error_msg)
        except Exception as e:
            error_msg = f"OpenAI DALL-E banner API call failed: {str(e)}"
            print(f"[DEBUG] {error_msg}")
            logger.error(error_msg)
            raise ValidationError(error_msg)

    def _enhance_banner_prompt_for_realism(self, original_prompt: str) -> str:
        """
        Enhance the DALL-E prompt specifically for realistic banner images using the same approach as content images
        """
        print(f"[DEBUG] Enhancing banner prompt for authentic, realistic photo generation")
        
        # Clean up the original prompt
        prompt = original_prompt.lower().strip()
        
        # Remove any existing style modifiers that might make it artistic/painted
        artistic_terms = [
            'cartoon', 'animated', 'illustration', 'drawing', 'sketch', 
            'comic', 'anime', 'manga', 'digital art', 'artwork', 'painting',
            'artistic', 'stylized', 'rendered', 'concept art', 'fantasy art',
            'abstract', 'watercolor', 'oil painting', 'acrylic', 'brush strokes',
            'professional composition', 'blog banner style', 'magazine quality', 
            'commercial photography', 'professional presentation', 'ai generated',
            'digital rendering', 'computer generated', 'cgi'
        ]
        
        for term in artistic_terms:
            prompt = prompt.replace(term, '').strip()
        
        # Build enhanced prompt focusing on authentic, realistic photography for banners
        # Use much stronger realism terms to force authentic photos
        enhanced_prompt = f"""Real authentic photograph shot with DSLR camera, {prompt.strip()}, genuine unposed candid photo, actual real world scene, smartphone photography, natural documentary style, unfiltered raw photo, real life moment, authentic street photography, no artificial effects, genuine everyday reality, natural lighting only, real people real places, actual photograph not artwork, photojournalism style, wide landscape shot"""
        
        # Ensure prompt doesn't exceed DALL-E length limits
        if len(enhanced_prompt) > 400:
            # Prioritize the most important realism terms for banners
            enhanced_prompt = f"Real authentic photograph, {prompt.strip()}, genuine unposed candid photo, actual real world scene, natural documentary style, unfiltered raw photo, real life moment, photojournalism style"
            if len(enhanced_prompt) > 400:
                enhanced_prompt = enhanced_prompt[:397] + "..."
        
        print(f"[DEBUG] Enhanced banner prompt for authentic photography: {enhanced_prompt}")
        print(f"[DEBUG] Enhanced banner prompt length: {len(enhanced_prompt)} characters")
        return enhanced_prompt

    def _download_and_upload_banner_image(self, image_url: str) -> str:
        """
        Download banner image from OpenAI and upload to S3
        """
        print(f"[DEBUG] Downloading and uploading banner image to S3")
        logger.info("Downloading and uploading generated banner image to S3")
        
        try:
            # Download image from OpenAI URL
            print(f"[DEBUG] Downloading banner image from: {image_url}")
            response = requests.get(image_url, timeout=30)
            response.raise_for_status()
            
            image_content = response.content
            print(f"[DEBUG] Downloaded banner image (size: {len(image_content)} bytes)")
            
            # Generate unique filename for banner
            filename = f"blog/banners/banner-{uuid.uuid4()}.png"
            print(f"[DEBUG] Generated banner filename: {filename}")
            
            # Upload to S3 using default storage
            image_file = ContentFile(image_content, name=filename)
            saved_path = default_storage.save(filename, image_file)
            
            # Get public URL
            s3_image_url = default_storage.url(saved_path)
            print(f"[DEBUG] Banner image uploaded successfully to: {s3_image_url}")
            
            return s3_image_url
            
        except Exception as e:
            error_msg = f"Banner image download/upload failed: {str(e)}"
            print(f"[DEBUG] {error_msg}")
            logger.error(error_msg)
            raise ValidationError(error_msg) 