from accounts.models import Affiliate, AffiliateReferral<PERSON>ser


def handle_affiliate_referral(user, affiliate):
    affiliate_object = Affiliate.objects.filter(affiliate_code=affiliate).first()
    if affiliate_object:
        # If user is already referred by someone else -> Return
        # As this is already handled in signup check - This is just a backup
        try:
            referred_user_exists = user.referred_user
            return None
        except Exception as e:
            pass

        affiliate_object.users_referred_count += 1
        affiliate_object.save(update_fields=['users_referred_count'])

        AffiliateReferralUser.objects.create(
            user=user,
            affiliate=affiliate_object
        )
