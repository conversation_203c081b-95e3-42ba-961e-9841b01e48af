"""

Helper methods for accounts app 
"""

def user_profile_complete_status(user):
    """
    user: User Model Object
    Check whether user have its profile completed or not
    """

    is_profile_completed = True
    required_fields = [
        "email",
        "phone_number",
        "full_name"
    ]

    for field in required_fields:
        value = getattr(user, field, None)
        if value is None or (isinstance(value, str) and not value.strip()):
            is_profile_completed = False
            break

    return is_profile_completed 


def get_user_data(user):
    """
    Get user data from user model
    """
    user_data = {
        'external_id': str(user.external_id),
        'name': user.full_name,
        'email': user.email,
        'phone_number': {
            'phone_number_country_code': user.phone_number_country_code,
            'phone_number': user.phone_number,
        },
        'user_type': user.user_type,
        'is_email_verified': user.is_email_verified,
        'is_social_user': user.is_social_user,
        'created_at': user.created_at,
        'last_login': user.last_login,
        'is_profile_completed': user_profile_complete_status(user),
    }
    return user_data


def get_masked_email(email):
    """
    Get masked email
    """
    if '@' not in email:
        email_length = len(email)
        start_count = 3
        end_count = 3
        mask_count = email_length - start_count - end_count
        return email[:start_count] + ('*' * mask_count) + email[-end_count:]
    
    name_part, domain_part = email.split('@', 1)
    start_count = 3
    if len(name_part) <= 3:
        start_count = len(name_part)
    # if len(name_part) <= 3:
    #     masked_name = name_part  # Not enough to mask
    # else:
    #     masked_name = name_part[:3] + '*' * (len(name_part) - 3)
    masked_name = name_part[:start_count] + '*' * 5

    return f"{masked_name}@{domain_part}"
