"""
Forms for user management in the accounts app.
"""
import logging
import uuid
from typing import Dict, Any
from django.utils import timezone
from django import forms
from django.utils.translation import gettext_lazy as _
from django.db import transaction
from django.contrib.admin.models import LogEntry, CHANGE
from django.contrib.contenttypes.models import ContentType
from django.utils.encoding import force_str
from django.conf import settings

from base.email_utils import send_mail_task, EmailContentManager
from accounts.utils.openai_blog_helper import OpenAIBlogHelper

from .models import Partner, User, Blog, Writer, Affiliate, GlobalDashboard
from .choices import UserTypeChoices, PartnerTypeChoices, PreferenceChoices, BlogTypeChoices, AffiliateStatusChoices

logger = logging.getLogger(__name__)

class SuperAdminCreationForm(forms.ModelForm):
    """
    A form for creating new Super Admin users.
    Uses email as the username field.
    """
    email = forms.EmailField(
        label=_("Email"),
        max_length=254,
        widget=forms.EmailInput(attrs={'autocomplete': 'email'})
    )
    password = forms.CharField(
        label=_("Password"),
        strip=False,
        widget=forms.PasswordInput(attrs={'autocomplete': 'new-password'}),
        help_text=_("Raw passwords are not stored, so there is no way to see this user's password, but you can change the password using <a href=\"../password/\">this form</a>.")
    )
    confirm_password = forms.CharField(
        label=_("Confirm password"),
        strip=False,
        widget=forms.PasswordInput(attrs={'autocomplete': 'new-password'})
    )
    full_name = forms.CharField(label=_("Full name"), max_length=150, required=False)

    class Meta:
        model = User
        fields = ("email", "full_name") # Fields from the model we directly map

    def clean_email(self):
        email = self.cleaned_data.get("email")
        if User.objects.filter(
            email__iexact=email,
            user_type=UserTypeChoices.SUPER_ADMIN.value
        ).exists():
            raise forms.ValidationError(_("A user with that email already exists."))
        return email

    def clean_confirm_password(self):
        password = self.cleaned_data.get("password")
        confirm_password = self.cleaned_data.get("confirm_password")
        if password and confirm_password and password != confirm_password:
            raise forms.ValidationError(_("The two password fields didn't match."))
        return confirm_password

    @transaction.atomic
    def save(self, commit=True):
        # This form's save method is responsible for creating the user via the manager
        # and then calling the email utility.
        # Note: super().save(commit=False) is not called as we use the manager directly.
        extra_fields = {}

        email = self.cleaned_data["email"]
        # Use the first password field for sending, as confirm_password might be cleared or changed by other logic
        # if clean_confirm_password is called multiple times or form is re-validated.
        raw_password_for_email = self.cleaned_data["password"] 
        password_for_creation = self.cleaned_data["confirm_password"] # Use confirmed password for creation
        full_name = self.cleaned_data.get("full_name")
        extra_fields['user_type'] = UserTypeChoices.SUPER_ADMIN.value

        partner = Partner.objects.filter(partner_type=PartnerTypeChoices.ZUUMM.value).first()
        if partner:
            extra_fields['partner'] = partner.id

        if full_name:
            extra_fields['full_name'] = full_name
        
        # Use the UserManager's create_superuser method
        self.instance = User.objects.create_superuser(
            email=email,
            password=password_for_creation, 
            **extra_fields
        )

        # Send welcome email to the new superadmin
        # Construct admin panel URL
        protocol = 'https'
        domain = settings.BE_BASE_URL
        # For superadmins, we don't need subdomain prefix as they access the main domain
        admin_url = f"{protocol}://{domain}/admin/login/"
        admin_url_display = f"{domain}/admin/login/"
        
        # Prepare email context
        email_context = {
            # 'admin_name': self.instance.full_name,
            'admin_email': self.instance.email,
            'admin_password': raw_password_for_email,
            'admin_url': admin_url,
            'admin_url_display': admin_url_display,
        }
        
        subject, body = EmailContentManager.get_email_content('superadmin_welcome', email_context)
        send_mail_task(subject, body, [self.instance.email])
        
        return self.instance

    def save_m2m(self, *args, **kwargs):
        """
        Admin calls this to save M2M data. For superuser creation,
        M2M (groups/permissions) are implicitly handled by is_superuser=True.
        This form has no explicit M2M fields to save.
        This method is here to prevent AttributeError during admin save process.
        """
        pass

class PartnerActivationForm(forms.ModelForm):
    """
    Form for activating or deactivating a partner and its related objects.
    This form handles the logic of activating/deactivating users associated with the partner.
    """
    class Meta:
        model = Partner
        # Note: entity_type is excluded as it should not be changeable by superadmin or partneradmin
        fields = ['entity_name', 'subdomain', 'vanity_domain', 'logo', 
                  'primary_theme', 'secondary_theme', 'is_active', 
                  'gst_number', 'company_registration_number', 'monthly_transaction_volume',
                  'package_type', 'referral_code', 'update_notification_enabled',
                  'preference', 'partner_type', 'organization_url']
        labels = {
            'is_active': _('Partner Active Status'),
            'logo': _('Partner Logo'),
            'primary_theme': _('Primary Partner Color'),
            'secondary_theme': _('Secondary Partner Color'),
            'entity_name': _('Business/Entity Name'),
            'update_notification_enabled': _('Keep me posted with updates'),
            'organization_url': _('Organization Website URL')
        }
        help_texts = {
            'is_active': _('When deactivated, all related admin users and regular users will also be deactivated.'),
            'logo': _('Upload a logo image for the partner.'),
            'primary_theme': _('Primary partner color (hex code, e.g. #336699).'),
            'secondary_theme': _('Secondary partner color (hex code, e.g. #336699).'),
            'subdomain': _('No spaces or special characters allowed. All lowercase.'),
            'vanity_domain': _('Enable to use a vanity domain.'),
            'referral_code': _('Optional referral code.'),
            'update_notification_enabled': _('Receive updates about new features and announcements.'),
            'organization_url': _('Website URL of the organization.')
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        # Hide partner_type field as requested
        if 'partner_type' in self.fields:
            self.fields['partner_type'].widget = forms.HiddenInput()

    def clean(self):
        cleaned_data = super().clean()
        is_active = cleaned_data.get('is_active')
        
        if self.instance.pk and self.instance.is_active != is_active:
            # Status is changing, validate here if needed
            pass
        
        return cleaned_data

    @transaction.atomic
    def save(self, commit=True):
        partner = super().save(commit=False)
        
        # Get old values for comparison
        if partner.pk:
            old_partner = Partner.objects.get(pk=partner.pk)
            old_status = old_partner.is_active
        else:
            old_status = False
        
        # If commit is True, save the partner and update related users
        if commit:
            partner.save()
            
            if partner.pk and old_status != partner.is_active:
                # Status changed, update related users and log the change
                self._update_related_users(partner)
                self._log_status_change(partner, old_status)
                
                # Send notification emails based on the status change
                if not partner.is_active:
                    self._send_deactivation_emails(partner)
                else:
                    self._send_activation_emails(partner)
                
        return partner

    def _update_related_users(self, partner):
        """Update all users associated with this partner to match the partner's active status"""
        User.objects.filter(partner=partner).update(is_active=partner.is_active)
    
    def _log_status_change(self, partner, old_status):
        """Create admin log entry for the status change"""
        if not self.user:
            return
            
        action_flag = CHANGE
        
        # Create a more detailed change message
        if old_status and not partner.is_active:
            change_message = f"Deactivated the partner '{partner.entity_name}'. All related users have been deactivated."
        elif not old_status and partner.is_active:
            change_message = f"Reactivated the partner '{partner.entity_name}'. All related users have been reactivated."
        else:
            change_message = f"Changed active status from {old_status} to {partner.is_active}"
        
        LogEntry.objects.log_action(
            user_id=self.user.id,
            content_type_id=ContentType.objects.get_for_model(partner).pk,
            object_id=partner.pk,
            object_repr=force_str(partner),
            action_flag=action_flag, 
            change_message=change_message
        )
    
    def _send_deactivation_emails(self, partner):
        """Send deactivation notification emails to partner admins"""
        # Get all partner admins for this partner
        partner_admins = User.objects.filter(
            partner=partner,
            user_type__in=UserTypeChoices.partner_admin_types(),
        )
        
        if not partner_admins.exists():
            return
            
        # Send deactivation email to each partner admin
        for admin in partner_admins:
            email_context = {
                'admin_name': admin.full_name or admin.email,
                'partner_name': partner.entity_name,
                'deactivation_date': partner.updated_at.strftime('%Y-%m-%d %I:%M %p')
            }
            
            subject, body = EmailContentManager.get_email_content('partner_deactivation', email_context)
            send_mail_task(subject, body, [admin.email])

    def _send_activation_emails(self, partner):
        """Send activation notification emails to partner admins"""
        # Get all partner admins for this partner
        partner_admins = User.objects.filter(
            partner=partner,
            user_type__in=UserTypeChoices.partner_admin_types(),
        )
        
        if not partner_admins.exists():
            return
        
        if partner.preference != PreferenceChoices.REFERRAL_LINK.value:
            # Construct admin panel URL
            protocol = 'https'
            subdomain = f"{partner.subdomain}"
            domain = settings.BE_BASE_URL
            admin_url = f"{protocol}://{subdomain}.{domain}/admin/login/"
            email_type = 'partner_activation'
        else:
            admin_url = None
            email_type = 'partner_activation_non_admin'
            
        # Send activation email to each partner admin
        for admin in partner_admins:
            email_context = {
                'admin_name': admin.full_name or admin.email,
                'partner_name': partner.entity_name,
                'activation_date': partner.updated_at.strftime('%Y-%m-%d %I:%M %p'),
                'admin_url': admin_url
            }
            
            subject, body = EmailContentManager.get_email_content(email_type, email_context)
            send_mail_task(subject, body, [admin.email]) 

class BlogForm(forms.ModelForm):
    """
    Single form for creating and updating blog posts with OpenAI integration.
    Field visibility is controlled by get_fieldsets in admin.
    """
    
    class Meta:
        model = Blog
        fields = [
            'title', 'content', 'slug', 'banner', 'read_time', 'type', 
            'is_active', 'writer', 'audio_file', 'seo_title', 'meta_description', 
            'meta_keywords', 'canonical_url', 'featured_image_alt_text', 
            'robots_directive', 'focus_keyword', 'meta_information',
            'auto_generate_content', 'auto_generate_slug', 'auto_generate_audio_file'
        ]
        widgets = {
            'content': forms.Textarea(attrs={'class': 'django-ckeditor-widget'}),
            'meta_description': forms.Textarea(attrs={'rows': 3}),
            'meta_keywords': forms.Textarea(attrs={'rows': 2}),
            'robots_directive': forms.Textarea(attrs={'rows': 2}),
            'meta_information': forms.TextInput(attrs={'placeholder': 'destination, category'}),
        }
        labels = {
            'title': _('Blog Title'),
            'content': _('Blog Content'),
            'slug': _('URL Slug'),
            'banner': _('Banner Image'),
            'read_time': _('Read Time (minutes)'),
            'type': _('Blog Type'),
            'is_active': _('Published'),
            'writer': _('Writer'),
            'audio_file': _('Audio File (Podcast)'),
            'auto_generate_content': _('Auto-generate Content'),
            'auto_generate_slug': _('Auto-generate Slug'),
            'auto_generate_audio_file': _('Auto-generate Audio File'),
            'seo_title': _('SEO Title'),
            'meta_description': _('Meta Description'),
            'meta_keywords': _('Meta Keywords'),
            'canonical_url': _('Canonical URL'),
            'featured_image_alt_text': _('Banner Alt Text'),
            'robots_directive': _('Robots Directive'),
            'focus_keyword': _('Focus Keyword'),
            'meta_information': _('Meta Information'),
        }
        help_texts = {
            'title': _('Enter a compelling title for your blog post.'),
            'content': _('Write your blog content using the rich text editor.'),
            'slug': _('URL-friendly version of the title (e.g., my-blog-post).'),
            'banner': _('Upload a banner image for the blog post.'),
            'read_time': _('Estimated reading time in minutes. Leave empty to auto-calculate.'),
            'type': _('Choose the type of blog content.'),
            'is_active': _('Set to published to make the blog live.'),
            'writer': _('Select the writer for this blog post.'),
            'audio_file': _('Upload audio file for podcasts. Required for PODCAST type blogs, optional for POST type blogs.'),
            'auto_generate_content': _('Mark true when you want to auto-generate content by OpenAI. If left empty, you need to upload the data manually.'),
            'auto_generate_slug': _('Mark true when you want to auto-generate URL slug by OpenAI. If left empty, you need to upload the data manually.'),
            'auto_generate_audio_file': _('Mark true when you want to auto-generate audio file by OpenAI. If left empty, you need to upload the data manually.'),
            'seo_title': _('SEO optimized title for search engines. Leave empty to auto-generate.'),
            'meta_description': _('Brief description for search engine results.'),
            'meta_keywords': _('Comma-separated keywords for SEO. Leave empty to auto-generate.'),
            'canonical_url': _('Canonical URL to prevent duplicate content issues. Leave empty to auto-generate.'),
            'featured_image_alt_text': _('Alt text for the banner image. Leave empty to auto-generate.'),
            'robots_directive': _('Instructions for search engine crawlers. Leave empty to auto-generate.'),
            'focus_keyword': _('Primary keyword this blog targets. Leave empty to auto-generate.'),
            'meta_information': _('Destination and category information (comma-separated). Leave empty to auto-generate.'),
        }

    def __init__(self, *args, **kwargs):
        print(f"[DEBUG] BlogForm.__init__ called")
        print(f"[DEBUG] args: {len(args) if args else 0}")
        print(f"[DEBUG] kwargs keys: {list(kwargs.keys()) if kwargs else []}")
        
        super().__init__(*args, **kwargs)
        
        print(f"[DEBUG] Form instance: {self.instance}")
        if self.instance and hasattr(self.instance, 'pk') and self.instance.pk:
            print(f"[DEBUG] Existing instance ID: {self.instance.pk}")
            print(f"[DEBUG] Instance type: {self.instance.type}")
            print(f"[DEBUG] Instance has audio_file: {bool(self.instance.audio_file)}")
        else:
            print(f"[DEBUG] New instance")
            
        # Make sure writer field shows only active writers
        self.fields['writer'].queryset = Writer.objects.filter(is_active=True)
        self.fields['banner'].required = False
        # Make slug field not required at form level (but preserve model-level unique constraint)
        self.fields['slug'].required = False
        
        # Make content field conditionally required
        # If auto_generate_content is True, content is not required
        if self.data:  # Only check when form is being submitted
            auto_generate_content = self.data.get('auto_generate_content')
            if auto_generate_content:
                self.fields['content'].required = False
                print(f"[DEBUG] Made content field not required due to auto_generate_content=True")
        
        print(f"[DEBUG] Form fields initialized: {list(self.fields.keys())}")

    def clean(self):
        """Clean method with OpenAI integration and file replacement handling"""
        print(f"[DEBUG] BlogForm.clean() started")
        logger.info("Starting blog form validation and enhancement")
        
        # Handle Django admin file replacement quirk FIRST
        self._handle_file_replacement_quirk()
        
        cleaned_data = super().clean()
        
        # Skip OpenAI processing if there are already validation errors
        if self.errors:
            print(f"[DEBUG] Skipping OpenAI processing due to existing form errors: {self.errors}")
            return cleaned_data
        
        # Get the auto-generation flags
        auto_generate_content = cleaned_data.get('auto_generate_content', False)
        auto_generate_slug = cleaned_data.get('auto_generate_slug', False)
        auto_generate_audio_file = cleaned_data.get('auto_generate_audio_file', False)
        blog_type = cleaned_data.get('type', 'POST')
        
        print(f"[DEBUG] Auto-generation flags - Content: {auto_generate_content}, Slug: {auto_generate_slug}, Audio: {auto_generate_audio_file}")
        
        # Only proceed with OpenAI processing if at least one auto-generation flag is True
        if auto_generate_content or auto_generate_slug or auto_generate_audio_file:
            try:
                print(f"[DEBUG] Blog type: {blog_type}")
                
                # Prepare data for OpenAI processing
                blog_data = self._prepare_blog_data_for_openai(cleaned_data)
                print(f"[DEBUG] Prepared blog data for OpenAI: {list(blog_data.keys())}")
                
                # Initialize OpenAI helper with auto-generation flags
                actual_blog_type = self.instance.type if (self.instance and self.instance.pk) else blog_type
                openai_helper = OpenAIBlogHelper(blog_data, actual_blog_type, {
                    'auto_generate_content': auto_generate_content,
                    'auto_generate_slug': auto_generate_slug,
                    'auto_generate_audio_file': auto_generate_audio_file
                })
                print(f"[DEBUG] OpenAI helper initialized")
                
                # Process with OpenAI
                enhanced_data = openai_helper.process()
                print(f"[DEBUG] OpenAI processing completed")
                
                # Update cleaned data with enhanced fields based on flags
                self._update_cleaned_data_with_enhanced(cleaned_data, enhanced_data, {
                    'auto_generate_content': auto_generate_content,
                    'auto_generate_slug': auto_generate_slug,
                    'auto_generate_audio_file': auto_generate_audio_file
                })
                print(f"[DEBUG] Cleaned data updated with enhanced fields")
                
                # Handle audio file for podcasts if auto-generation is enabled
                actual_blog_type = self.instance.type if (self.instance and self.instance.pk) else blog_type
                if actual_blog_type == 'PODCAST' and auto_generate_audio_file and enhanced_data.get('audio_file_url'):
                    self._handle_podcast_audio_file(cleaned_data, enhanced_data)
                    print(f"[DEBUG] Handled podcast audio file URL")
                
                # Handle banner image generation (always auto-generate if no banner provided)
                self._handle_banner_image(cleaned_data, enhanced_data)
                
                print(f"[DEBUG] Blog form validation and enhancement completed successfully")
                logger.info("Blog form validation and enhancement completed successfully")
                
            except Exception as e:
                error_msg = f"Blog enhancement failed: {str(e)}"
                print(f"[DEBUG] {error_msg}")
                logger.error(error_msg)
                # Don't raise validation error for OpenAI failures - form should still work
                logger.warning("Continuing with manual blog data due to OpenAI processing failure")
        else:
            print(f"[DEBUG] No auto-generation flags enabled, skipping OpenAI processing")
        
        # Always ensure slug has UUID appended (regardless of OpenAI success/failure)
        self._ensure_slug_has_uuid(cleaned_data)
        
        return cleaned_data

    def _prepare_blog_data_for_openai(self, cleaned_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare blog data for OpenAI processing
        """
        print(f"[DEBUG] Preparing blog data for OpenAI processing")
        
        blog_data = {}
        
        # Required fields
        blog_data['title'] = cleaned_data.get('title', '')
        blog_data['content'] = cleaned_data.get('content', '')
        blog_data['type'] = cleaned_data.get('type', 'POST')
        
        # Optional fields that might already be filled
        optional_fields = [
            'slug', 'read_time', 'seo_title', 'meta_description', 'meta_keywords',
            'canonical_url', 'featured_image_alt_text', 'robots_directive', 'focus_keyword',
            'meta_information'
        ]
        
        for field in optional_fields:
            value = cleaned_data.get(field)
            if value:
                blog_data[field] = value
        
        # Handle audio file
        if cleaned_data.get('audio_file'):
            blog_data['audio_file'] = True  # Indicate that audio file exists
        
        print(f"[DEBUG] Blog data prepared with keys: {list(blog_data.keys())}")
        return blog_data

    def _update_cleaned_data_with_enhanced(self, cleaned_data: Dict[str, Any], enhanced_data: Dict[str, Any], auto_flags: Dict[str, bool]) -> None:
        """
        Update cleaned data with OpenAI enhanced fields based on auto-generation flags
        """
        print(f"[DEBUG] Updating cleaned data with enhanced fields based on flags")
        
        # Handle content auto-generation
        if auto_flags.get('auto_generate_content') and 'content' in enhanced_data:
            cleaned_data['content'] = enhanced_data['content']
            print(f"[DEBUG] Updated content with OpenAI generated value")
        
        # Handle slug
        if auto_flags.get('auto_generate_slug') and 'slug' in enhanced_data:
            cleaned_data['slug'] = enhanced_data['slug']
            print(f"[DEBUG] Updated slug with OpenAI generated value")
        
        # Always update other SEO fields that weren't specifically controlled by flags
        other_fields = [
            'read_time', 'seo_title', 'meta_description', 'meta_keywords',
            'canonical_url', 'featured_image_alt_text', 'robots_directive', 
            'focus_keyword', 'meta_information'
        ]
        
        for field in other_fields:
            if field in enhanced_data and enhanced_data[field]:
                # Only update if the original field was empty
                if not cleaned_data.get(field):
                    cleaned_data[field] = enhanced_data[field]
                    print(f"[DEBUG] Updated {field} with OpenAI generated value")
        
        print(f"[DEBUG] Cleaned data update completed")

    def _handle_podcast_audio_file(self, cleaned_data: Dict[str, Any], enhanced_data: Dict[str, Any]) -> None:
        """
        Handle podcast audio file URL from OpenAI generation
        """
        print(f"[DEBUG] Handling podcast audio file")
        
        try:
            audio_url = enhanced_data.get('audio_file_url')
            if audio_url:
                # Store the audio URL for later processing in save method
                self._generated_audio_url = audio_url
                print(f"[DEBUG] Stored generated audio URL for save method: {audio_url}")
                logger.info(f"Generated audio file URL stored: {audio_url}")
            
        except Exception as e:
            error_msg = f"Failed to handle podcast audio file: {str(e)}"
            print(f"[DEBUG] {error_msg}")
            logger.error(error_msg)

    def _handle_banner_image(self, cleaned_data: Dict[str, Any], enhanced_data: Dict[str, Any]) -> None:
        """
        Handle banner image URL from OpenAI generation
        """
        print(f"[DEBUG] Handling banner image")
        
        try:
            banner_url = enhanced_data.get('banner_image_url')
            if banner_url and not cleaned_data.get('banner'):
                # Store the banner URL for later processing in save method
                self._generated_banner_url = banner_url
                print(f"[DEBUG] Stored generated banner URL for save method: {banner_url}")
                logger.info(f"Generated banner image URL stored: {banner_url}")
            
        except Exception as e:
            error_msg = f"Failed to handle banner image: {str(e)}"
            print(f"[DEBUG] {error_msg}")
            logger.error(error_msg)

    def clean_slug(self):
        slug = self.cleaned_data.get('slug')
        
        # Slug is no longer required at form level, so it might be empty
        if not slug:
            return slug
            
        # Skip uniqueness validation here since UUID appending logic in clean() 
        # will ensure uniqueness before database save
        print(f"[DEBUG] Slug validation passed (uniqueness handled by UUID appending): {slug}")
        return slug

    def clean_type(self):
        """Ensure blog type cannot be changed for existing blogs"""
        new_type = self.cleaned_data.get('type')
        
        # For existing blogs, prevent type changes
        if self.instance and self.instance.pk:
            # Get the original blog from database to compare
            try:
                original_blog = Blog.objects.get(pk=self.instance.pk)
                original_type = original_blog.type
                
                if new_type != original_type:
                    print(f"[DEBUG] Blocked blog type change attempt: {original_type} -> {new_type}")
                    raise forms.ValidationError(
                        _('Blog type cannot be changed after creation. Current type: %(current_type)s') % 
                        {'current_type': original_type}
                    )
                    
                print(f"[DEBUG] Blog type validation passed - no change detected: {original_type}")
                return original_type  # Return the original type to ensure no change
                
            except Blog.DoesNotExist:
                # This shouldn't happen in normal cases, but handle gracefully
                print(f"[DEBUG] Warning: Could not find original blog for type validation")
                return new_type
        else:
            # New blog - any type is allowed
            print(f"[DEBUG] New blog type set: {new_type}")
            return new_type

    def clean_audio_file(self):
        """Validate audio file based on blog type and auto-generation settings"""
        audio_file = self.cleaned_data.get('audio_file')
        # Read from raw form data instead of cleaned_data
        auto_generate_audio_file = bool(self.data.get('auto_generate_audio_file'))
        blog_type = self.cleaned_data.get('type', 'POST')
        
        print(f"[DEBUG] clean_audio_file called with audio_file: {audio_file}")
        print(f"[DEBUG] Type of audio_file: {type(audio_file)}")
        print(f"[DEBUG] auto_generate_audio_file: {auto_generate_audio_file}")
        print(f"[DEBUG] blog_type: {blog_type}")
        print(f"[DEBUG] Has instance: {bool(self.instance)}")
        if self.instance and hasattr(self.instance, 'pk'):
            print(f"[DEBUG] Instance PK: {self.instance.pk}")
        
        # For existing blogs being updated
        if self.instance and self.instance.pk:
            try:
                original_blog = Blog.objects.get(pk=self.instance.pk)
                print(f"[DEBUG] Original blog type: {original_blog.type}")
                print(f"[DEBUG] Original blog has audio_file: {bool(original_blog.audio_file)}")
                if original_blog.audio_file:
                    print(f"[DEBUG] Original audio_file name: {original_blog.audio_file.name}")
                
                # Only validate for PODCAST type blogs
                if original_blog.type == BlogTypeChoices.PODCAST.value:
                    # Check if admin is trying to remove/clear the audio file
                    # audio_file will be False when admin checked the clear checkbox
                    is_clearing_audio = (audio_file is False) and original_blog.audio_file
                    
                    if is_clearing_audio:
                        print(f"[DEBUG] Detected attempt to clear audio file from podcast blog")
                        
                        if auto_generate_audio_file:
                            # Allow clearing if auto-generation is enabled
                            print(f"[DEBUG] Allowing audio file clearing because auto-generation is enabled")
                        else:
                            # Prevent clearing if auto-generation is disabled
                            print(f"[DEBUG] Blocking audio file clearing because auto-generation is disabled")
                            raise forms.ValidationError(
                                _('Audio file cannot be removed from podcast blogs when auto-generation is disabled. '
                                  'Either enable "Auto-generate Audio File" or upload a replacement audio file.')
                            )
                
                # Validate that PODCAST blogs have audio file when auto-generation is disabled
                if blog_type == BlogTypeChoices.PODCAST.value and not auto_generate_audio_file:
                    # Check if there will be no audio file after this update
                    will_have_audio = audio_file and hasattr(audio_file, 'name') and audio_file.name
                    original_has_audio = original_blog.audio_file and original_blog.audio_file.name
                    
                    if not will_have_audio and not original_has_audio:
                        print(f"[DEBUG] Podcast blog missing audio file with auto-generation disabled")
                        raise forms.ValidationError(
                            _('Podcast blogs must have an audio file when auto-generation is disabled. '
                              'Either upload an audio file or enable "Auto-generate Audio File".')
                        )
                        
            except Blog.DoesNotExist:
                # This shouldn't happen in normal cases, but handle gracefully
                print(f"[DEBUG] Warning: Could not find original blog for audio file validation")
        
        else:
            # For new blog creation
            if blog_type == BlogTypeChoices.PODCAST.value and not auto_generate_audio_file:
                # New podcast blog without auto-generation must have audio file
                has_audio = audio_file and hasattr(audio_file, 'name') and audio_file.name
                if not has_audio:
                    print(f"[DEBUG] New podcast blog missing audio file with auto-generation disabled")
                    raise forms.ValidationError(
                        _('Podcast blogs must have an audio file when auto-generation is disabled. '
                          'Either upload an audio file or enable "Auto-generate Audio File".')
                    )
        
        print(f"[DEBUG] Audio file validation passed")
        print(f"[DEBUG] Returning audio_file: {audio_file}")
        return audio_file

    def _handle_file_replacement_quirk(self):
        """Handle Django admin file widget quirk for audio file replacement"""
        # Check if this is a file replacement scenario
        audio_file_clear = self.data.get('audio_file-clear')
        has_new_audio_file = 'audio_file' in self.files and self.files['audio_file']
        
        if has_new_audio_file and not audio_file_clear:
            # User is uploading a new file without checking clear - this is normal replacement
            # Django admin sometimes incorrectly flags this as an error, so we prevent it
            print(f"[DEBUG] Detected audio file replacement scenario")
            
            # Remove any existing audio_file validation errors related to "both clear and upload"
            if hasattr(self, '_errors') and 'audio_file' in self._errors:
                error_messages = [str(error) for error in self._errors['audio_file']]
                # Remove the specific Django admin error about clear checkbox
                filtered_errors = [error for error in error_messages 
                                 if 'check the clear checkbox' not in error.lower()]
                
                if len(filtered_errors) != len(error_messages):
                    print(f"[DEBUG] Removed Django admin file widget error for replacement")
                    if filtered_errors:
                        self._errors['audio_file'] = filtered_errors
                    else:
                        del self._errors['audio_file']

    @transaction.atomic
    def save(self, commit=True):
        print(f"[DEBUG] BlogForm.save() called with commit={commit}")
        logger.info(f"BlogForm.save() called with commit={commit}")
        
        blog = super().save(commit=False)
        
        # Debug audio file information
        print(f"[DEBUG] Blog instance audio_file: {blog.audio_file}")
        print(f"[DEBUG] Blog instance audio_file type: {type(blog.audio_file)}")
        if blog.audio_file:
            print(f"[DEBUG] Blog audio_file name: {blog.audio_file.name}")
        
        # Check cleaned data for audio file
        audio_file_in_cleaned_data = self.cleaned_data.get('audio_file')
        print(f"[DEBUG] Audio file in cleaned_data: {audio_file_in_cleaned_data}")
        print(f"[DEBUG] Audio file in cleaned_data type: {type(audio_file_in_cleaned_data)}")
        
        # Handle published_at based on is_active status changes
        if self.instance.pk:  # This is an update
            old_blog = Blog.objects.get(pk=self.instance.pk)
            
            # If blog is being published for the first time or republished
            if blog.is_active and not old_blog.is_active:
                from django.utils import timezone
                blog.published_at = timezone.now()
                print(f"[DEBUG] Set published_at for blog publication")
            # If blog is being unpublished
            elif not blog.is_active and old_blog.is_active:
                blog.published_at = None
                print(f"[DEBUG] Cleared published_at for blog unpublication")
        else:  # This is a creation
            if blog.is_active and not blog.published_at:
                from django.utils import timezone
                blog.published_at = timezone.now()
                print(f"[DEBUG] Set published_at for new blog creation")
        
        # Handle generated audio file URL if exists
        if hasattr(self, '_generated_audio_url') and self._generated_audio_url:
            try:
                print(f"[DEBUG] Processing generated audio URL: {self._generated_audio_url}")
                # The audio is already uploaded to S3, we just need to store the path
                # Extract the path from the full URL for the FileField
                import urllib.parse
                parsed_url = urllib.parse.urlparse(self._generated_audio_url)
                audio_path = parsed_url.path.lstrip('/')  # Remove leading slash
                
                # Set the audio file path directly
                blog.audio_file.name = audio_path
                print(f"[DEBUG] Set audio_file path: {audio_path}")
                logger.info(f"Audio file path set: {audio_path}")
                    
            except Exception as e:
                error_msg = f"Failed to set generated audio file: {str(e)}"
                print(f"[DEBUG] {error_msg}")
                logger.error(error_msg)
        
        # Handle generated banner image URL if exists
        if hasattr(self, '_generated_banner_url') and self._generated_banner_url:
            try:
                print(f"[DEBUG] Processing generated banner URL: {self._generated_banner_url}")
                # The banner is already uploaded to S3, we just need to store the path
                # Extract the path from the full URL for the FileField
                import urllib.parse
                parsed_url = urllib.parse.urlparse(self._generated_banner_url)
                banner_path = parsed_url.path.lstrip('/')  # Remove leading slash
                
                # Set the banner file path directly
                blog.banner.name = banner_path
                print(f"[DEBUG] Set banner path: {banner_path}")
                logger.info(f"Banner file path set: {banner_path}")
                    
            except Exception as e:
                error_msg = f"Failed to set generated banner file: {str(e)}"
                print(f"[DEBUG] {error_msg}")
                logger.error(error_msg)
        
        if commit:
            print(f"[DEBUG] About to save blog with audio_file: {blog.audio_file}")
            blog.save()
            print(f"[DEBUG] Blog saved successfully: {blog.title}")
            logger.info(f"Blog saved successfully: {blog.title}")
        
        return blog

    def _ensure_slug_has_uuid(self, cleaned_data: Dict[str, Any]) -> None:
        """
        Ensure slug has UUID appended (regardless of OpenAI success/failure)
        """
        print(f"[DEBUG] Ensuring slug has UUID appended")
        
        # Handle slug - PRESERVE admin input but always append UUID for uniqueness
        admin_slug = cleaned_data.get('slug', '').strip()
        
        # Get or generate the blog's UUID (first 8 hex digits) for all cases
        if self.instance:
            if self.instance.pk:
                # Existing blog - use its external_id
                blog_uuid_short = str(self.instance.external_id).replace('-', '')[:8]
            else:
                # New blog - generate or use existing external_id
                import uuid
                if not hasattr(self.instance, 'external_id') or not self.instance.external_id:
                    self.instance.external_id = uuid.uuid4()
                blog_uuid_short = str(self.instance.external_id).replace('-', '')[:8]
        else:
            import uuid
            blog_uuid_short = str(uuid.uuid4()).replace('-', '')[:8]
        
        if admin_slug:
            # Admin has entered a slug - preserve base but ensure UUID is appended
            if not admin_slug.endswith(f'-{blog_uuid_short}'):
                final_slug = f"{admin_slug}-{blog_uuid_short}"
                cleaned_data['slug'] = final_slug
                print(f"[DEBUG] Admin slug with UUID appended: {admin_slug} -> {final_slug}")
            else:
                # UUID already appended
                cleaned_data['slug'] = admin_slug
                print(f"[DEBUG] Admin slug already has UUID: {admin_slug}")
        else:
            # No admin slug provided - generate from title if available
            title = cleaned_data.get('title', '')
            if title:
                import re
                basic_slug = re.sub(r'[^a-zA-Z0-9\s-]', '', title.lower())
                basic_slug = re.sub(r'\s+', '-', basic_slug).strip('-')[:50]
                
                final_slug = f"{basic_slug}-{blog_uuid_short}" if basic_slug else f"blog-{blog_uuid_short}"
                cleaned_data['slug'] = final_slug
                print(f"[DEBUG] Generated fallback slug: {final_slug}")
            else:
                final_slug = f"blog-{blog_uuid_short}"
                cleaned_data['slug'] = final_slug
                print(f"[DEBUG] Generated minimal slug: {final_slug}")
        
        print(f"[DEBUG] Final slug after UUID processing: {cleaned_data.get('slug')}")


class AffiliateForm(forms.ModelForm):
    class Meta:
        model = Affiliate
        fields = ['status', 'rejection_reason']

    def clean_rejection_reason(self):
        """Validate rejection reason field"""
        status = self.data.get('status')  # Get directly from form data since clean() hasn't run yet
        rejection_reason = self.cleaned_data.get('rejection_reason')

        if status == AffiliateStatusChoices.REJECTED.value:
            if not rejection_reason or not rejection_reason.strip():
                raise forms.ValidationError('Rejection reason is required when rejecting an affiliate request.')
        return rejection_reason

    def generate_affiliate_code(self):
        while True:
            code = str(uuid.uuid4())[:8].upper()
            if not Affiliate.objects.filter(affiliate_code=code).exists():
                return code

    def clean(self):
        cleaned_data = super().clean()
        status = cleaned_data.get('status')
        
        if self.instance.pk:
            old_affiliate = Affiliate.objects.get(pk=self.instance.pk)
            
            # Prevent changing status if already approved or rejected
            if old_affiliate.status in [AffiliateStatusChoices.APPROVED.value, AffiliateStatusChoices.REJECTED.value]:
                if status != old_affiliate.status:
                    raise forms.ValidationError({
                        'status': f'Cannot change status from {old_affiliate.status}. Request is already finalized.'
                    })
            
            # Prevent rejecting an approved affiliate
            if old_affiliate.status == AffiliateStatusChoices.APPROVED.value and status == AffiliateStatusChoices.REJECTED.value:
                raise forms.ValidationError({
                    'status': 'Cannot reject an approved affiliate'
                })

        # Set timestamps based on status
        if status == AffiliateStatusChoices.APPROVED.value:
            cleaned_data['approved_at'] = timezone.now()
            cleaned_data['rejected_at'] = None  # Clear rejected timestamp
            cleaned_data['rejection_reason'] = None  # Clear rejection reason
        elif status == AffiliateStatusChoices.REJECTED.value:
            cleaned_data['rejected_at'] = timezone.now()
            cleaned_data['approved_at'] = None  # Clear approved timestamp
        elif status == AffiliateStatusChoices.PENDING.value:
            # Clear both timestamps if setting back to pending
            cleaned_data['approved_at'] = None
            cleaned_data['rejected_at'] = None
            cleaned_data['rejection_reason'] = None

        return cleaned_data

    def save(self, commit=True):
        affiliate = super().save(commit=False)
        
        # Set timestamp fields from cleaned_data
        affiliate.approved_at = self.cleaned_data.get('approved_at')
        affiliate.rejected_at = self.cleaned_data.get('rejected_at')
        affiliate.rejection_reason = self.cleaned_data.get('rejection_reason')
        
        if commit:
            affiliate.save()

        status = self.cleaned_data.get('status')
        if status == AffiliateStatusChoices.APPROVED.value:
            # Generate affiliate code if not exists
            if not affiliate.affiliate_code:
                affiliate.affiliate_code = self.generate_affiliate_code()
                affiliate.save()

            # Mark user as affiliate
            affiliate.user.is_referrer = True
            affiliate.user.save()

            # Send approval email
            email_context = {
                'user_name': affiliate.user.full_name or affiliate.user.email,
                'affiliate_link': f"{settings.FE_AFFILIATE_BASE_URL}{affiliate.affiliate_code}",
            }
            subject, body = EmailContentManager.get_email_content('affiliate_approved', email_context)
            send_mail_task(subject, body, [affiliate.user.email])

        elif status == AffiliateStatusChoices.REJECTED.value:
            # Send rejection email
            email_context = {
                'user_name': affiliate.user.full_name or affiliate.user.email,
                'rejection_reason': affiliate.rejection_reason,
            }
            subject, body = EmailContentManager.get_email_content('affiliate_rejected', email_context)
            send_mail_task(subject, body, [affiliate.user.email])

        return affiliate

class GlobalDashboardForm(forms.ModelForm):
    """
    Form for GlobalDashboard with validation to ensure only one value per type
    """
    class Meta:
        model = GlobalDashboard
        fields = ['type', 'value']
        widgets = {
            'type': forms.Select(attrs={'class': 'form-control'}),
            'value': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Enter value'})
        }
        labels = {
            'type': 'Dashboard Type',
            'value': 'Dashboard Value'
        }

    def clean_type(self):
        """Validate that only one value exists per type"""
        dashboard_type = self.cleaned_data.get('type')
        
        if not dashboard_type:
            return dashboard_type
            
        # Check for existing records with the same type
        existing_query = GlobalDashboard.objects.filter(type=dashboard_type)
        
        # If this is an update (instance exists), exclude current instance
        if self.instance and self.instance.pk:
            existing_query = existing_query.exclude(pk=self.instance.pk)
            
        if existing_query.exists():
            existing_record = existing_query.first()
            raise forms.ValidationError(
                f'A dashboard entry for type "{dashboard_type}" already exists with value: "{existing_record.value}". '
                f'Only one value is allowed per type. Please update the existing entry or choose a different type.'
            )
            
        return dashboard_type

    def clean_value(self):
        """Validate value field"""
        value = self.cleaned_data.get('value')
        
        if not value or not value.strip():
            raise forms.ValidationError('Value cannot be empty.')
            
        # Trim whitespace
        return value.strip()


from django.contrib.auth.forms import PasswordChangeForm
from django.core.exceptions import ValidationError

class CustomPasswordChangeForm(PasswordChangeForm):
    def clean_new_password1(self):
        new_password1 = self.cleaned_data.get("new_password1")
        old_password = self.cleaned_data.get("old_password")

        if new_password1 and old_password and new_password1 == old_password:
            raise ValidationError("New password cannot be the same as the old password.")
        return new_password1