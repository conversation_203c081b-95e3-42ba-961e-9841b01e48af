#!/usr/bin/env python3
"""
TripJack Hotels Fetcher - Simple Standalone Script
Run this script directly from terminal to fetch hotels from TripJack API
"""

import os
import sys
import django
import argparse
from pathlib import Path
from django.conf import settings
def setup_django():
    """Setup Django environment"""
    project_root = Path(__file__).parent.absolute()
    sys.path.insert(0, str(project_root))
    
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'zuumm.settings')
    
    # Hardcode API credentials  
    os.environ['TRIPJACK_API_KEY'] = settings.TRIPJACK_API_KEY
    os.environ['TRIPJACK_API_URL'] = settings.TRIPJACK_API_URL
    
    django.setup()

def main():
    """Main function to run the fetcher"""
    parser = argparse.ArgumentParser(description='TripJack Hotels Fetcher')
    parser.add_argument('--production', action='store_true', 
                       help='Run in production mode (fetch all hotels, not just test batch)')
    parser.add_argument('--no-images', action='store_true',
                       help='Skip image downloading')
    parser.add_argument('--max-hotels', type=int, default=50,
                       help='Maximum number of hotels to process (default: 50)')
    parser.add_argument('--skip-validation', action='store_true',
                       help='Skip API prerequisites validation')
    
    args = parser.parse_args()
    
    try:
        # Setup Django
        setup_django()
        
        # Import the new TripJack fetcher (not the deprecated one)
        from base.tripjack_hotels_fetcher.main import TripJackHotelsFetcher
        
        print("🚀 Starting TripJack Hotels Fetcher...")
        print("="*60)
        
        if args.production:
            print("🔥 Running in PRODUCTION mode - fetching ALL hotels")
        else:
            print(f"🧪 Running in TEST mode - max {args.max_hotels} hotels")
        
        if args.no_images:
            print("📷 Image downloading DISABLED")
        else:
            print("📷 Image downloading ENABLED")
            
        if args.skip_validation:
            print("⚠️  Skipping API validation")
        
        print("="*60)
        
        # Create and configure the fetcher
        fetcher = TripJackHotelsFetcher()
        
        print("✅ TripJack fetcher initialized successfully")
        print("🔍 Starting hotel fetch and processing...")
        
        # Run the fetching process using the new method
        success = fetcher.run()
        
        if success:
            print("✅ Process completed successfully!")
        else:
            print("⚠️  Process completed with some issues")
        
        # Get and display statistics using the correct method name
        try:
            stats = fetcher.get_current_statistics()
            
            print("\n" + "="*60)
            print("📊 FINAL STATISTICS:")
            print(f"""
   Total Hotels Fetched: {stats.get('total_hotels_fetched', 0)}
   Successful Hotels: {stats.get('successful_hotels', 0)}
   Failed Hotels: {stats.get('failed_hotels', 0)}
   Skipped Hotels: {stats.get('skipped_hotels', 0)}
   With Destinations: {stats.get('hotels_with_destinations', 0)}
   Without Destinations: {stats.get('hotels_without_destinations', 0)}
   API Calls Made: {stats.get('api_calls_made', 0)}
   API Errors: {stats.get('api_errors', 0)}
   Batches Processed: {stats.get('total_batches_processed', 0)}
            """)
            
            if stats.get('execution_time'):
                print(f"   Execution Time: {stats['execution_time']:.2f} seconds")
                
        except Exception as e:
            print(f"⚠️  Could not retrieve statistics: {e}")
        
        print("="*60)
        
    except KeyboardInterrupt:
        print("\n⚠️  Process interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        print("Check that:")
        print("  1. TRIPJACK_API_KEY is valid")
        print("  2. Django settings are properly configured")
        print("  3. Database is accessible")
        print("  4. TripJack API endpoints are correct")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main() 