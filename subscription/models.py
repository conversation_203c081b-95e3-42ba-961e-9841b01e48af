from django.db import models
from subscription.choices import PLAN_TYPE_CHOICES, TRANSACTION_TYPE_CHOICES, STATUS_CHOICES
from accounts.models import User

# Create your models here.

class BaseModel(models.Model):
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        abstract = True

class SubscriptionPlan(BaseModel):
    name = models.CharField(max_length=100)
    razorpay_plan_id = models.CharField(max_length=100, unique=True)  # from Razorpay
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    currency = models.CharField(max_length=10, default="INR")
    interval = models.CharField(max_length=10, choices=PLAN_TYPE_CHOICES)

    def __str__(self):
        return f"{self.name} ({self.interval})"
    
class Subscription(BaseModel):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="subscriptions")
    plan = models.ForeignKey(SubscriptionPlan, on_delete=models.CASCADE)
    razorpay_subscription_id = models.CharField(max_length=100, unique=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default="created")
    start_date = models.DateTimeField(auto_now_add=True)
    end_date = models.DateTimeField(null=True, blank=True)
    cancelled_at = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"{self.user} - {self.plan} - {self.status}"
    
class Transaction(BaseModel):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="transactions")
    subscription = models.ForeignKey(Subscription, on_delete=models.CASCADE, null=True, blank=True, related_name="transactions")
    razorpay_payment_id = models.CharField(max_length=100, null=True, blank=True)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    currency = models.CharField(max_length=10, default="INR")
    type = models.CharField(max_length=20, choices=TRANSACTION_TYPE_CHOICES)
    status = models.CharField(max_length=20, default="created")  # created, paid, failed

    def __str__(self):
        return f"{self.user} - {self.amount} {self.currency} - {self.type}"