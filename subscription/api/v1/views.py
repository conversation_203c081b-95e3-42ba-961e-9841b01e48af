import razorpay
from django.conf import settings
import json
from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.decorators import action
from rest_framework.views import APIView
from rest_framework.permissions import AllowAny
from django.utils import timezone
from django.http import JsonResponse
from django.utils.timezone import now
import hmac
import hashlib
from subscription.models import SubscriptionPlan, Subscription, Transaction
from .serializers import PlanSerializer, SubscriptionSerializer, TransactionSerializer


client = razorpay.Client(auth=(settings.RAZORPAY_KEY_ID, settings.RAZORPAY_KEY_SECRET))


class PlanViewSet(viewsets.ReadOnlyModelViewSet):
    permission_classes = [AllowAny]
    queryset = SubscriptionPlan.objects.all()
    serializer_class = PlanSerializer


class SubscriptionViewSet(viewsets.ModelViewSet):
    permission_classes = [AllowAny]
    queryset = Subscription.objects.all()
    serializer_class = SubscriptionSerializer

    def create(self, request, *args, **kwargs):
        """ Create a Razorpay subscription """
        user = request.user
        plan_id = request.data.get("plan_id")

        try:
            plan = SubscriptionPlan.objects.get(id=plan_id)
        except SubscriptionPlan.DoesNotExist:
            return Response({"error": "Invalid plan"}, status=status.HTTP_400_BAD_REQUEST)

        razorpay_subscription = client.subscription.create({
            "plan_id": plan.razorpay_plan_id,
            "customer_notify": 1,
            "total_count": 12 if plan.interval == "monthly" else 1, 
        })

        subscription = Subscription.objects.create(
            user=user,
            plan=plan,
            razorpay_subscription_id=razorpay_subscription["id"],
            status="created",
            start_date=None,
            end_date=None 
        )

        return Response(SubscriptionSerializer(subscription).data, status=status.HTTP_201_CREATED)

    @action(detail=True, methods=["post"])
    def cancel(self, request, pk=None):
        """ Cancel subscription """
        subscription = self.get_object()
        client.subscription.cancel(
                subscription.razorpay_subscription_id,
                {"cancel_at_cycle_end": 1}
            )
        subscription.status = "cancelled"
        subscription.cancelled_at = timezone.now() 
        subscription.save()
        return Response({"message": "Subscription cancelled."})

    @action(detail=True, methods=["post"])
    def upgrade(self, request, pk=None):
        """ Upgrade to another plan """
        subscription = self.get_object()
        new_plan_id = request.data.get("new_plan_id")

        try:
            new_plan = SubscriptionPlan.objects.get(id=new_plan_id)
        except SubscriptionPlan.DoesNotExist:
            return Response({"error": "Invalid plan"}, status=status.HTTP_400_BAD_REQUEST)

        # Razorpay doesn’t support direct plan upgrade, so: cancel + create new
        client.subscription.cancel(subscription.razorpay_subscription_id)
        subscription.status = "cancelled"
        subscription.save()

        razorpay_subscription = client.subscription.create({
            "plan_id": new_plan.razorpay_plan_id,
            "customer_notify": 1,
            "total_count": 12 if new_plan.interval == "monthly" else 1,
        })

        new_subscription = Subscription.objects.create(
            user=subscription.user,
            plan=new_plan,
            razorpay_subscription_id=razorpay_subscription["id"],
            status="active"
        )

        return Response(SubscriptionSerializer(new_subscription).data, status=status.HTTP_201_CREATED)

class RazorpayWebhookView(APIView):
    permission_classes = [AllowAny] 

    def post(self, request, *args, **kwargs):
        webhook_signature = request.headers.get('X-Razorpay-Signature')
        webhook_body = request.body
        # Verify webhook signature
        expected_signature = hmac.new(
            settings.RAZORPAY_WEBHOOK_SECRET.encode(),
            webhook_body,
            hashlib.sha256
        ).hexdigest()

        if not hmac.compare_digest(webhook_signature, expected_signature):
            return Response({'error': 'Invalid signature'}, status=400)

        webhook_data = json.loads(webhook_body)
        event = webhook_data.get('event')

        event_type = event.get("event")

        if event_type == "subscription.activated":
            data = event["payload"]["subscription"]["entity"]

            subscription_id = data["id"]
            start_at = data.get("start_at")  
            end_at = data.get("end_at")   

            Subscription.objects.filter(razorpay_subscription_id=subscription_id).update(
                status="active",
                start_date=timezone.datetime.fromtimestamp(start_at, tz=timezone.utc),
                end_date=timezone.datetime.fromtimestamp(end_at, tz=timezone.utc),
            )
        elif event_type == "subscription.completed":
            data = event["payload"]["subscription"]["entity"]
            subscription_id = data["id"]

            Subscription.objects.filter(razorpay_subscription_id=subscription_id).update(
                status="expired"
            )
        elif event_type == "subscription.cancelled":
            sub_id = event["payload"]["subscription"]["entity"]["id"]
            subscription = Subscription.objects.filter(
                razorpay_subscription_id=sub_id
            ).first()
            if subscription:
                subscription.status = "cancelled"
                subscription.cancelled_at = now()
                subscription.save()

        elif event_type == "subscription.pending":
            subscription = event["payload"]["subscription"]["entity"]
            razorpay_id = subscription["id"]

            # Keep status as created (don’t start subscription yet)
            Subscription.objects.filter(razorpay_subscription_id=razorpay_id).update(
                status="created",
                start_date=None,
                end_date=None
        )

        elif event_type == "payment.captured":
            payment = event["payload"]["payment"]["entity"]
            subscription = Subscription.objects.filter(
                razorpay_subscription_id=payment.get("subscription_id")
            ).first()
            Transaction.objects.create(
                subscription=subscription,
                razorpay_payment_id=payment["id"],
                amount=payment["amount"] / 100,
                currency=payment["currency"],
                txn_type="subscription",
                status="success",
            )

        return JsonResponse({"status": "ok"})