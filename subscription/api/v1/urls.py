from django.urls import path
from . import views

urlpatterns = [
    path("plans/", views.PlanViewSet.as_view({"get": "list"}), name="subscription-plans"),
    path("create-subscription/", views.SubscriptionViewSet.as_view({"get": "list", "post": "create"}), name="subscriptions"),
    path("subscriptions/<int:pk>/cancel/", views.SubscriptionViewSet.as_view({"post": "cancel"}), name="cancel-subscription"),
    path("subscriptions/<int:pk>/upgrade/", views.SubscriptionViewSet.as_view({"post": "upgrade"}), name="upgrade-subscription"),
    path("webhook/", views.RazorpayWebhookView.as_view(), name="razorpay-webhook"),
]