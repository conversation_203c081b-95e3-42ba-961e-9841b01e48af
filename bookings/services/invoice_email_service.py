"""
Invoice Email Service for sending invoices to booking users
"""
import logging
import tempfile
import os
from typing import List, Optional
from django.template.loader import render_to_string
from django.conf import settings
from base.email_utils import send_mail_task
from bookings.models import Booking, BookingItinerary

logger = logging.getLogger(__name__)


class InvoiceEmailService:
    """
    Service to handle sending invoice emails for bookings with PDF attachments
    """
    
    @staticmethod
    def send_main_booking_invoice(booking: Booking) -> bool:
        """
        Send main booking invoice to the booking user
        
        Args:
            booking: Booking instance
            
        Returns:
            bool: True if email sent successfully, False otherwise
        """
        logger.info(f"[InvoiceEmailService] Starting send_main_booking_invoice for booking {booking.external_id}")
        
        try:
            if not booking.user or not booking.user.email:
                if not booking.booker_email:
                    logger.warning(f"No email address for booking {booking.external_id}")
                    return False
                recipient_email = booking.booker_email
            else:
                recipient_email = booking.user.email
            
            # Prepare attachments
            attachments = []
            if booking.invoice_file:
                temp_file = InvoiceEmailService._download_pdf_to_temp(booking.invoice_file)
                if temp_file:
                    attachments.append(temp_file)
            
            if not attachments:
                logger.warning(f"No main invoice file found for booking {booking.external_id}")
                return False
            
            # Send email
            success = InvoiceEmailService._send_invoice_email(
                booking=booking,
                recipient_email=recipient_email,
                subject="Your Booking Invoice",
                attachments=attachments,
                invoice_type="main booking"
            )
            
            # Cleanup temp files
            InvoiceEmailService._cleanup_temp_files(attachments)
            
            return success
            
        except Exception as e:
            logger.error(f"Error sending main booking invoice for booking {booking.external_id}: {str(e)}")
            return False
    
    @staticmethod
    def send_type_invoices(booking: Booking, invoice_type: str) -> bool:
        """
        Send invoices of a specific type (Flight, Hotel, Activity, Transfer) to the booking user
        
        Args:
            booking: Booking instance
            invoice_type: Type of invoices to send ('Flight', 'Hotel', 'Activity', 'Transfer')
            
        Returns:
            bool: True if email sent successfully, False otherwise
        """
        logger.info(f"[InvoiceEmailService] Starting send_{invoice_type.lower()}_invoices for booking {booking.external_id}")
        
        try:
            if not booking.user or not booking.user.email:
                if not booking.booker_email:
                    logger.warning(f"No email address for booking {booking.external_id}")
                    return False
                recipient_email = booking.booker_email
            else:
                recipient_email = booking.user.email
            
            # Get all itinerary items of the specified type
            itinerary_items = BookingItinerary.objects.filter(
                booking=booking,
                type=invoice_type
            )
            
            if not itinerary_items.exists():
                logger.warning(f"No {invoice_type.lower()} items found for booking {booking.external_id}")
                return False
            
            # Collect invoice files
            attachments = []
            for item in itinerary_items:
                invoice_file = None
                
                if invoice_type == 'Flight' and item.flight_booking:
                    invoice_file = item.flight_booking.invoice_file
                elif invoice_type == 'Hotel' and item.hotel_booking:
                    invoice_file = item.hotel_booking.invoice_file
                elif invoice_type == 'Activity' and item.activity_booking:
                    invoice_file = item.activity_booking.invoice_file
                elif invoice_type == 'Transfer' and item.transfer_booking:
                    invoice_file = item.transfer_booking.invoice_file
                
                if invoice_file:
                    temp_file = InvoiceEmailService._download_pdf_to_temp(invoice_file)
                    if temp_file:
                        attachments.append(temp_file)
            
            if not attachments:
                logger.warning(f"No {invoice_type.lower()} invoice files found for booking {booking.external_id}")
                return False
            
            # Send email
            success = InvoiceEmailService._send_invoice_email(
                booking=booking,
                recipient_email=recipient_email,
                subject=f"Your {invoice_type} Invoices",
                attachments=attachments,
                invoice_type=f"{invoice_type.lower()} invoices"
            )
            
            # Cleanup temp files
            InvoiceEmailService._cleanup_temp_files(attachments)
            
            return success
            
        except Exception as e:
            logger.error(f"Error sending {invoice_type.lower()} invoices for booking {booking.external_id}: {str(e)}")
            return False
    
    @staticmethod
    def _send_invoice_email(booking: Booking, recipient_email: str, subject: str, 
                           attachments: List[str], invoice_type: str) -> bool:
        """
        Internal method to send invoice email using the template
        
        Args:
            booking: Booking instance
            recipient_email: Recipient email address
            subject: Email subject
            attachments: List of attachment file paths
            invoice_type: Type of invoice for logging
            
        Returns:
            bool: True if email sent successfully
        """
        try:
            # Prepare email context
            user_name = "Dear Customer"
            if booking.user:
                # Use full_name field from the User model
                if booking.user.full_name and booking.user.full_name.strip():
                    user_name = booking.user.full_name.strip()
                else:
                    # Fallback to email username if full_name is empty
                    user_name = booking.user.email.split('@')[0]
            elif booking.booker_email:
                # Fallback to booker email username
                user_name = booking.booker_email.split('@')[0]
            
            context = {
                'user_name': user_name,
                'booking': booking,
                'booking_reference': booking.external_id,
            }
            
            # Render email body using the template
            body = render_to_string('emails/invoice_email.html', context)
            
            # Send email
            logger.info(f"[InvoiceEmailService] Sending {invoice_type} email to {recipient_email} with {len(attachments)} attachments")
            success = send_mail_task(
                subject=subject,
                body=body,
                recipient_list=[recipient_email],
                attachments=attachments
            )
            
            if success:
                logger.info(f"Invoice email sent successfully for booking {booking.external_id} ({invoice_type})")
            else:
                logger.error(f"Failed to send invoice email for booking {booking.external_id} ({invoice_type})")
                
            return success
            
        except Exception as e:
            logger.error(f"Error in _send_invoice_email for booking {booking.external_id}: {str(e)}")
            return False
    
    @staticmethod
    def _download_pdf_to_temp(pdf_file) -> Optional[str]:
        """
        Download PDF from S3 to temporary file for email attachment
        
        Args:
            pdf_file: FileField instance
            
        Returns:
            str: Path to temporary file or None if failed
        """
        try:
            import requests
            
            # Create temporary file
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
            
            # For S3 storage, download from URL
            if hasattr(pdf_file, 'url'):
                logger.debug(f"[InvoiceEmailService] Downloading PDF from URL: {pdf_file.url}")
                response = requests.get(pdf_file.url, timeout=30)
                response.raise_for_status()
                temp_file.write(response.content)
                logger.debug(f"[InvoiceEmailService] PDF downloaded successfully - size: {len(response.content)} bytes")
            else:
                # For local storage, read file directly
                logger.debug(f"[InvoiceEmailService] Reading PDF from local storage")
                pdf_file.open('rb')
                content = pdf_file.read()
                temp_file.write(content)
                pdf_file.close()
                logger.debug(f"[InvoiceEmailService] PDF read successfully - size: {len(content)} bytes")
            
            temp_file.close()
            logger.debug(f"[InvoiceEmailService] PDF downloaded to temp file: {temp_file.name}")
            return temp_file.name
            
        except Exception as e:
            logger.error(f"Error downloading PDF to temp file: {str(e)}")
            if 'temp_file' in locals():
                try:
                    temp_file.close()
                except:
                    pass
            return None
    
    @staticmethod
    def _cleanup_temp_files(file_paths: List[str]):
        """
        Clean up temporary files
        
        Args:
            file_paths: List of file paths to delete
        """
        for file_path in file_paths:
            try:
                if os.path.exists(file_path):
                    os.unlink(file_path)
                    logger.debug(f"[InvoiceEmailService] Cleaned up temp file: {file_path}")
            except Exception as e:
                logger.warning(f"[InvoiceEmailService] Failed to cleanup temp file {file_path}: {str(e)}")
