"""
Invoice Generation Service for different booking types
Handles generation of PDF invoices for flights, hotels, activities, transfers and main booking voucher
"""
import os
import tempfile
from io import BytesIO
from datetime import datetime, date
from decimal import Decimal
from django.template.loader import render_to_string
from django.core.files.base import ContentFile
from django.conf import settings
# Import weasyprint lazily to avoid dependency issues at module level
import logging
import uuid
from bookings.utils import InvoiceS3Service

logger = logging.getLogger(__name__)


class InvoiceGenerationService:
    """
    Service to generate invoices for different booking types
    """
    
    def __init__(self):
        self.invoice_s3_service = InvoiceS3Service()
        logger.info(f"[InvoiceGenerationService] Initialized")

    def _prepare_context_data(self, booking_obj, booking_type: str):
        """
        Prepare context data for invoice template based on booking type
        
        Args:
            booking_obj: Booking object (FlightBooking, HotelBooking, etc.)
            booking_type: Type of booking ('flight', 'hotel', 'transfer', 'activity', 'main')
            
        Returns:
            dict: Context data for template rendering
        """
        logger.info(f"[InvoiceGenerationService] Preparing context for {booking_type} booking")
        
        # Get partner info from related booking
        partner = None
        main_booking = None
        
        if hasattr(booking_obj, 'day_items') and booking_obj.day_items.exists():
            day_item = booking_obj.day_items.first()
            if day_item and day_item.booking:
                main_booking = day_item.booking
                partner = day_item.booking.partner
        
        if booking_type == 'hotel':
            return self._prepare_hotel_context(booking_obj, partner, main_booking)
        elif booking_type == 'flight':
            return self._prepare_flight_context(booking_obj, partner, main_booking)
        elif booking_type == 'transfer':
            return self._prepare_transfer_context(booking_obj, partner, main_booking)
        elif booking_type == 'activity':
            return self._prepare_activity_context(booking_obj, partner, main_booking)
        elif booking_type == 'main':
            return self._prepare_main_booking_context(booking_obj)
        else:
            raise ValueError(f"Unsupported booking type: {booking_type}")

    def _prepare_hotel_context(self, hotel_booking, partner, main_booking):
        """Prepare context data for hotel invoice"""
        logger.debug(f"[InvoiceGenerationService] Preparing hotel context for booking: {hotel_booking.tripjack_booking_id}")
        
        # Extract hotel details from booking_details JSON
        booking_details = hotel_booking.booking_details or {}
        hotel_info = booking_details.get('hInfo', {})
        
        # Basic hotel information
        hotel_name = hotel_booking.hotel_name or hotel_info.get('name', 'Hotel Name Not Available')
        hotel_address = hotel_info.get('ad', 'Address Not Available')
        hotel_rating = hotel_info.get('rt', 0)  # Star rating
        hotel_phone = hotel_info.get('cN', 'Phone Not Available')
        
        # Room and guest information
        room_data = hotel_booking.room_data or []
        guest_name = "Guest Name Not Available"
        room_type = "Room Type Not Available"
        inclusions = "No Inclusions"
        
        # Extract guest information from room data or main booking
        if room_data and len(room_data) > 0:
            first_room = room_data[0]
            guest_details = first_room.get('guest_details', [])
            if guest_details:
                first_guest = guest_details[0]
                guest_name = f"{first_guest.get('first_name', '')} {first_guest.get('last_name', '')}".strip()
            room_type = first_room.get('room_type', room_type)
            inclusions = first_room.get('inclusions', inclusions)
        
        # Fallback to main booking user info
        if guest_name == "Guest Name Not Available" and main_booking:
            if main_booking.user:
                guest_name = f"{main_booking.user.first_name} {main_booking.user.last_name}".strip()
            if not guest_name.strip():
                guest_name = main_booking.booker_email.split('@')[0].title()
        
        # Calculate nights
        check_in = hotel_booking.check_in_date
        check_out = hotel_booking.check_out_date
        nights = (check_out.date() - check_in.date()).days if check_in and check_out else 1
        
        # Format dates
        formatted_check_in_date = check_in.strftime('%d-%b-%Y') if check_in else 'N/A'
        formatted_check_out_date = check_out.strftime('%d-%b-%Y') if check_out else 'N/A'
        formatted_check_in_time = check_in.strftime('%I:%M %p') if check_in else '2:00 PM'
        formatted_check_out_time = check_out.strftime('%I:%M %p') if check_out else '11:00 AM'
        
        # Pricing breakdown (estimated from total amount)
        total_amount = float(hotel_booking.amount)
        # Rough estimation: 80% base fare, 20% taxes
        base_fare = total_amount * 0.8
        taxes_fees = total_amount * 0.2
        
        # Generate booking reference and confirmation number
        booking_reference = f"P{hotel_booking.tripjack_booking_id}" if hotel_booking.tripjack_booking_id else f"P{uuid.uuid4().hex[:8].upper()}"
        confirmation_no = hotel_booking.confirmation_number or f"CW{uuid.uuid4().hex[:8]}"
        
        context = {
            # Company/Partner Info
                            'company_name': 'Zuumm',
            'company_logo_url': self._get_logo_url(),
            
            # Booking Reference Info
            'booking_reference': booking_reference,
            'confirmation_no': confirmation_no,
            'date_of_issue': datetime.now().strftime('%d-%b-%Y'),
            
            # Hotel Information
            'hotel_name': hotel_name,
            'hotel_rating': hotel_rating,
            'hotel_address': hotel_address,
            'hotel_phone': hotel_phone,
            
            # Check-in/Check-out Details
            'check_in_date': formatted_check_in_date,
            'check_in_time': formatted_check_in_time,
            'check_out_date': formatted_check_out_date,
            'check_out_time': formatted_check_out_time,
            'total_rooms': hotel_booking.number_of_rooms,
            'total_nights': f"{nights} Night{'s' if nights != 1 else ''}",
            
            # Guest Information
            'guest_name': guest_name,
            'room_type': room_type,
            'inclusions': inclusions,
            
            # Pricing
            'base_fare': f"₹ {base_fare:,.0f}",
            'taxes_fees': f"₹ {taxes_fees:,.0f}",
            'total_amount': f"₹ {total_amount:,.0f}",
            
            # Additional Information
            'special_requests': 'NA',
            'cancellation_policy': 'Full refund if you cancel this booking by check-in date.',
            
            # Contact Information
            'support_email': '<EMAIL>',
            'support_phone': '+91 9591767328',
            
            # Raw booking details for additional processing
            'booking_details': booking_details,
        }
        
        logger.debug(f"[InvoiceGenerationService] Hotel context prepared for: {hotel_name}")
        return context

    def _prepare_flight_context(self, flight_booking, partner, main_booking):
        """Prepare context data for flight invoice"""
        logger.debug(f"[InvoiceGenerationService] Preparing flight context for booking: {flight_booking.tripjack_booking_id}")
        
        # Extract flight details from booking_details JSON
        booking_details = flight_booking.booking_details or {}
        
        # Extract basic flight information
        booking_reference = f"IBA{flight_booking.tripjack_booking_id}"
        pnr_code = flight_booking.tripjack_pnr or f"BLR-MAA: VCM58R"
        
        # Extract trip details - handle different API response structures
        trip_details = booking_details.get('tripDetails', [])
        order_details = booking_details.get('order', {})
        item_infos = order_details.get('itemInfos', {})
        air_info = item_infos.get('AIR', {})
        
        # Default flight information
        departure_code = "BLR"
        departure_city = "Bengaluru"
        departure_airport = "Kempegowda International Airport"
        arrival_code = "MAA"
        arrival_city = "Chennai"
        arrival_airport = "Chennai International Airport"
        departure_time = "07:15"
        arrival_time = "08:20"
        departure_date = "29 Jul 2025"
        arrival_date = "29 Jul 2025"
        airline_name = "IndiGo"
        airline_code = "6E"
        flight_status = "CONFIRMED"
        class_type = "ECONOMY(M)"
        aircraft_type = "6E - 6269"
        duration = "1 hour 5 minute"
        baggage_allowance = "6E Eats choice of the day (veg) and beverage"
        
        # Try to extract real flight data if available
        if trip_details and len(trip_details) > 0:
            first_trip = trip_details[0]
            segments = first_trip.get('sI', [])
            if segments:
                segment = segments[0]
                da = segment.get('da', {})
                aa = segment.get('aa', {})
                
                # Departure info
                departure_code = da.get('code', departure_code)
                departure_city = da.get('city', departure_city)
                departure_airport = da.get('name', departure_airport)
                departure_time = segment.get('dt', departure_time)[-5:]  # Get time part
                
                # Arrival info  
                arrival_code = aa.get('code', arrival_code)
                arrival_city = aa.get('city', arrival_city)
                arrival_airport = aa.get('name', arrival_airport)
                arrival_time = segment.get('at', arrival_time)[-5:]  # Get time part
                
                # Format dates
                if segment.get('dt'):
                    try:
                        from datetime import datetime
                        dt_obj = datetime.fromisoformat(segment['dt'].replace('Z', '+00:00'))
                        departure_date = dt_obj.strftime('%d %b %Y')
                    except:
                        pass
                        
                if segment.get('at'):
                    try:
                        from datetime import datetime
                        at_obj = datetime.fromisoformat(segment['at'].replace('Z', '+00:00'))
                        arrival_date = at_obj.strftime('%d %b %Y')
                    except:
                        pass
                
                # Airline info
                fD = segment.get('fD', {})
                airline_code = fD.get('aI', {}).get('code', airline_code)
                airline_name = fD.get('aI', {}).get('name', airline_name)
                aircraft_type = f"{airline_code} - {fD.get('fN', '6269')}"
                
                # Duration
                duration_mins = segment.get('duration', 65)
                hours = duration_mins // 60
                mins = duration_mins % 60
                duration = f"{hours} hour {mins} minute"
        
        # Extract passenger information
        passengers = []
        traveller_infos = air_info.get('travellerInfos', [])
        
        if not traveller_infos and flight_booking.passengers.exists():
            # Fallback to passenger model data
            for passenger in flight_booking.passengers.all():
                passengers.append({
                    'title': passenger.title,
                    'first_name': passenger.first_name,
                    'last_name': passenger.last_name,
                    'type': passenger.passenger_type,
                    'frequent_flyer': '',
                    'baggage': '',
                    'meal': baggage_allowance,
                    'seat': ''
                })
        else:
            # Extract from booking details
            for traveller in traveller_infos:
                passengers.append({
                    'title': traveller.get('ti', 'MR'),
                    'first_name': traveller.get('fN', 'Satvik'),
                    'last_name': traveller.get('lN', 'Loganathan'),
                    'type': 'Adult',
                    'frequent_flyer': '',
                    'baggage': '',
                    'meal': baggage_allowance,
                    'seat': ''
                })
        
        # If no passengers found, add default
        if not passengers:
            passengers = [{
                'title': 'MR',
                'first_name': 'Satvik',
                'last_name': 'Loganathan',
                'type': 'Adult',
                'frequent_flyer': '',
                'baggage': '',
                'meal': baggage_allowance,
                'seat': ''
            }]
        
        # Calculate pricing (estimate from total amount)
        total_amount = float(flight_booking.amount)
        base_fare = total_amount * 0.72  # ~72% base fare
        taxes_fees = total_amount * 0.23  # ~23% taxes
        add_ons = total_amount * 0.05    # ~5% add-ons
        
        context = {
            # Company/Partner Info
                            'company_name': 'Zuumm',
            'company_logo_url': self._get_logo_url(),
            
            # Booking Reference Info
            'booking_reference': booking_reference,
            'date_of_issue': datetime.now().strftime('%d %b %Y'),
            'pnr_code': pnr_code,
            
            # Flight Information
            'segment_number': '1',
            'departure_city': departure_city,
            'departure_code': departure_code,
            'departure_airport': departure_airport,
            'departure_time': departure_time,
            'departure_date': departure_date,
            'arrival_code': arrival_code,
            'arrival_city': arrival_city, 
            'arrival_airport': arrival_airport,
            'arrival_time': arrival_time,
            'arrival_date': arrival_date,
            'airline_name': airline_name,
            'airline_code': airline_code,
            'flight_status': flight_status,
            'class_type': class_type,
            'aircraft_type': aircraft_type,
            'duration': duration,
            'flight_type': 'NON_STOP',
            'seat_count': f"6E - {len(passengers)*1000 + 269}",
            'baggage_allowance': baggage_allowance,
            
            # Passenger Information
            'passengers': passengers,
            
            # Pricing
            'base_fare': f"{base_fare:.0f}",
            'taxes_fees': f"{taxes_fees:.0f}",
            'add_ons': f"{add_ons:.0f}",
            'total_amount': f"{total_amount:.0f}",
            
            # Contact Information
                            'support_email': '<EMAIL>',
            'support_phone': '+91 9591767328',
            'support_whatsapp': '+91 9606496359',
            'visa_phone': '81510 17317',
            'visa_email': '<EMAIL>',
            
            # Raw booking details for additional processing
            'booking_details': booking_details,
        }
        
        logger.debug(f"[InvoiceGenerationService] Flight context prepared for: {airline_name} {departure_code}-{arrival_code}")
        return context

    def _prepare_transfer_context(self, transfer_booking, partner, main_booking):
        """Prepare context data for transfer invoice"""
        logger.debug(f"[InvoiceGenerationService] Preparing transfer context for booking: {transfer_booking.transferz_booking_id}")
        
        # Extract transfer details from booking_details JSON and model fields
        booking_details = transfer_booking.booking_details or {}
        
        # Basic transfer information
        booking_reference = f"TXF{transfer_booking.transferz_booking_id}"
        transferz_booking_id = transfer_booking.transferz_booking_id
        confirmation_number = transfer_booking.confirmation_number or transferz_booking_id
        
        # Location and timing information
        pickup_location = transfer_booking.pickup_location or "Pickup Location"
        dropoff_location = transfer_booking.dropoff_location or "Drop-off Location"
        
        # Format pickup date and time
        pickup_datetime = transfer_booking.pickup_datetime
        if pickup_datetime:
            pickup_date = pickup_datetime.strftime('%d %b %Y')
            pickup_time = pickup_datetime.strftime('%H:%M')
        else:
            pickup_date = "TBD"
            pickup_time = "TBD"
        
        # Vehicle and service details
        vehicle_type = transfer_booking.vehicle_type or "Standard Vehicle"
        service_type = transfer_booking.service_type or "Private Transfer"
        passenger_count = transfer_booking.passenger_count or 1
        
        # Passenger information
        passenger_name = transfer_booking.passenger_name or "Passenger"
        passenger_email = transfer_booking.passenger_email or main_booking.booker_email if main_booking else "<EMAIL>"
        passenger_phone = transfer_booking.passenger_phone or ""
        
        # Flight information for airport transfers
        flight_number = transfer_booking.flight_number or ""
        
        # Special requirements
        special_requirements = transfer_booking.special_requirements or ""
        
        # Pricing information
        total_amount = float(transfer_booking.amount)
        currency = transfer_booking.currency or 'USD'
        
        # Calculate pricing breakdown (estimate based on total)
        base_fare = total_amount * 0.75  # ~75% base fare
        service_charges = total_amount * 0.15  # ~15% service charges
        taxes_fees = total_amount * 0.10  # ~10% taxes
        
        # Status information
        transfer_status = transfer_booking.status or 'CONFIRMED'
        if transfer_status == 'SUCCESS':
            transfer_status = 'CONFIRMED'
        
        # Estimated duration (basic calculation or from API if available)
        estimated_duration = "30-60 minutes"
        
        # Try to extract additional details from Transferz API response
        if booking_details:
            journeys = booking_details.get('journeys', [])
            if journeys:
                first_journey = journeys[0]
                
                # Extract pricing from journey if available
                price_summary = first_journey.get('priceSummary', {})
                if price_summary.get('price'):
                    total_amount = float(price_summary['price'])
                    currency = price_summary.get('currency', currency)
                    
                    # Recalculate breakdown with actual amount
                    base_fare = total_amount * 0.75
                    service_charges = total_amount * 0.15
                    taxes_fees = total_amount * 0.10
                
                # Extract journey details if available
                route_info = first_journey.get('route', {})
                if route_info:
                    pickup_location = route_info.get('from', {}).get('name', pickup_location)
                    dropoff_location = route_info.get('to', {}).get('name', dropoff_location)
                
                # Extract estimated duration if available
                duration_mins = first_journey.get('estimatedDurationMinutes')
                if duration_mins:
                    hours = duration_mins // 60
                    mins = duration_mins % 60
                    if hours > 0:
                        estimated_duration = f"{hours}h {mins}m"
                    else:
                        estimated_duration = f"{mins} minutes"
        
        context = {
            # Company/Partner Info
                            'company_name': 'Zuumm',
            'company_logo_url': self._get_logo_url(),
            
            # Booking Reference Info
            'booking_reference': booking_reference,
            'date_of_issue': datetime.now().strftime('%d %b %Y'),
            'confirmation_number': confirmation_number,
            'transferz_booking_id': transferz_booking_id,
            'transfer_status': transfer_status,
            
            # Transfer Route Information
            'pickup_location': pickup_location,
            'dropoff_location': dropoff_location,
            'pickup_date': pickup_date,
            'pickup_time': pickup_time,
            'estimated_duration': estimated_duration,
            'flight_number': flight_number,
            
            # Vehicle and Service Information
            'vehicle_type': vehicle_type,
            'service_type': service_type,
            'passenger_count': passenger_count,
            
            # Passenger Information
            'passenger_name': passenger_name,
            'passenger_email': passenger_email,
            'passenger_phone': passenger_phone,
            
            # Special Requirements
            'special_requirements': special_requirements,
            
            # Pricing Information
            'currency': currency,
            'total_amount': f"{total_amount:.2f}",
            'base_fare': f"{base_fare:.2f}",
            'service_charges': f"{service_charges:.2f}",
            'taxes_fees': f"{taxes_fees:.2f}",
            
            # Contact Information
                            'support_email': '<EMAIL>',
            'support_phone': '+91 9591767328',
            'support_whatsapp': '+91 9606496359',
            
            # Raw booking details for additional processing
            'booking_details': booking_details,
        }
        
        logger.debug(f"[InvoiceGenerationService] Transfer context prepared for: {pickup_location} to {dropoff_location}")
        return context

    def _prepare_activity_context(self, activity_booking, partner, main_booking):
        """Prepare context data for activity invoice"""
        logger.debug(f"[InvoiceGenerationService] Preparing activity context for booking: {activity_booking.id}")
        
        # Activity bookings are manually uploaded, no automatic generation needed
        context = {
                            'company_name': 'Zuumm',
            'company_logo_url': self._get_logo_url(),
            'booking_reference': f"A{activity_booking.id}",
        }
        
        return context

    def _prepare_main_booking_context(self, main_booking):
        """Prepare context data for main booking voucher"""
        logger.debug(f"[InvoiceGenerationService] Preparing main booking context for booking: {main_booking.id}")
        
        from bookings.models import BookingItinerary
        from datetime import datetime, timedelta
        
        # Calculate payment status
        total_amount = float(main_booking.total_amount)
        amount_paid = float(main_booking.amount_paid)
        remaining_amount = total_amount - amount_paid
        
        # Determine payment scenario
        is_fully_paid = remaining_amount <= 0.01  # Account for floating point precision
        is_partially_paid = amount_paid > 0 and not is_fully_paid
        is_unpaid = amount_paid <= 0.01
        
        # Get booking itinerary items grouped by day
        booking_items = BookingItinerary.objects.filter(booking=main_booking).order_by('day_number', 'order')
        
        # Build itinerary data
        itinerary_days = {}
        current_day = None
        day_data = None
        
        for item in booking_items:
            if current_day != item.day_number:
                # Save previous day
                if day_data:
                    itinerary_days[current_day] = day_data
                
                # Start new day
                current_day = item.day_number
                day_data = {
                    'day_number': current_day,
                    'day_title': f"Day {current_day}",
                    'items': []
                }
            
            # Add item data
            item_data = {
                'type': item.type,
                'order': item.order
            }
            
            # Extract specific booking details based on type
            if item.type == 'Flight' and item.flight_booking:
                flight = item.flight_booking
                item_data.update({
                    'title': f"Flight {flight.tripjack_booking_id}",
                    'description': f"Departure - Arrival",  # Can be enhanced with actual route data
                    'status': flight.status,
                    'amount': float(flight.amount),
                    'details': flight.booking_details
                })
            
            elif item.type == 'Hotel' and item.hotel_booking:
                hotel = item.hotel_booking
                item_data.update({
                    'title': hotel.hotel_name or "Hotel Booking",
                    'description': f"Check-in: {hotel.check_in_date} | Check-out: {hotel.check_out_date}",
                    'status': hotel.status,
                    'amount': float(hotel.amount),
                    'details': hotel.booking_details
                })
            
            elif item.type == 'Transfer' and item.transfer_booking:
                transfer = item.transfer_booking
                item_data.update({
                    'title': f"Transfer - {transfer.vehicle_type}",
                    'description': f"{transfer.pickup_location} to {transfer.dropoff_location}",
                    'status': transfer.status,
                    'amount': float(transfer.amount),
                    'details': transfer.booking_details
                })
            
            elif item.type == 'Activity' and item.activity_booking:
                activity = item.activity_booking
                item_data.update({
                    'title': activity.activity_name or "Activity",
                    'description': activity.location or "",
                    'status': activity.status,
                    'amount': float(activity.amount),
                    'details': activity.meta_information
                })
            
            # Only add to day if we found a valid booking (skip items without actual bookings)
            if item_data.get('title'):  # Only add if title was set (meaning booking exists)
                day_data['items'].append(item_data)
            else:
                logger.debug(f"[InvoiceGenerationService] Skipping {item.type} item without actual booking")
        
        # Add final day
        if day_data:
            itinerary_days[current_day] = day_data
        
        # Package information
        package_info = {}
        if main_booking.package:
            package = main_booking.package
            package_info = {
                'title': package.title or "Travel Package",
                'duration': f"{len(itinerary_days)}N/{len(itinerary_days)+1}D" if itinerary_days else "Multi-day",
                'highlights': package.highlights if hasattr(package, 'highlights') else [],
                'inclusions': package.inclusions if hasattr(package, 'inclusions') else [],
                'exclusions': package.exclusions if hasattr(package, 'exclusions') else []
            }
        
        # Customer information
        customer_name = main_booking.booker_user.get_full_name() if main_booking.booker_user else "Valued Customer"
        customer_email = main_booking.booker_email
        customer_phone = main_booking.booker_phone_number
        
        # Emergency contact (mock data - can be enhanced with real data)
        emergency_contact = {
            'name': 'John Doe',
            'phone': '+917654321323',
            'email': '<EMAIL>'
        }
        
        # Voucher details
        voucher_number = f"ZM-{datetime.now().year}-{str(main_booking.id).zfill(3)}"
        booking_date = main_booking.created_at.strftime('%d-%b-%Y')
        
        # Travel dates
        if itinerary_days:
            first_day = min(itinerary_days.keys())
            last_day = max(itinerary_days.keys())
            # Assuming each day is consecutive - can be enhanced with actual dates
            start_date = main_booking.created_at.date()
            end_date = start_date + timedelta(days=len(itinerary_days))
            travel_dates = f"{start_date.strftime('%b %d')}-{end_date.strftime('%d, %Y')}"
        else:
            travel_dates = "TBD"
        
        # Payment schedule data
        payment_schedule = []
        
        if is_fully_paid:
            # Full payment scenario
            payment_schedule.append({
                'type': 'full',
                'description': 'Full Payment',
                'amount': total_amount,
                'date': main_booking.created_at.strftime('%b %d, %Y'),
                'status': 'paid'
            })
        elif is_partially_paid:
            # Partial payment scenario - determine how many parts
            payment_count = 2  # Default to 2 parts
            
            if amount_paid >= total_amount * 0.8:
                # If paid 80% or more, assume it's nearly complete (2 parts, both paid)
                part1_amount = total_amount * 0.5
                part2_amount = total_amount - part1_amount
                
                payment_schedule.extend([
                    {
                        'type': 'part1',
                        'description': 'Part 1',
                        'amount': part1_amount,
                        'date': main_booking.created_at.strftime('%b %d, %Y'),
                        'status': 'paid'
                    },
                    {
                        'type': 'part2',
                        'description': 'Part 2',
                        'amount': part2_amount,
                        'date': main_booking.created_at.strftime('%b %d, %Y'),
                        'status': 'paid'
                    }
                ])
            else:
                # Partial payment (first part paid, second pending)
                part1_amount = amount_paid
                part2_amount = remaining_amount
                due_date = main_booking.created_at.date() + timedelta(days=2)
                
                payment_schedule.extend([
                    {
                        'type': 'part1',
                        'description': 'Part 1',
                        'amount': part1_amount,
                        'date': main_booking.created_at.strftime('%b %d, %Y'),
                        'status': 'paid'
                    },
                    {
                        'type': 'part2',
                        'description': 'Part 2',
                        'amount': part2_amount,
                        'date': due_date.strftime('%b %d, %Y'),
                        'status': 'pending'
                    }
                ])
        
        # Contact and support information
        support_info = {
            'email': '<EMAIL>',
            'phone': '+91 9591767328',
            'whatsapp': '+91 9606496359'
        }
        
        # Travel information (mock data - can be enhanced with destination-specific info)
        travel_info = {
            'weather': {
                'season': 'December Weather',
                'temperature': 'Mild winter, 8-15°C',
                'advice': 'Pack layers, light jackets, comfortable walking shoes, and umbrella for occasional rain.'
            },
            'emergency_contacts': {
                'local': 'Local emergency services: 112',
                'embassy': 'Embassy contact details available on request'
            },
            'currency': {
                'local': 'Local currency accepted',
                'cards': 'International cards widely accepted'
            },
            'packing': {
                'essentials': 'Comfortable walking shoes, weather-appropriate clothing',
                'documents': 'Valid passport and travel insurance'
            },
            'restaurants': 'Recommended local dining options available in destination guide'
        }
        
        context = {
            # Company/Partner Info
            'company_name': main_booking.partner.entity_name if main_booking.partner else 'Zuumm',
            'company_logo_url': self._get_logo_url(),
            
            # Booking Information
            'booking_reference': f"B{main_booking.external_id or main_booking.id}",
            'voucher_number': voucher_number,
            'booking_date': booking_date,
            
            # Customer Information
            'customer_name': customer_name,
            'customer_email': customer_email,
            'customer_phone': customer_phone,
            'emergency_contact': emergency_contact,
            
            # Package Information
            'package_info': package_info,
            'travel_dates': travel_dates,
            'guest_count': "2 Adults",  # Can be enhanced with actual guest data
            'duration': package_info.get('duration', 'Multi-day'),
            
            # Itinerary Data
            'itinerary_days': itinerary_days,
            
            # Payment Information
            'total_amount': total_amount,
            'amount_paid': amount_paid,
            'remaining_amount': remaining_amount,
            'payment_schedule': payment_schedule,
            'is_fully_paid': is_fully_paid,
            'is_partially_paid': is_partially_paid,
            'is_unpaid': is_unpaid,
            
            # Support and Contact
            'support_info': support_info,
            'travel_info': travel_info,
            
            # Links and Actions
            'payment_link': 'https://app.zuumm.co/Xm2V/cikoefyj',  # Can be dynamic
            'policies_link': 'https://www.zuumm.ai/privacy',
            'terms_link': 'https://www.zuumm.ai/terms',
            
            # Raw booking data for additional processing
            'main_booking': main_booking,
        }
        
        logger.debug(f"[InvoiceGenerationService] Main booking context prepared - Payment Status: {'Fully Paid' if is_fully_paid else 'Partially Paid' if is_partially_paid else 'Unpaid'}")
        return context

    def _get_main_booking_template_name(self, context):
        """Determine which main booking template to use based on payment status"""
        # For now, always use invoice2OneShortFullPaid.html for all main booking invoices
        template_name = 'invoices/invoice2OneShortFullPaid.html'
        logger.debug(f"[InvoiceGenerationService] Selected template: {template_name} (simplified - one template for all)")
        
        return template_name

    def _get_logo_url(self):
        """Get company logo URL for templates"""
        from django.contrib.staticfiles import finders
        
        logo_path = finders.find('images/admin_logo.png')
        if logo_path:
            return f"file://{logo_path}"
        else:
            return f"{settings.STATIC_URL}images/admin_logo.png"

    def generate_invoice(self, booking_obj, booking_type: str):
        """
        Generate invoice PDF for a booking
        
        Args:
            booking_obj: Booking object
            booking_type: Type of booking ('flight', 'hotel', 'transfer', 'activity', 'main')
            
        Returns:
            tuple: (pdf_content, context_data) - PDF as bytes and context used
        """
        logger.info(f"[InvoiceGenerationService] Starting invoice generation for {booking_type} booking")
        
        try:
            # Prepare context data
            context = self._prepare_context_data(booking_obj, booking_type)
            
            # Get template name
            if booking_type == 'main':
                # Determine template based on payment status
                template_name = self._get_main_booking_template_name(context)
            else:
                template_name = f'invoices/{booking_type}_invoice.html'
            
            # Render HTML
            html_content = render_to_string(template_name, context)
            logger.debug(f"[InvoiceGenerationService] HTML rendered for {booking_type} invoice")
            
            # Generate PDF
            pdf_content = self._generate_pdf_from_html(html_content)
            logger.info(f"[InvoiceGenerationService] PDF generated successfully for {booking_type} booking")
            
            return pdf_content, context
            
        except Exception as e:
            logger.error(f"[InvoiceGenerationService] Error generating {booking_type} invoice: {str(e)}")
            raise

    def _generate_pdf_from_html(self, html_content: str):
        """Generate PDF from HTML content using existing PDF utilities"""
        logger.debug(f"[InvoiceGenerationService] Converting HTML to PDF using existing utilities")
        
        try:
            # Use the existing PDF generation utility
            from base.pdf_utils import PDFGenerator
            
            # Create a temporary HTML file since PDFGenerator expects template path
            import tempfile
            with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False) as f:
                f.write(html_content)
                temp_html_path = f.name
            
            try:
                # Generate PDF using existing utility
                pdf_bytes = PDFGenerator.get_pdf_bytes(temp_html_path, {})
                logger.debug(f"[InvoiceGenerationService] PDF conversion completed using PDFGenerator")
                return pdf_bytes
                
            finally:
                # Clean up temporary file
                import os
                if os.path.exists(temp_html_path):
                    os.unlink(temp_html_path)
            
        except Exception as e:
            logger.error(f"[InvoiceGenerationService] Error converting HTML to PDF: {str(e)}")
            # Fallback to direct weasyprint if existing utility fails
            try:
                from weasyprint import HTML, CSS
                from weasyprint.text.fonts import FontConfiguration
                
                font_config = FontConfiguration()
                css_content = """
                @page {
                    size: A4;
                    margin: 1cm;
                }
                body {
                    font-family: 'Arial', sans-serif;
                    font-size: 12px;
                    line-height: 1.4;
                    color: #333;
                }
                .invoice-container {
                    max-width: 800px;
                    margin: 0 auto;
                    padding: 20px;
                }
                """
                
                css = CSS(string=css_content, font_config=font_config)
                html_doc = HTML(string=html_content)
                pdf_bytes = html_doc.write_pdf(stylesheets=[css])
                
                logger.debug(f"[InvoiceGenerationService] PDF conversion completed using fallback method")
                return pdf_bytes
                
            except Exception as fallback_error:
                logger.error(f"[InvoiceGenerationService] Fallback PDF generation also failed: {str(fallback_error)}")
                raise

    def _make_context_json_serializable(self, context):
        """
        Convert context data to be JSON serializable by removing Django model instances
        and other non-serializable objects
        
        Args:
            context: Dictionary containing template context
            
        Returns:
            dict: JSON-serializable version of context
        """
        import json
        from datetime import datetime, date
        from decimal import Decimal
        from django.db import models
        
        def serialize_value(obj):
            """Recursively serialize values to JSON-compatible types"""
            if obj is None:
                return None
            elif isinstance(obj, (str, int, float, bool)):
                return obj
            elif isinstance(obj, (datetime, date)):
                return obj.isoformat()
            elif isinstance(obj, Decimal):
                return float(obj)
            elif isinstance(obj, models.Model):
                # For Django models, return just the ID or key identifier
                model_name = obj.__class__.__name__
                if hasattr(obj, 'id'):
                    return f"{model_name}(id={obj.id})"
                else:
                    return f"{model_name}(object)"
            elif isinstance(obj, dict):
                return {key: serialize_value(value) for key, value in obj.items()}
            elif isinstance(obj, (list, tuple)):
                return [serialize_value(item) for item in obj]
            else:
                # For other objects, try to convert to string
                try:
                    # Test if it's already JSON serializable
                    json.dumps(obj)
                    return obj
                except (TypeError, ValueError):
                    return str(obj)
        
        try:
            serializable_context = serialize_value(context)
            logger.debug(f"[InvoiceGenerationService] Context made JSON serializable")
            return serializable_context
        except Exception as e:
            logger.warning(f"[InvoiceGenerationService] Error making context JSON serializable: {str(e)}")
            # Return a minimal context if serialization fails
            return {
                "error": "Context serialization failed",
                "error_details": str(e),
                "timestamp": datetime.now().isoformat()
            }

    def save_invoice_to_booking(self, booking_obj, booking_type: str):
        """
        Generate invoice and save it to the booking object
        
        Args:
            booking_obj: Booking object
            booking_type: Type of booking
            
        Returns:
            str: URL of saved invoice file
        """
        logger.info(f"[InvoiceGenerationService] Saving invoice for {booking_type} booking")
        
        try:
            # Generate invoice
            pdf_content, context = self.generate_invoice(booking_obj, booking_type)
            
            # Create filename
            booking_id = getattr(booking_obj, 'tripjack_booking_id', None) or \
                        getattr(booking_obj, 'transferz_booking_id', None) or \
                        getattr(booking_obj, 'id', 'unknown')
            filename = f"{booking_type}_invoice_{booking_id}_{uuid.uuid4().hex[:8]}.pdf"
            
            # Save to FileField
            pdf_file = ContentFile(pdf_content, name=filename)
            booking_obj.invoice_file.save(filename, pdf_file, save=False)
            
            # Save context data to invoice_json (make it JSON serializable)
            booking_obj.invoice_json = self._make_context_json_serializable(context)
            booking_obj.save()
            
            logger.info(f"[InvoiceGenerationService] Invoice saved successfully: {booking_obj.invoice_file.url}")
            return booking_obj.invoice_file.url
            
        except Exception as e:
            logger.error(f"[InvoiceGenerationService] Error saving invoice: {str(e)}")
            raise
