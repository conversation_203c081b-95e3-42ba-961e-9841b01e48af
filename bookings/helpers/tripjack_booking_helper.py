import requests
from django.conf import settings
from bookings.logging_config import booking_metrics
import logging
import json

# Configure logger for Tripjack API helper
logger = logging.getLogger(__name__)


class TripjackBookingHelper:
    """
    A helper class to encapsulate all API interactions with the Tripjack service.
    This reflects the official API documentation for flights and hotels.
    """

    def __init__(self):
        self.api_key = settings.TRIPJACK_API_KEY
        self.base_url = settings.TRIPJACK_API_URL
        self.headers = {
            'Content-Type': 'application/json',
            'apikey': self.api_key
        }
        self.timeout = 45 # Default timeout for requests
        
        logger.info(f"[TripjackHelper] Initialized with base URL: {self.base_url}")
        logger.debug(f"[TripjackHelper] API Key configured: {'Yes' if self.api_key else 'No'}")
        logger.debug(f"[TripjackHelper] API Key length: {len(self.api_key) if self.api_key else 0}")
        logger.debug(f"[TripjackHelper] Headers configured: {list(self.headers.keys())}")

    def _make_request(self, method, endpoint, payload=None):
        """
        Generic method to make requests to Tripjack API with comprehensive error handling.
        Returns structured response instead of raising exceptions for API errors.
        """
        url = f"{self.base_url}/{endpoint}"
        logger.info(f"[TripjackHelper] Making {method} request to: {endpoint}")
        logger.debug(f"[TripjackHelper] Full URL: {url}")
        logger.debug(f"[TripjackHelper] Request method: {method}")
        logger.debug(f"[TripjackHelper] Request timeout: {self.timeout}s")

        # Sanitize payload for logging
        if payload:
            logger.debug(f"[TripjackHelper] Starting payload sanitization")
            sanitized_payload = self._sanitize_payload_for_logging(payload)
            logger.debug(f"[TripjackHelper] Payload sanitization completed")
            logger.debug(f"[TripjackHelper] Request payload (sanitized): {json.dumps(sanitized_payload, indent=2)}")
            logger.debug(f"[TripjackHelper] Payload size: {len(json.dumps(payload))} characters")

        logger.debug(f"[TripjackHelper] Sending request with timeout: {self.timeout}s")

        try:
            if method.upper() == 'POST':
                response = requests.post(url, json=payload, headers=self.headers, timeout=self.timeout)
            elif method.upper() == 'GET':
                response = requests.get(url, params=payload, headers=self.headers, timeout=self.timeout)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")

            logger.info(f"[TripjackHelper] Response received - Status Code: {response.status_code}")
            logger.debug(f"[TripjackHelper] Response headers: {dict(response.headers)}")
            logger.debug(f"[TripjackHelper] Response encoding: {response.encoding}")
            logger.debug(f"[TripjackHelper] Response elapsed time: {response.elapsed.total_seconds()}s")
            logger.debug(f"[TripjackHelper] Response content: {response.text}")

            # Parse JSON response
            try:
                response_data = response.json()
            except ValueError as e:
                logger.error(f"[TripjackHelper] Failed to parse JSON response: {str(e)}")
                logger.error(f"[TripjackHelper] Raw response text: {response.text}")
                return {
                    'success': False,
                    'error': 'Invalid JSON response from Tripjack',
                    'raw_response': response.text,
                    'status_code': response.status_code
                }

            # Check if response indicates success
            if response.status_code == 200:
                # Check Tripjack's internal status
                status_info = response_data.get('status', {})
                if status_info.get('success', True):  # Default to True if not specified
                    logger.info(f"[TripjackHelper] API call successful")
                    return response_data
                else:
                    # Tripjack returned 200 but with success=false
                    logger.warning(f"[TripjackHelper] Tripjack API returned success=false")
                    logger.warning(f"[TripjackHelper] Tripjack status: {status_info}")
                    errors = response_data.get('errors', [])
                    if errors:
                        logger.warning(f"[TripjackHelper] Tripjack errors: {errors}")
                    
                    return {
                        'success': False,
                        'tripjack_errors': errors,
                        'tripjack_status': status_info,
                        'status_code': response.status_code,
                        'full_response': response_data
                    }
            else:
                # Non-200 status code
                logger.error(f"[TripjackHelper] API call failed with status {response.status_code}")
                
                # Try to extract Tripjack error details
                errors = response_data.get('errors', [])
                status_info = response_data.get('status', {})
                
                if errors:
                    logger.error(f"[TripjackHelper] Tripjack errors: {errors}")
                    error_messages = [err.get('message', 'Unknown error') for err in errors]
                    error_codes = [err.get('errCode', 'Unknown') for err in errors]
                    
                    return {
                        'success': False,
                        'tripjack_errors': errors,
                        'tripjack_status': status_info,
                        'error_messages': error_messages,
                        'error_codes': error_codes,
                        'status_code': response.status_code,
                        'full_response': response_data
                    }
                else:
                    return {
                        'success': False,
                        'error': f'HTTP {response.status_code}: {response.text}',
                        'status_code': response.status_code,
                        'full_response': response_data
                    }

        except requests.exceptions.Timeout:
            logger.error(f"[TripjackHelper] Request timeout after {self.timeout}s")
            return {
                'success': False,
                'error': f'Request timeout after {self.timeout} seconds',
                'error_type': 'timeout'
            }
        except requests.exceptions.ConnectionError as e:
            logger.error(f"[TripjackHelper] Connection error: {str(e)}")
            return {
                'success': False,
                'error': f'Connection error: {str(e)}',
                'error_type': 'connection'
            }
        except requests.exceptions.RequestException as e:
            logger.error(f"[TripjackHelper] Request error: {str(e)}")
            return {
                'success': False,
                'error': f'Request error: {str(e)}',
                'error_type': 'request'
            }
        except Exception as e:
            logger.error(f"[TripjackHelper] Unexpected error: {str(e)}")
            return {
                'success': False,
                'error': f'Unexpected error: {str(e)}',
                'error_type': 'unexpected'
            }

    # --- Flight APIs ---

    def review_flight(self, price_id: str):
        """ Calls Tripjack's Flight Review API: POST /fms/v1/review """
        logger.info(f"[TripjackHelper] Starting flight review for price_id: {price_id}")
        logger.debug(f"[TripjackHelper] Price ID length: {len(price_id)}")
        logger.debug(f"[TripjackHelper] Flight review endpoint: fms/v1/air/review")
        
        endpoint = "fms/v1/review"
        payload = {"priceIds": [price_id]}
        
        logger.debug(f"[TripjackHelper] Flight review payload: {payload}")
        
        response = self._make_request('POST', endpoint, payload)
        logger.info(f"[TripjackHelper] Flight review completed successfully")
        
        # Log important response details
        booking_id = response.get('bookingId')
        total_price_info = response.get('totalPriceInfo', {})
        total_price = total_price_info.get('totalFareDetail', {}).get('fC', {}).get('TF')
        
        logger.info(f"[TripjackHelper] Flight review result - Booking ID: {booking_id}, Total Price: {total_price}")
        logger.debug(f"[TripjackHelper] Booking ID length: {len(booking_id) if booking_id else 0}")
        logger.debug(f"[TripjackHelper] Total price info keys: {list(total_price_info.keys()) if total_price_info else 'None'}")
        
        # Log additional flight details if available
        if 'tripDetails' in response:
            trip_details = response['tripDetails']
            logger.debug(f"[TripjackHelper] Trip details available - Count: {len(trip_details) if isinstance(trip_details, list) else 'Not a list'}")
        
        return response

    def hold_flight(self, flight_payload):
        """
        Holds a flight booking without payment (Step 3 in the KT document).
        This calls the /oms/v1/air/book endpoint WITHOUT payment information.
        """
        endpoint = "oms/v1/air/book"
        logger.info(f"[TripjackHelper] Starting flight hold for booking_id: {flight_payload.get('bookingId')}")
        logger.debug(f"[TripjackHelper] Flight hold endpoint: {endpoint}")
        logger.debug(f"[TripjackHelper] Flight payload keys: {list(flight_payload.keys())}")
        
        # Log passenger and SSR counts for debugging
        traveller_info = flight_payload.get('travellerInfo', [])
        logger.debug(f"[TripjackHelper] Number of passengers: {len(traveller_info)}")
        
        ssr_baggage = flight_payload.get('ssrBaggageInfos', [])
        ssr_meals = flight_payload.get('ssrMealInfos', [])
        ssr_seats = flight_payload.get('ssrSeatInfos', [])
        logger.debug(f"[TripjackHelper] SSR counts - Baggage: {len(ssr_baggage)}, Meals: {len(ssr_meals)}, Seats: {len(ssr_seats)}")
        
        # Ensure no payment info is included for hold
        if 'paymentInfos' in flight_payload:
            logger.warning(f"[TripjackHelper] Removing payment info from hold request")
            flight_payload = {k: v for k, v in flight_payload.items() if k != 'paymentInfos'}
        
        logger.info(f"[TripjackHelper] HOLD MODE - No payment info included")
        
        response = self._make_request('POST', endpoint, flight_payload)
        
        # Handle new structured response format
        if not response.get('success', True):
            # API call failed
            logger.error(f"[TripjackHelper] Flight hold failed")
            
            # Extract error information
            tripjack_errors = response.get('tripjack_errors', [])
            error_messages = response.get('error_messages', [])
            error_codes = response.get('error_codes', [])
            
            if tripjack_errors:
                logger.error(f"[TripjackHelper] Tripjack errors: {tripjack_errors}")
                
            # Raise a custom exception with Tripjack error details
            from bookings.api.v1.services import BookingException
            
            if tripjack_errors:
                primary_error = tripjack_errors[0]
                error_message = primary_error.get('message', 'Unknown Tripjack error')
                error_code = primary_error.get('errCode', 'Unknown')
                error_details = primary_error.get('details', error_message)
                
                raise BookingException(
                    f"Tripjack API Error [{error_code}]: {error_message}",
                    details={
                        'tripjack_errors': tripjack_errors,
                        'error_codes': error_codes,
                        'error_messages': error_messages,
                        'api_endpoint': endpoint,
                        'status_code': response.get('status_code'),
                        'booking_id': flight_payload.get('bookingId')
                    }
                )
            else:
                # Generic error
                error_msg = response.get('error', 'Unknown error occurred')
                raise BookingException(
                    f"Flight hold failed: {error_msg}",
                    details={
                        'api_endpoint': endpoint,
                        'status_code': response.get('status_code'),
                        'booking_id': flight_payload.get('bookingId'),
                        'response': response
                    }
                )
        
        logger.info(f"[TripjackHelper] Flight hold successful")
        
        # Extract PNR from successful response
        pnr_details = response.get('pnrDetails', {})
        if pnr_details:
            pnr = pnr_details.get('pnr')
            if pnr:
                logger.info(f"[TripjackHelper] Flight PNR obtained: {pnr}")
            else:
                logger.warning(f"[TripjackHelper] No PNR found in pnrDetails")
        else:
            logger.warning(f"[TripjackHelper] No pnrDetails found in response")
        
        return response

    def book_flight(self, flight_payload: dict):
        """ Calls Tripjack's Flight Booking API: POST /oms/v1/air/book """
        booking_id = flight_payload.get('bookingId', 'Unknown')
        logger.info(f"[TripjackHelper] Starting flight booking for booking_id: {booking_id}")
        logger.debug(f"[TripjackHelper] Flight booking endpoint: oms/v1/air/book")
        logger.debug(f"[TripjackHelper] Flight payload keys: {list(flight_payload.keys())}")
        
        # Log passenger count
        traveller_info = flight_payload.get('travellerInfo', [])
        logger.debug(f"[TripjackHelper] Number of passengers: {len(traveller_info)}")
        
        # Log payment info
        payment_infos = flight_payload.get('paymentInfos', [])
        if payment_infos:
            total_amount = sum(float(p.get('amount', 0)) for p in payment_infos)
            logger.debug(f"[TripjackHelper] Total booking amount: {total_amount}")
        
        # Log SSR details
        ssr_baggage = flight_payload.get('ssrBaggageInfos', [])
        ssr_meals = flight_payload.get('ssrMealInfos', [])
        ssr_seats = flight_payload.get('ssrSeatInfos', [])
        logger.debug(f"[TripjackHelper] SSR counts - Baggage: {len(ssr_baggage)}, Meals: {len(ssr_meals)}, Seats: {len(ssr_seats)}")
        
        endpoint = "oms/v1/air/book"
        
        response = self._make_request('POST', endpoint, flight_payload)
        logger.info(f"[TripjackHelper] Flight booking completed successfully")
        
        # Log important response details
        order = response.get('order', {})
        booking_id = order.get('bookingId', flight_payload.get('bookingId'))
        pnr_details = order.get('itemInfos', {}).get('AIR', {}).get('travellerInfos', [{}])[0].get('pnrDetails', {})
        pnr = next(iter(pnr_details.values()), None) if pnr_details else None
        
        logger.info(f"[TripjackHelper] Flight booking successful")
        logger.info(f"[TripjackHelper] Flight PNR: {pnr}")
        logger.info(f"[TripjackHelper] Flight order booking_id: {booking_id}")
        logger.debug(f"[TripjackHelper] Flight PNR details: {pnr_details}")
        
        return response

    def confirm_flight(self, confirm_payload: dict):
        """
        Confirms a previously held flight booking with payment (Step 4 in the KT document).
        This calls the /oms/v1/air/confirm-book endpoint WITH payment information.
        """
        endpoint = "oms/v1/air/confirm-book"
        logger.info(f"[TripjackHelper] Starting flight confirmation for booking_id: {confirm_payload.get('bookingId')}")
        logger.debug(f"[TripjackHelper] Flight confirm endpoint: {endpoint}")
        logger.debug(f"[TripjackHelper] Confirm payload keys: {list(confirm_payload.keys())}")
        
        # Log payment information
        payment_infos = confirm_payload.get('paymentInfos', [])
        if payment_infos:
            total_amount = sum(float(p.get('amount', 0)) for p in payment_infos)
            logger.debug(f"[TripjackHelper] Confirmation amount: {total_amount}")
        
        logger.info(f"[TripjackHelper] CONFIRM MODE - Payment info included")
        
        booking_metrics.increment_metric('tripjack_api_calls')
        
        response = self._make_request('POST', endpoint, confirm_payload)
        
        # Handle new structured response format
        if not response.get('success', True):
            # API call failed
            logger.error(f"[TripjackHelper] Flight confirmation failed")
            
            # Extract error information
            tripjack_errors = response.get('tripjack_errors', [])
            error_messages = response.get('error_messages', [])
            error_codes = response.get('error_codes', [])
            
            if tripjack_errors:
                logger.error(f"[TripjackHelper] Tripjack errors: {tripjack_errors}")
                
            # Raise a custom exception with Tripjack error details
            from bookings.api.v1.services import BookingException
            
            if tripjack_errors:
                primary_error = tripjack_errors[0]
                error_message = primary_error.get('message', 'Unknown Tripjack error')
                error_code = primary_error.get('errCode', 'Unknown')
                error_details = primary_error.get('details', error_message)
                
                raise BookingException(
                    f"Tripjack Confirmation Error [{error_code}]: {error_message}",
                    details={
                        'tripjack_errors': tripjack_errors,
                        'error_codes': error_codes,
                        'error_messages': error_messages,
                        'api_endpoint': endpoint,
                        'status_code': response.get('status_code'),
                        'booking_id': confirm_payload.get('bookingId')
                    }
                )
            else:
                # Generic error
                error_msg = response.get('error', 'Unknown error occurred')
                raise BookingException(
                    f"Flight confirmation failed: {error_msg}",
                    details={
                        'api_endpoint': endpoint,
                        'status_code': response.get('status_code'),
                        'booking_id': confirm_payload.get('bookingId'),
                        'response': response
                    }
                )
        
        logger.info(f"[TripjackHelper] Flight confirmation successful")
        
        # Extract confirmation details from successful response
        order = response.get('order', {})
        status = order.get('status', 'Unknown')
        booking_id = order.get('bookingId', confirm_payload.get('bookingId'))
        
        logger.info(f"[TripjackHelper] Confirmation status: {status}")
        logger.info(f"[TripjackHelper] Confirmed booking_id: {booking_id}")
        
        return response

    def release_flight(self, booking_id: str, pnr_list: list):
        """
        Release/cancel a held flight (for cleanup of orphaned holds)
        """
        endpoint = "oms/v1/air/unhold"
        payload = {
            "bookingId": booking_id,
            "pnrs": pnr_list
        }
        
        logger.info(f"[TripjackHelper] Starting flight release for booking_id: {booking_id}")
        logger.debug(f"[TripjackHelper] Flight release endpoint: {endpoint}")
        logger.debug(f"[TripjackHelper] PNRs to release: {pnr_list}")
        
        booking_metrics.increment_metric('tripjack_api_calls')
        
        response = self._make_request('POST', endpoint, payload)
        
        logger.info(f"[TripjackHelper] Flight release successful")
        logger.debug(f"[TripjackHelper] Release response: {response}")
        
        return response

    def get_flight_booking_details(self, booking_id: str):
        """
        Retrieve complete flight booking details including PNRs, ticket numbers, etc.
        Endpoint: POST /oms/v1/booking-details
        """
        endpoint = "oms/v1/booking-details"
        payload = {
            "bookingId": booking_id,
            "requirePaxPricing": True  # Get the most detailed pricing information
        }
        
        logger.info(f"[TripjackHelper] Starting flight booking details retrieval for booking_id: {booking_id}")
        logger.debug(f"[TripjackHelper] Flight details endpoint: {endpoint}")
        logger.debug(f"[TripjackHelper] Details payload: {payload}")
        
        booking_metrics.increment_metric('tripjack_api_calls')
        
        response = self._make_request('POST', endpoint, payload)
        
        # Extract key information from response
        order = response.get('order', {})
        booking_status = order.get('status', 'Unknown')
        item_infos = order.get('itemInfos', {})
        air_info = item_infos.get('AIR', {})
        traveller_infos = air_info.get('travellerInfos', [])
        
        # Extract ticket numbers if available
        ticket_numbers = []
        for traveller in traveller_infos:
            ticket_details = traveller.get('ticketNumberDetails', {})
            if ticket_details:
                ticket_numbers.extend(list(ticket_details.values()))
        
        logger.info(f"[TripjackHelper] Flight booking details retrieved successfully")
        logger.info(f"[TripjackHelper] Booking status: {booking_status}")
        logger.info(f"[TripjackHelper] Number of ticket numbers: {len(ticket_numbers)}")
        logger.debug(f"[TripjackHelper] Ticket numbers: {ticket_numbers}")
        
        return response

    def get_hotel_booking_details(self, booking_id: str):
        """
        Retrieve complete hotel booking details including confirmation number, voucher data, etc.
        Endpoint: POST /hms/v1/booking-detail
        """
        endpoint = "hms/v1/booking-detail"
        payload = {"bookingId": booking_id}
        
        logger.info(f"[TripjackHelper] Starting hotel booking details retrieval for booking_id: {booking_id}")
        logger.debug(f"[TripjackHelper] Hotel details endpoint: {endpoint}")
        logger.debug(f"[TripjackHelper] Details payload: {payload}")
        
        booking_metrics.increment_metric('tripjack_api_calls')
        
        response = self._make_request('POST', endpoint, payload)
        
        # Extract key information from response
        confirmation_no = response.get('confirmationNo', 'Unknown')
        booking_status = response.get('status', 'Unknown')
        hotel_name = response.get('hotelName', 'Unknown')
        voucher_data = response.get('voucherData', {})
        
        logger.info(f"[TripjackHelper] Hotel booking details retrieved successfully")
        logger.info(f"[TripjackHelper] Hotel name: {hotel_name}")
        logger.info(f"[TripjackHelper] Confirmation number: {confirmation_no}")
        logger.info(f"[TripjackHelper] Booking status: {booking_status}")
        logger.debug(f"[TripjackHelper] Voucher data available: {bool(voucher_data)}")
        
        return response

    def download_invoice(self, booking_id: str, booking_type: str):
        """
        Attempts to find and return an invoice/voucher download URL from Tripjack booking details.
        
        Args:
            booking_id: Tripjack booking ID
            booking_type: 'flight' or 'hotel'
            
        Returns:
            str: Download URL if found, None otherwise
        """
        logger.info(f"[TripjackHelper] Attempting to download invoice for {booking_type} booking_id: {booking_id}")
        
        try:
            # Get booking details first
            if booking_type.lower() == 'flight':
                details_response = self.get_flight_booking_details(booking_id)
                logger.debug(f"[TripjackHelper] Invoice endpoint: oms/v1/booking-details")
            elif booking_type.lower() == 'hotel':
                details_response = self.get_hotel_booking_details(booking_id)
                logger.debug(f"[TripjackHelper] Invoice endpoint: hms/v1/booking-detail")
            else:
                logger.error(f"[TripjackHelper] Unsupported booking type: {booking_type}")
                return None
            
            # Comprehensive search for invoice/ticket URLs
            download_url = None
            possible_url_fields = [
                'invoiceUrl', 'voucherUrl', 'ticketUrl', 'downloadUrl', 'documentUrl',
                'eTicketUrl', 'pdfUrl', 'receiptUrl', 'confirmationUrl'
            ]
            
            logger.debug(f"[TripjackHelper] Searching for invoice URL in response...")
            logger.debug(f"[TripjackHelper] Response top-level keys: {list(details_response.keys())}")
            
            # Search in top level
            for field in possible_url_fields:
                if field in details_response and details_response[field]:
                    download_url = details_response[field]
                    logger.info(f"[TripjackHelper] Found invoice URL in top level '{field}': {download_url}")
                    break
            
            # Search in order section
            if not download_url and 'order' in details_response:
                order = details_response['order']
                logger.debug(f"[TripjackHelper] Searching in order section, keys: {list(order.keys())}")
                for field in possible_url_fields:
                    if field in order and order[field]:
                        download_url = order[field]
                        logger.info(f"[TripjackHelper] Found invoice URL in order.{field}: {download_url}")
                        break
            
            # Search in itemInfos section
            if not download_url and 'itemInfos' in details_response:
                item_infos = details_response['itemInfos']
                logger.debug(f"[TripjackHelper] Searching in itemInfos section, keys: {list(item_infos.keys())}")
                
                # For flights, search in AIR section
                if booking_type.lower() == 'flight' and 'AIR' in item_infos:
                    air_info = item_infos['AIR']
                    logger.debug(f"[TripjackHelper] Searching in AIR section, keys: {list(air_info.keys())}")
                    for field in possible_url_fields:
                        if field in air_info and air_info[field]:
                            download_url = air_info[field]
                            logger.info(f"[TripjackHelper] Found invoice URL in AIR.{field}: {download_url}")
                            break
                    
                    # Search deeper in trip infos
                    if not download_url and 'tripInfos' in air_info:
                        for trip_info in air_info['tripInfos']:
                            logger.debug(f"[TripjackHelper] Searching in tripInfo, keys: {list(trip_info.keys())}")
                            for field in possible_url_fields:
                                if field in trip_info and trip_info[field]:
                                    download_url = trip_info[field]
                                    logger.info(f"[TripjackHelper] Found invoice URL in tripInfo.{field}: {download_url}")
                                    break
                            if download_url:
                                break
                
                # For hotels, search in HOTEL section
                elif booking_type.lower() == 'hotel' and 'HOTEL' in item_infos:
                    hotel_info = item_infos['HOTEL']
                    logger.debug(f"[TripjackHelper] Searching in HOTEL section, keys: {list(hotel_info.keys())}")
                    for field in possible_url_fields:
                        if field in hotel_info and hotel_info[field]:
                            download_url = hotel_info[field]
                            logger.info(f"[TripjackHelper] Found invoice URL in HOTEL.{field}: {download_url}")
                            break
            
            # Search in traveller infos (sometimes tickets are per passenger)
            if not download_url and 'itemInfos' in details_response:
                item_infos = details_response['itemInfos']
                if booking_type.lower() == 'flight' and 'AIR' in item_infos:
                    air_info = item_infos['AIR']
                    if 'travellerInfos' in air_info:
                        logger.debug(f"[TripjackHelper] Searching in travellerInfos")
                        for traveller in air_info['travellerInfos']:
                            logger.debug(f"[TripjackHelper] Checking traveller, keys: {list(traveller.keys())}")
                            for field in possible_url_fields:
                                if field in traveller and traveller[field]:
                                    download_url = traveller[field]
                                    logger.info(f"[TripjackHelper] Found invoice URL in traveller.{field}: {download_url}")
                                    break
                            if download_url:
                                break
            
            if download_url:
                logger.info(f"[TripjackHelper] Invoice/voucher URL found: {download_url}")
                return download_url
            else:
                logger.warning(f"[TripjackHelper] No invoice/voucher URL found in response for {booking_type} booking")
                
                # Log the full response structure for debugging
                logger.debug(f"[TripjackHelper] Full response keys: {list(details_response.keys())}")
                if 'order' in details_response:
                    logger.debug(f"[TripjackHelper] Order keys: {list(details_response['order'].keys())}")
                if 'itemInfos' in details_response:
                    for key, value in details_response['itemInfos'].items():
                        if isinstance(value, dict):
                            logger.debug(f"[TripjackHelper] ItemInfos.{key} keys: {list(value.keys())}")
                
                return None
                
        except Exception as e:
            logger.error(f"[TripjackHelper] Error downloading invoice for {booking_type} booking: {str(e)}")
            return None

    # --- Hotel APIs ---

    def review_hotel(self, search_id: str = None, hotel_id: str = None, option_id: str = None):
        """ Calls Tripjack's Hotel Review API: POST /hms/v1/hotel-review """
        logger.info(f"[TripjackHelper] Starting hotel review - search_id: {search_id}, hotel_id: {hotel_id}, option_id: {option_id}")
        logger.debug(f"[TripjackHelper] Hotel review endpoint: hms/v1/hotel-review")
        logger.debug(f"[TripjackHelper] Hotel ID length: {len(hotel_id) if hotel_id else 0}")
        logger.debug(f"[TripjackHelper] Option ID length: {len(option_id) if option_id else 0}")
        
        endpoint = "hms/v1/hotel-review"
        payload = {
            "hotelId": hotel_id,
            "optionId": option_id
        }
        
        # Only include searchId if provided
        if search_id:
            payload["searchId"] = search_id
        
        logger.debug(f"[TripjackHelper] Hotel review payload: {payload}")
        
        response = self._make_request('POST', endpoint, payload)
        logger.info(f"[TripjackHelper] Hotel review completed successfully")
        
        # Log important response details from actual structure
        booking_id = response.get('bookingId')
        h_info = response.get('hInfo', {})
        hotel_name = h_info.get('name')
        
        # Get price from ops array
        ops = h_info.get('ops', [])
        total_price = ops[0].get('tp') if ops else 0
        
        logger.info(f"[TripjackHelper] Hotel review result - Booking ID: {booking_id}, Hotel: {hotel_name}, Total Price: {total_price}")
        logger.debug(f"[TripjackHelper] Hotel booking ID length: {len(booking_id) if booking_id else 0}")
        logger.debug(f"[TripjackHelper] Hotel info keys: {list(h_info.keys()) if h_info else 'No hInfo data'}")
        
        # Log additional hotel details if available
        if ops:
            logger.debug(f"[TripjackHelper] Options count: {len(ops)}")
            if 'ris' in ops[0]:
                rooms = ops[0]['ris']
                logger.debug(f"[TripjackHelper] Rooms count: {len(rooms)}")
        
        return response

    def book_hotel(self, hotel_payload: dict):
        """ Calls Tripjack's Hotel Booking API: POST /oms/v1/hotel/book """
        booking_id = hotel_payload.get('bookingId', 'Unknown')
        logger.info(f"[TripjackHelper] Starting hotel booking for booking_id: {booking_id}")
        logger.debug(f"[TripjackHelper] Hotel booking endpoint: oms/v1/hotel/book")
        logger.debug(f"[TripjackHelper] Hotel payload keys: {list(hotel_payload.keys())}")
        
        # Log traveller info
        room_traveller_info = hotel_payload.get('roomTravellerInfo', [])
        logger.debug(f"[TripjackHelper] Number of room traveller groups: {len(room_traveller_info)}")
        
        # Log delivery info
        delivery_info = hotel_payload.get('deliveryInfo', {})
        if delivery_info:
            emails = delivery_info.get('emails', [])
            contacts = delivery_info.get('contacts', [])
            logger.debug(f"[TripjackHelper] Delivery emails: {len(emails)}, contacts: {len(contacts)}")
        
        # Log payment info
        payment_infos = hotel_payload.get('paymentInfos', [])
        if payment_infos:
            total_amount = sum(float(p.get('amount', 0)) for p in payment_infos)
            logger.debug(f"[TripjackHelper] Total booking amount: {total_amount}")
        
        endpoint = "oms/v1/hotel/book"
        
        response = self._make_request('POST', endpoint, hotel_payload)
        
        # Handle new structured response format
        if not response.get('success', True):
            # API call failed
            logger.error(f"[TripjackHelper] Hotel booking failed")
            
            # Extract error information
            tripjack_errors = response.get('tripjack_errors', [])
            error_messages = response.get('error_messages', [])
            error_codes = response.get('error_codes', [])
            
            if tripjack_errors:
                logger.error(f"[TripjackHelper] Tripjack errors: {tripjack_errors}")
                
            # Raise a custom exception with Tripjack error details
            from bookings.api.v1.services import BookingException
            
            if tripjack_errors:
                primary_error = tripjack_errors[0]
                error_message = primary_error.get('message', 'Unknown Tripjack error')
                error_code = primary_error.get('errCode', 'Unknown')
                error_details = primary_error.get('details', error_message)
                
                raise BookingException(
                    f"Tripjack Hotel API Error [{error_code}]: {error_message}",
                    details={
                        'tripjack_errors': tripjack_errors,
                        'error_codes': error_codes,
                        'error_messages': error_messages,
                        'api_endpoint': endpoint,
                        'status_code': response.get('status_code'),
                        'booking_id': hotel_payload.get('bookingId')
                    }
                )
            else:
                # Generic error
                error_msg = response.get('error', 'Unknown error occurred')
                raise BookingException(
                    f"Hotel booking failed: {error_msg}",
                    details={
                        'api_endpoint': endpoint,
                        'status_code': response.get('status_code'),
                        'booking_id': hotel_payload.get('bookingId'),
                        'response': response
                    }
                )
        
        logger.info(f"[TripjackHelper] Hotel booking completed successfully")
        
        # Log important response details - simplified success response
        response_booking_id = response.get('bookingId', booking_id)
        status_info = response.get('status', {})
        success = status_info.get('success', False)
        http_status = status_info.get('httpStatus', 'Unknown')
        
        logger.info(f"[TripjackHelper] Hotel booking result - Booking ID: {response_booking_id}, Success: {success}, HTTP Status: {http_status}")
        logger.debug(f"[TripjackHelper] Response keys: {list(response.keys())}")
        
        return response
    
    def _sanitize_payload_for_logging(self, payload):
        """
        Sanitize sensitive information in payload for logging.
        Masks passport numbers, GST numbers, and other sensitive data.
        """
        if not isinstance(payload, dict):
            return payload
            
        sanitized = payload.copy()
        
        # Sanitize traveller information
        if 'travellerInfo' in sanitized:
            logger.debug(f"[TripjackHelper] Sanitizing traveller info for {len(sanitized['travellerInfo'])} passengers")
            sanitized_travellers = []
            for traveller in sanitized['travellerInfo']:
                sanitized_traveller = traveller.copy()
                # Mask passport number
                if 'pNum' in sanitized_traveller:
                    sanitized_traveller['pNum'] = '***MASKED***'
                sanitized_travellers.append(sanitized_traveller)
            sanitized['travellerInfo'] = sanitized_travellers
        
        # Sanitize GST information
        if 'gstInfo' in sanitized and sanitized['gstInfo']:
            if 'gstNumber' in sanitized['gstInfo']:
                sanitized['gstInfo']['gstNumber'] = '***MASKED***'
        
        # Count masked items for logging
        masked_count = 0
        if 'travellerInfo' in payload:
            for traveller in payload['travellerInfo']:
                if 'pNum' in traveller:
                    masked_count += 1
        
        if masked_count > 0:
            logger.debug(f"[TripjackHelper] Masked {masked_count} passport numbers")
        else:
            logger.debug(f"[TripjackHelper] Masked items: None")
            
        return sanitized
