import logging
import json
import requests
from datetime import datetime
from django.conf import settings
from django.core.cache import cache
from requests.exceptions import HTTPError, RequestException

# Configure logger for Transferz API helper
logger = logging.getLogger(__name__)


class TransferzBookingHelper:
    """
    Helper class for Transferz API interactions.
    Based on team's FastAPI implementation with dynamic token and API key generation.
    
    Authentication Flow:
    1. Generate access token using email/password
    2. Create API key using access token
    3. Use API key for all booking operations
    
    API Documentation:
    - Auth: https://gateway.staging.transferz.com/auth
    - API: https://warpdrive.staging.transferz.com
    """
    
    # Redis key constants
    TOKEN_KEY = "transferz:access_token"
    API_KEY_KEY = "transferz:api_key"
    API_KEY_ID_KEY = "transferz:api_key_id"
    
    def __init__(self, partner=None):
        """
        Initialize TransferzBookingHelper
        
        Args:
            partner: Partner object (not used in this auth flow but kept for compatibility)
        """
        self.partner = partner
        
        # Get URLs from settings
        self.auth_url = getattr(settings, 'TRANSFERZ_AUTH_URL', 'https://gateway.staging.transferz.com/auth')
        self.api_url = getattr(settings, 'TRANSFERZ_API_URL', 'https://warpdrive.staging.transferz.com')
        
        # Get credentials from settings
        self.email = getattr(settings, 'TRANSFERZ_EMAIL', '<EMAIL>')
        self.password = getattr(settings, 'TRANSFERZ_PASSWORD', 'sXaM8eE8fNqE')
        
        self.timeout = 30
        
        logger.info(f"[TransferzHelper] Initialized with auth URL: {self.auth_url}")
        logger.info(f"[TransferzHelper] API URL: {self.api_url}")
        logger.info(f"[TransferzHelper] Email: {self.email}")
        if self.partner:
            logger.info(f"[TransferzHelper] Partner: {self.partner.entity_name}")

    def _make_request(self, method: str, url: str, headers=None, json_data=None, timeout=None):
        """Make HTTP request with error handling"""
        if timeout is None:
            timeout = self.timeout
        
        logger.info(f"[TransferzHelper] Making {method} request to: {url}")
        
        # Log request details for debugging
        if json_data:
            logger.debug(f"[TransferzHelper] Request payload: {json_data}")
        if headers:
            # Sanitize headers for logging (hide API keys)
            safe_headers = {k: ('***HIDDEN***' if 'api' in k.lower() or 'auth' in k.lower() else v) for k, v in headers.items()}
            logger.debug(f"[TransferzHelper] Request headers: {safe_headers}")
        
        try:
            response = requests.request(
                method=method, 
                url=url, 
                headers=headers, 
                json=json_data, 
                timeout=timeout
            )
            response.raise_for_status()
            
            logger.info(f"[TransferzHelper] Response received - Status Code: {response.status_code}")
            return response.json()
            
        except HTTPError as e:
            logger.error(f"[TransferzHelper] HTTP error for {method} {url}: {str(e)}")
            
            # Log response details for debugging
            if hasattr(e, 'response') and e.response is not None:
                try:
                    error_response = e.response.json()
                    logger.error(f"[TransferzHelper] Error response body: {error_response}")
                except:
                    logger.error(f"[TransferzHelper] Error response text: {e.response.text}")
                logger.error(f"[TransferzHelper] Error response status: {e.response.status_code}")
                logger.error(f"[TransferzHelper] Error response headers: {dict(e.response.headers)}")
            
            if e.response.status_code == 401:
                # Clear cached credentials on 401
                self._clear_cached_credentials()
            raise
        except RequestException as e:
            logger.error(f"[TransferzHelper] Request error for {method} {url}: {str(e)}")
            raise

    def _clear_cached_credentials(self):
        """Clear all cached credentials from Redis"""
        try:
            cache.delete_many([self.TOKEN_KEY, self.API_KEY_KEY, self.API_KEY_ID_KEY])
            logger.info("[TransferzHelper] Cleared cached Transferz credentials")
        except Exception as e:
            logger.error(f"[TransferzHelper] Error clearing cached credentials: {str(e)}")

    def _get_access_token(self):
        """Get access token, refreshing if necessary"""
        # Check Redis cache first
        cached_token = cache.get(self.TOKEN_KEY)
        if cached_token:
            logger.debug("[TransferzHelper] Using cached access token")
            return cached_token

        # Generate new token
        logger.info("[TransferzHelper] Generating new Transferz access token")
        auth_data = {
            "email": self.email,
            "password": self.password
        }

        headers = {
            "Accept": "application/json",
            "Content-Type": "application/json"
        }

        response = self._make_request(
            "POST",
            f"{self.auth_url}/auth/generate-token",
            headers=headers,
            json_data=auth_data
        )

        access_token = response["accessToken"]
        expires_in = response["expiresInSeconds"]

        # Cache with 5 minute buffer before expiry
        cache_ttl = max(expires_in - 300, 60)
        cache.set(self.TOKEN_KEY, access_token, cache_ttl)

        logger.info(f"[TransferzHelper] Cached new access token for {cache_ttl} seconds")
        return access_token

    def _get_api_key(self):
        """Get API key, creating if necessary"""
        logger.debug(f"[TransferzHelper] Starting _get_api_key process")
        
        # Check Redis cache first
        try:
            cached_api_key = cache.get(self.API_KEY_KEY)
            logger.debug(f"[TransferzHelper] Redis cache lookup for API key: {'Found' if cached_api_key else 'Not found'}")
            
            if cached_api_key:
                logger.debug(f"[TransferzHelper] Using cached API key - Length: {len(cached_api_key)}")
                logger.debug(f"[TransferzHelper] Cached API key prefix: {cached_api_key[:10]}...")
                return cached_api_key
        except Exception as e:
            logger.error(f"[TransferzHelper] Error accessing Redis cache for API key: {str(e)}")

        # Create new API key
        logger.info("[TransferzHelper] Creating new Transferz API key")
        access_token = self._get_access_token()
        
        logger.debug(f"[TransferzHelper] Access token obtained for API key creation - Length: {len(access_token) if access_token else 0}")

        headers = {
            "Accept": "application/json",
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }

        api_key_data = {
            "description": "Zuumm Django Backend"
        }

        try:
            logger.debug(f"[TransferzHelper] Making API key creation request to: {self.auth_url}/api-keys/me")
            response = self._make_request(
                "POST",
                f"{self.auth_url}/api-keys/me",
                headers=headers,
                json_data=api_key_data
            )

            api_key = response["key"]
            api_key_id = response["id"]
            expires_str = response["expires"]
            
            logger.debug(f"[TransferzHelper] API key creation response - Key length: {len(api_key)}, ID: {api_key_id}, Expires: {expires_str}")

            # Parse expiry date and calculate TTL
            expires_dt = datetime.strptime(expires_str, "%Y-%m-%d %H:%M:%S")
            now = datetime.now()
            ttl_seconds = int((expires_dt - now).total_seconds())

            # Cache with 1 day buffer before expiry
            cache_ttl = max(ttl_seconds - 86400, 3600)
            
            logger.debug(f"[TransferzHelper] Caching API key with TTL: {cache_ttl} seconds")

            try:
                cache.set(self.API_KEY_KEY, api_key, cache_ttl)
                cache.set(self.API_KEY_ID_KEY, str(api_key_id), cache_ttl)
                logger.debug(f"[TransferzHelper] API key successfully cached in Redis")
                
                # Verify cache write
                cached_verify = cache.get(self.API_KEY_KEY)
                logger.debug(f"[TransferzHelper] Cache write verification: {'Success' if cached_verify == api_key else 'Failed'}")
                
            except Exception as cache_e:
                logger.error(f"[TransferzHelper] Error caching API key in Redis: {str(cache_e)}")

            logger.info(f"[TransferzHelper] Cached new API key for {cache_ttl} seconds")
            return api_key

        except HTTPError as e:
            if e.response.status_code == 401:
                # Token expired, clear cache and retry once
                logger.warning("[TransferzHelper] Token expired during API key creation, retrying")
                self._clear_cached_credentials()
                access_token = self._get_access_token()
                headers["Authorization"] = f"Bearer {access_token}"

                response = self._make_request(
                    "POST",
                    f"{self.auth_url}/api-keys/me",
                    headers=headers,
                    json_data=api_key_data
                )

                api_key = response["key"]
                api_key_id = response["id"]
                expires_str = response["expires"]

                expires_dt = datetime.strptime(expires_str, "%Y-%m-%d %H:%M:%S")
                now = datetime.now()
                ttl_seconds = int((expires_dt - now).total_seconds())
                cache_ttl = max(ttl_seconds - 86400, 3600)

                cache.set(self.API_KEY_KEY, api_key, cache_ttl)
                cache.set(self.API_KEY_ID_KEY, str(api_key_id), cache_ttl)

                logger.info(f"[TransferzHelper] Cached new API key after retry for {cache_ttl} seconds")
                return api_key
            raise

    def _delete_api_key_on_401(self):
        """Delete the current API key when we get 401"""
        try:
            api_key_id = cache.get(self.API_KEY_ID_KEY)
            if not api_key_id:
                logger.warning("[TransferzHelper] No API key ID found to delete")
                return

            access_token = self._get_access_token()
            headers = {"Authorization": f"Bearer {access_token}"}

            self._make_request(
                "DELETE", 
                f"{self.auth_url}/api-keys/{api_key_id}", 
                headers=headers
            )

            logger.info(f"[TransferzHelper] Deleted API key {api_key_id}")

        except (HTTPError, RequestException) as e:
            logger.error(f"[TransferzHelper] Error deleting API key: {str(e)}")
        finally:
            # Clear cached credentials regardless
            self._clear_cached_credentials()

    def _make_api_request(self, method: str, endpoint: str, payload=None):
        """Make authenticated API request to Transferz API"""
        logger.info(f"[TransferzHelper] Starting API request: {method} {endpoint}")
        
        # Check Redis connection first
        try:
            from django.core.cache import cache
            cache_test = cache.get('test_key_redis_connection', 'not_found')
            logger.debug(f"[TransferzHelper] Redis connection test result: {cache_test}")
        except Exception as e:
            logger.error(f"[TransferzHelper] Redis connection failed: {str(e)}")
        
        # Get API key with detailed logging
        logger.debug(f"[TransferzHelper] Attempting to get API key from cache/generation")
        api_key = self._get_api_key()
        
        # Log API key details (safely)
        if api_key:
            logger.info(f"[TransferzHelper] API key obtained successfully - Length: {len(api_key)}")
            logger.debug(f"[TransferzHelper] API key prefix: {api_key[:10]}...")
        else:
            logger.error(f"[TransferzHelper] Failed to obtain API key!")
            raise Exception("No API key available for Transferz requests")

        headers = {
            "X-API-Key": api_key,
            "accept": "application/json",
            "content-type": "application/json"
        }

        url = f"{self.api_url}/{endpoint}"
        logger.info(f"[TransferzHelper] Full request URL: {url}")
        logger.debug(f"[TransferzHelper] Request headers (safe): {{'X-API-Key': 'HIDDEN', 'accept': headers.get('accept'), 'content-type': headers.get('content-type')}}")
        
        if payload:
            logger.debug(f"[TransferzHelper] Request payload: {payload}")
            logger.debug(f"[TransferzHelper] Payload type: {type(payload)}")
            logger.debug(f"[TransferzHelper] Payload JSON serializable test:")
            try:
                import json
                json_str = json.dumps(payload)
                logger.debug(f"[TransferzHelper] Payload JSON length: {len(json_str)}")
            except Exception as e:
                logger.error(f"[TransferzHelper] Payload JSON serialization failed: {str(e)}")

        try:
            response = self._make_request(method, url, headers=headers, json_data=payload)
            logger.info(f"[TransferzHelper] API request completed successfully")
            return response

        except HTTPError as e:
            logger.error(f"[TransferzHelper] HTTP error in API request: {str(e)}")
            
            if e.response.status_code == 401:
                logger.warning(f"[TransferzHelper] 401 Unauthorized - API key may be expired or invalid")
                logger.debug(f"[TransferzHelper] Current API key prefix: {api_key[:10] if api_key else 'None'}...")
                
                # Check what's in Redis cache
                try:
                    cached_api_key = cache.get(self.API_KEY_KEY)
                    cached_api_key_id = cache.get(self.API_KEY_ID_KEY)
                    cached_token = cache.get(self.TOKEN_KEY)
                    
                    logger.debug(f"[TransferzHelper] Redis cache status:")
                    logger.debug(f"  - API Key cached: {'Yes' if cached_api_key else 'No'}")
                    logger.debug(f"  - API Key ID cached: {'Yes' if cached_api_key_id else 'No'}")
                    logger.debug(f"  - Access Token cached: {'Yes' if cached_token else 'No'}")
                    
                    if cached_api_key:
                        logger.debug(f"  - Cached API Key prefix: {cached_api_key[:10]}...")
                        logger.debug(f"  - Cached API Key matches current: {cached_api_key == api_key}")
                        
                except Exception as cache_e:
                    logger.error(f"[TransferzHelper] Error checking Redis cache: {str(cache_e)}")
                
                # Both token and API key expired, delete and retry
                logger.warning("[TransferzHelper] API key expired, deleting and retrying")
                self._delete_api_key_on_401()

                # Retry with new credentials
                logger.info(f"[TransferzHelper] Retrying with fresh API key")
                api_key = self._get_api_key()
                headers["X-API-Key"] = api_key
                
                logger.debug(f"[TransferzHelper] New API key prefix: {api_key[:10] if api_key else 'None'}...")

                response = self._make_request(method, url, headers=headers, json_data=payload)
                logger.info("[TransferzHelper] Successfully completed request after retry")
                return response
            raise

    def get_quotes(self, quote_request):
        """
        For Transferz integration, we skip the quotes step and go directly to booking.
        This method is kept for compatibility but will redirect to create_booking.
        
        Based on working cURL example:
        The frontend should provide quoteId and other details directly.
        """
        logger.warning("[TransferzHelper] get_quotes called - Transferz integration uses direct booking")
        logger.info("[TransferzHelper] Returning empty quotes - frontend should provide quoteId directly")
        
        # Return empty quotes since Transferz requires direct booking with quoteId
        return []

    def create_booking(self, booking_params):
        """
        Create transfer booking using the official Transferz API.
        
        Based on working cURL example:
        curl --location 'https://warpdrive.staging.transferz.com/partners/bookings' \
        --header 'X-API-Key: E3oKfM8CdsxJnN5y71JtDDHB' \
        --header 'accept: application/json' \
        --header 'content-type: application/json' \
        --data-raw '{
          "booker": {
            "email": "<EMAIL>" 
          },
          "quotes": [
            {
              "traveller": {
                "email": "<EMAIL>"
              },
              "quoteId": 1035316743,
              "travelAddons": [
                {
                  "quoteTravelAddonId": 5102731555,
                  "amount": 1 
                }
              ]
            }
          ],
          "partnerReference": "Zuumm"
        }'
        
        Args:
            booking_params: Dictionary containing:
                - quote_id: Required quoteId from frontend
                - traveller_email: Traveller's email 
                - travel_addons: Optional travel addons array
                - partner_reference: Optional reference (defaults to "Zuumm")
                
        Returns:
            API response with booking details including booking ID and code
        """
        logger.info("[TransferzHelper] Creating transfer booking with direct API call")
        logger.debug(f"[TransferzHelper] Booking params keys: {list(booking_params.keys())}")
        
        # Extract required parameters
        quote_id = booking_params.get('quote_id')
        if not quote_id:
            raise Exception("'quote_id' is required for Transferz booking creation")
        
        traveller_email = booking_params.get('traveller_email', '<EMAIL>')
        travel_addons = booking_params.get('travel_addons', [])
        partner_reference = booking_params.get('partner_reference', 'Zuumm')
        
        # Build the exact payload structure from working cURL
        transferz_booking_payload = {
            "booker": {
                "email": "<EMAIL>"  # Always fixed as per your requirement
            },
            "quotes": [
                {
                    "traveller": {
                        "email": traveller_email
                    },
                    "quoteId": quote_id,
                    "travelAddons": travel_addons
                }
            ],
            "partnerReference": partner_reference
        }
        
        logger.debug(f"[TransferzHelper] Transferz booking payload: {transferz_booking_payload}")
        
        response = self._make_api_request("POST", "partners/bookings", transferz_booking_payload)
        
        logger.info("[TransferzHelper] Transfer booking created successfully")
        logger.info(f"[TransferzHelper] Booking ID: {response.get('id')}, Code: {response.get('code')}")
        
        return response

    def pay_by_invoice(self, booking_id: str, payment_params=None):
        """
        Pay for transfer booking by invoice using the official Transferz API.
        
        Official Endpoint: POST /partners/bookings/{id}/pay-by-invoice
        Documentation: https://developers.transferz.com/reference/paybyinvoice-1
        
        Pays for a booking on invoice (only supported for selected partners).
        
        Args:
            booking_id: The booking ID to pay for
            payment_params: Optional payment parameters
            
        Returns:
            API response with payment confirmation
        """
        logger.info(f"[TransferzHelper] Processing transfer payment for booking: {booking_id}")
        
        if payment_params is None:
            payment_params = {}
        
        if payment_params:
            logger.debug(f"[TransferzHelper] Payment params keys: {list(payment_params.keys())}")
        
        response = self._make_api_request("POST", f"partners/bookings/{booking_id}/pay-by-invoice", payment_params)
        
        logger.info("[TransferzHelper] Transfer payment completed successfully")
        return response

    def get_booking_details(self, booking_id: str):
        """
        Get transfer booking details.
        
        Official Endpoint: GET /partners/bookings/{id}
        Documentation: https://developers.transferz.com/reference/getbookingbyid
        
        Args:
            booking_id: The booking ID to get details for
            
        Returns:
            API response with booking details
        """
        logger.info(f"[TransferzHelper] Getting transfer booking details for: {booking_id}")
        
        response = self._make_api_request("GET", f"partners/bookings/{booking_id}")
        
        logger.info("[TransferzHelper] Transfer booking details retrieved successfully")
        return response

    # Legacy compatibility methods
    def search_transfers(self, search_params):
        """
        Legacy method - replaced by get_quotes
        Search for available transfers.
        """
        logger.warning("[TransferzHelper] search_transfers is deprecated, use get_quotes instead")
        return self.get_quotes(search_params)

    def get_quote(self, quote_params):
        """
        Legacy method - replaced by get_quotes
        Get quote for specific transfer option.
        """
        logger.warning("[TransferzHelper] get_quote is deprecated, use get_quotes instead")
        return self.get_quotes(quote_params)

    def hold_transfer(self, quote_id: str, transfer_details):
        """
        Hold a transfer booking without payment.
        
        Since the official API doesn't have a separate hold endpoint,
        we create a booking and then can pay later using pay_by_invoice.
        """
        logger.info(f"[TransferzHelper] Holding transfer for quote: {quote_id}")
        logger.debug(f"[TransferzHelper] Transfer details keys: {list(transfer_details.keys())}")
        
        # Prepare booking parameters
        booking_params = {
            'quote_id': quote_id,
        }
        
        # Add optional parameters if provided
        if transfer_details.get('passenger_details'):
            booking_params['passenger_details'] = transfer_details['passenger_details']
        if transfer_details.get('contact_info'):
            booking_params['contact_info'] = transfer_details['contact_info']
        if transfer_details.get('special_requirements'):
            booking_params['special_requirements'] = transfer_details['special_requirements']
        if transfer_details.get('flight_number'):
            booking_params['flight_number'] = transfer_details['flight_number']
        
        # Create booking without immediate payment
        response = self.create_booking(booking_params)
        
        logger.info("[TransferzHelper] Transfer hold (booking creation) completed successfully")
        return response
