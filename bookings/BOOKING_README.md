# Package Booking System - Complete Flow Documentation

## Table of Contents
1. [Overview](#overview)
2. [Core Booking Philosophy](#core-booking-philosophy)
3. [API Flow Sequence](#api-flow-sequence)
4. [API Endpoints Reference](#api-endpoints-reference)
5. [Invoice and Booking Details](#invoice-and-booking-details)
6. [Frontend-Backend Integration](#frontend-backend-integration)
7. [Testing Strategy](#testing-strategy)
8. [Error Handling](#error-handling)
9. [Production Considerations](#production-considerations)

## Overview

This system implements a **stateful, two-step "Hold-then-Confirm" booking process** for multi-product travel packages (Flights, Hotels, Activities) using the Tripjack API. This is the industry-standard approach that ensures price validation and prevents booking failures.

### Key Features
- ✅ **Multi-product packages**: Combine flights, hotels, and activities
- ✅ **Hold-then-Confirm flow**: Lock prices before payment
- ✅ **Comprehensive logging**: Full request/response tracking
- ✅ **Invoice management**: Automatic download and S3 storage
- ✅ **Booking details**: Complete Tripjack data retrieval
- ✅ **Error recovery**: Orphaned hold cleanup mechanisms
- ✅ **Security**: Server-side price validation and data sanitization

---

## Core Booking Philosophy

### Why Hold-then-Confirm?

❌ **Stateless approach problems:**
- Risk of orphaned holds
- Security vulnerabilities (client can manipulate prices)
- No cleanup mechanism for abandoned bookings

✅ **Stateful approach benefits:**
- Server controls the complete lifecycle
- Price locked and validated on server
- Background cleanup of abandoned holds
- Single source of truth in our database

### Booking States

```
PENDING → ON_HOLD → SUCCESS
   ↓         ↓
FAILED   CANCELLED
```

---

## API Flow Sequence

### Complete 5-Step Flow (Including Invoice Retrieval)

```mermaid
sequenceDiagram
    participant FE as Frontend
    participant BE as Backend
    participant TJ as Tripjack API
    participant DB as Database
    participant S3 as AWS S3

    Note over FE,S3: Step 1: Search (Not implemented in this module)
    FE->>BE: Search flights/hotels
    BE->>TJ: Search APIs
    TJ-->>BE: Available options with priceIds
    BE-->>FE: Search results

    Note over FE,S3: Step 2: Review (Price Validation)
    FE->>BE: POST /api/bookings/v1/review
    BE->>TJ: POST /fms/v1/review or /hms/v1/revalidate
    TJ-->>BE: {bookingId, amount, validated pricing}
    BE-->>FE: {booking_id, amount}

    Note over FE,S3: Step 3: Hold (Reserve without payment)
    FE->>BE: POST /api/bookings/v1/packages/hold
    BE->>DB: Create booking with ON_HOLD status
    BE->>TJ: POST /oms/v1/air/book (without payment)
    TJ-->>BE: PNR + hold confirmation
    BE->>TJ: POST /oms/v1/booking-details
    TJ-->>BE: Complete booking details
    BE->>TJ: GET invoice/voucher URL (if available)
    TJ-->>BE: Invoice URL
    BE->>S3: Download and upload invoice
    S3-->>BE: S3 URL
    BE->>DB: Save PNR, booking details, invoice URL
    BE-->>FE: {status: "PROVISIONAL_SUCCESS", booking_reference_id}

    Note over FE,S3: Step 4: Confirm (Finalize with payment)
    FE->>BE: POST /api/bookings/v1/packages/confirm
    BE->>DB: Find ON_HOLD booking
    BE->>TJ: POST /oms/v1/air/confirm-book (with payment)
    TJ-->>BE: Final ticket confirmation
    BE->>TJ: POST /oms/v1/booking-details (final details)
    TJ-->>BE: Final booking details with ticket numbers
    BE->>TJ: GET final invoice URL
    TJ-->>BE: Final invoice URL
    BE->>S3: Download and upload final invoice
    S3-->>BE: Final S3 URL
    BE->>DB: Update to SUCCESS status, save final details
    BE-->>FE: {status: "SUCCESS"}

    Note over FE,S3: Step 5: Retrieve Details & Invoice
    FE->>BE: GET /api/bookings/v1/booking-details/{id}
    BE->>DB: Fetch complete booking data
    BE->>S3: Generate presigned URL for invoice
    S3-->>BE: Presigned download URL
    BE-->>FE: Complete booking details + invoice download URL
```

---

## API Endpoints Reference

### 1. Review API - `POST /api/bookings/v1/review`

**Purpose**: Validate pricing and get temporary booking_id

**Request Body:**
```json
{
  "type": "Flight",
  "details": {
    "priceId": "12-5936970268_DELBOM9W313BOMBLR9W443_216104423654421"
  }
}
```

**Success Response:**
```json
{
  "type": "Flight",
  "booking_id": "TJS113200003503",
  "amount": 24500.50,
  "provider_response": { /* Full Tripjack response */ }
}
```

### 2. Hold API - `POST /api/bookings/v1/packages/hold`

**Purpose**: Place package on hold without payment

**Request Body:**
```json
{
  "booker_email": "<EMAIL>",
  "booker_phone_number": "+************",
  "itinerary": [
    {
      "day_number": 1,
      "date": "2025-10-20",
      "items": [
        {
          "order": 1,
          "type": "Flight",
          "booking_id": "TJS113200003503",
          "amount": 24500.50,
          "flight_details": {
            "travellerInfo": [
              {
                "title": "MR",
                "first_name": "Vaibhav",
                "last_name": "Sharma",
                "passenger_type": "ADULT",
                "dob": "1990-01-01"
              }
            ]
          }
        }
      ]
    }
  ]
}
```

**Success Response:**
```json
{
  "status": "PROVISIONAL_SUCCESS",
  "message": "Your package has been placed on hold. Complete payment to confirm booking.",
  "booking_reference_id": "uuid-string",
  "total_amount": 24500.50,
  "hold_expires_in_minutes": 30
}
```

### 3. Confirm API - `POST /api/bookings/v1/packages/confirm`

**Purpose**: Confirm held package after payment

**Request Body:**
```json
{
  "booking_reference_id": "uuid-from-hold-response"
}
```

**Success Response:**
```json
{
  "status": "SUCCESS",
  "message": "Your package has been successfully confirmed and booked.",
  "booking_reference_id": "uuid-string"
}
```

### 4. Booking Details API - `GET /api/bookings/v1/booking-details/{booking_reference_id}/`

**Purpose**: Retrieve complete booking information including invoices

**Success Response:**
```json
{
  "booking_reference_id": "uuid-string",
  "status": "SUCCESS",
  "total_amount": 24500.50,
  "booker_email": "<EMAIL>",
  "booker_phone": "+************",
  "created_at": "2025-01-20T10:30:00Z",
  "updated_at": "2025-01-20T11:00:00Z",
  "itinerary": [
    {
      "day_number": 1,
      "date": "2025-10-20",
      "items": [
        {
          "type": "Flight",
          "tripjack_booking_id": "TJS113200003503",
          "tripjack_pnr": "ABC123",
          "status": "SUCCESS",
          "amount": 24500.50,
          "ticket_numbers": ["1234567890123"],
          "invoice_download_url": "https://s3-presigned-url...",
          "booking_details": { /* Complete Tripjack response */ }
        }
      ]
    }
  ]
}
```

---

## Invoice and Booking Details Management

### When Invoices Are Available

**Important**: Invoices from Tripjack are typically only available **after the booking is fully confirmed and ticketed**. This means:

1. **During HOLD Phase**: No invoices are available as the booking is only provisionally held
2. **After CONFIRM Phase**: Invoices should be available once the booking is fully processed and tickets are issued
3. **Timing**: There might be a delay (few minutes to hours) between confirmation and invoice generation

### Invoice Retrieval Process

The system automatically attempts to download invoices at two stages:
1. **After Hold**: Attempts to fetch (usually unsuccessful until confirmation)
2. **After Confirm**: Should successfully fetch invoices if available

### Common Invoice Issues

**Issue 1: No Invoice URL Found**
```
[WARNING] No invoice/voucher URL found in response for flight booking
```
- **Cause**: Tripjack hasn't generated the invoice yet or booking isn't fully confirmed
- **Solution**: Wait and try again later, or check booking status with Tripjack

**Issue 2: Booking Status Still PENDING**
```
[INFO] Booking status: PENDING
```
- **Cause**: Booking confirmation failed or is still processing
- **Solution**: Check error logs for confirmation API failures

**Issue 3: Invalid Action for Order Status**
```
"errCode":"2520","message":" Invalid Action Requested for current Order Status."
```
- **Cause**: Trying to confirm a booking that's not in holdable state
- **Solution**: Check booking current status before attempting confirmation

### Manual Invoice Retrieval

If automatic retrieval fails, you can manually fetch invoices:

```bash
# Get booking details to check for invoice URLs
curl -X POST "https://apitest.tripjack.com/oms/v1/booking-details" \
  -H "Content-Type: application/json" \
  -H "apikey: YOUR_API_KEY" \
  -d '{"bookingId": "BOOKING_ID", "requirePaxPricing": true}'
```

Look for these possible invoice fields in the response:
- `invoiceUrl`, `voucherUrl`, `ticketUrl`, `downloadUrl`
- `order.invoiceUrl`, `itemInfos.AIR.ticketUrl`
- `itemInfos.AIR.travellerInfos[].ticketUrl` (per passenger)

---

## Frontend-Backend Integration

### Frontend Responsibilities

1. **Search Phase**: Display search results, capture `priceId`
2. **Review Phase**: Call review API, store `booking_id` and `amount`
3. **Hold Phase**: Submit complete itinerary, store `booking_reference_id`
4. **Payment Phase**: Process payment with external gateway
5. **Confirm Phase**: Call confirm API after successful payment
6. **Details Phase**: Retrieve booking details and download invoices

### Backend Responsibilities

1. **Price Validation**: Always validate with Tripjack APIs
2. **State Management**: Maintain proper booking lifecycle
3. **External API Integration**: Handle all Tripjack interactions
4. **Invoice Management**: Download and store invoices automatically
5. **Data Enrichment**: Fetch complete booking details from Tripjack
6. **Security**: Generate secure presigned URLs for invoice access

---

## Testing Strategy

### Sequential Testing Flow

```bash
# Step 1: Search (Tripjack Direct - to get priceId)
curl -X POST "https://apitest.tripjack.com/fms/v1/air-search-all" \
  -H "Content-Type: application/json" \
  -H "apikey: YOUR_API_KEY" \
  -d '{
    "searchQuery": {
      "cabinClass": "ECONOMY",
      "paxInfo": {"ADULT": 1, "CHILD": 0, "INFANT": 0},
      "routeInfos": [{
        "fromCityOrAirport": {"code": "DEL"},
        "toCityOrAirport": {"code": "BOM"},
        "travelDate": "2025-02-15"
      }]
    }
  }'

# Step 2: Review (Your Backend)
curl -X POST "http://localhost:8000/api/bookings/v1/review" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "Flight",
    "details": {"priceId": "PRICE_ID_FROM_STEP_1"}
  }'

# Step 3: Hold
curl -X POST "http://localhost:8000/api/bookings/v1/packages/hold" \
  -H "Content-Type: application/json" \
  -d '{
    "booker_email": "<EMAIL>",
    "booker_phone_number": "+************",
    "itinerary": [
      {
        "day_number": 1,
        "date": "2025-02-15",
        "items": [
          {
            "order": 1,
            "type": "Flight",
            "booking_id": "BOOKING_ID_FROM_STEP_2",
            "amount": AMOUNT_FROM_STEP_2,
            "is_international": false,
            "passengers_bifurcation": {
              "adult": 1,
              "child": 0,
              "infant": 0
            },
            "flight_details": {
              "travellerInfo": [
                {
                  "title": "MR",
                  "first_name": "John",
                  "last_name": "Doe",
                  "passenger_type": "ADULT",
                  "dob": "1990-01-01",
                  "gender": "M",
                  "passport_nationality": "IN",
                  "passport_number": "87UYITB",
                  "passport_expiry_date": "2030-09-08"
                }
              ],
              "gst_info": {
                "gst_number": "07ZZAS7YY6XXZF",
                "email": "<EMAIL>",
                "registered_name": "XYZ Pvt Ltd",
                "mobile": "9728408906",
                "address": "Delhi"
              },
              "delivery_info": {
                "emails": ["<EMAIL>"],
                "contacts": ["+************"]
              }
            }
          }
        ]
      }
    ]
  }'

# Step 4: Confirm
curl -X POST "http://localhost:8000/api/bookings/v1/packages/confirm" \
  -H "Content-Type: application/json" \
  -d '{
    "booking_reference_id": "BOOKING_REFERENCE_FROM_STEP_3"
  }'

# Step 5: Get Details & Invoice
curl -X GET "http://localhost:8000/api/bookings/v1/booking-details/BOOKING_REFERENCE_ID/"
```

### Migration Commands

```bash
# Create and apply new invoice field migrations
python manage.py makemigrations bookings
python manage.py migrate
```

---

## Error Handling

### Invoice Download Errors

The system is designed to be resilient:
- If invoice download fails, the booking still succeeds
- Error is logged but doesn't affect main booking flow
- Can retry invoice download later via admin interface

### Common Issues

| Error | Cause | Solution |
|-------|-------|----------|
| `Invoice not found` | Tripjack doesn't provide invoice URL | Normal - not all bookings have invoices immediately |
| `S3 upload failed` | AWS credentials/permissions | Check AWS settings |
| `Presigned URL expired` | URL older than expiration time | Call API again for new URL |

---

## Production Considerations

### 1. S3 Configuration

```python
# Required Django settings for S3 storage
AWS_ACCESS_KEY_ID = 'your-access-key'
AWS_SECRET_ACCESS_KEY = 'your-secret-key'
AWS_S3_REGION_NAME = 'us-east-1'
AWS_STORAGE_BUCKET_NAME = 'zuumm-invoices'

# For django-storages S3 backend
DEFAULT_FILE_STORAGE = 'storages.backends.s3boto3.S3Boto3Storage'
AWS_S3_CUSTOM_DOMAIN = f'{AWS_STORAGE_BUCKET_NAME}.s3.amazonaws.com'
```

### 2. Invoice Cleanup

```python
# Example Celery task for old invoice cleanup
@periodic_task(run_every=crontab(day_of_month='1'))
def cleanup_old_invoices():
    """Delete invoices older than 1 year"""
    cutoff_date = timezone.now() - timedelta(days=365)
    old_invoices = FlightBooking.objects.filter(
        created_at__lt=cutoff_date,
        invoice__isnull=False
    )
    
    service = InvoiceS3Service()
    for booking in old_invoices:
        service.delete_invoice(booking.invoice)
        booking.invoice = None
        booking.save()
```

### 3. Monitoring

Key metrics to monitor:
- Invoice download success rate
- S3 storage usage
- Presigned URL generation failures
- Booking details fetch success rate

---

## Database Schema

### New Fields Added

**FlightBooking:**
```sql
-- Invoice related fields
invoice VARCHAR(100) NULL; -- FileField path
booking_details JSONB NULL;
ticket_numbers JSONB NULL;
```

**HotelBooking:**
```sql
-- Invoice related fields  
invoice VARCHAR(100) NULL; -- FileField path
booking_details JSONB NULL;
voucher_data JSONB NULL;
status VARCHAR(20) NULL;
```

---

## Integration with Tripjack APIs

### Flight Booking Details API
- **Endpoint**: `POST /oms/v1/booking-details`
- **Purpose**: Get complete flight booking information
- **Response**: PNRs, ticket numbers, passenger details, pricing breakdown

### Hotel Booking Details API  
- **Endpoint**: `POST /hms/v1/booking-detail`
- **Purpose**: Get complete hotel booking information
- **Response**: Confirmation numbers, voucher data, cancellation policies

### Invoice Retrieval
- **Method**: Check booking details response for invoice/voucher URLs
- **Formats**: PDF, HTML (auto-detected)
- **Storage**: Automatic S3 upload with metadata

---

**Last Updated**: January 2025  
**Version**: 2.0  
**Authors**: Development Team  

**References:**
- [Tripjack Hotel API Documentation](https://www.scribd.com/document/814027835/Tripjack-Hotel-API-Documentation-v-1-0#fullscreen&from_embed)
- [Tripjack Flight API Documentation](https://drive.google.com/file/d/1VcxH-ltiPQaBBqq69f-yuDK8xWZQ8mnN/view) 

## **IMPORTANT: Understanding Tripjack's Hold/Confirm Flow**

**According to Official Tripjack Documentation**:

### **Hold API (`/oms/v1/air/book` without paymentInfo)**:
- **Creates an actual booking** with the airline/supplier
- **Returns PNR and booking confirmation**
- **Reserves seats** but does not issue tickets
- **Booking status**: Confirmed but not ticketed
- **Time limit**: Usually 15-30 minutes to confirm with payment

### **Confirm API (`/oms/v1/air/confirm-book` with paymentInfo)**:
- **Takes the held booking** (identified by bookingId)
- **Processes payment** and issues tickets
- **Converts held reservation** to confirmed ticketed booking

### **Key Points from Documentation**:

1. **Hold Process**: "After validation, Booking will be done and Supplier Booking is Success"
2. **PNR Generation**: "in BookingDetails API Call will get PNR and TicketNumber if any"
3. **Confirm Process**: Takes held booking and confirms with payment to issue tickets

### **Common Issues**:

**Error 2520: "Invalid Action Requested for current Order Status"**
- **Cause**: Trying to confirm a booking that is not in proper hold state
- **Solutions**:
  - Check if hold actually created a PNR
  - Verify booking status with `/oms/v1/booking-details`
  - Ensure hold time limit hasn't expired
  - Use `/oms/v1/air/fare-validate` before confirm

**No PNR in Hold Response**:
- **Cause**: Hold request may have failed validation or supplier booking failed
- **Solution**: Check hold response for errors, validate passenger details

This flow ensures:
- Hold creates actual airline reservations with PNR
- Confirm processes payment and issues tickets
- Both steps work with real airline bookings, not just session management 