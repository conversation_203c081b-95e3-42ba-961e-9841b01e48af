from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from .serializers import ReviewRequestSerializer, PackageBookingRequestSerializer
from bookings.helpers.transferz_booking_helper import TransferzBookingHelper
from bookings.helpers.tripjack_booking_helper import Tripjack<PERSON>ook<PERSON><PERSON>elper
from .services import PackageBookingService
from accounts.models import User
from bookings.logging_config import booking_metrics
import logging
import json
from bookings.models import Booking, BookingItinerary
from rest_framework.permissions import AllowAny, IsAuthenticated
from bookings.api.v1.services import BookingException
from bookings.utils import InvoiceS3Service
import requests
from django.conf import settings

# Configure logger for booking views
logger = logging.getLogger(__name__)


class ReviewAPIView(APIView):
    """
    API to validate pricing and availability for a single travel product
    and retrieve a temporary booking_id from the supplier.
    
    This is Step 2 in the booking flow: Search -> Review -> Hold -> Confirm
    """
    permission_classes = [AllowAny]
    # permission_classes = [IsAuthenticated]

    def post(self, request, *args, **kwargs):
        user = User.objects.filter(is_active=True).first()
        # user = request.user
        logger.info(f"[ReviewAPI] Starting review request for user: {getattr(user, 'email', 'Anonymous')}")
        logger.info(f"[ReviewAPI] Request IP: {request.META.get('REMOTE_ADDR', 'Unknown')}")
        logger.info(f"[ReviewAPI] User Agent: {request.META.get('HTTP_USER_AGENT', 'Unknown')}")
        
        booking_metrics.increment_metric('review_api_calls')
        
        logger.debug(f"[ReviewAPI] Request data: {json.dumps(request.data, indent=2, default=str)}")
        logger.info(f"[ReviewAPI] Request size: {len(json.dumps(request.data, default=str))} characters")
        
        serializer = ReviewRequestSerializer(data=request.data)
        if not serializer.is_valid():
            logger.error(f"[ReviewAPI] Serializer validation failed: {serializer.errors}")
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        logger.info(f"[ReviewAPI] Serializer validation passed")
        
        review_type = serializer.validated_data['type']
        details = serializer.validated_data['details']
        
        logger.info(f"[ReviewAPI] Processing {review_type} review")
        logger.debug(f"[ReviewAPI] Review details: {details}")
        
        # Initialize Tripjack helper
        tripjack_helper = TripjackBookingHelper()
        logger.info(f"[ReviewAPI] TripjackBookingHelper initialized")
        
        response_data = {}
        
        if review_type == "Flight":
            logger.info(f"[ReviewAPI] Calling Tripjack flight review API")
            price_id = details['priceId']
            logger.debug(f"[ReviewAPI] Flight priceId: {price_id}")
            
            response = tripjack_helper.review_flight(price_id)
            logger.info(f"[ReviewAPI] Tripjack flight review successful")
            logger.debug(f"[ReviewAPI] Tripjack response size: {len(json.dumps(response, default=str))} characters")
            
            # Parse response for standardized format
            booking_id = response.get('bookingId')
            amount = response.get('totalPriceInfo', {}).get('totalFareDetail', {}).get('fC', {}).get('TF', 0)
            
            logger.info(f"[ReviewAPI] Flight review completed - booking_id: {booking_id}, amount: {amount}")
            
            response_data = {
                "type": "Flight",
                "booking_id": booking_id,
                "amount": amount,
                "provider_response": response
            }

        elif review_type == "Hotel":
            logger.info(f"[ReviewAPI] Calling Tripjack hotel review API")
            search_id = details.get('search_id')
            hotel_id = details['hotel_id'] 
            option_id = details['option_id']
            
            logger.debug(f"[ReviewAPI] Hotel details - search_id: {search_id}, hotel_id: {hotel_id}, option_id: {option_id}")
            
            response = tripjack_helper.review_hotel(search_id, hotel_id, option_id)
            
            # Check if the Tripjack API call was successful
            if not response.get('success', True):
                logger.error(f"[ReviewAPI] Tripjack hotel review failed")
                
                # Extract error information
                tripjack_errors = response.get('tripjack_errors', [])
                error_messages = response.get('error_messages', [])
                error_codes = response.get('error_codes', [])
                
                # Also check for alerts (like SOLDOUT)
                full_response = response.get('full_response', {})
                alerts = full_response.get('alerts', [])
                
                if tripjack_errors:
                    primary_error = tripjack_errors[0]
                    error_code = primary_error.get('errCode', 'Unknown')
                    error_message = primary_error.get('message', 'Unknown error')
                    
                    # Check for alerts to provide more specific messages
                    alert_message = ""
                    if alerts:
                        for alert in alerts:
                            alert_type = alert.get('type', '')
                            alert_msg = alert.get('message', '')
                            if alert_type == 'SOLDOUT':
                                alert_message = f" Alert: {alert_msg}"
                                break
                    
                    # Map common hotel error codes to user-friendly messages
                    user_message = error_message + alert_message
                    if error_code == "6034" and any(alert.get('type') == 'SOLDOUT' for alert in alerts):
                        user_message = "The selected hotel room is no longer available (SOLD OUT). Please search again for current availability."
                    elif error_code == "408":
                        user_message = "Access denied. This may be due to IP restrictions or insufficient API permissions for hotel booking."
                    elif error_code == "404":
                        user_message = "Hotel or room option not found. Please search again for current availability."
                    elif error_code == "1071":
                        user_message = "The selected hotel room is no longer available at this price."
                    
                    logger.error(f"[ReviewAPI] Tripjack Error [{error_code}]: {error_message}")
                    if alerts:
                        logger.error(f"[ReviewAPI] Tripjack Alerts: {alerts}")
                    
                    return Response({
                        "status": "failure",
                        "message": user_message,
                        "error_code": error_code,
                        "error_details": error_message,
                        "alerts": alerts,
                        "tripjack_response": {
                            "errors": tripjack_errors,
                            "alerts": alerts,
                            "status": full_response.get('status', {})
                        }
                    }, status=status.HTTP_400_BAD_REQUEST)
                else:
                    # Generic error
                    error_msg = response.get('error', 'Unknown error occurred')
                    logger.error(f"[ReviewAPI] Generic hotel review error: {error_msg}")
                    
                    # Still check for alerts even if no specific errors
                    if alerts:
                        logger.error(f"[ReviewAPI] Tripjack Alerts: {alerts}")
                        
                    return Response({
                        "status": "failure", 
                        "message": "Hotel review failed. Please try again.",
                        "error_details": error_msg,
                        "alerts": alerts
                    }, status=status.HTTP_400_BAD_REQUEST)
            
            logger.info(f"[ReviewAPI] Tripjack hotel review successful")
            logger.debug(f"[ReviewAPI] Tripjack response size: {len(json.dumps(response, default=str))} characters")
            
            # Parse response for standardized format using actual structure
            booking_id = response.get('bookingId')
            
            # Get price from hInfo.ops array
            h_info = response.get('hInfo', {})
            ops = h_info.get('ops', [])
            amount = ops[0].get('tp', 0) if ops else 0
            
            logger.info(f"[ReviewAPI] Hotel review completed - booking_id: {booking_id}, amount: {amount}")
            
            response_data = {
                "type": "Hotel", 
                "booking_id": booking_id,
                "amount": amount,
                "provider_response": response
            }

        elif review_type == "Transfer":
            logger.info(f"[ReviewAPI] Processing Transferz direct booking (no quotes step)")
            transfer_details = details
            logger.debug(f"[ReviewAPI] Transfer details: {transfer_details}")
            
            # Extract required parameters for Transferz booking
            quote_id = transfer_details.get('quote_id')
            traveller_email = transfer_details.get('traveller_email')
            
            if not quote_id:
                logger.error(f"[ReviewAPI] Missing required quote_id for Transferz booking")
                return Response({
                    "status": "error",
                    "message": "quote_id is required for transfer booking"
                }, status=status.HTTP_400_BAD_REQUEST)
            
            if not traveller_email:
                logger.error(f"[ReviewAPI] Missing required traveller_email for Transferz booking")
                return Response({
                    "status": "error", 
                    "message": "traveller_email is required for transfer booking"
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Get partner from request if available
            partner = getattr(request, 'domain', None)
            transferz_helper = TransferzBookingHelper(partner=partner)
            
            # Prepare booking parameters for Transferz
            booking_params = {
                'quote_id': quote_id,
                'traveller_email': traveller_email,
                'travel_addons': transfer_details.get('travel_addons', []),
                'partner_reference': transfer_details.get('partner_reference', 'Zuumm')
            }
            
            try:
                # Call Transferz booking API directly
                booking_response = transferz_helper.create_booking(booking_params)
                
                logger.info(f"[ReviewAPI] Transferz booking successful")
                logger.info(f"[ReviewAPI] Booking ID: {booking_response.get('id')}, Code: {booking_response.get('code')}")
                
                # Extract relevant information from response
                booking_id = booking_response.get('id')
                booking_code = booking_response.get('code')
                journeys = booking_response.get('journeys', [])
                
                # Calculate total amount from journeys
                total_amount = 0
                currency = 'USD'
                
                if journeys:
                    first_journey = journeys[0]
                    price_summary = first_journey.get('priceSummary', {})
                    total_amount = price_summary.get('price', 0)
                    currency = price_summary.get('currency', 'USD')
                
                response_data = {
                    "type": "Transfer",
                    "booking_id": booking_id,
                    "booking_code": booking_code,
                    "amount": total_amount,
                    "currency": currency,
                    "provider_response": booking_response,
                    "message": "Transfer booking created successfully"
                }
                
                logger.info(f"[ReviewAPI] Transfer booking completed - ID: {booking_id}, Amount: {total_amount} {currency}")
                
            except Exception as e:
                logger.error(f"[ReviewAPI] Transferz booking failed: {str(e)}")
                
                response_data = {
                    "type": "Transfer",
                    "booking_id": None,
                    "amount": 0,
                    "currency": "USD",
                    "provider_response": None,
                    "message": f"Transfer booking failed: {str(e)}",
                    "error": str(e)
                }
                
                return Response(response_data, status=status.HTTP_400_BAD_REQUEST)
        
        logger.info(f"[ReviewAPI] Review request completed successfully")
        logger.debug(f"[ReviewAPI] Final response: {json.dumps(response_data, indent=2, default=str)}")
        
        return Response(response_data, status=status.HTTP_200_OK)


class PackageHoldAPIView(APIView):
    """
    API to place a hold on the entire package without payment.
    This creates booking records with ON_HOLD status.
    
    This is Step 3 in the booking flow: Search -> Review -> Hold -> Confirm
    """
    permission_classes = [AllowAny]
    # permission_classes = [IsAuthenticated]
    
    def _sanitize_request_data(self, data):
        """Remove sensitive information from request data for logging"""
        logger.debug(f"[PackageHoldAPI] Sanitizing request data for logging")
        
        sanitized = json.loads(json.dumps(data, default=str))
        
        # Sanitize passenger information
        if 'itinerary' in sanitized:
            for day_idx, day in enumerate(sanitized['itinerary']):
                logger.debug(f"[PackageHoldAPI] Sanitizing day {day_idx + 1}")
                if 'items' in day:
                    for item_idx, item in enumerate(day['items']):
                        logger.debug(f"[PackageHoldAPI] Sanitizing item {item_idx + 1} of type {item.get('type')}")
                        if 'flight_details' in item and 'travellerInfo' in item['flight_details']:
                            for pass_idx, passenger in enumerate(item['flight_details']['travellerInfo']):
                                logger.debug(f"[PackageHoldAPI] Sanitizing passenger {pass_idx + 1}")
                                if 'passport_number' in passenger:
                                    passenger['passport_number'] = '***MASKED***'
                        if 'flight_details' in item and 'gst_info' in item['flight_details'] and item['flight_details']['gst_info']:
                            if 'gst_number' in item['flight_details']['gst_info']:
                                item['flight_details']['gst_info']['gst_number'] = '***MASKED***'
        
        logger.debug(f"[PackageHoldAPI] Request data sanitization completed")
        return sanitized
    
    def post(self, request, *args, **kwargs):
        partner = getattr(request, 'domain', None)
        if not partner:
            logger.error(f"[PackageHoldAPI] Partner domain not resolved from request")
            return Response({
                'status': 'error',
                'message': 'Partner domain not resolved'
            }, status=status.HTTP_400_BAD_REQUEST)

        logger.info(f"[PackageHoldAPI] Starting package hold request")
        logger.info(f"[PackageHoldAPI] Partner: {partner}")
        logger.info(f"[PackageHoldAPI] Request IP: {request.META.get('REMOTE_ADDR', 'Unknown')}")
        logger.info(f"[PackageHoldAPI] User Agent: {request.META.get('HTTP_USER_AGENT', 'Unknown')}")
        
        # Get user (simplified for now - you may want to get actual authenticated user)
        user = User.objects.filter(is_active=True).first()
        # user = request.user
        
        serializer = PackageBookingRequestSerializer(data=request.data)
        if not serializer.is_valid():
            logger.error(f"[PackageHoldAPI] Serializer validation failed: {serializer.errors}")
            booking_metrics.increment_metric('failed_bookings')
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        logger.info(f"[PackageHoldAPI] Serializer validation passed")

        # Initialize the service with HOLD mode
        service = PackageBookingService(user, partner, serializer.validated_data, mode='HOLD')
        logger.info(f"[PackageHoldAPI] PackageBookingService initialized in HOLD mode")
        
        try:
            booking_obj = service.create_package()
            logger.info(f"[PackageHoldAPI] Package hold successful")
            
            response_data = {
                "status": "SUCCESS",
                "message": "Your package has been successfully placed on hold.",
                "booking_reference_id": str(booking_obj.external_id),
                "total_amount": float(booking_obj.total_amount),
                "expires_in": "15 minutes"
            }
            
            return Response(response_data, status=status.HTTP_201_CREATED)
            
        except BookingException as e:
            logger.error(f"[PackageHoldAPI] Booking exception: {str(e)}")
            booking_metrics.increment_metric('failed_bookings')
            
            # Extract error details
            error_details = getattr(e, 'details', {})
            tripjack_errors = error_details.get('tripjack_errors', [])
            
            if tripjack_errors:
                primary_error = tripjack_errors[0]
                error_code = primary_error.get('errCode', 'Unknown')
                error_message = primary_error.get('message', 'Unknown error occurred')
                
                # Map common error codes to user-friendly messages (for all booking types)
                user_message = error_message
                action_required = "Please try again or contact support if the issue persists."
                
                if error_code == "2516":
                    user_message = "Booking request failed - delivery information issue. Please check your contact details."
                    action_required = "Please verify your email and phone number and try again."
                elif error_code == "1089":
                    user_message = "Invalid passenger title. Please check passenger information."
                    action_required = "Please verify passenger titles (Mr, Ms, Mrs, etc.) and try again."
                elif error_code == "2542":
                    user_message = "Invalid passenger details. Please verify all passenger information."
                    action_required = "Please check all passenger details and try again."
                elif error_code == "6021":
                    user_message = "Booking failed due to supplier restrictions. Please try again."
                    action_required = "Please try a different option or contact support."
                elif error_code == "408":
                    user_message = "Access denied for booking. Please contact support."
                    action_required = "Please contact support for assistance."
                elif error_code == "6034":
                    user_message = "The selected option is no longer available. Please search again."
                    action_required = "Please search for alternative options."
                
                response_data = {
                    "status": "FAILED",
                    "message": user_message,
                    "error_code": error_code,
                    "error_details": error_message,
                    "action_required": action_required
                }
                
                return Response(response_data, status=status.HTTP_400_BAD_REQUEST)
            else:
                response_data = {
                    "status": "FAILED",
                    "message": "Unable to place booking on hold. Please try again.",
                    "error_details": str(e),
                    "action_required": "Please try again or contact support if the issue persists."
                }
                
                return Response(response_data, status=status.HTTP_400_BAD_REQUEST)
        
        except Exception as e:
            logger.error(f"[PackageHoldAPI] Unexpected error: {str(e)}")
            booking_metrics.increment_metric('failed_bookings')
            
            response_data = {
                "status": "FAILED",
                "message": "An unexpected error occurred. Please try again.",
                "error_details": str(e),
                "action_required": "Please contact support if the issue persists."
            }
            
            return Response(response_data, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class PackageConfirmAPIView(APIView):
    """
    API to confirm bookings. Supports two modes:
    1. Confirm previously held package (with booking_reference_id) - for flights
    2. Direct booking (with full payload) - for hotels
    
    This is Step 4 in the booking flow: Search -> Review -> Hold -> Confirm
    OR Step 3 for hotels: Search -> Review -> Confirm
    """
    permission_classes = [AllowAny]
    # permission_classes = [IsAuthenticated]

    def post(self, request, *args, **kwargs):
        partner = getattr(request, 'domain', None)
        if not partner:
            return Response({
                'status': 'error',
                'message': 'Partner domain not resolved'
            }, status=status.HTTP_400_BAD_REQUEST)

        logger.info(f"[PackageConfirmAPI] Starting package confirmation")
        logger.info(f"[PackageConfirmAPI] Request IP: {request.META.get('REMOTE_ADDR', 'Unknown')}")
        logger.info(f"[PackageConfirmAPI] User Agent: {request.META.get('HTTP_USER_AGENT', 'Unknown')}")
        
        # Check if this is a confirmation of held booking or direct booking
        booking_reference_id = request.data.get('booking_reference_id')
        
        if booking_reference_id:
            # Mode 1: Confirm previously held booking (flights)
            logger.info(f"[PackageConfirmAPI] Confirming held booking: {booking_reference_id}")
            return self._confirm_held_booking(request, partner, booking_reference_id)
        else:
            # Mode 2: Direct booking (hotels)
            logger.info(f"[PackageConfirmAPI] Processing direct booking")
            return self._direct_booking(request, partner)
    
    def _confirm_held_booking(self, request, partner, booking_reference_id):
        """Confirm a previously held booking (flights)"""
        # Initialize services 
        tripjack_helper = TripjackBookingHelper()
        invoice_service = InvoiceS3Service()
        
        # Create service instance for confirmation (no validated_data needed)
        service = PackageBookingService(
            user=None, 
            partner=partner,
            validated_data={}, 
            mode='CONFIRM'
        )
        service.tripjack_helper = tripjack_helper
        service.invoice_service = invoice_service
        
        logger.info(f"[PackageConfirmAPI] PackageBookingService initialized in CONFIRM mode")
        
        try:
            # Confirm the package
            booking_obj = service.confirm_package(booking_reference_id)
            
            logger.info(f"[PackageConfirmAPI] Package confirmation completed successfully")
            logger.info(f"[PackageConfirmAPI] Confirmed booking ID: {booking_obj.id}")
            logger.info(f"[PackageConfirmAPI] Final status: {booking_obj.status}")
            
            response_data = {
                "status": "SUCCESS",
                "message": "Your package has been successfully confirmed and booked.",
                "booking_reference_id": str(booking_obj.external_id)
            }
            
            logger.info(f"[PackageConfirmAPI] Final response: {response_data}")
            
            return Response(response_data, status=status.HTTP_200_OK)
            
        except BookingException as e:
            logger.error(f"[PackageConfirmAPI] Booking exception: {str(e)}")
            
            # Extract error details
            error_details = getattr(e, 'details', {})
            tripjack_errors = error_details.get('tripjack_errors', [])
            
            # Prepare user-friendly error response
            if tripjack_errors:
                # Use Tripjack's error message for user
                primary_error = tripjack_errors[0]
                error_code = primary_error.get('errCode', 'Unknown')
                error_message = primary_error.get('message', 'Unknown error occurred')
                error_details_text = primary_error.get('details', error_message)
                
                # Map common error codes to user-friendly messages
                user_message = error_message
                if error_code == "2520":
                    user_message = "This booking cannot be confirmed at this time. It may have expired or already been processed."
                elif error_code == "2024":
                    user_message = "The booking session has expired. Please start the booking process again."
                elif error_code == "1071":
                    user_message = "The selected flight is no longer available. Please search again."
                
                response_data = {
                    "status": "FAILED",
                    "message": user_message,
                    "error_code": error_code,
                    "error_details": error_details_text,
                    "action_required": "Please contact support or try the booking process again."
                }
                
                logger.error(f"[PackageConfirmAPI] Tripjack Error [{error_code}]: {error_message}")
                return Response(response_data, status=status.HTTP_400_BAD_REQUEST)
            else:
                # Generic booking error
                response_data = {
                    "status": "FAILED",
                    "message": "Unable to confirm booking. Please try again.",
                    "error_details": str(e),
                    "action_required": "Please try again or contact support if the issue persists."
                }
                
                logger.error(f"[PackageConfirmAPI] Generic booking error: {str(e)}")
                return Response(response_data, status=status.HTTP_400_BAD_REQUEST)
                
        except Exception as e:
            logger.error(f"[PackageConfirmAPI] Unexpected error: {str(e)}")
            logger.error(f"[PackageConfirmAPI] Error type: {type(e).__name__}")
            
            response_data = {
                "status": "FAILED",
                "message": "An unexpected error occurred. Please try again.",
                "error_details": "Internal server error",
                "action_required": "Please try again or contact support if the issue persists."
            }
            
            return Response(response_data, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def _direct_booking(self, request, partner):
        """Direct booking without hold (hotels)"""
        # Get user (simplified for now - you may want to get actual authenticated user)
        user = User.objects.filter(is_active=True).first()
        # user = request.user
        
        serializer = PackageBookingRequestSerializer(data=request.data)
        if not serializer.is_valid():
            logger.error(f"[PackageConfirmAPI] Serializer validation failed: {serializer.errors}")
            booking_metrics.increment_metric('failed_bookings')
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        logger.info(f"[PackageConfirmAPI] Serializer validation passed for direct booking")

        # Initialize the service with CONFIRM mode for direct booking
        service = PackageBookingService(user, partner, serializer.validated_data, mode='CONFIRM')
        logger.info(f"[PackageConfirmAPI] PackageBookingService initialized in CONFIRM mode for direct booking")
        
        try:
            booking_obj = service.create_package()
            logger.info(f"[PackageConfirmAPI] Direct booking successful")
            
            response_data = {
                "status": "SUCCESS",
                "message": "Your package has been successfully booked.",
                "booking_reference_id": str(booking_obj.external_id),
                "total_amount": float(booking_obj.total_amount)
            }
            
            return Response(response_data, status=status.HTTP_201_CREATED)
            
        except BookingException as e:
            logger.error(f"[PackageConfirmAPI] Direct booking exception: {str(e)}")
            booking_metrics.increment_metric('failed_bookings')
            
            # Extract error details
            error_details = getattr(e, 'details', {})
            tripjack_errors = error_details.get('tripjack_errors', [])
            
            if tripjack_errors:
                primary_error = tripjack_errors[0]
                error_code = primary_error.get('errCode', 'Unknown')
                error_message = primary_error.get('message', 'Unknown error occurred')
                
                # Map common error codes to user-friendly messages (for all booking types)
                user_message = error_message
                action_required = "Please try again or contact support if the issue persists."
                
                if error_code == "2516":
                    user_message = "Booking request failed - delivery information issue. Please check your contact details."
                    action_required = "Please verify your email and phone number and try again."
                elif error_code == "1089":
                    user_message = "Invalid passenger title. Please check passenger information."
                    action_required = "Please verify passenger titles (Mr, Ms, Mrs, etc.) and try again."
                elif error_code == "2542":
                    user_message = "Invalid passenger details. Please verify all passenger information."
                    action_required = "Please check all passenger details and try again."
                elif error_code == "6021":
                    user_message = "Booking failed due to supplier restrictions. Please try again."
                    action_required = "Please try a different option or contact support."
                elif error_code == "408":
                    user_message = "Access denied for booking. Please contact support."
                    action_required = "Please contact support for assistance."
                elif error_code == "6034":
                    user_message = "The selected option is no longer available. Please search again."
                    action_required = "Please search for alternative options."
                
                response_data = {
                    "status": "FAILED",
                    "message": user_message,
                    "error_code": error_code,
                    "error_details": error_message,
                    "action_required": action_required
                }
                
                return Response(response_data, status=status.HTTP_400_BAD_REQUEST)
            else:
                response_data = {
                    "status": "FAILED",
                    "message": "Unable to complete booking. Please try again.",
                    "error_details": str(e),
                    "action_required": "Please try again or contact support if the issue persists."
                }
                
                return Response(response_data, status=status.HTTP_400_BAD_REQUEST)
        
        except Exception as e:
            logger.error(f"[PackageConfirmAPI] Unexpected error in direct booking: {str(e)}")
            booking_metrics.increment_metric('failed_bookings')
            
            response_data = {
                "status": "FAILED",
                "message": "An unexpected error occurred. Please try again.",
                "error_details": str(e),
                "action_required": "Please contact support if the issue persists."
            }
            
            return Response(response_data, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class BookingDetailsAPIView(APIView):
    """
    API to retrieve detailed booking information including invoices
    """
    permission_classes = [AllowAny]
    # permission_classes = [IsAuthenticated]

    def get(self, request, booking_reference_id, *args, **kwargs):
        logger.info(f"[BookingDetailsAPI] Starting booking details retrieval")
        logger.info(f"[BookingDetailsAPI] Booking reference ID: {booking_reference_id}")
        logger.info(f"[BookingDetailsAPI] Request IP: {request.META.get('REMOTE_ADDR', 'Unknown')}")
        
        try:
            # Find the booking
            booking = Booking.objects.get(external_id=booking_reference_id)
            logger.info(f"[BookingDetailsAPI] Found booking - ID: {booking.id}, Status: {booking.status}")
            
            # Get all booking itinerary items grouped by day
            booking_items = BookingItinerary.objects.filter(booking=booking).order_by('day_number', 'order')
            
            response_data = {
                "booking_reference_id": str(booking.external_id),
                "status": booking.status,
                "total_amount": float(booking.total_amount),
                "booker_email": booking.booker_email,
                "booker_phone": booking.booker_phone_number,
                "created_at": booking.created_at.isoformat(),
                "updated_at": booking.updated_at.isoformat(),
                "package": {
                    "id": str(booking.package.external_id) if booking.package else None,
                    "title": booking.package.title if booking.package else None,
                    "package_no": booking.package.package_no if booking.package else None,
                } if booking.package else None,
                "itinerary": []
            }
            
            from bookings.utils import InvoiceS3Service
            invoice_service = InvoiceS3Service()
            
            # Add main booking invoice URL (always include the key)
            if booking.invoice_file:
                try:
                    presigned_url = invoice_service.generate_presigned_url(booking.invoice_file, expiration=3600)
                    response_data["main_invoice_download_url"] = presigned_url
                    logger.info(f"[BookingDetailsAPI] Generated presigned URL for main booking invoice")
                except Exception as e:
                    logger.warning(f"[BookingDetailsAPI] Failed to generate presigned URL for main booking invoice: {str(e)}")
                    response_data["main_invoice_download_url"] = None
            else:
                response_data["main_invoice_download_url"] = None
                logger.info(f"[BookingDetailsAPI] No main booking invoice file found")
            
            # Group items by day
            current_day = None
            day_data = None
            
            for item in booking_items:
                # Start a new day if needed
                if current_day != item.day_number:
                    if day_data:  # Save previous day
                        response_data["itinerary"].append(day_data)
                    
                    current_day = item.day_number
                    day_data = {
                        "day_number": item.day_number,
                        "date": item.date.isoformat(),
                        "items": []
                    }
                
                item_data = {
                    "type": item.type,
                    "order": item.order
                }
                
                if item.type == 'Flight' and item.flight_booking:
                    flight = item.flight_booking
                    item_data.update({
                        "tripjack_booking_id": flight.tripjack_booking_id,
                        "tripjack_pnr": flight.tripjack_pnr,
                        "status": flight.status,
                        "amount": float(flight.amount),
                        "is_international": flight.is_international,
                        "passengers_bifurcation": flight.passengers_bifurcation,
                        "ticket_numbers": flight.ticket_numbers,
                        "booking_details": flight.booking_details
                    })
                    
                    # Generate presigned URL for invoice if available
                    if flight.invoice_file:
                        try:
                            presigned_url = invoice_service.generate_presigned_url(flight.invoice_file, expiration=3600)
                            item_data["invoice_download_url"] = presigned_url
                            logger.info(f"[BookingDetailsAPI] Generated presigned URL for flight invoice")
                        except Exception as e:
                            logger.warning(f"[BookingDetailsAPI] Failed to generate presigned URL for flight invoice: {str(e)}")
                
                elif item.type == 'Hotel' and item.hotel_booking:
                    hotel = item.hotel_booking
                    item_data.update({
                        "tripjack_booking_id": hotel.tripjack_booking_id,
                        "tripjack_hotel_id": hotel.tripjack_hotel_id,
                        "confirmation_number": hotel.confirmation_number,
                        "hotel_name": hotel.hotel_name,
                        "check_in_date": hotel.check_in_date.isoformat(),
                        "check_out_date": hotel.check_out_date.isoformat(),
                        "status": hotel.status,
                        "amount": float(hotel.amount),
                        "room_data": hotel.room_data,
                        "booking_details": hotel.booking_details
                    })
                    
                    # Generate presigned URL for invoice if available
                    if hotel.invoice_file:
                        try:
                            presigned_url = invoice_service.generate_presigned_url(hotel.invoice_file, expiration=3600)
                            item_data["invoice_download_url"] = presigned_url
                            logger.info(f"[BookingDetailsAPI] Generated presigned URL for hotel invoice")
                        except Exception as e:
                            logger.warning(f"[BookingDetailsAPI] Failed to generate presigned URL for hotel invoice: {str(e)}")
                
                elif item.type == 'Activity' and item.activity_booking:
                    activity = item.activity_booking
                    item_data.update({
                        "activity_name": activity.activity_name,
                        "amount": float(activity.amount),
                        "provider": activity.provider,
                        "provider_booking_id": activity.provider_booking_id,
                        "date": activity.date.isoformat() if activity.date else None,
                        "timing": activity.timing,
                        "location": activity.location,
                        "meta_information": activity.meta_information
                    })
                    
                    # Generate presigned URL for invoice if available (manually uploaded)
                    if activity.invoice_file:
                        try:
                            presigned_url = invoice_service.generate_presigned_url(activity.invoice_file, expiration=3600)
                            item_data["invoice_download_url"] = presigned_url
                            logger.info(f"[BookingDetailsAPI] Generated presigned URL for activity invoice")
                        except Exception as e:
                            logger.warning(f"[BookingDetailsAPI] Failed to generate presigned URL for activity invoice: {str(e)}")
                
                elif item.type == 'Transfer' and item.transfer_booking:
                    transfer = item.transfer_booking
                    item_data.update({
                        "transferz_booking_id": transfer.transferz_booking_id,
                        "pickup_location": transfer.pickup_location,
                        "dropoff_location": transfer.dropoff_location,
                        "pickup_datetime": transfer.pickup_datetime.isoformat(),
                        "amount": float(transfer.amount),
                        "currency": transfer.currency,
                        "status": transfer.status,
                        "confirmation_number": transfer.confirmation_number,
                        "meta_information": transfer.meta_information
                    })
                    
                    # Generate presigned URL for invoice if available
                    if transfer.invoice_file:
                        try:
                            presigned_url = invoice_service.generate_presigned_url(transfer.invoice_file, expiration=3600)
                            item_data["invoice_download_url"] = presigned_url
                            logger.info(f"[BookingDetailsAPI] Generated presigned URL for transfer invoice")
                        except Exception as e:
                            logger.warning(f"[BookingDetailsAPI] Failed to generate presigned URL for transfer invoice: {str(e)}")
                
                day_data["items"].append(item_data)
            
            # Don't forget to add the last day
            if day_data:
                response_data["itinerary"].append(day_data)
            
            logger.info(f"[BookingDetailsAPI] Successfully retrieved booking details")
            logger.debug(f"[BookingDetailsAPI] Response data size: {len(json.dumps(response_data, default=str))} characters")
            
            return Response(response_data, status=status.HTTP_200_OK)
            
        except Booking.DoesNotExist:
            logger.error(f"[BookingDetailsAPI] Booking not found: {booking_reference_id}")
            return Response(
                {"error": f"Booking not found: {booking_reference_id}"}, 
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"[BookingDetailsAPI] Unexpected error: {str(e)}", exc_info=True)
            return Response(
                {"error": f"Failed to retrieve booking details: {str(e)}"}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
