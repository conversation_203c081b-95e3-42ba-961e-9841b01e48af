from django.urls import path
from bookings.api.v1 import views


urlpatterns = [
    path(
        "review", 
        views.ReviewAPIView.as_view(),
        name="review-flights-hotels"
    ),
    # New proper flow endpoints as per KT document
    path(
        "packages/hold", 
        views.PackageHoldAPIView.as_view(),
        name="hold-package"
    ),
    path(
        "packages/confirm", 
        views.PackageConfirmAPIView.as_view(),
        name="confirm-package"
    ),
    # Booking details and invoice retrieval
    path(
        "booking-details/<str:booking_reference_id>/", 
        views.BookingDetailsAPIView.as_view(),
        name="booking-details"
    ),
    
]