from django.db import transaction
from bookings.models import (
    Booking, BookingItinerary, FlightBooking, 
    HotelBooking, ActivityBooking, TransferBooking, Passenger, BookedFlightSSR
)
from bookings.helpers.tripjack_booking_helper import TripjackBookingHelper
from bookings.helpers.transferz_booking_helper import TransferzBookingHelper
from bookings.logging_config import booking_metrics, log_performance
from bookings.utils import InvoiceS3Service
from bookings.services.invoice_generation_service import InvoiceGenerationService
import logging
import json
from datetime import date, datetime
from packages.models import Destination, CustomPackage
from decimal import Decimal
from django.db import transaction
from django.utils import timezone
from bookings.choices import BookingStatus

# Configure logger for booking services
logger = logging.getLogger(__name__)

class BookingException(Exception):
    """Custom exception for booking failures to return clean error messages."""
    def __init__(self, message, failed_item=None, details=None):
        super().__init__(message)
        self.failed_item = failed_item
        self.details = details or {}

class PackageBookingService:
    """
    Service to handle the creation of a complete package booking.
    Orchestrates DB operations and third-party API calls.
    
    Supports two modes:
    - HOLD: Place package on hold without payment (Step 3)
    - CONFIRM: Confirm a held package after payment (Step 4)
    """
    def __init__(self, user, partner, validated_data: dict, mode='HOLD'):
        self.user = user
        self.partner = partner
        self.validated_data = validated_data
        self.tripjack_helper = TripjackBookingHelper()
        self.transferz_helper = TransferzBookingHelper(partner=partner)  # Pass partner for Redis key lookup
        self.invoice_service = InvoiceS3Service()
        self.invoice_generation_service = InvoiceGenerationService()
        self.mode = mode
        
        logger.info(f"[PackageBookingService] Initialized for user: {user.email if user else 'None'}")
        logger.info(f"[PackageBookingService] Partner: {partner}")
        logger.info(f"[PackageBookingService] Mode: {mode}")
        logger.debug(f"[PackageBookingService] Validated data summary - Days: {len(validated_data.get('itinerary', []))}")
        logger.debug(f"[PackageBookingService] Service instance created with ID: {id(self)}")
        logger.info(f"[PackageBookingService] Invoice service initialized")
        
        if mode in ['HOLD', 'CONFIRM']:
            logger.info(f"[PackageBookingService] Booker email: {validated_data.get('booker_email')}")
            logger.info(f"[PackageBookingService] Booker phone: {validated_data.get('booker_phone_number')}")
            logger.info(f"[PackageBookingService] Package key: {validated_data.get('package')}")
        elif mode == 'CONFIRM':
            logger.info(f"[PackageBookingService] Booking reference ID: {validated_data.get('booking_reference_id')}")

    def _get_package_from_key(self, package_key):
        """Get Package instance from package key if provided"""
        if not package_key:
            return None
            
        try:
            # Try to get package by external_id first
            try:
                package = CustomPackage.objects.get(external_id=package_key, partner=self.partner)
                logger.info(f"[PackageBookingService] Found package by external_id: {package.title}")
                return package
            except CustomPackage.DoesNotExist:
                pass
            
            # Try to get package by package_no
            try:
                package = CustomPackage.objects.get(package_no=package_key, partner=self.partner)
                logger.info(f"[PackageBookingService] Found package by package_no: {package.title}")
                return package
            except CustomPackage.DoesNotExist:
                pass
            
            # Try to get package by ID
            try:
                package = CustomPackage.objects.get(id=package_key, partner=self.partner)
                logger.info(f"[PackageBookingService] Found package by ID: {package.title}")
                return package
            except (CustomPackage.DoesNotExist, ValueError):
                pass
                
            logger.warning(f"[PackageBookingService] Package not found for key: {package_key}")
            return None
            
        except Exception as e:
            logger.error(f"[PackageBookingService] Error getting package: {str(e)}")
            return None

    def _prepare_tripjack_data(self, data):
        """
        Convert Python objects to JSON-serializable format for Tripjack API
        """
        logger.debug(f"[PackageBookingService] Preparing data for Tripjack API")
        
        if isinstance(data, dict):
            result = {}
            for key, value in data.items():
                result[key] = self._prepare_tripjack_data(value)
            return result
        elif isinstance(data, list):
            return [self._prepare_tripjack_data(item) for item in data]
        elif isinstance(data, (date, datetime)):
            # Convert date/datetime objects to ISO string format
            formatted_date = data.isoformat() if hasattr(data, 'isoformat') else str(data)
            logger.debug(f"[PackageBookingService] Converted date object to string: {formatted_date}")
            return formatted_date
        else:
            return data

    def _prepare_tripjack_flight_payload(self, flight_details):
        """
        Convert our flight details format to Tripjack API format
        """
        logger.debug(f"[PackageBookingService] Converting flight details to Tripjack format")
        
        # Convert delivery_info to deliveryInfo
        tripjack_payload = {}
        
        if 'delivery_info' in flight_details:
            tripjack_payload['deliveryInfo'] = flight_details['delivery_info']
        
        # Convert travellerInfo to proper format
        if 'travellerInfo' in flight_details:
            tripjack_travellers = []
            for traveller in flight_details['travellerInfo']:
                tripjack_traveller = {
                    'ti': traveller.get('title', '').title(),  # MR -> Mr
                    'fN': traveller.get('first_name', ''),
                    'lN': traveller.get('last_name', ''),
                    'pt': traveller.get('passenger_type', 'ADULT'),
                    'dob': self._convert_to_string(traveller.get('dob', '')),
                    'gender': traveller.get('gender', ''),
                }
                
                # Add passport details if present
                if traveller.get('passport_number'):
                    tripjack_traveller['pNum'] = traveller['passport_number']
                if traveller.get('passport_nationality'):
                    tripjack_traveller['pNat'] = traveller['passport_nationality']
                if traveller.get('passport_expiry_date'):
                    tripjack_traveller['pED'] = self._convert_to_string(traveller['passport_expiry_date'])
                if traveller.get('passport_issue_date'):
                    tripjack_traveller['pID'] = self._convert_to_string(traveller['passport_issue_date'])
                    
                tripjack_travellers.append(tripjack_traveller)
            
            tripjack_payload['travellerInfo'] = tripjack_travellers
        
        # Add other fields as needed
        for key, value in flight_details.items():
            if key not in ['delivery_info', 'travellerInfo']:
                tripjack_payload[key] = self._convert_to_string(value)
                
        logger.debug(f"[PackageBookingService] Tripjack payload conversion completed")
        return tripjack_payload

    def _prepare_tripjack_hotel_payload(self, hotel_details):
        """
        Convert our hotel details format to Tripjack API format according to official working cURL
        """
        logger.debug(f"[PackageBookingService] Converting hotel details to Tripjack format")
        
        tripjack_payload = {}
        
        # Required booking ID (should be set externally)
        # tripjack_payload['bookingId'] will be added by caller
        
        # Required type field
        tripjack_payload['type'] = 'HOTEL'
        
        # Convert guest details to roomTravellerInfo format
        rooms = hotel_details.get('rooms', [])
        room_traveller_info = []
        
        if rooms:
            for room in rooms:
                guest_details = room.get('guest_details', [])
                traveller_info = []
                
                for guest in guest_details:
                    traveller = {
                        'fN': guest.get('first_name', ''),
                        'lN': guest.get('last_name', ''),
                        'ti': guest.get('title', 'Mr'),
                        'pt': 'ADULT'  # Assuming adult for now
                    }
                    # Add PAN if available (as shown in working cURL)
                    if guest.get('pan'):
                        traveller['pan'] = guest['pan']
                    
                    traveller_info.append(traveller)
                
                room_traveller_info.append({
                    'travellerInfo': traveller_info
                })
        
        # If no room details, use lead guest info
        if not room_traveller_info:
            lead_guest = hotel_details.get('lead_guest', {})
            if lead_guest:
                room_traveller_info = [{
                    'travellerInfo': [{
                        'fN': lead_guest.get('first_name', ''),
                        'lN': lead_guest.get('last_name', ''),
                        'ti': lead_guest.get('title', 'Mr'),
                        'pt': 'ADULT'
                    }]
                }]
        
        tripjack_payload['roomTravellerInfo'] = room_traveller_info
        
        # Add delivery info (required by Tripjack)
        lead_guest = hotel_details.get('lead_guest', {})
        delivery_info = {
            'emails': [lead_guest.get('email', '<EMAIL>')],
            'contacts': [lead_guest.get('phone', '1234567890')],
            'code': ['+91']  # Default country code
        }
        tripjack_payload['deliveryInfo'] = delivery_info
        
        # Add payment info (required for booking)
        amount = hotel_details.get('amount') or hotel_details.get('total_amount') or 0
        tripjack_payload['paymentInfos'] = [{'amount': float(amount)}]
                
        logger.debug(f"[PackageBookingService] Hotel payload conversion completed")
        logger.debug(f"[PackageBookingService] Tripjack hotel payload keys: {list(tripjack_payload.keys())}")
        logger.debug(f"[PackageBookingService] roomTravellerInfo count: {len(tripjack_payload.get('roomTravellerInfo', []))}")
        logger.debug(f"[PackageBookingService] paymentInfos amount: {tripjack_payload.get('paymentInfos', [{}])[0].get('amount', 0)}")
        return tripjack_payload

    def _convert_to_string(self, value):
        """Convert date objects and other non-serializable objects to strings"""
        if isinstance(value, (date, datetime)):
            return value.isoformat() if hasattr(value, 'isoformat') else str(value)
        elif isinstance(value, dict):
            # Recursively handle dictionaries
            return {k: self._convert_to_string(v) for k, v in value.items()}
        elif isinstance(value, list):
            # Recursively handle lists
            return [self._convert_to_string(item) for item in value]
        return value

    @log_performance
    def _create_passengers_and_ssrs(self, flight_booking_obj, traveller_info_list):
        """
        Creates Passenger and BookedFlightSSR records in the DB.
        Returns a dictionary formatted for the Tripjack API payload.
        """
        logger.info(f"[PackageBookingService] Creating passengers and SSRs for flight booking ID: {flight_booking_obj.id}")
        logger.debug(f"[PackageBookingService] Number of passengers: {len(traveller_info_list)}")
        logger.debug(f"[PackageBookingService] Flight booking tripjack_booking_id: {flight_booking_obj.tripjack_booking_id}")
        
        ssr_payload = {"ssrBaggageInfos": [], "ssrMealInfos": [], "ssrSeatInfos": []}
        
        for idx, passenger_data in enumerate(traveller_info_list):
            logger.debug(f"[PackageBookingService] Processing passenger {idx + 1}: {passenger_data.get('first_name', 'Unknown')} {passenger_data.get('last_name', 'Unknown')}")
            logger.debug(f"[PackageBookingService] Passenger {idx + 1} type: {passenger_data.get('passenger_type', 'Unknown')}")
            logger.debug(f"[PackageBookingService] Passenger {idx + 1} title: {passenger_data.get('title', 'Unknown')}")
            
            # Pop SSR data so it's not passed to the Passenger model constructor
            baggage = passenger_data.pop('baggage', [])
            meals = passenger_data.pop('meals', [])
            seats = passenger_data.pop('seats', [])

            logger.debug(f"[PackageBookingService] Passenger {idx + 1} SSRs - Baggage: {len(baggage)}, Meals: {len(meals)}, Seats: {len(seats)}")

            passenger = Passenger.objects.create(
                flight_booking=flight_booking_obj,
                **passenger_data, # Unpack the rest of the validated data
                have_ssr_details=bool(baggage or meals or seats)
            )
            logger.debug(f"[PackageBookingService] Created passenger record with ID: {passenger.id}")
            logger.debug(f"[PackageBookingService] Passenger {idx + 1} has_ssr_details: {passenger.have_ssr_details}")

            # Create SSR records
            for ssr_idx, ssr_data in enumerate(baggage):
                BookedFlightSSR.objects.create(passenger=passenger, ssr_type='Baggage', **ssr_data)
                ssr_payload['ssrBaggageInfos'].append(ssr_data)
                logger.debug(f"[PackageBookingService] Created baggage SSR {ssr_idx + 1} for passenger {passenger.id}: {ssr_data}")
                    
            for ssr_idx, ssr_data in enumerate(meals):
                BookedFlightSSR.objects.create(passenger=passenger, ssr_type='Meal', **ssr_data)
                ssr_payload['ssrMealInfos'].append(ssr_data)
                logger.debug(f"[PackageBookingService] Created meal SSR {ssr_idx + 1} for passenger {passenger.id}: {ssr_data}")
                    
            for ssr_idx, ssr_data in enumerate(seats):
                BookedFlightSSR.objects.create(passenger=passenger, ssr_type='Seat', **ssr_data)
                ssr_payload['ssrSeatInfos'].append(ssr_data)
                logger.debug(f"[PackageBookingService] Created seat SSR {ssr_idx + 1} for passenger {passenger.id}: {ssr_data}")

        logger.info(f"[PackageBookingService] Successfully created {len(traveller_info_list)} passengers with SSRs")
        logger.debug(f"[PackageBookingService] Total SSRs created - Baggage: {len(ssr_payload['ssrBaggageInfos'])}, Meals: {len(ssr_payload['ssrMealInfos'])}, Seats: {len(ssr_payload['ssrSeatInfos'])}")
        logger.debug(f"[PackageBookingService] SSR payload for API: {json.dumps(ssr_payload, indent=2, default=str)}")
        
        return ssr_payload

    @log_performance
    def _process_flight_booking(self, item_data: dict, booking):
        """
        Process a single flight booking item.
        Returns the created FlightBooking object.
        """
        logger.info(f"[PackageBookingService] Starting flight booking process")
        logger.debug(f"[PackageBookingService] Flight item data keys: {list(item_data.keys())}")
        
        booking_id = item_data['booking_id']
        flight_details = item_data['flight_details']
        
        logger.info(f"[PackageBookingService] Flight booking ID: {booking_id}")
        logger.debug(f"[PackageBookingService] Flight details keys: {list(flight_details.keys())}")
        
        # Prepare data for Tripjack API call
        tripjack_data = self._prepare_tripjack_flight_payload(flight_details)
        
        # Call Tripjack hold API if in HOLD mode
        if self.mode == 'HOLD':
            logger.info(f"[PackageBookingService] Calling Tripjack hold API for booking: {booking_id}")
            
            # Add booking_id to the tripjack_data payload
            tripjack_data['bookingId'] = booking_id
            
            response = self.tripjack_helper.hold_flight(tripjack_data)
            logger.info(f"[PackageBookingService] Tripjack hold response received")
            logger.debug(f"[PackageBookingService] Hold response keys: {list(response.keys())}")
            
            # Update booking ID with Tripjack response
            booking_id = response.get('bookingId', booking_id)
            logger.debug(f"[PackageBookingService] Updated booking ID: {booking_id}")
        
        # Extract relevant flight information
        total_passengers = flight_details.get('total_passengers', 0)
        passengers_bifurcation = flight_details.get('passengers_bifurcation', {})
        is_international = flight_details.get('is_international', False)
        
        logger.debug(f"[PackageBookingService] Flight passengers: {total_passengers}")
        logger.debug(f"[PackageBookingService] International flight: {is_international}")
        logger.debug(f"[PackageBookingService] Passengers breakdown: {passengers_bifurcation}")
        
        # Create flight booking record
        flight_booking = FlightBooking.objects.create(
            tripjack_booking_id=booking_id,
            status=BookingStatus.ON_HOLD if self.mode == 'HOLD' else BookingStatus.PENDING,
            amount=item_data['amount'],
            is_international=is_international,
            total_passengers=total_passengers,
            passengers_bifurcation=passengers_bifurcation,
            gst_info=flight_details.get('gst_info'),
            delivery_info=flight_details.get('delivery_info'),
        )
        logger.info(f"[PackageBookingService] Created flight booking record with ID: {flight_booking.id}")
        logger.debug(f"[PackageBookingService] Flight booking Tripjack ID: {flight_booking.tripjack_booking_id}")
        logger.debug(f"[PackageBookingService] Flight booking status: {flight_booking.status}")

        # Create passenger records
        passengers_data = flight_details.get('travellerInfo', [])
        logger.info(f"[PackageBookingService] Creating {len(passengers_data)} passenger records")
        
        for passenger_data in passengers_data:
            logger.debug(f"[PackageBookingService] Creating passenger: {passenger_data.get('firstName', 'Unknown')} {passenger_data.get('lastName', 'Unknown')}")
            
            passenger = Passenger.objects.create(
                flight_booking=flight_booking,
                title=passenger_data.get('title', ''),
                first_name=passenger_data.get('firstName', ''),
                last_name=passenger_data.get('lastName', ''),
                passenger_type=passenger_data.get('pt', 'ADULT'),
                dob=passenger_data.get('dob'),
                gender=passenger_data.get('gender'),
                passport_number=passenger_data.get('passport_number'),
                passport_nationality=passenger_data.get('passport_nationality'),
                passport_issue_date=passenger_data.get('passport_issue_date'),
                passport_expiry_date=passenger_data.get('passport_expiry_date'),
            )
            logger.debug(f"[PackageBookingService] Created passenger record with ID: {passenger.id}")
            
            # Create SSR records for this passenger
            for ssr_type in ['baggage', 'meals', 'seats']:
                ssr_list = passenger_data.get(ssr_type, [])
                for ssr_data in ssr_list:
                    BookedFlightSSR.objects.create(
                        passenger=passenger,
                        ssr_type=ssr_type.upper(),
                        segment_key=ssr_data.get('segment_key', ''),
                        code=ssr_data.get('code', '')
                    )
                    logger.debug(f"[PackageBookingService] Created {ssr_type} SSR for passenger")
        
        # Store PNR details if available
        pnr_details = flight_details.get('pnr_details', [])
        logger.debug(f"[PackageBookingService] Flight booking PNR details count: {len(pnr_details)}")
        
        # Generate invoice for successful flight booking
        if flight_booking.status == BookingStatus.SUCCESS:
            try:
                logger.info(f"[PackageBookingService] Generating invoice for successful flight booking")
                invoice_url = self.invoice_generation_service.save_invoice_to_booking(
                    flight_booking, 
                    'flight'
                )
                logger.info(f"[PackageBookingService] Flight invoice generated: {invoice_url}")
            except Exception as e:
                logger.warning(f"[PackageBookingService] Failed to generate flight invoice: {str(e)}")
                # Continue processing even if invoice generation fails
              
        logger.info(f"[PackageBookingService] Flight booking processing completed")
        return flight_booking

    @log_performance
    def _process_hotel_booking(self, item_data: dict, booking):
        """
        Process a single hotel booking item.
        Returns the created HotelBooking object.
        """
        logger.info(f"[PackageBookingService] Starting hotel booking process")
        logger.debug(f"[PackageBookingService] Hotel item data keys: {list(item_data.keys())}")
        
        booking_id = item_data['booking_id']
        hotel_details = item_data['hotel_details']
        
        logger.info(f"[PackageBookingService] Hotel booking ID: {booking_id}")
        logger.debug(f"[PackageBookingService] Hotel details keys: {list(hotel_details.keys())}")
        
        # Call Tripjack book API directly for both HOLD and CONFIRM modes (no hold step for hotels)
        if self.mode in ['HOLD', 'CONFIRM']:
            logger.info(f"[PackageBookingService] Calling Tripjack hotel booking API for booking: {booking_id} (mode: {self.mode})")
            
            # Add amount to hotel details for payload preparation
            hotel_details['amount'] = item_data['amount']
            
            # Convert hotel details to proper format for Tripjack API
            tripjack_hotel_data = self._prepare_tripjack_hotel_payload(hotel_details)
            tripjack_hotel_data['bookingId'] = booking_id
            
            response = self.tripjack_helper.book_hotel(tripjack_hotel_data)
            logger.info(f"[PackageBookingService] Tripjack hotel booking response received")
            logger.debug(f"[PackageBookingService] Booking response keys: {list(response.keys())}")
            
            # Update booking ID with Tripjack response if different
            response_booking_id = response.get('bookingId', booking_id)
            if response_booking_id != booking_id:
                booking_id = response_booking_id
                logger.debug(f"[PackageBookingService] Updated hotel booking ID: {booking_id}")
        
        # Extract hotel information
        hotel_name = hotel_details.get('hotel_name', 'Unknown Hotel')
        check_in_date = hotel_details.get('check_in_date')
        check_out_date = hotel_details.get('check_out_date')
        room_data = hotel_details.get('rooms', [])
        
        logger.debug(f"[PackageBookingService] Hotel name: {hotel_name}")
        logger.debug(f"[PackageBookingService] Check-in: {check_in_date}")
        logger.debug(f"[PackageBookingService] Check-out: {check_out_date}")
        logger.debug(f"[PackageBookingService] Number of rooms: {len(room_data)}")
        
        # Create hotel booking record
        hotel_booking = HotelBooking.objects.create(
            tripjack_booking_id=booking_id,
            hotel_name=hotel_name,
            check_in_date=check_in_date,
            check_out_date=check_out_date,
            room_data=room_data,
            amount=item_data['amount'],
            status=BookingStatus.SUCCESS,  # Hotel booking is immediate for both HOLD and CONFIRM
            number_of_rooms=len(room_data),
            number_of_adults=hotel_details.get('adults', 0),
            number_of_children=hotel_details.get('children', 0),
        )
        logger.info(f"[PackageBookingService] Created hotel booking record with ID: {hotel_booking.id}")
        logger.debug(f"[PackageBookingService] Hotel booking Tripjack ID: {hotel_booking.tripjack_booking_id}")
        logger.debug(f"[PackageBookingService] Hotel booking status: {hotel_booking.status}")
        
        # Generate invoice for successful hotel booking
        if hotel_booking.status == BookingStatus.SUCCESS:
            try:
                logger.info(f"[PackageBookingService] Generating invoice for successful hotel booking")
                invoice_url = self.invoice_generation_service.save_invoice_to_booking(
                    hotel_booking, 
                    'hotel'
                )
                logger.info(f"[PackageBookingService] Hotel invoice generated: {invoice_url}")
            except Exception as e:
                logger.warning(f"[PackageBookingService] Failed to generate hotel invoice: {str(e)}")
                # Continue processing even if invoice generation fails
        
        logger.info(f"[PackageBookingService] Hotel booking processing completed")
        return hotel_booking

    @log_performance
    def _process_activity_booking(self, item_data: dict, booking):
        """
        Process a single activity booking item.
        Returns the created ActivityBooking object.
        """
        logger.info(f"[PackageBookingService] Starting activity booking process")
        logger.debug(f"[PackageBookingService] Activity item data keys: {list(item_data.keys())}")
        
        activity_details = item_data['activity_details']
        
        logger.info(f"[PackageBookingService] Activity name: {activity_details.get('activity_name')}")
        logger.debug(f"[PackageBookingService] Activity details keys: {list(activity_details.keys())}")
        
        # Convert date objects to strings for JSON serialization
        activity_details_for_json = self._convert_to_string(activity_details)
        
        # Create activity booking record
        activity_booking = ActivityBooking.objects.create(
            activity_name=activity_details['activity_name'],
            amount=item_data['amount'],
            provider=activity_details.get('external_provider', 'GetYourGuide'),
            provider_booking_id=activity_details.get('external_booking_reference'),
            date=activity_details.get('activity_date'),
            participants_count=len(activity_details.get('participants', [])),
            location=activity_details.get('location', {}).get('name', ''),
            description=activity_details.get('description', ''),
            meta_information={
                'participants': activity_details_for_json.get('participants', []),
                'location_details': activity_details_for_json.get('location', {}),
                'provider_details': activity_details_for_json
            }
        )
        logger.info(f"[PackageBookingService] Created activity booking record with ID: {activity_booking.id}")
        logger.debug(f"[PackageBookingService] Activity booking provider_booking_id: {activity_booking.provider_booking_id}")
        
        logger.info(f"[PackageBookingService] Activity booking processing completed")
        return activity_booking

    @log_performance
    def _process_transfer_booking(self, item_data: dict, booking):
        """
        Process a transfer booking using Transferz direct booking API.
        
        Transfer Flow:
        1. REVIEW API: Creates the booking directly with Transferz (status: NOT_PAID)
        2. HOLD mode: Create placeholder record with booking_id from review
        3. CONFIRM mode: Use booking_id from review and call pay_by_invoice to complete payment
        
        Expected item_data structure for HOLD:
        {
            "booking_id": "1678421",  # From review API response
            "transfer_details": {
                "pickup_location": "JFK Airport",
                "dropoff_location": "Manhattan", 
                "pickup_datetime": "2025-09-15T10:00:00Z",
                "passenger_count": 2,
                "vehicle_type": "sedan",
                "traveller_email": "<EMAIL>"
            }
        }
        
        Expected item_data structure for CONFIRM:
        {
            "booking_id": "1678421",  # From review API response
            "transfer_details": {
                "traveller_email": "<EMAIL>",
                # ... other details
            }
        }
        """
        logger.info(f"[PackageBookingService] Processing transfer booking in {self.mode} mode")
        
        transfer_details = item_data.get('transfer_details', {})
        logger.debug(f"[PackageBookingService] Transfer details: {transfer_details}")
        
        # Get booking_id from review API response (should be in item_data)
        transferz_booking_id = item_data.get('booking_id')
        
        if not transferz_booking_id:
            raise BookingException(
                "Missing required booking_id for transfer booking",
                details={"error": "booking_id from review API is required for transfer booking"}
            )
        
        logger.info(f"[PackageBookingService] Using Transferz booking ID from review: {transferz_booking_id}")
        
        # Extract metadata from transfer_details
        pickup_location = transfer_details.get('pickup_location', '')
        dropoff_location = transfer_details.get('dropoff_location', '')
        pickup_datetime = transfer_details.get('pickup_datetime')
        traveller_email = transfer_details.get('traveller_email', '<EMAIL>')
        
        # Convert pickup_datetime string to datetime object if needed
        if isinstance(pickup_datetime, str):
            try:
                from datetime import datetime
                pickup_datetime = datetime.fromisoformat(pickup_datetime.replace('Z', '+00:00'))
            except:
                pickup_datetime = timezone.now()
        elif pickup_datetime is None:
            pickup_datetime = timezone.now()
        
        if self.mode == 'HOLD':
            # HOLD mode: Just create a record with booking_id from review API
            # Don't call Transferz API again since booking was already created in review
            logger.info(f"[PackageBookingService] Creating transfer booking record for HOLD mode")
            
            transfer_booking = TransferBooking.objects.create(
                transferz_booking_id=str(transferz_booking_id),
                transferz_quote_id='',  # Quote ID not needed for hold
                pickup_location=pickup_location,
                dropoff_location=dropoff_location,
                pickup_datetime=pickup_datetime,
                vehicle_type=transfer_details.get('vehicle_type', ''),
                service_type=transfer_details.get('service_type', ''),
                passenger_count=transfer_details.get('passenger_count', 1),
                amount=item_data.get('amount', 0),
                currency=transfer_details.get('currency', 'INR'),
                status=BookingStatus.ON_HOLD,
                confirmation_number='',  # Will be updated when confirmed
                passenger_name=traveller_email,
                passenger_email=traveller_email,
                booking_details={'mode': 'HOLD', 'booking_created_in_review': True},
                meta_information=self._convert_to_string(transfer_details)
            )
            
            logger.info(f"[PackageBookingService] Transfer booking record created for HOLD - ID: {transfer_booking.id}")
            return transfer_booking
            
        elif self.mode == 'CONFIRM':
            # CONFIRM mode: Call pay_by_invoice to complete the payment
            logger.info(f"[PackageBookingService] Processing transfer payment for CONFIRM mode")
            
            try:
                # Call Transferz pay_by_invoice API
                payment_response = self.transferz_helper.pay_by_invoice(
                    booking_id=str(transferz_booking_id),
                    payment_params={}  # Additional payment parameters if needed
                )
                
                logger.info(f"[PackageBookingService] Transferz payment successful for booking: {transferz_booking_id}")
                
                # Get updated booking details after payment
                try:
                    booking_details = self.transferz_helper.get_booking_details(str(transferz_booking_id))
                    logger.info(f"[PackageBookingService] Retrieved updated booking details after payment")
                except Exception as details_error:
                    logger.warning(f"[PackageBookingService] Could not fetch updated booking details: {str(details_error)}")
                    booking_details = {'payment_response': payment_response}
                
                # Extract updated information from booking details
                total_amount = item_data.get('amount', 0)
                currency = 'INR'
                booking_code = ''
                
                if 'journeys' in booking_details:
                    journeys = booking_details.get('journeys', [])
                    if journeys:
                        first_journey = journeys[0]
                        price_summary = first_journey.get('priceSummary', {})
                        total_amount = price_summary.get('price', total_amount)
                        currency = price_summary.get('currency', currency)
                        booking_code = first_journey.get('code', '')
                        
                        # Update location details from actual booking
                        origin = first_journey.get('origin', {})
                        destination = first_journey.get('destination', {})
                        pickup_info = first_journey.get('pickup', {})
                        
                        if origin.get('resolvedAddress'):
                            pickup_location = origin.get('resolvedAddress')
                        if destination.get('resolvedAddress'):
                            dropoff_location = destination.get('resolvedAddress')
                        if pickup_info.get('localTime'):
                            try:
                                pickup_datetime = datetime.fromisoformat(pickup_info.get('localTime'))
                            except:
                                pass
                
                # Create transfer booking record with payment completion
                transfer_booking = TransferBooking.objects.create(
                    transferz_booking_id=str(transferz_booking_id),
                    transferz_quote_id='',  # Quote ID not needed for direct booking
                    pickup_location=pickup_location,
                    dropoff_location=dropoff_location,
                    pickup_datetime=pickup_datetime,
                    vehicle_type=transfer_details.get('vehicle_type', ''),
                    service_type=transfer_details.get('service_type', ''),
                    passenger_count=transfer_details.get('passenger_count', 1),
                    amount=total_amount,
                    currency=currency,
                    status=BookingStatus.SUCCESS,
                    confirmation_number=booking_code,
                    passenger_name=traveller_email,
                    passenger_email=traveller_email,
                    booking_details=booking_details,
                    meta_information=self._convert_to_string({
                        **transfer_details,
                        'payment_response': payment_response,
                        'mode': 'CONFIRM'
                    })
                )
                
                logger.info(f"[PackageBookingService] Transfer booking record created for CONFIRM - ID: {transfer_booking.id}")
                
                # Generate invoice for successful transfer booking
                if transfer_booking.status == BookingStatus.SUCCESS:
                    try:
                        logger.info(f"[PackageBookingService] Generating invoice for successful transfer booking")
                        invoice_url = self.invoice_generation_service.save_invoice_to_booking(
                            transfer_booking, 
                            'transfer'
                        )
                        logger.info(f"[PackageBookingService] Transfer invoice generated: {invoice_url}")
                    except Exception as e:
                        logger.warning(f"[PackageBookingService] Failed to generate transfer invoice: {str(e)}")
                        # Continue processing even if invoice generation fails
                
                return transfer_booking
                
            except Exception as e:
                logger.error(f"[PackageBookingService] Transfer payment failed: {str(e)}")
                
                # Create a failed booking record
                transfer_booking = TransferBooking.objects.create(
                    transferz_booking_id=str(transferz_booking_id),
                    transferz_quote_id='',
                    pickup_location=pickup_location,
                    dropoff_location=dropoff_location,
                    pickup_datetime=pickup_datetime,
                    vehicle_type=transfer_details.get('vehicle_type', ''),
                    service_type=transfer_details.get('service_type', ''),
                    passenger_count=transfer_details.get('passenger_count', 1),
                    amount=0,
                    currency='INR',
                    status=BookingStatus.FAILED,
                    confirmation_number='',
                    passenger_name=traveller_email,
                    passenger_email=traveller_email,
                    booking_details={'error': str(e), 'payment_failed': True},
                    meta_information=self._convert_to_string(transfer_details)
                )
                
                # Re-raise the exception with booking details
                raise BookingException(
                    f"Transfer payment failed: {str(e)}",
                    details={
                        "transferz_error": str(e),
                        "booking_id": transfer_booking.id,
                        "transferz_booking_id": transferz_booking_id
                    }
                )

    @transaction.atomic
    @log_performance
    def create_package(self):
        """
        Main service method to create the entire package.
        Wrapped in a transaction to ensure all-or-nothing data integrity.
        
        Behavior:
        - HOLD: Creates booking with ON_HOLD status, calls hold APIs
        """
        logger.info(f"[PackageBookingService] Starting package creation process")
        logger.info(f"[PackageBookingService] Mode: {self.mode}")
        logger.info(f"[PackageBookingService] Booker: {self.validated_data['booker_email']}")
        logger.info(f"[PackageBookingService] Transaction is atomic: True")
        logger.debug(f"[PackageBookingService] Database transaction started")
        
        # Get package if provided
        package = self._get_package_from_key(self.validated_data.get('package'))
        
        # Handle destination - fetch by external_id (destination is mandatory)
        destination_external_id = self.validated_data['destination']  # Required field
        try:
            destination = Destination.objects.get(
                external_id=destination_external_id,
                partner=self.partner
            )
            logger.info(f"[PackageBookingService] Found destination: {destination.title} (external_id: {destination_external_id})")
        except Destination.DoesNotExist:
            logger.error(f"[PackageBookingService] Invalid destination external_id: {destination_external_id}")
            raise BookingException(f"Invalid destination: {destination_external_id}")
        
        # Determine initial booking status based on mode
        if self.mode == 'HOLD':
            initial_status = BookingStatus.ON_HOLD
        else:
            initial_status = BookingStatus.PENDING
        
        booking = Booking.objects.create(
            user=self.user,
            partner=self.partner,
            package=package,
            destination=destination,
            booker_email=self.validated_data['booker_email'],
            booker_phone_number=self.validated_data['booker_phone_number'],
            status=initial_status
        )
        logger.info(f"[PackageBookingService] Created main booking record with ID: {booking.id}")
        logger.debug(f"[PackageBookingService] Main booking status: {booking.status}")
        logger.debug(f"[PackageBookingService] Main booking reference ID: {booking.external_id}")
        if package:
            logger.info(f"[PackageBookingService] Associated with package: {package.title}")
            
        total_amount = 0
        total_days = len(self.validated_data['itinerary'])
        logger.info(f"[PackageBookingService] Processing {total_days} days of itinerary")

        for day_idx, day_data in enumerate(self.validated_data['itinerary']):
            day_number = day_data['day_number']
            logger.info(f"[PackageBookingService] Processing day {day_number} ({day_idx + 1}/{total_days})")
            logger.debug(f"[PackageBookingService] Day {day_number} date: {day_data['date']}")
                
            total_items = len(day_data['items'])
            logger.info(f"[PackageBookingService] Day {day_number} has {total_items} items to process")
            
            for item_idx, item in enumerate(sorted(day_data['items'], key=lambda x: x.get('order', 0))):
                item_type = item['type']
                item_amount = item['amount']
                total_amount += item_amount
                
                logger.info(f"[PackageBookingService] Processing item {item_idx + 1}/{total_items} - Type: {item_type}, Amount: {item_amount}")
                logger.debug(f"[PackageBookingService] Running total amount: {total_amount}")
                logger.debug(f"[PackageBookingService] Item order: {item.get('order', 0)}")
                
                # Create the booking itinerary item with the appropriate booking type
                itinerary_item_data = {
                    'booking': booking,
                    'day_number': day_number,
                    'date': day_data['date'],
                    'type': item_type,
                    'order': item.get('order', 0)
                }

                if item_type == 'Flight':
                    logger.info(f"[PackageBookingService] Processing flight booking for day {day_number}")
                    flight_booking = self._process_flight_booking(item, booking)
                    itinerary_item_data['flight_booking'] = flight_booking
                    
                elif item_type == 'Hotel':
                    logger.info(f"[PackageBookingService] Processing hotel booking for day {day_number}")
                    hotel_booking = self._process_hotel_booking(item, booking)
                    itinerary_item_data['hotel_booking'] = hotel_booking
                    
                elif item_type == 'Activity':
                    logger.info(f"[PackageBookingService] Processing activity booking for day {day_number}")
                    activity_booking = self._process_activity_booking(item, booking)
                    itinerary_item_data['activity_booking'] = activity_booking
                    
                elif item_type == 'Transfer':
                    logger.info(f"[PackageBookingService] Processing transfer booking for day {day_number}")
                    transfer_booking = self._process_transfer_booking(item, booking)
                    itinerary_item_data['transfer_booking'] = transfer_booking
                
                # Create the booking itinerary record
                itinerary_item = BookingItinerary.objects.create(**itinerary_item_data)
                logger.info(f"[PackageBookingService] Created booking itinerary item with ID: {itinerary_item.id}")
                logger.debug(f"[PackageBookingService] Itinerary item type: {itinerary_item.type}")
        
        # Update booking with final details
        booking.total_amount = total_amount
        
        # Set final status based on mode
        if self.mode == 'HOLD':
            booking.status = BookingStatus.ON_HOLD
            logger.info(f"[PackageBookingService] Package placed on hold successfully")
        else:
            booking.status = BookingStatus.SUCCESS
            logger.info(f"[PackageBookingService] Package booking completed successfully")
            
        booking.save()
        
        logger.info(f"[PackageBookingService] Package creation completed")
        logger.info(f"[PackageBookingService] Final booking details - ID: {booking.id}, Total Amount: {total_amount}, Status: {booking.status}")
        logger.debug(f"[PackageBookingService] Database transaction committed")
        logger.debug(f"[PackageBookingService] Booking updated_at: {booking.updated_at}")
        
        # Log final metrics
        booking_metrics.log_metrics_summary()
        
        return booking

    @transaction.atomic
    @log_performance
    def confirm_package(self, booking_reference_id=None):
        """
        Confirms a previously held package booking by calling Tripjack's confirm APIs.
        This is the final step after payment completion.
        """
        logger.info(f"[PackageBookingService] Starting package confirmation")
        logger.info(f"[PackageBookingService] Booking reference ID: {booking_reference_id}")
        logger.info(f"[PackageBookingService] Transaction is atomic: True")
        
        try:
            booking_obj = Booking.objects.get(
                external_id=booking_reference_id,
                status=BookingStatus.ON_HOLD
            )
            logger.info(f"[PackageBookingService] Found booking to confirm - ID: {booking_obj.id}")
        except Booking.DoesNotExist:
            raise BookingException(
                f"No booking found with reference ID {booking_reference_id} or booking is not in ON_HOLD status"
            )
        
        # Get all flight bookings that need confirmation
        flight_bookings = FlightBooking.objects.filter(
            day_items__booking=booking_obj,
            status=BookingStatus.ON_HOLD
        )
        
        # Get all hotel bookings that need confirmation
        hotel_bookings = HotelBooking.objects.filter(
            day_items__booking=booking_obj,
            status=BookingStatus.ON_HOLD
        )
        
        logger.info(f"[PackageBookingService] Found {flight_bookings.count()} flight bookings to confirm")
        logger.info(f"[PackageBookingService] Found {hotel_bookings.count()} hotel bookings to confirm")
        
        # Confirm each flight booking
        for flight_booking in flight_bookings:
            logger.info(f"[PackageBookingService] Confirming flight booking ID: {flight_booking.id}")
            
            confirm_payload = {
                "bookingId": flight_booking.tripjack_booking_id,
                "paymentInfos": [
                    {"amount": str(flight_booking.amount)}
                ]
            }
            
            booking_metrics.increment_metric('tripjack_api_calls')
            
            try:
                response = self.tripjack_helper.confirm_flight(confirm_payload)
                
                flight_booking.status = BookingStatus.SUCCESS
                if not flight_booking.meta_information:
                    flight_booking.meta_information = {}
                flight_booking.meta_information['confirm_response'] = response
                flight_booking.save()
                
                logger.info(f"[PackageBookingService] Flight booking {flight_booking.id} confirmed successfully")
                
                # Fetch final booking details
                self._fetch_and_save_booking_details(flight_booking, 'flight')
                
            except BookingException as e:
                logger.error(f"[PackageBookingService] Flight confirmation failed: {str(e)}")
                flight_booking.status = BookingStatus.FAILED
                flight_booking.save()
                raise BookingException(
                    f"Flight confirmation failed: {str(e)}",
                    details=getattr(e, 'details', {})
                )
        
        # Confirm each hotel booking
        for hotel_booking in hotel_bookings:
            logger.info(f"[PackageBookingService] Confirming hotel booking ID: {hotel_booking.id}")
            
            # Hotels don't have separate confirm API in Tripjack - they are booked directly
            # But we need to verify the booking status and fetch final details
            try:
                # Fetch final hotel booking details
                self._fetch_and_save_booking_details(hotel_booking, 'hotel')
                
                # Update status to SUCCESS if details fetch was successful
                hotel_booking.status = BookingStatus.SUCCESS
                hotel_booking.save()
                
                logger.info(f"[PackageBookingService] Hotel booking {hotel_booking.id} confirmed successfully")
                
            except Exception as e:
                logger.error(f"[PackageBookingService] Hotel confirmation failed: {str(e)}")
                hotel_booking.status = BookingStatus.FAILED
                hotel_booking.save()
                raise BookingException(
                    f"Hotel confirmation failed: {str(e)}",
                    details={'error_type': type(e).__name__}
                )
        
        # Confirm each transfer booking
        transfer_bookings = TransferBooking.objects.filter(
            day_items__booking=booking_obj,
            status=BookingStatus.ON_HOLD
        )
        
        logger.info(f"[PackageBookingService] Found {transfer_bookings.count()} transfer bookings to confirm")
        
        for transfer_booking in transfer_bookings:
            logger.info(f"[PackageBookingService] Confirming transfer booking ID: {transfer_booking.id}")
            
            try:
                # For transfers, confirmation might involve payment or just status update
                # depending on whether they were paid during hold or need payment now
                if transfer_booking.transferz_booking_id.startswith(('PLACEHOLDER_', 'FAILED_')):
                    # Skip API calls for placeholder or failed bookings
                    logger.warning(f"[PackageBookingService] Skipping confirmation for placeholder/failed transfer booking: {transfer_booking.transferz_booking_id}")
                    transfer_booking.status = BookingStatus.FAILED
                    transfer_booking.save()
                    continue
                
                # If transfer was created with real API, we might need to pay or confirm
                if hasattr(self.transferz_helper, 'pay_by_invoice'):
                    try:
                        # Attempt to pay for the transfer using the correct API
                        logger.info(f"[PackageBookingService] Attempting to pay for transfer booking: {transfer_booking.transferz_booking_id}")
                        payment_response = self.transferz_helper.pay_by_invoice(
                            booking_id=transfer_booking.transferz_booking_id,
                            payment_params={}  # Additional payment parameters can be added here if needed
                        )
                        logger.info(f"[PackageBookingService] Transfer payment completed for booking: {transfer_booking.transferz_booking_id}")
                        
                        # Update transfer booking with payment details
                        if not transfer_booking.meta_information:
                            transfer_booking.meta_information = {}
                        transfer_booking.meta_information['payment_response'] = payment_response
                        
                    except Exception as payment_error:
                        logger.warning(f"[PackageBookingService] Transfer payment failed, but continuing: {str(payment_error)}")
                        # Some transfers might be paid differently, so don't fail the entire process
                
                # Fetch final transfer booking details
                try:
                    details_response = self.transferz_helper.get_booking_details(transfer_booking.transferz_booking_id)
                    transfer_booking.booking_details = details_response
                    logger.info(f"[PackageBookingService] Updated transfer booking details from API")
                except Exception as details_error:
                    logger.warning(f"[PackageBookingService] Could not fetch transfer booking details: {str(details_error)}")
                
                # Update status to SUCCESS
                transfer_booking.status = BookingStatus.SUCCESS
                transfer_booking.save()
                
                logger.info(f"[PackageBookingService] Transfer booking {transfer_booking.id} confirmed successfully")
                
                # Generate invoice for confirmed transfer booking
                try:
                    logger.info(f"[PackageBookingService] Generating invoice for confirmed transfer booking")
                    invoice_url = self.invoice_generation_service.save_invoice_to_booking(
                        transfer_booking, 
                        'transfer'
                    )
                    logger.info(f"[PackageBookingService] Transfer invoice generated: {invoice_url}")
                except Exception as e:
                    logger.warning(f"[PackageBookingService] Failed to generate transfer invoice: {str(e)}")
                    # Continue processing even if invoice generation fails
                
            except Exception as e:
                logger.error(f"[PackageBookingService] Transfer confirmation failed: {str(e)}")
                transfer_booking.status = BookingStatus.FAILED
                transfer_booking.save()
                
                # For transfers, we might want to continue even if one fails
                # unless it's critical. Log but don't necessarily fail the entire booking
                logger.warning(f"[PackageBookingService] Transfer booking {transfer_booking.id} failed confirmation, but continuing with other bookings")
        
        # Update main booking status
        booking_obj.status = BookingStatus.SUCCESS
        booking_obj.save()
        
        # Generate main booking invoice after all individual bookings are confirmed
        try:
            logger.info(f"[PackageBookingService] Generating main booking invoice after successful confirmation")
            invoice_url = self.invoice_generation_service.save_invoice_to_booking(
                booking_obj, 
                'main'
            )
            logger.info(f"[PackageBookingService] Main booking invoice generated: {invoice_url}")
            
            # TODO: Add email sending functionality here
            # self._send_invoice_email(booking_obj, invoice_url)
            
        except Exception as e:
            logger.warning(f"[PackageBookingService] Failed to generate main booking invoice: {str(e)}")
            # Continue even if invoice generation fails - booking is already successful
        
        logger.info(f"[PackageBookingService] Package confirmation completed")
        logger.info(f"[PackageBookingService] Final booking status: {booking_obj.status}")
        
        return booking_obj
    
    def _sanitize_tripjack_payload_for_logging(self, payload):
        """
        Sanitize sensitive data in Tripjack payloads for logging.
        Masks passport numbers, GST numbers, and other sensitive fields.
        """
        if not isinstance(payload, dict):
            return payload
            
        sanitized = payload.copy()
        
        # Sanitize traveller info
        if 'travellerInfo' in sanitized:
            logger.debug(f"[PackageBookingService] Sanitizing traveller info for {len(sanitized['travellerInfo'])} passengers")
            sanitized_travellers = []
            for i, traveller in enumerate(sanitized['travellerInfo'], 1):
                logger.debug(f"[PackageBookingService] Sanitizing traveller {i}")
                sanitized_traveller = traveller.copy()
                if 'pNum' in sanitized_traveller:
                    sanitized_traveller['pNum'] = '***MASKED***'
                sanitized_travellers.append(sanitized_traveller)
            sanitized['travellerInfo'] = sanitized_travellers
            logger.debug(f"[PackageBookingService] Tripjack traveller sanitization completed")
        
        # Sanitize GST info
        if 'gstInfo' in sanitized and sanitized['gstInfo']:
            if 'gstNumber' in sanitized['gstInfo']:
                sanitized['gstInfo']['gstNumber'] = '***MASKED***'
        
        logger.debug(f"[PackageBookingService] Tripjack payload sanitization completed")
        return sanitized

    def _fetch_and_save_booking_details(self, booking_obj, booking_type: str):
        """
        Fetch detailed booking information from Tripjack and save to database
        Also attempt to download and save invoices if available
        """
        logger.info(f"[PackageBookingService] Fetching booking details for {booking_type} booking: {booking_obj.tripjack_booking_id}")
        
        try:
            if booking_type.lower() == 'flight':
                details_response = self.tripjack_helper.get_flight_booking_details(booking_obj.tripjack_booking_id)
                
                # Save booking details
                booking_obj.booking_details = details_response
                
                # Extract and save ticket numbers
                order = details_response.get('order', {})
                item_infos = order.get('itemInfos', {})
                air_info = item_infos.get('AIR', {})
                traveller_infos = air_info.get('travellerInfos', [])
                
                ticket_numbers = []
                for traveller in traveller_infos:
                    ticket_details = traveller.get('ticketNumberDetails', {})
                    if ticket_details:
                        ticket_numbers.extend(list(ticket_details.values()))
                
                if ticket_numbers:
                    booking_obj.ticket_numbers = ticket_numbers
                    logger.info(f"[PackageBookingService] Saved {len(ticket_numbers)} ticket numbers for flight booking")
                
            elif booking_type.lower() == 'hotel':
                details_response = self.tripjack_helper.get_hotel_booking_details(booking_obj.tripjack_booking_id)
                
                # Save booking details
                booking_obj.booking_details = details_response
                
                # Extract and save voucher data
                voucher_data = details_response.get('voucherData', {})
                if voucher_data:
                    booking_obj.voucher_data = voucher_data
                    logger.info(f"[PackageBookingService] Saved voucher data for hotel booking")
                
                # Update confirmation number if different/more detailed
                confirmation_no = details_response.get('confirmationNo')
                if confirmation_no and confirmation_no != booking_obj.confirmation_number:
                    booking_obj.confirmation_number = confirmation_no
                    logger.info(f"[PackageBookingService] Updated hotel confirmation number: {confirmation_no}")
            
            # Save booking details first
            booking_obj.save()
            
            # Generate and save invoice
            try:
                logger.info(f"[PackageBookingService] Generating invoice for {booking_type} booking")
                
                # First attempt to download invoice from Tripjack if available
                invoice_url = self.tripjack_helper.download_invoice(booking_obj.tripjack_booking_id, booking_type)
                
                if invoice_url:
                    logger.info(f"[PackageBookingService] Tripjack invoice URL found, downloading official invoice")
                    file_url = self.invoice_service.download_and_save_invoice(
                        invoice_url, 
                        booking_obj, 
                        booking_type
                    )
                    logger.info(f"[PackageBookingService] Official invoice saved to FileField: {file_url}")
                else:
                    # Generate our own invoice if Tripjack doesn't provide one
                    logger.info(f"[PackageBookingService] No Tripjack invoice available, generating custom invoice")
                    invoice_url = self.invoice_generation_service.save_invoice_to_booking(
                        booking_obj, 
                        booking_type
                    )
                    logger.info(f"[PackageBookingService] Custom invoice generated and saved: {invoice_url}")
                    
            except Exception as invoice_error:
                logger.warning(f"[PackageBookingService] Failed to generate/download invoice for {booking_type} booking: {str(invoice_error)}")
                # Don't fail the entire booking process if invoice generation fails
            
            logger.info(f"[PackageBookingService] Successfully processed booking details for {booking_type} booking")
            
        except Exception as e:
            logger.error(f"[PackageBookingService] Failed to fetch booking details for {booking_type} booking: {str(e)}")
            # Don't fail the entire booking process if details fetch fails

    @log_performance
    def confirm_package(self, booking_reference_id):
        """
        Confirm a previously held package booking
        
        Args:
            booking_reference_id: External ID of the booking to confirm
            
        Returns:
            Booking: The confirmed booking object
        """
        logger.info(f"[PackageBookingService] Starting package confirmation")
        logger.info(f"[PackageBookingService] Booking reference ID: {booking_reference_id}")
        
        with transaction.atomic():
            logger.info(f"[PackageBookingService] Transaction is atomic: {transaction.get_autocommit()}")
            
            # Find the booking to confirm
            try:
                booking_obj = Booking.objects.get(external_id=booking_reference_id)
                logger.info(f"[PackageBookingService] Found booking to confirm - ID: {booking_obj.id}")
            except Booking.DoesNotExist:
                logger.error(f"[PackageBookingService] Booking not found: {booking_reference_id}")
                raise BookingException(f"Booking not found: {booking_reference_id}")
            
            # Get all pending booking items to confirm
            flight_bookings = FlightBooking.objects.filter(
                day_items__booking=booking_obj, 
                status__in=[BookingStatus.PENDING, BookingStatus.ON_HOLD]
            )
            hotel_bookings = HotelBooking.objects.filter(
                day_items__booking=booking_obj, 
                status__in=[BookingStatus.PENDING, BookingStatus.ON_HOLD]
            )
            transfer_bookings = TransferBooking.objects.filter(
                day_items__booking=booking_obj, 
                status__in=[BookingStatus.PENDING, BookingStatus.ON_HOLD]
            )
            
            logger.info(f"[PackageBookingService] Found {flight_bookings.count()} flight bookings to confirm")
            logger.info(f"[PackageBookingService] Found {hotel_bookings.count()} hotel bookings to confirm")
            logger.info(f"[PackageBookingService] Found {transfer_bookings.count()} transfer bookings to confirm")
            
            # Confirm all flight bookings
            for flight_booking in flight_bookings:
                logger.info(f"[PackageBookingService] Confirming flight booking ID: {flight_booking.id}")
                try:
                    # Create confirm payload for Tripjack
                    confirm_payload = {
                        "bookingId": flight_booking.tripjack_booking_id
                    }
                    response = self.tripjack_helper.confirm_flight(confirm_payload)
                    if response:
                        flight_booking.status = BookingStatus.SUCCESS
                        flight_booking.save()
                        logger.info(f"[PackageBookingService] Flight booking {flight_booking.id} confirmed successfully")
                        
                        # Generate invoice for successful flight booking
                        try:
                            logger.info(f"[PackageBookingService] Generating invoice for confirmed flight booking")
                            invoice_url = self.invoice_generation_service.save_invoice_to_booking(
                                flight_booking, 
                                'flight'
                            )
                            logger.info(f"[PackageBookingService] Flight invoice generated: {invoice_url}")
                        except Exception as e:
                            logger.warning(f"[PackageBookingService] Failed to generate flight invoice: {str(e)}")
                    else:
                        logger.error(f"[PackageBookingService] Failed to confirm flight booking {flight_booking.id}")
                        raise BookingException(f"Failed to confirm flight booking {flight_booking.id}")
                except Exception as e:
                    logger.error(f"[PackageBookingService] Error confirming flight booking {flight_booking.id}: {str(e)}")
                    raise BookingException(f"Failed to confirm flight booking {flight_booking.id}: {str(e)}")
            
            # For hotel bookings, if they're in HOLD/PENDING status, they might need additional confirmation
            # But based on Tripjack API, hotels are usually confirmed immediately upon booking
            for hotel_booking in hotel_bookings:
                logger.info(f"[PackageBookingService] Updating hotel booking status ID: {hotel_booking.id}")
                try:
                    # Hotels are typically confirmed immediately upon booking in Tripjack
                    # Just update status and generate invoice
                    hotel_booking.status = BookingStatus.SUCCESS
                    hotel_booking.save()
                    logger.info(f"[PackageBookingService] Hotel booking {hotel_booking.id} marked as confirmed")
                    
                    # Generate invoice for hotel booking
                    try:
                        logger.info(f"[PackageBookingService] Generating invoice for confirmed hotel booking")
                        invoice_url = self.invoice_generation_service.save_invoice_to_booking(
                            hotel_booking, 
                            'hotel'
                        )
                        logger.info(f"[PackageBookingService] Hotel invoice generated: {invoice_url}")
                    except Exception as e:
                        logger.warning(f"[PackageBookingService] Failed to generate hotel invoice: {str(e)}")
                except Exception as e:
                    logger.error(f"[PackageBookingService] Error updating hotel booking {hotel_booking.id}: {str(e)}")
                    raise BookingException(f"Failed to confirm hotel booking {hotel_booking.id}: {str(e)}")
            
            # Confirm all transfer bookings
            for transfer_booking in transfer_bookings:
                logger.info(f"[PackageBookingService] Confirming transfer booking ID: {transfer_booking.id}")
                
                # For transfers, we need to pay and then get booking details
                try:
                    logger.info(f"[PackageBookingService] Attempting to pay for transfer booking: {transfer_booking.transferz_booking_id}")
                    payment_response = self.transferz_helper.pay_by_invoice(transfer_booking.transferz_booking_id)
                    
                    if payment_response:
                        logger.info(f"[PackageBookingService] Transfer payment completed for booking: {transfer_booking.transferz_booking_id}")
                        
                        # Get updated booking details after payment
                        details_response = self.transferz_helper.get_booking_details(transfer_booking.transferz_booking_id)
                        if details_response:
                            transfer_booking.booking_details = details_response
                            transfer_booking.status = BookingStatus.SUCCESS
                            transfer_booking.save()
                            logger.info(f"[PackageBookingService] Updated transfer booking details from API")
                            logger.info(f"[PackageBookingService] Transfer booking {transfer_booking.id} confirmed successfully")
                            
                            # Generate invoice for confirmed transfer booking
                            try:
                                logger.info(f"[PackageBookingService] Generating invoice for confirmed transfer booking")
                                invoice_url = self.invoice_generation_service.save_invoice_to_booking(
                                    transfer_booking, 
                                    'transfer'
                                )
                                logger.info(f"[PackageBookingService] Transfer invoice generated: {invoice_url}")
                            except Exception as e:
                                logger.warning(f"[PackageBookingService] Failed to generate transfer invoice: {str(e)}")
                        else:
                            logger.error(f"[PackageBookingService] Failed to get transfer booking details after payment")
                            raise BookingException(f"Failed to get transfer booking details: {transfer_booking.transferz_booking_id}")
                    else:
                        logger.error(f"[PackageBookingService] Failed to pay for transfer booking: {transfer_booking.transferz_booking_id}")
                        raise BookingException(f"Failed to pay for transfer booking: {transfer_booking.transferz_booking_id}")
                except Exception as e:
                    logger.error(f"[PackageBookingService] Error confirming transfer booking {transfer_booking.id}: {str(e)}")
                    raise BookingException(f"Failed to confirm transfer booking {transfer_booking.id}: {str(e)}")
            
            # Update main booking status
            booking_obj.status = BookingStatus.SUCCESS
            booking_obj.save()
            logger.info(f"[PackageBookingService] Main booking status updated to SUCCESS")
            
            # Generate main booking invoice after all individual bookings are confirmed
            try:
                logger.info(f"[PackageBookingService] Generating main booking invoice after successful confirmation")
                invoice_url = self.invoice_generation_service.save_invoice_to_booking(
                    booking_obj,
                    'main'
                )
                logger.info(f"[PackageBookingService] Main booking invoice generated: {invoice_url}")
                # TODO: Add email sending functionality here
                # self._send_invoice_email(booking_obj, invoice_url)
            except Exception as e:
                logger.warning(f"[PackageBookingService] Failed to generate main booking invoice: {str(e)}")
            
            logger.info(f"[PackageBookingService] Package confirmation completed successfully")
            return booking_obj
