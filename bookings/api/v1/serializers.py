from rest_framework import serializers
import logging

# Configure logger for booking serializers
logger = logging.getLogger(__name__)


class FlightReviewDetailsSerializer(serializers.Serializer):
    priceId = serializers.CharField(required=True)


class HotelReviewDetailsSerializer(serializers.Serializer):
    search_id = serializers.CharField(required=False)
    hotel_id = serializers.CharField(required=True)
    option_id = serializers.CharField(required=True)


class TransferReviewDetailsSerializer(serializers.Serializer):
    # Since Transferz requires direct booking with quoteId, 
    # review step just returns that we need quoteId from frontend
    quote_id = serializers.IntegerField(required=True, help_text="QuoteId obtained from Transferz frontend/API")
    traveller_email = serializers.EmailField(required=True, help_text="Email of the traveller")
    travel_addons = serializers.ListField(
        child=serializers.DictField(), 
        required=False, 
        default=list,
        help_text="Array of travel addons like meet & greet"
    )
    partner_reference = serializers.Char<PERSON>ield(required=False, default="Zuumm")


class ReviewRequestSerializer(serializers.Serializer):
    type = serializers.ChoiceField(choices=["Flight", "Hotel", "Transfer"])
    details = serializers.JSONField()

    def validate(self, data):
        review_type = data.get('type')
        details = data.get('details')
        
        logger.info(f"[ReviewRequestSerializer] Validating {review_type} review request")
        logger.debug(f"[ReviewRequestSerializer] Details received: {details}")
        
        try:
            if review_type == "Flight":
                serializer = FlightReviewDetailsSerializer(data=details)
            elif review_type == "Hotel":
                serializer = HotelReviewDetailsSerializer(data=details)
            elif review_type == "Transfer":
                serializer = TransferReviewDetailsSerializer(data=details)
            else:
                logger.error(f"[ReviewRequestSerializer] Invalid review type: {review_type}")
                raise serializers.ValidationError({"type": "Invalid review type provided."})
                
            serializer.is_valid(raise_exception=True)
            logger.info(f"[ReviewRequestSerializer] {review_type} review validation successful")
            
        except serializers.ValidationError as e:
            logger.error(f"[ReviewRequestSerializer] {review_type} validation failed: {e.detail}")
            raise
        except Exception as e:
            logger.error(f"[ReviewRequestSerializer] Unexpected validation error: {str(e)}", exc_info=True)
            raise
        
        return data


class PassengerSSRSerializer(serializers.Serializer):
    segment_key = serializers.CharField(max_length=100)
    code = serializers.CharField(max_length=50)


class PassengerSerializer(serializers.Serializer):
    title = serializers.CharField(max_length=10)
    first_name = serializers.CharField(max_length=100)
    last_name = serializers.CharField(max_length=100)
    passenger_type = serializers.ChoiceField(choices=["ADULT", "CHILD", "INFANT"])
    dob = serializers.DateField(required=False, allow_null=True)
    gender = serializers.ChoiceField(choices=["M", "F"], required=False, allow_null=True)
    passport_nationality = serializers.CharField(required=False, allow_blank=True, max_length=5)
    passport_number = serializers.CharField(required=False, allow_blank=True, max_length=50)
    passport_expiry_date = serializers.DateField(required=False, allow_null=True)
    passport_issue_date = serializers.DateField(required=False, allow_null=True)
    
    baggage = PassengerSSRSerializer(many=True, required=False)
    meals = PassengerSSRSerializer(many=True, required=False)
    seats = PassengerSSRSerializer(many=True, required=False)
    
    def validate_title(self, value):
        """Validate and normalize title for Tripjack API"""
        logger.debug(f"[PassengerSerializer] Validating title: {value}")
        
        # Convert to uppercase and validate against allowed titles for Tripjack
        value = value.upper()
        # Updated titles based on Tripjack API requirements
        allowed_titles = ['MR', 'MS', 'MRS', 'MISS', 'DR', 'MASTER', 'MSTR']
        
        if value not in allowed_titles:
            logger.error(f"[PassengerSerializer] Invalid title: {value}. Allowed: {allowed_titles}")
            raise serializers.ValidationError(f"Invalid title. Allowed values: {', '.join(allowed_titles)}")
        
        logger.debug(f"[PassengerSerializer] Title validation passed: {value}")
        return value
    
    def validate_gender(self, value):
        """Validate gender field"""
        if value:
            logger.debug(f"[PassengerSerializer] Validating gender: {value}")
            value = value.upper()
            if value not in ['M', 'F']:
                logger.error(f"[PassengerSerializer] Invalid gender: {value}")
                raise serializers.ValidationError("Gender must be 'M' or 'F'")
            logger.debug(f"[PassengerSerializer] Gender validation passed: {value}")
        return value


class GSTInfoSerializer(serializers.Serializer):
    gst_number = serializers.CharField()
    email = serializers.EmailField()
    registered_name = serializers.CharField()
    mobile = serializers.CharField()
    address = serializers.CharField()


class DeliveryInfoSerializer(serializers.Serializer):
    emails = serializers.ListField(child=serializers.EmailField())
    contacts = serializers.ListField(child=serializers.CharField())


class FlightDetailsSerializer(serializers.Serializer):
    travellerInfo = PassengerSerializer(many=True)
    gst_info = GSTInfoSerializer(required=False)
    delivery_info = DeliveryInfoSerializer(required=True)


class HotelRoomPaxInfoChildSerializer(serializers.Serializer):
    age = serializers.IntegerField(min_value=0, max_value=17)


class HotelRoomPaxInfoSerializer(serializers.Serializer):
    adults = serializers.IntegerField(min_value=1)
    children = HotelRoomPaxInfoChildSerializer(many=True, required=False)


class HotelGuestDetailsSerializer(serializers.Serializer):
    title = serializers.CharField()
    first_name = serializers.CharField()
    last_name = serializers.CharField()
    pan = serializers.CharField(required=False, allow_blank=True)


class HotelRoomSerializer(serializers.Serializer):
    room_type_name = serializers.CharField(required=False, allow_blank=True)  # Made optional
    paxInfo = HotelRoomPaxInfoSerializer(required=False)  # Made optional  
    guest_details = HotelGuestDetailsSerializer(many=True, required=False)  # Added guest_details
    number_of_adults = serializers.IntegerField(required=False)
    number_of_children = serializers.IntegerField(required=False)


class LeadGuestSerializer(serializers.Serializer):
    title = serializers.CharField()
    first_name = serializers.CharField()
    last_name = serializers.CharField()
    email = serializers.EmailField(required=False)
    phone = serializers.CharField(required=False)


class HotelDetailsSerializer(serializers.Serializer):
    tripjack_hotel_id = serializers.CharField(required=False, allow_blank=True)  # Made optional
    hotel_name = serializers.CharField(required=False, allow_blank=True)  # Made optional
    hotel_id = serializers.CharField(required=False, allow_blank=True)  # Added hotel_id field
    option_id = serializers.CharField(required=False, allow_blank=True)  # Added option_id field
    check_in_date = serializers.DateTimeField(required=False, allow_null=True)  # Made optional
    check_out_date = serializers.DateTimeField(required=False, allow_null=True)  # Made optional
    rooms = HotelRoomSerializer(many=True, required=False)  # Made optional
    lead_guest = LeadGuestSerializer(required=False)  # Made optional
    amount = serializers.DecimalField(max_digits=10, decimal_places=2, required=False)  # Added amount field


class ActivityDetailsSerializer(serializers.Serializer):
    external_provider = serializers.CharField()
    external_booking_reference = serializers.CharField()
    activity_name = serializers.CharField()
    activity_date = serializers.DateField()
    activity_time = serializers.CharField()
    participants = serializers.JSONField() # Simple validation for now
    location = serializers.JSONField()


class TransferDetailsSerializer(serializers.Serializer):
    # All fields are optional since the actual booking was created in review step
    # These are just metadata fields for display/tracking purposes
    
    pickup_location = serializers.CharField(required=False, allow_blank=True)
    dropoff_location = serializers.CharField(required=False, allow_blank=True) 
    pickup_datetime = serializers.DateTimeField(required=False, allow_null=True)
    passenger_count = serializers.IntegerField(required=False, allow_null=True)
    vehicle_type = serializers.CharField(required=False, allow_blank=True)
    service_type = serializers.CharField(required=False, allow_blank=True)
    traveller_email = serializers.EmailField(required=False, allow_blank=True)
    
    # Optional legacy fields (not used in new flow but kept for compatibility)
    quote_id = serializers.IntegerField(required=False, help_text="Legacy field - not required in new flow")
    travel_addons = serializers.ListField(
        child=serializers.DictField(), 
        required=False, 
        default=list,
        help_text="Legacy field - not used in hold/confirm flow"
    )
    partner_reference = serializers.CharField(required=False, default="Zuumm")
    quote_details = serializers.JSONField(required=False)
    passenger_details = serializers.JSONField(required=False)
    contact_info = serializers.JSONField(required=False)
    special_requirements = serializers.CharField(required=False, allow_blank=True)
    flight_number = serializers.CharField(required=False, allow_blank=True)
    currency = serializers.CharField(default='USD')


class ItineraryItemSerializer(serializers.Serializer):
    type = serializers.ChoiceField(choices=["Flight", "Hotel", "Activity", "Transfer"])
    order = serializers.IntegerField(min_value=0, default=0)
    booking_id = serializers.CharField(required=False, allow_blank=True)
    quote_id = serializers.CharField(required=False, allow_blank=True)
    amount = serializers.DecimalField(max_digits=10, decimal_places=2)
    
    flight_details = FlightDetailsSerializer(required=False)
    hotel_details = HotelDetailsSerializer(required=False)
    activity_details = ActivityDetailsSerializer(required=False)
    transfer_details = TransferDetailsSerializer(required=False)

    def validate(self, data):
        item_type = data.get('type')
        logger.debug(f"[ItineraryItemSerializer] Validating {item_type} item")
        
        try:
            if item_type == "Flight" and not data.get('flight_details'):
                logger.error(f"[ItineraryItemSerializer] Missing flight_details for Flight item")
                raise serializers.ValidationError({"flight_details": "This field is required for Flight items."})
            if item_type == "Hotel" and not data.get('hotel_details'):
                logger.error(f"[ItineraryItemSerializer] Missing hotel_details for Hotel item")
                raise serializers.ValidationError({"hotel_details": "This field is required for Hotel items."})
            if item_type == "Activity" and not data.get('activity_details'):
                logger.error(f"[ItineraryItemSerializer] Missing activity_details for Activity item")
                raise serializers.ValidationError({"activity_details": "This field is required for Activity items."})
            if item_type == "Transfer" and not data.get('transfer_details'):
                logger.error(f"[ItineraryItemSerializer] Missing transfer_details for Transfer item")
                raise serializers.ValidationError({"transfer_details": "This field is required for Transfer items."})
            
            # All item types now require booking_id (from their respective review APIs)
            if not data.get('booking_id'):
                logger.error(f"[ItineraryItemSerializer] Missing booking_id for {item_type} item")
                raise serializers.ValidationError({"booking_id": f"This field is required for {item_type} items. booking_id should come from the review API response."})
            
            logger.debug(f"[ItineraryItemSerializer] {item_type} item validation successful")
            
        except serializers.ValidationError:
            raise
        except Exception as e:
            logger.error(f"[ItineraryItemSerializer] Unexpected validation error for {item_type}: {str(e)}", exc_info=True)
            raise
        
        return data


class ItineraryDaySerializer(serializers.Serializer):
    day_number = serializers.IntegerField(min_value=1)
    date = serializers.DateField()
    items = ItineraryItemSerializer(many=True)


class PackageBookingRequestSerializer(serializers.Serializer):
    booker_email = serializers.EmailField()
    booker_phone_number = serializers.CharField()
    package = serializers.CharField(required=False, allow_null=True, allow_blank=True, help_text="Package ID or package key to associate with this booking")
    destination = serializers.CharField(required=True, help_text="Destination external_id for the booking")
    itinerary = ItineraryDaySerializer(many=True, allow_empty=False)
    
    def validate(self, data):
        logger.info(f"[PackageBookingRequestSerializer] Starting package booking validation")
        logger.info(f"[PackageBookingRequestSerializer] Booker: {data.get('booker_email')}")
        logger.info(f"[PackageBookingRequestSerializer] Itinerary days: {len(data.get('itinerary', []))}")
        
        try:
            # Count total items for logging
            total_items = 0
            item_types = {"Flight": 0, "Hotel": 0, "Activity": 0, "Transfer": 0}
            
            for day in data.get('itinerary', []):
                day_items = day.get('items', [])
                total_items += len(day_items)
                
                for item in day_items:
                    item_type = item.get('type')
                    if item_type in item_types:
                        item_types[item_type] += 1
            
            logger.info(f"[PackageBookingRequestSerializer] Total items: {total_items}")
            logger.info(f"[PackageBookingRequestSerializer] Item breakdown: {item_types}")
            
            # Validate day numbers are sequential and start from 1
            itinerary = data.get('itinerary', [])
            if itinerary:
                day_numbers = [day.get('day_number') for day in itinerary]
                expected_day_numbers = list(range(1, len(itinerary) + 1))
                
                if sorted(day_numbers) != expected_day_numbers:
                    logger.error(f"[PackageBookingRequestSerializer] Invalid day numbers: {day_numbers}, expected: {expected_day_numbers}")
                    raise serializers.ValidationError({
                        "itinerary": "Day numbers must be sequential starting from 1"
                    })
            
            logger.info(f"[PackageBookingRequestSerializer] Package booking validation successful")
            
        except serializers.ValidationError:
            raise
        except Exception as e:
            logger.error(f"[PackageBookingRequestSerializer] Unexpected validation error: {str(e)}", exc_info=True)
            raise
        
        return data
