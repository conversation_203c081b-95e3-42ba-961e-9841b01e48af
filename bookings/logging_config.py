"""
Logging configuration for the bookings module.
This module provides structured logging for the package booking functionality.
"""

import logging
import os
from datetime import datetime


class BookingLogFormatter(logging.Formatter):
    """
    Custom formatter for booking logs that adds context and structure
    """
    
    def __init__(self):
        super().__init__()
        
    def format(self, record):
        # Create a structured format for booking logs
        timestamp = datetime.fromtimestamp(record.created).strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
        
        # Add booking context if available
        booking_id = getattr(record, 'booking_id', 'N/A')
        user_email = getattr(record, 'user_email', 'N/A')
        
        # Format the message
        formatted_message = f"[{timestamp}] [{record.levelname}] [{record.name}] "
        
        if booking_id != 'N/A':
            formatted_message += f"[BookingID: {booking_id}] "
        if user_email != 'N/A':
            formatted_message += f"[User: {user_email}] "
            
        formatted_message += record.getMessage()
        
        # Add exception info if present
        if record.exc_info:
            formatted_message += f"\n{self.formatException(record.exc_info)}"
            
        return formatted_message


def setup_booking_logger(logger_name=None, log_level=logging.INFO):
    """
    Set up a structured logger for booking operations
    
    Args:
        logger_name: Name of the logger (defaults to the calling module)
        log_level: Logging level (default: INFO)
    
    Returns:
        Configured logger instance
    """
    if logger_name is None:
        logger_name = __name__
    
    logger = logging.getLogger(logger_name)
    
    # Avoid adding handlers multiple times
    if logger.handlers:
        return logger
    
    logger.setLevel(log_level)
    
    # Create console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(log_level)
    console_handler.setFormatter(BookingLogFormatter())
    
    logger.addHandler(console_handler)
    
    # Create file handler for booking-specific logs (if in production)
    try:
        from django.conf import settings
        if hasattr(settings, 'LOGGING_DIR'):
            booking_log_file = os.path.join(settings.LOGGING_DIR, 'bookings.log')
            file_handler = logging.FileHandler(booking_log_file)
            file_handler.setLevel(logging.DEBUG)  # File gets more detailed logs
            file_handler.setFormatter(BookingLogFormatter())
            logger.addHandler(file_handler)
    except Exception:
        # If Django settings not available or LOGGING_DIR not configured, skip file logging
        pass
    
    return logger


def log_booking_context(logger, booking_id=None, user_email=None):
    """
    Add booking context to log records
    
    Args:
        logger: Logger instance
        booking_id: Booking ID to include in logs
        user_email: User email to include in logs
    
    Returns:
        Logger adapter with context
    """
    extra = {}
    if booking_id:
        extra['booking_id'] = booking_id
    if user_email:
        extra['user_email'] = user_email
    
    return logging.LoggerAdapter(logger, extra)


class BookingMetrics:
    """
    Simple metrics collector for booking operations
    """
    
    def __init__(self):
        self.metrics = {
            'total_bookings': 0,
            'successful_bookings': 0,
            'failed_bookings': 0,
            'flight_bookings': 0,
            'hotel_bookings': 0,
            'activity_bookings': 0,
            'review_api_calls': 0,
            'tripjack_api_calls': 0,
        }
        self.logger = setup_booking_logger('bookings.metrics')
    
    def increment_metric(self, metric_name):
        """Increment a specific metric"""
        if metric_name in self.metrics:
            self.metrics[metric_name] += 1
            self.logger.debug(f"[BookingMetrics] {metric_name}: {self.metrics[metric_name]}")
    
    def log_metrics_summary(self):
        """Log current metrics summary"""
        self.logger.info(f"[BookingMetrics] Current metrics: {self.metrics}")
    
    def reset_metrics(self):
        """Reset all metrics to zero"""
        for key in self.metrics:
            self.metrics[key] = 0
        self.logger.info("[BookingMetrics] All metrics reset to zero")


# Global metrics instance
booking_metrics = BookingMetrics()


def log_performance(func):
    """
    Decorator to log function execution time and performance
    """
    import time
    from functools import wraps
    
    @wraps(func)
    def wrapper(*args, **kwargs):
        logger = setup_booking_logger(f'bookings.performance.{func.__name__}')
        start_time = time.time()
        
        logger.debug(f"[Performance] Starting {func.__name__}")
        
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            logger.info(f"[Performance] {func.__name__} completed in {execution_time:.3f}s")
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"[Performance] {func.__name__} failed after {execution_time:.3f}s: {str(e)}")
            raise
    
    return wrapper


# Convenience functions for different log levels
def log_booking_info(message, **kwargs):
    """Log booking information"""
    logger = setup_booking_logger('bookings.info')
    logger.info(message, extra=kwargs)


def log_booking_error(message, **kwargs):
    """Log booking error"""
    logger = setup_booking_logger('bookings.error')
    logger.error(message, extra=kwargs)


def log_booking_debug(message, **kwargs):
    """Log booking debug information"""
    logger = setup_booking_logger('bookings.debug')
    logger.debug(message, extra=kwargs)


def log_booking_warning(message, **kwargs):
    """Log booking warning"""
    logger = setup_booking_logger('bookings.warning')
    logger.warning(message, extra=kwargs)


# Example usage and configuration constants
BOOKING_LOG_LEVELS = {
    'CRITICAL': logging.CRITICAL,
    'ERROR': logging.ERROR,
    'WARNING': logging.WARNING,
    'INFO': logging.INFO,
    'DEBUG': logging.DEBUG,
}

# Default logger for the module
default_logger = setup_booking_logger('bookings') 