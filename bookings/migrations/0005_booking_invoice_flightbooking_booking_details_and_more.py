# Generated by Django 4.2 on 2025-08-07 09:29

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("bookings", "0004_activitybooking_booking_bookingitinerary_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="booking",
            name="invoice",
            field=models.FileField(
                blank=True,
                help_text="Downloaded invoice/ticket file from Tripjack",
                max_length=150,
                null=True,
                upload_to="invoices/bookings/",
            ),
        ),
        migrations.AddField(
            model_name="flightbooking",
            name="booking_details",
            field=models.JSONField(
                blank=True,
                help_text="Complete booking details from Tripjack booking-details API",
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="flightbooking",
            name="invoice",
            field=models.FileField(
                blank=True,
                help_text="Downloaded invoice/ticket file from Tripjack",
                max_length=150,
                null=True,
                upload_to="invoices/flights/",
            ),
        ),
        migrations.AddField(
            model_name="flightbooking",
            name="ticket_numbers",
            field=models.JSONField(
                blank=True, help_text="Ticket numbers for all passengers", null=True
            ),
        ),
        migrations.AddField(
            model_name="hotelbooking",
            name="booking_details",
            field=models.JSONField(
                blank=True,
                help_text="Complete booking details from Tripjack booking-detail API",
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="hotelbooking",
            name="invoice",
            field=models.FileField(
                blank=True,
                help_text="Downloaded invoice/voucher file from Tripjack",
                max_length=150,
                null=True,
                upload_to="invoices/hotels/",
            ),
        ),
        migrations.AddField(
            model_name="hotelbooking",
            name="status",
            field=models.CharField(
                blank=True,
                choices=[
                    ("Pending", "Pending"),
                    ("On_Hold", "On Hold"),
                    ("Success", "Success"),
                    ("Confirmed", "Confirmed"),
                    ("Failed", "Failed"),
                    ("Cancelled", "Cancelled"),
                ],
                default="Pending",
                max_length=20,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="hotelbooking",
            name="voucher_data",
            field=models.JSONField(
                blank=True, help_text="Voucher information for hotel booking", null=True
            ),
        ),
        migrations.AlterField(
            model_name="booking",
            name="status",
            field=models.CharField(
                choices=[
                    ("Pending", "Pending"),
                    ("On_Hold", "On Hold"),
                    ("Success", "Success"),
                    ("Confirmed", "Confirmed"),
                    ("Failed", "Failed"),
                    ("Cancelled", "Cancelled"),
                ],
                default="Pending",
                max_length=20,
            ),
        ),
        migrations.AlterField(
            model_name="flightbooking",
            name="status",
            field=models.CharField(
                choices=[
                    ("Pending", "Pending"),
                    ("On_Hold", "On Hold"),
                    ("Success", "Success"),
                    ("Confirmed", "Confirmed"),
                    ("Failed", "Failed"),
                    ("Cancelled", "Cancelled"),
                ],
                default="Pending",
                max_length=20,
            ),
        ),
    ]
