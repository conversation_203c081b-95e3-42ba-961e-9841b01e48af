# Generated by Django 4.2 on 2025-07-09 05:17

import base.storage_utils
from decimal import Decimal
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import django_better_admin_arrayfield.models.fields
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("packages", "0018_package_rating_description_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("accounts", "0018_alter_blog_slug"),
    ]

    operations = [
        migrations.CreateModel(
            name="Voucher",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("restored_at", models.DateTimeField(blank=True, null=True)),
                ("transaction_id", models.UUIDField(blank=True, null=True)),
                ("external_id", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "booking_id",
                    models.CharField(
                        help_text="Unique booking identifier",
                        max_length=100,
                        unique=True,
                    ),
                ),
                (
                    "booking_date",
                    models.DateTimeField(
                        default=django.utils.timezone.now,
                        help_text="Date when booking was made",
                    ),
                ),
                (
                    "user_name",
                    models.CharField(
                        blank=True,
                        help_text="Name of the person who made the booking",
                        max_length=255,
                    ),
                ),
                (
                    "user_email",
                    models.EmailField(
                        blank=True,
                        help_text="Email of the person who made the booking",
                        max_length=254,
                    ),
                ),
                (
                    "package_name",
                    models.CharField(
                        blank=True,
                        help_text="Name of the package booked",
                        max_length=255,
                    ),
                ),
                (
                    "package_type",
                    models.CharField(
                        choices=[
                            ("Standard", "Standard"),
                            ("Premium", "Premium"),
                            ("Deluxe", "Deluxe"),
                            ("Luxury", "Luxury"),
                            ("Budget", "Budget"),
                            ("Customized", "Customized"),
                        ],
                        default="Standard",
                        help_text="Type of package (e.g., Standard, Premium, Deluxe)",
                        max_length=100,
                    ),
                ),
                (
                    "number_of_guests",
                    models.PositiveIntegerField(
                        default=1,
                        help_text="Number of guests for this booking",
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(50),
                        ],
                    ),
                ),
                (
                    "package_start_date",
                    models.DateField(help_text="Start date of the package/travel"),
                ),
                (
                    "package_end_date",
                    models.DateField(help_text="End date of the package/travel"),
                ),
                (
                    "inclusions",
                    django_better_admin_arrayfield.models.fields.ArrayField(
                        base_field=models.CharField(max_length=255),
                        blank=True,
                        help_text="What's included in the package (e.g., 'Accommodation, Meals, Transport')",
                        size=None,
                    ),
                ),
                (
                    "total_amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Total amount for the booking",
                        max_digits=12,
                    ),
                ),
                (
                    "amount_paid",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        help_text="Amount already paid",
                        max_digits=12,
                    ),
                ),
                (
                    "payment_mode",
                    models.CharField(
                        choices=[
                            ("Credit Card", "Credit Card"),
                            ("Debit Card", "Debit Card"),
                            ("UPI", "UPI"),
                            ("Net Banking", "Net Banking"),
                            ("Bank Transfer", "Bank Transfer"),
                            ("Cash", "Cash"),
                            ("Digital Wallet", "Digital Wallet"),
                            ("EMI", "EMI"),
                            ("Others", "Others"),
                        ],
                        help_text="Payment mode used (e.g., Credit Card, UPI, Bank Transfer)",
                        max_length=255,
                    ),
                ),
                (
                    "payment_date",
                    models.DateField(help_text="Date when payment was made"),
                ),
                (
                    "special_notes",
                    django_better_admin_arrayfield.models.fields.ArrayField(
                        base_field=models.CharField(max_length=255),
                        blank=True,
                        help_text="Any special notes or requirements for the booking (e.g., 'No smoking, Early check-in')",
                        size=None,
                    ),
                ),
                (
                    "voucher_pdf",
                    models.FileField(
                        blank=True,
                        help_text="Generated PDF voucher file",
                        null=True,
                        upload_to=base.storage_utils.voucher_pdf_upload_path,
                    ),
                ),
                (
                    "package",
                    models.ForeignKey(
                        blank=True,
                        help_text="Package associated with this voucher",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="vouchers",
                        to="packages.package",
                    ),
                ),
                (
                    "partner",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="vouchers",
                        to="accounts.partner",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        help_text="User who made the booking",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="vouchers",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Voucher",
                "verbose_name_plural": "Vouchers",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="DaywiseItinerary",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("restored_at", models.DateTimeField(blank=True, null=True)),
                ("transaction_id", models.UUIDField(blank=True, null=True)),
                ("external_id", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "day_number",
                    models.PositiveIntegerField(
                        help_text="Day number in the itinerary (1, 2, 3, etc.)"
                    ),
                ),
                ("date", models.DateField(help_text="Date for this day of itinerary")),
                (
                    "title",
                    models.CharField(
                        help_text="Title for the day (e.g., 'Arrival in Goa')",
                        max_length=255,
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True,
                        help_text="Detailed description of activities for the day",
                    ),
                ),
                (
                    "hotel_name",
                    models.CharField(
                        blank=True, help_text="Hotel name for the night", max_length=255
                    ),
                ),
                (
                    "room_type",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("Standard", "Standard Room"),
                            ("Deluxe", "Deluxe Room"),
                            ("Suite", "Suite"),
                            ("Premium", "Premium Room"),
                            ("Luxury", "Luxury Room"),
                            ("Villa", "Villa"),
                            ("Cottage", "Cottage"),
                            ("Apartment", "Apartment"),
                        ],
                        help_text="Type of room (e.g., Deluxe, Suite, Standard)",
                        max_length=100,
                    ),
                ),
                (
                    "check_in_time",
                    models.TimeField(
                        blank=True, help_text="Check-in time for hotel", null=True
                    ),
                ),
                (
                    "check_out_time",
                    models.TimeField(
                        blank=True, help_text="Check-out time from hotel", null=True
                    ),
                ),
                (
                    "activities",
                    models.TextField(
                        blank=True,
                        help_text="Activities planned for the day (comma-separated: Sightseeing, Shopping, Beach visit)",
                    ),
                ),
                (
                    "meals_included",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("Breakfast", "Breakfast"),
                            ("Lunch", "Lunch"),
                            ("Dinner", "Dinner"),
                            ("Breakfast & Lunch", "Breakfast & Lunch"),
                            ("Lunch & Dinner", "Lunch & Dinner"),
                            ("Breakfast & Dinner", "Breakfast & Dinner"),
                            ("All Meals", "All Meals (Breakfast, Lunch & Dinner)"),
                            ("No Meals", "No Meals Included"),
                        ],
                        help_text="Meals included (e.g., Breakfast, Lunch, Dinner)",
                        max_length=255,
                    ),
                ),
                (
                    "transportation",
                    models.CharField(
                        blank=True,
                        help_text="Transportation details for the day",
                        max_length=255,
                    ),
                ),
                (
                    "voucher",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="daywise_itinerary",
                        to="bookings.voucher",
                    ),
                ),
            ],
            options={
                "verbose_name": "Daywise Itinerary",
                "verbose_name_plural": "Daywise Itineraries",
                "ordering": ["voucher", "day_number"],
                "unique_together": {("voucher", "day_number")},
            },
        ),
    ]
