# Generated by Django 4.2 on 2025-08-07 11:14

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("bookings", "0006_passenger_gender"),
    ]

    operations = [
        migrations.AlterField(
            model_name="bookingitinerary",
            name="booking",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="itinerary_days",
                to="bookings.booking",
            ),
        ),
        migrations.AlterField(
            model_name="bookingitinerarydayitem",
            name="activity_booking",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="day_items",
                to="bookings.activitybooking",
            ),
        ),
        migrations.AlterField(
            model_name="bookingitinerarydayitem",
            name="booking_itinerary_day",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="day_items",
                to="bookings.bookingitinerary",
            ),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name="bookingitinerarydayitem",
            name="flight_booking",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="day_items",
                to="bookings.flightbooking",
            ),
        ),
        migrations.AlterField(
            model_name="bookingitinerarydayitem",
            name="hotel_booking",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="day_items",
                to="bookings.hotelbooking",
            ),
        ),
        migrations.AlterField(
            model_name="bookingitinerarydayitem",
            name="type",
            field=models.CharField(
                choices=[
                    ("Hotel", "Hotel"),
                    ("Activity", "Activity"),
                    ("Flight", "Flight"),
                    ("Transfer", "Transfer"),
                ],
                max_length=20,
            ),
        ),
    ]
