# Generated by Django 4.2 on 2025-08-26 05:54

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("packages", "0035_custompackageitinerary_and_more"),
        ("bookings", "0011_alter_booking_destination"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="activitybooking",
            name="invoice_json",
        ),
        migrations.RemoveField(
            model_name="booking",
            name="invoice",
        ),
        migrations.AddField(
            model_name="booking",
            name="invoice_file",
            field=models.FileField(
                blank=True,
                help_text="Generated main booking invoice/voucher file",
                max_length=150,
                null=True,
                upload_to="invoices/main_bookings/",
            ),
        ),
        migrations.AddField(
            model_name="booking",
            name="invoice_json",
            field=models.JSONField(
                blank=True,
                help_text="Context data used for main booking invoice generation",
                null=True,
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="booking",
            name="destination",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="bookings",
                to="packages.destination",
            ),
        ),
    ]
