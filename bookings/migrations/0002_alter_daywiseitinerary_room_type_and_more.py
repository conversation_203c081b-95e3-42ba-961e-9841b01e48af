# Generated by Django 4.2 on 2025-07-10 07:01

from django.db import migrations, models
import django_better_admin_arrayfield.models.fields


class Migration(migrations.Migration):

    dependencies = [
        ("bookings", "0001_initial"),
    ]

    operations = [
        migrations.AlterField(
            model_name="daywiseitinerary",
            name="room_type",
            field=models.CharField(
                blank=True,
                help_text="Type of room (e.g., Deluxe, Suite, Standard)",
                max_length=100,
            ),
        ),
        migrations.AlterField(
            model_name="voucher",
            name="booking_id",
            field=models.CharField(
                blank=True,
                help_text="Leave blank to auto-generate. Format: ZUUMM[8-char-code]",
                max_length=100,
            ),
        ),
        migrations.AlterField(
            model_name="voucher",
            name="inclusions",
            field=django_better_admin_arrayfield.models.fields.ArrayField(
                base_field=models.CharField(max_length=255),
                blank=True,
                default=list,
                help_text="What's included in the package (e.g., 'Accommodation, Meals, Transport')",
                size=None,
            ),
        ),
        migrations.AlterField(
            model_name="voucher",
            name="special_notes",
            field=django_better_admin_arrayfield.models.fields.ArrayField(
                base_field=models.CharField(max_length=255),
                blank=True,
                default=list,
                help_text="Any special notes or requirements for the booking (e.g., 'No smoking, Early check-in')",
                size=None,
            ),
        ),
    ]
