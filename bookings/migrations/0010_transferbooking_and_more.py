# Generated by Django 4.2 on 2025-08-20 06:31

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("packages", "0035_custompackageitinerary_and_more"),
        ("bookings", "0009_booking_package_booking_partner"),
    ]

    operations = [
        migrations.CreateModel(
            name="TransferBooking",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("restored_at", models.DateTimeField(blank=True, null=True)),
                ("transaction_id", models.UUIDField(blank=True, null=True)),
                ("external_id", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                ("transferz_booking_id", models.CharField(max_length=100, unique=True)),
                (
                    "transferz_quote_id",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("pickup_location", models.CharField(max_length=255)),
                ("dropoff_location", models.CharField(max_length=255)),
                ("pickup_datetime", models.DateTimeField()),
                (
                    "vehicle_type",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "service_type",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("passenger_count", models.PositiveIntegerField(default=1)),
                ("amount", models.DecimalField(decimal_places=2, max_digits=10)),
                ("currency", models.CharField(default="USD", max_length=3)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("Pending", "Pending"),
                            ("On_Hold", "On Hold"),
                            ("Success", "Success"),
                            ("Confirmed", "Confirmed"),
                            ("Failed", "Failed"),
                            ("Cancelled", "Cancelled"),
                        ],
                        default="Pending",
                        max_length=20,
                    ),
                ),
                (
                    "confirmation_number",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "passenger_name",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "passenger_phone",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                (
                    "passenger_email",
                    models.EmailField(blank=True, max_length=254, null=True),
                ),
                ("special_requirements", models.TextField(blank=True, null=True)),
                (
                    "flight_number",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                (
                    "booking_details",
                    models.JSONField(
                        blank=True,
                        help_text="Complete booking details from Transferz API",
                        null=True,
                    ),
                ),
                (
                    "quote_details",
                    models.JSONField(
                        blank=True,
                        help_text="Quote details from Transferz API",
                        null=True,
                    ),
                ),
                ("meta_information", models.JSONField(blank=True, null=True)),
                (
                    "invoice_file",
                    models.FileField(
                        blank=True,
                        help_text="Downloaded invoice/voucher file",
                        max_length=150,
                        null=True,
                        upload_to="invoices/transfers/",
                    ),
                ),
                ("invoice_json", models.JSONField(blank=True, null=True)),
            ],
            options={
                "ordering": ["-created_at"],
                "abstract": False,
            },
        ),
        migrations.RenameField(
            model_name="hotelbooking",
            old_name="invoice",
            new_name="invoice_file",
        ),
        migrations.RemoveField(
            model_name="flightbooking",
            name="invoice",
        ),
        migrations.RemoveField(
            model_name="hotelbooking",
            name="voucher_data",
        ),
        migrations.AddField(
            model_name="activitybooking",
            name="invoice_file",
            field=models.FileField(
                blank=True,
                help_text="Downloaded invoice/voucher file",
                max_length=150,
                null=True,
                upload_to="invoices/activities/",
            ),
        ),
        migrations.AddField(
            model_name="activitybooking",
            name="invoice_json",
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="booking",
            name="amount_paid",
            field=models.DecimalField(decimal_places=2, default=0.0, max_digits=10),
        ),
        migrations.AddField(
            model_name="booking",
            name="booker_user",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="booked_bookings",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="booking",
            name="destination",
            field=models.ForeignKey(
                null=True,
                blank=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="bookings",
                to="packages.destination",
            ),
        ),
        migrations.AddField(
            model_name="bookingitinerary",
            name="activity_booking",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="day_items",
                to="bookings.activitybooking",
            ),
        ),
        migrations.AddField(
            model_name="bookingitinerary",
            name="flight_booking",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="day_items",
                to="bookings.flightbooking",
            ),
        ),
        migrations.AddField(
            model_name="bookingitinerary",
            name="hotel_booking",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="day_items",
                to="bookings.hotelbooking",
            ),
        ),
        migrations.AddField(
            model_name="bookingitinerary",
            name="order",
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name="bookingitinerary",
            name="type",
            field=models.CharField(
                choices=[
                    ("Hotel", "Hotel"),
                    ("Activity", "Activity"),
                    ("Flight", "Flight"),
                    ("Transfer", "Transfer"),
                ],
                default="Hotel",
                max_length=255,
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="flightbooking",
            name="invoice_file",
            field=models.FileField(
                blank=True,
                help_text="Downloaded invoice/voucher file from Tripjack",
                max_length=150,
                null=True,
                upload_to="invoices/flights/",
            ),
        ),
        migrations.AddField(
            model_name="flightbooking",
            name="invoice_json",
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="hotelbooking",
            name="hotel",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="packages.tripjackhotels",
            ),
        ),
        migrations.AddField(
            model_name="hotelbooking",
            name="invoice_json",
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="booking",
            name="package",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="bookings",
                to="packages.custompackage",
            ),
        ),
        migrations.DeleteModel(
            name="BookingItineraryDayItem",
        ),
        migrations.AddField(
            model_name="bookingitinerary",
            name="transfer_booking",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="day_items",
                to="bookings.transferbooking",
            ),
        ),
    ]
