# Bookings App - Voucher Management System

## Overview

The `bookings` app provides voucher/receipt functionality similar to MakeMyTrip's booking confirmations. It allows creation and management of travel booking vouchers through the Django admin interface.

## Models

### 1. Voucher
The main model representing a booking receipt/voucher with the following key features:

#### Key Fields:
- **Partner Integration**: Each voucher belongs to a partner
- **Package & User Relations**: Optional foreign keys to Package and User models
- **Booking Details**: Unique booking ID, booking date, confirmation status
- **User Information**: Name and email (can be filled manually)
- **Package Information**: Name, type, guest count, dates
- **Financial Tracking**: Total amount, paid amount, payment methods
- **Travel Dates**: Package dates and actual travel dates
- **Special Notes**: Additional requirements or notes

#### Auto-Generated Features:
- **Booking ID**: Auto-generates format `ZUUMM[8-CHAR-CODE]` if not provided
- **Auto-Confirmation**: Automatically confirms when fully paid
- **Calculated Properties**: `remaining_amount`, `is_fully_paid`

### 2. DaywiseItinerary
Detailed day-by-day itinerary for future MMT-like detailed vouchers:

#### Key Fields:
- **Day Planning**: Day number, date, title, description
- **Accommodation**: Hotel name, room type, check-in/out times
- **Services**: Meals included, activities, transportation
- **Ordering**: Automatically ordered by day number

## Admin Interface Features

### Voucher Admin:
- **Smart Filtering**: Package and User dropdowns filtered by partner
- **Visual Indicators**: Color-coded payment status and remaining amounts
- **Organized Fieldsets**: Grouped fields for better UX
- **Inline Itinerary**: Add day-wise itinerary directly from voucher form
- **Search & Filter**: Search by booking ID, user details, package name
- **Partner-based Access**: Non-superusers see only their partner's vouchers

### Key Admin Features:
1. **Dynamic Dropdowns**: Package and User lists filtered by current partner
2. **Financial Display**: Color-coded remaining amounts and payment status
3. **Auto-calculations**: Remaining amount and payment status computed automatically
4. **Inline Editing**: Add itinerary days directly from voucher edit page

## Installation & Setup

### 1. Add to INSTALLED_APPS
Already added to `zuumm/settings.py`:
```python
INSTALLED_APPS = [
    # ... other apps
    "bookings",
]
```

### 2. Run Migrations
```bash
python manage.py makemigrations bookings
python manage.py migrate
```

### 3. Admin Access
The models are automatically registered in Django admin with custom configurations.

## Usage Examples

### Creating a Voucher in Admin:

1. **Basic Information**:
   - Partner: Auto-selected based on current user
   - Booking ID: Leave blank for auto-generation
   - Confirmation status

2. **User Details**:
   - Select existing user OR manually enter name/email
   - User dropdown shows only users from same partner

3. **Package Information**:
   - Select existing package OR manually enter package details
   - Package dropdown shows only packages from same partner
   - Set guest count, travel dates

4. **Financial Information**:
   - Enter total amount and amount paid
   - Add payment modes and dates
   - System auto-calculates remaining amount

5. **Itinerary (Optional)**:
   - Add day-wise itinerary using inline forms
   - Set hotel details, meals, activities for each day

## Partner-based Filtering

The app implements smart partner-based filtering:

- **Package Dropdown**: Shows only packages from same partner
- **User Dropdown**: Shows only users from same partner  
- **Voucher List**: Non-superusers see only their partner's vouchers
- **Auto-Assignment**: Partner auto-assigned based on current user

## Future Enhancements

### Planned Features:
1. **API Endpoints**: REST API for voucher management
2. **PDF Generation**: Generate printable voucher PDFs
3. **Email Integration**: Send voucher emails to customers
4. **Status Tracking**: Enhanced booking status workflow
5. **Payment Integration**: Direct payment gateway integration
6. **Template System**: Customizable voucher templates per partner

### MMT-like Features (Future):
- **Detailed Itinerary**: Rich day-wise itinerary with images
- **Hotel Integration**: Direct hotel booking integration
- **Transport Booking**: Flight/train booking integration
- **Cancellation Management**: Cancellation and refund workflows
- **Review System**: Customer feedback and rating system

## Model Structure

```
Voucher
├── partner (FK to Partner)
├── package (FK to Package) - nullable
├── user (FK to User) - nullable
├── booking_id (auto-generated)
├── user_name, user_email
├── package_name, package_type, guests
├── travel dates
├── financial fields
└── DaywiseItinerary (reverse FK)
    ├── day_number, date, title
    ├── hotel_name, room_type
    ├── meals, activities, transport
    └── check-in/out times
```

## Admin Icons & UI

- **App Icon**: Receipt icon (`fas fa-receipt`)
- **Voucher Icon**: Receipt icon (`fas fa-receipt`)  
- **Itinerary Icon**: Calendar day icon (`fas fa-calendar-day`)
- **Color Coding**: Green for paid, red for pending amounts
- **Responsive Design**: Works well on mobile and desktop

## Best Practices

1. **Always set Partner**: Ensure every voucher has a partner assigned
2. **Use Existing Relations**: Link to existing Package/User when possible
3. **Complete Financial Info**: Always fill total amount and payment details
4. **Descriptive Booking IDs**: Let system auto-generate or use meaningful IDs
5. **Detailed Itinerary**: Add day-wise details for better customer experience

## Support & Maintenance

- **Logging**: All admin actions are logged in Django admin logs
- **Soft Delete**: Inherits soft delete from BaseModel
- **Audit Trail**: Created/updated timestamps tracked automatically
- **Data Integrity**: Foreign key constraints ensure data consistency 