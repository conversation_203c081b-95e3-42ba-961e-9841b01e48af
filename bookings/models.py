from django.db import models
from django.core.validators import MinV<PERSON>ueValidator, MaxValueValidator
from django.utils import timezone
from decimal import Decimal
from django_better_admin_arrayfield.models.fields import ArrayField

from base.models import BaseModel
from accounts.models import User, Partner
from packages.models import Package, CustomPackage, CustomActivity, TripjackHotels, Destination
from .choices import (
    PackageTypeChoices, PaymentModeChoices, MealTypeChoices, BOOKING_ID_PREFIX,
    BookingStatus, BookingItineraryDayItemType, PassengerType, SSRType, 
    PassengerGenderChoices,
)
from base.storage_utils import (
    voucher_pdf_upload_path,
)


class Voucher(BaseModel):
    """
    Voucher model for booking receipts - similar to MakeMyTrip booking confirmations
    """
    # Foreign Keys
    partner = models.ForeignKey(
        Partner, 
        on_delete=models.CASCADE, 
        related_name='vouchers'
    )
    package = models.ForeignKey(
        Package, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='vouchers',
        help_text="Package associated with this voucher"
    )
    user = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='vouchers',
        help_text="User who made the booking"
    )
    
    # Booking Details
    booking_id = models.CharField(
        max_length=100, 
        # unique=True,
        blank=True,  # Allow blank in forms for auto-generation
        help_text="Leave blank to auto-generate. Format: ZUUMM[8-char-code]"
    )
    booking_date = models.DateTimeField(
        default=timezone.now,
        help_text="Date when booking was made"
    )
    
    # User Details (can be filled manually even if user FK is null)
    user_name = models.CharField(
        max_length=255,
        blank=True,
        help_text="Name of the person who made the booking"
    )
    user_email = models.EmailField(
        blank=True,
        help_text="Email of the person who made the booking"
    )
    
    # Package Details
    package_name = models.CharField(
        max_length=255,
        blank=True,
        help_text="Name of the package booked"
    )
    package_type = models.CharField(
        max_length=100,
        choices=PackageTypeChoices.choices,
        default=PackageTypeChoices.STANDARD,
        help_text="Type of package (e.g., Standard, Premium, Deluxe)"
    )
    number_of_guests = models.PositiveIntegerField(
        default=1,
        validators=[MinValueValidator(1), MaxValueValidator(50)],
        help_text="Number of guests for this booking"
    )
    
    # Travel Dates
    package_start_date = models.DateField(
        help_text="Start date of the package/travel"
    )
    package_end_date = models.DateField(
        help_text="End date of the package/travel"
    )
    
    # Package Description
    inclusions = ArrayField(
        models.CharField(max_length=255),
        blank=True,
        default=list,
        help_text="What's included in the package (e.g., 'Accommodation, Meals, Transport')"
    )
    
    # Financial Details
    total_amount = models.DecimalField(
        max_digits=12, 
        decimal_places=2,
        blank=True,
        help_text="Total amount for the booking"
    )
    amount_paid = models.DecimalField(
        max_digits=12, 
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Amount already paid"
    )
    payment_mode = models.CharField(
        max_length=255,
        choices=PaymentModeChoices.choices,
        help_text="Payment mode used (e.g., Credit Card, UPI, Bank Transfer)"
    )
    payment_date = models.DateField(
        help_text="Date when payment was made"
    )
    
    # Additional Details
    special_notes = ArrayField(
        models.CharField(max_length=255),
        blank=True,
        default=list,
        help_text="Any special notes or requirements for the booking (e.g., 'No smoking, Early check-in')"
    )
    
    # PDF Voucher
    voucher_pdf = models.FileField(
        upload_to=voucher_pdf_upload_path,
        blank=True,
        null=True,
        help_text="Generated PDF voucher file"
    )
    
    class Meta:
        verbose_name = 'Voucher'
        verbose_name_plural = 'Vouchers'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Voucher {self.booking_id} - {self.user_name}"
    
    def clean(self):
        """Model-level validation for date constraints"""
        from django.core.exceptions import ValidationError
        
        errors = {}
        
        # 1. Validate package start date < package end date
        if self.package_start_date and self.package_end_date:
            if self.package_start_date >= self.package_end_date:
                errors['package_end_date'] = 'Package end date must be after the package start date.'
        
        # # 2. Validate booking date should be earlier than package start/end date
        # if self.booking_date and self.package_start_date:
        #     booking_date_only = self.booking_date.date() if hasattr(self.booking_date, 'date') else self.booking_date
        #     if booking_date_only > self.package_start_date:
        #         errors['booking_date'] = 'Booking date must be earlier than the package start date.'
        
        if self.booking_date and self.package_end_date:
            booking_date_only = self.booking_date.date() if hasattr(self.booking_date, 'date') else self.booking_date
            if booking_date_only >= self.package_end_date:
                errors['booking_date'] = 'Booking date must be earlier than the package end date.'
        
        if errors:
            raise ValidationError(errors)
    
    @property
    def remaining_amount(self):
        """Calculate remaining amount to be paid"""
        return self.total_amount - self.amount_paid
    
    @property
    def is_fully_paid(self):
        """Check if booking is fully paid"""
        return self.amount_paid >= self.total_amount
    
    def save(self, *args, **kwargs):
        # Validate dates before saving
        self.full_clean()
        
        # Auto-generate booking ID if not provided
        if not self.booking_id:
            import uuid
            self.booking_id = f"{BOOKING_ID_PREFIX}{str(uuid.uuid4())[:8].upper()}"
        
        super().save(*args, **kwargs)


class DaywiseItinerary(BaseModel):
    """
    Model to store day-wise itinerary for vouchers - for future detailed itinerary like MMT
    """
    voucher = models.ForeignKey(
        Voucher, 
        on_delete=models.CASCADE, 
        related_name='daywise_itinerary'
    )
    day_number = models.PositiveIntegerField(
        help_text="Day number in the itinerary (1, 2, 3, etc.)"
    )
    date = models.DateField(
        help_text="Date for this day of itinerary"
    )
    day_title = models.CharField(
        max_length=255,
        help_text="Title for the day (e.g., 'Arrival in Goa')"
    )
    description = models.TextField(
        blank=True,
        help_text="Detailed description of activities for the day"
    )
    
    # Hotel/Accommodation details
    hotel_name = models.CharField(
        max_length=255,
        blank=True,
        help_text="Hotel name for the night"
    )
    room_type = models.CharField(
        max_length=100,
        # choices=RoomTypeChoices.choices,
        blank=True,
        help_text="Type of room (e.g., Deluxe, Suite, Standard)"
    )
    check_in_time = models.TimeField(
        null=True, 
        blank=True,
        help_text="Check-in time for hotel"
    )
    check_out_time = models.TimeField(
        null=True, 
        blank=True,
        help_text="Check-out time from hotel"
    )
    
    # Activities and highlights
    activities = models.TextField(
        blank=True,
        help_text="Activities planned for the day (comma-separated: Sightseeing, Shopping, Beach visit)"
    )
    meals_included = models.CharField(
        max_length=255,
        choices=MealTypeChoices.choices,
        blank=True,
        help_text="Meals included (e.g., Breakfast, Lunch, Dinner)"
    )
    
    # Transportation
    transportation = models.CharField(
        max_length=255,
        blank=True,
        help_text="Transportation details for the day"
    )
    
    class Meta:
        verbose_name = 'Daywise Itinerary'
        verbose_name_plural = 'Daywise Itineraries'
        ordering = ['voucher', 'day_number']
        unique_together = ('voucher', 'day_number')
    
    def __str__(self):
        return f"Day {self.day_number} - {self.day_title} ({self.voucher.booking_id})"
    
    def clean(self):
        """Model-level validation for itinerary date constraints"""
        from django.core.exceptions import ValidationError
        import logging
        
        logger = logging.getLogger(__name__)
        errors = {}
        
        logger.debug(f"[DaywiseItinerary.clean] Starting validation for day {self.day_number}, voucher_id: {self.voucher_id}")
        
        # Skip validation if voucher_id is None or empty (during new voucher creation)
        if not self.voucher_id:
            logger.debug(f"[DaywiseItinerary.clean] Skipping validation - no voucher_id assigned yet")
            return
        
        logger.info(f"[DaywiseItinerary.clean] Validating itinerary day {self.day_number} for voucher_id: {self.voucher_id}")
        
        # Skip validation if this is a new itinerary with a voucher_id that doesn't exist in DB yet
        # This can happen during new voucher creation when inline forms reference stale IDs
        try:
            from .models import Voucher
            voucher_exists = Voucher.objects.filter(pk=self.voucher_id).exists()
            logger.debug(f"[DaywiseItinerary.clean] Voucher {self.voucher_id} exists in database: {voucher_exists}")
            
            if not voucher_exists:
                logger.warning(f"[DaywiseItinerary.clean] Skipping validation - voucher_id {self.voucher_id} does not exist in database yet (likely stale reference)")
                return
        except Exception as e:
            logger.warning(f"[DaywiseItinerary.clean] Error checking voucher existence: {str(e)} - skipping validation")
            return
        
        # Validate itinerary date is within package date range
        if self.date and self.voucher_id:
            logger.debug(f"[DaywiseItinerary.clean] Validating date {self.date} for voucher {self.voucher_id}")
            try:
                # First try to get voucher from the instance if it's already loaded
                voucher = None
                if hasattr(self, 'voucher') and self.voucher:
                    voucher = self.voucher
                    logger.debug(f"[DaywiseItinerary.clean] Using loaded voucher instance: {voucher.pk}")
                else:
                    # Try to get from database
                    try:
                        from .models import Voucher
                        voucher = Voucher.objects.get(pk=self.voucher_id)
                        logger.debug(f"[DaywiseItinerary.clean] Fetched voucher from database: {voucher.pk}")
                    except Voucher.DoesNotExist:
                        logger.warning(f"[DaywiseItinerary.clean] Voucher {self.voucher_id} not found during validation - skipping date validation")
                        return
                
                # Validate dates if we have a voucher with package dates
                if voucher and voucher.package_start_date and voucher.package_end_date:
                    logger.debug(f"[DaywiseItinerary.clean] Package dates - Start: {voucher.package_start_date}, End: {voucher.package_end_date}, Itinerary: {self.date}")
                    
                    if self.date < voucher.package_start_date or self.date > voucher.package_end_date:
                        error_msg = (
                            f'Itinerary date ({self.date.strftime("%d %B %Y")}) must be between '
                            f'package start date ({voucher.package_start_date.strftime("%d %B %Y")}) and '
                            f'package end date ({voucher.package_end_date.strftime("%d %B %Y")}).'
                        )
                        errors['date'] = error_msg
                        logger.warning(f"[DaywiseItinerary.clean] Date validation failed: {error_msg}")
                    else:
                        logger.debug(f"[DaywiseItinerary.clean] Date validation passed for itinerary date {self.date}")
                else:
                    logger.debug(f"[DaywiseItinerary.clean] No package dates to validate against - voucher: {voucher}, start_date: {voucher.package_start_date if voucher else None}, end_date: {voucher.package_end_date if voucher else None}")
                        
            except Exception as e:
                logger.warning(f"[DaywiseItinerary.clean] Error during date validation: {str(e)} - skipping validation")
                return
        else:
            logger.debug(f"[DaywiseItinerary.clean] No date or voucher_id to validate - date: {self.date}, voucher_id: {self.voucher_id}")
        
        if errors:
            logger.error(f"[DaywiseItinerary.clean] Validation errors found: {errors}")
            raise ValidationError(errors)
        else:
            logger.debug(f"[DaywiseItinerary.clean] Validation completed successfully for day {self.day_number}")
    
    def save(self, *args, **kwargs):
        # Validate dates before saving
        import logging
        logger = logging.getLogger(__name__)
        logger.debug(f"[DaywiseItinerary.save] Saving itinerary day {self.day_number} for voucher {self.voucher_id}")
        
        try:
            self.full_clean()
            logger.debug(f"[DaywiseItinerary.save] Validation passed for day {self.day_number}")
        except Exception as e:
            logger.error(f"[DaywiseItinerary.save] Validation failed for day {self.day_number}: {str(e)}")
            raise
            
        super().save(*args, **kwargs)
        logger.info(f"[DaywiseItinerary.save] Successfully saved itinerary day {self.day_number} with ID {self.pk}")




"""Booking Models"""

class Booking(BaseModel):
    """
    The main model representing a complete package booking made by a user.
    """

    # The user who made the booking
    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='bookings'
    )
    partner = models.ForeignKey(
        Partner,
        on_delete=models.CASCADE,
        related_name='bookings'
    )
    package = models.ForeignKey(
        CustomPackage,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='bookings'
    )
    destination = models.ForeignKey(
        Destination,
        on_delete=models.DO_NOTHING,
        related_name='bookings',
        null=True,
        blank=True
    )
    status = models.CharField(max_length=20, choices=BookingStatus.choices, default=BookingStatus.PENDING)
    total_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    amount_paid = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    
    booker_user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='booked_bookings'
    )
    booker_email = models.EmailField()
    booker_phone_number = models.CharField(max_length=20)
    
    # Main booking invoice fields (for consolidated voucher)
    invoice_file = models.FileField(
        max_length=150,
        upload_to='invoices/main_bookings/', 
        null=True, 
        blank=True, 
        help_text="Generated main booking invoice/voucher file"
    )
    invoice_json = models.JSONField(null=True, blank=True, help_text="Context data used for main booking invoice generation")
    
    def __str__(self):
        return f"Booking {self.id} - {self.status}"


class BookingItinerary(BaseModel):
    """
    Represents a day in the booking itinerary.
    Contains multiple items (flights, hotels, activities) for that day.
    """
    booking = models.ForeignKey(
        Booking, 
        on_delete=models.CASCADE,
        related_name='itinerary_days'  # Added related_name
    )
    day_number = models.PositiveIntegerField()
    date = models.DateField()
    type = models.CharField(max_length=255, choices=BookingItineraryDayItemType.choices)

    # Foreign keys to specific booking types (only one should be set)
    flight_booking = models.ForeignKey(
        'FlightBooking', 
        on_delete=models.CASCADE, 
        null=True, 
        blank=True,
        related_name='day_items'
    )
    hotel_booking = models.ForeignKey(
        'HotelBooking', 
        on_delete=models.CASCADE, 
        null=True, 
        blank=True,
        related_name='day_items'
    )
    activity_booking = models.ForeignKey(
        'ActivityBooking', 
        on_delete=models.CASCADE, 
        null=True, 
        blank=True,
        related_name='day_items'
    )
    transfer_booking = models.ForeignKey(
        'TransferBooking', 
        on_delete=models.CASCADE, 
        null=True, 
        blank=True,
        related_name='day_items'
    )
    
    order = models.PositiveIntegerField(default=0)

    def __str__(self):
        return f"{self.day_number} for Booking {self.booking.id}"


class FlightBooking(BaseModel):
    """
    Stores all details related to a single flight booking.
    An instance of this model will be the `content_object` for a BookingItineraryDayItem.
    """
    tripjack_booking_id = models.CharField(max_length=100, unique=True)
    tripjack_pnr = models.CharField(max_length=100, null=True, blank=True)
    
    status = models.CharField(max_length=20, choices=BookingStatus.choices, default=BookingStatus.PENDING.value)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    is_international = models.BooleanField(default=False)
    total_passengers = models.PositiveIntegerField(default=0)
    passengers_bifurcation = models.JSONField(null=True, blank=True)
    
    # Storing complex but non-relational data as JSON is efficient.
    # For more complex querying, you could normalize these into their own models.
    gst_info = models.JSONField(null=True, blank=True)
    delivery_info = models.JSONField(null=True, blank=True)
    
    meta_information = models.JSONField(null=True, blank=True)
    booking_details = models.JSONField(null=True, blank=True, help_text="Complete booking details from Tripjack booking-details API")
    ticket_numbers = models.JSONField(null=True, blank=True, help_text="Ticket numbers for all passengers")

    # Invoice and booking details fields
    invoice_file = models.FileField(
        max_length=150,
        upload_to='invoices/flights/', 
        null=True, 
        blank=True, 
        help_text="Downloaded invoice/voucher file from Tripjack"
    )
    invoice_json = models.JSONField(null=True, blank=True)

    def __str__(self):
        return f"Flight Booking {self.tripjack_booking_id} - {self.status}"


class HotelBooking(BaseModel):
    """
    Placeholder model to store details for a hotel booking.
    This demonstrates the flexibility of the GenericForeignKey system.
    """
    tripjack_booking_id = models.CharField(max_length=100)
    tripjack_hotel_id = models.CharField(max_length=100, null=True, blank=True)
    confirmation_number = models.CharField(max_length=100, null=True, blank=True)

    hotel = models.ForeignKey(TripjackHotels, on_delete=models.SET_NULL, null=True, blank=True)
    hotel_name = models.CharField(max_length=255)
    check_in_date = models.DateTimeField()
    check_out_date = models.DateTimeField()
    room_data = models.JSONField(null=True, blank=True)
    
    number_of_rooms = models.PositiveIntegerField(default=1)
    number_of_adults = models.PositiveIntegerField(null=True, blank=True)
    number_of_children = models.PositiveIntegerField(null=True, blank=True)
    
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    status = models.CharField(max_length=20, choices=BookingStatus.choices, default=BookingStatus.PENDING, null=True, blank=True)
    meta_information = models.JSONField(null=True, blank=True)
    booking_details = models.JSONField(null=True, blank=True, help_text="Complete booking details from Tripjack booking-detail API")

    # Invoice and booking details fields
    invoice_file = models.FileField(
        max_length=150,
        upload_to='invoices/hotels/', 
        null=True, 
        blank=True, 
        help_text="Downloaded invoice/voucher file from Tripjack"
    )
    invoice_json = models.JSONField(null=True, blank=True)

    def __str__(self):
        return f"Hotel Booking for {self.hotel_name}"


class ActivityBooking(BaseModel):
    """
    Placeholder model to store details foCASCADEr an activity booking.
    This demonstrates the flexibility of the GenericForeignKey system.
    """
    activity = models.ForeignKey(CustomActivity, on_delete=models.SET_NULL, null=True, blank=True)

    activity_name = models.CharField(max_length=255, null=True, blank=True)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    participants_count = models.PositiveIntegerField(default=0)

    provider = models.CharField(max_length=255, default="GetYourGuide")
    provider_booking_id = models.CharField(max_length=255, null=True, blank=True)
    date = models.DateField(null=True, blank=True)
    timing = models.CharField(max_length=255, null=True, blank=True)
    address = models.TextField(null=True, blank=True)
    location = models.CharField(max_length=255, null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    meta_information = models.JSONField(null=True, blank=True)

    # Invoice and booking details fields
    invoice_file = models.FileField(
        max_length=150,
        upload_to='invoices/activities/', 
        null=True, 
        blank=True, 
        help_text="Downloaded invoice/voucher file"
    )
    # Note: Activities only have invoice_file (manual upload), no invoice_json needed per requirements


class TransferBooking(BaseModel):
    """
    Stores all details related to a single transfer booking via Transferz API.
    """
    # Transferz booking reference
    transferz_booking_id = models.CharField(max_length=100, unique=True)
    transferz_quote_id = models.CharField(max_length=100, null=True, blank=True)
    
    # Booking details
    pickup_location = models.CharField(max_length=255)
    dropoff_location = models.CharField(max_length=255)
    pickup_datetime = models.DateTimeField()
    
    # Vehicle and service details
    vehicle_type = models.CharField(max_length=100, null=True, blank=True)
    service_type = models.CharField(max_length=100, null=True, blank=True)  # private, shared, etc.
    passenger_count = models.PositiveIntegerField(default=1)
    
    # Pricing
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    currency = models.CharField(max_length=3, default='INR')
    
    # Status and metadata
    status = models.CharField(max_length=20, choices=BookingStatus.choices, default=BookingStatus.PENDING)
    confirmation_number = models.CharField(max_length=100, null=True, blank=True)
    
    # Contact details
    passenger_name = models.CharField(max_length=255, null=True, blank=True)
    passenger_phone = models.CharField(max_length=20, null=True, blank=True)
    passenger_email = models.EmailField(null=True, blank=True)
    
    # Additional details
    special_requirements = models.TextField(null=True, blank=True)
    flight_number = models.CharField(max_length=20, null=True, blank=True)  # for airport transfers
    
    # API response storage
    booking_details = models.JSONField(null=True, blank=True, help_text="Complete booking details from Transferz API")
    quote_details = models.JSONField(null=True, blank=True, help_text="Quote details from Transferz API")
    meta_information = models.JSONField(null=True, blank=True)
    
    # Invoice and booking details fields
    invoice_file = models.FileField(
        max_length=150,
        upload_to='invoices/transfers/', 
        null=True, 
        blank=True, 
        help_text="Downloaded invoice/voucher file"
    )
    invoice_json = models.JSONField(null=True, blank=True)
    
    def __str__(self):
        return f"Transfer Booking {self.transferz_booking_id} - {self.pickup_location} to {self.dropoff_location}"


# class BookingItineraryDayItem(BaseModel):
#     """
#     Represents a single item (Flight, Hotel, etc.) within a given day.
#     This model uses a GenericForeignKey to point to the actual booked item.
#     """
#     booking_itinerary_day = models.ForeignKey(
#         BookingItinerary, 
#         on_delete=models.CASCADE,
#         related_name='day_items'
#     )
#     type = models.CharField(max_length=20, choices=BookingItineraryDayItemType.choices)
    
#     # Foreign keys to specific booking types (only one should be set)
#     flight_booking = models.ForeignKey(
#         'FlightBooking', 
#         on_delete=models.CASCADE, 
#         null=True, 
#         blank=True,
#         related_name='day_items'
#     )
#     hotel_booking = models.ForeignKey(
#         'HotelBooking', 
#         on_delete=models.CASCADE, 
#         null=True, 
#         blank=True,
#         related_name='day_items'
#     )
#     activity_booking = models.ForeignKey(
#         'ActivityBooking', 
#         on_delete=models.CASCADE, 
#         null=True, 
#         blank=True,
#         related_name='day_items'
#     )
#     transfer_booking = models.ForeignKey(
#         'TransferBooking', 
#         on_delete=models.CASCADE, 
#         null=True, 
#         blank=True,
#         related_name='day_items'
#     )
    
#     order = models.PositiveIntegerField(default=0)

#     class Meta:
#         ordering = ['order']

#     def __str__(self):
#         return f"Item {self.order} on {self.booking_itinerary_day}"


class Passenger(BaseModel):
    """
    Stores details for a single passenger associated with a flight booking.
    """
    flight_booking = models.ForeignKey(FlightBooking, on_delete=models.CASCADE, related_name='passengers')
    
    title = models.CharField(max_length=10)
    first_name = models.CharField(max_length=100)
    last_name = models.CharField(max_length=100)
    passenger_type = models.CharField(max_length=10, choices=PassengerType.choices)
    dob = models.DateField(null=True, blank=True)
    gender = models.CharField(max_length=1, choices=PassengerGenderChoices.choices, null=True, blank=True)
    have_ssr_details = models.BooleanField(default=False)
    
    # Passport details (nullable as they are not always required)
    passport_number = models.CharField(max_length=50, null=True, blank=True)
    passport_nationality = models.CharField(max_length=5, null=True, blank=True)
    passport_issue_date = models.DateField(null=True, blank=True)
    passport_expiry_date = models.DateField(null=True, blank=True)
    
    def __str__(self):
        return f"{self.first_name} {self.last_name} ({self.passenger_type})"


class BookedFlightSSR(BaseModel):
    """
    Stores a selected Special Service Request (SSR) like meals, baggage, or seats
    for a specific passenger on a specific flight segment.
    """

    passenger = models.ForeignKey(Passenger, on_delete=models.CASCADE, related_name='ssrs')
    ssr_type = models.CharField(max_length=10, choices=SSRType.choices)
    segment_key = models.CharField(max_length=100)
    code = models.CharField(max_length=50)

    def __str__(self):
        return f"{self.ssr_type}: {self.code} for {self.passenger.first_name} on segment {self.segment_key}"
