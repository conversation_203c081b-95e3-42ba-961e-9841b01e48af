from django.contrib import admin
from django.urls import reverse
from django.utils.html import format_html
from django.utils.safestring import mark_safe
from django import forms
from .models import (
    Voucher, Booking, BookingItinerary, 
    FlightBooking, HotelBooking, ActivityBooking, TransferBooking,
    Passenger, BookedFlightSSR
)
import logging

logger = logging.getLogger(__name__)


@admin.register(Voucher)
class VoucherAdmin(admin.ModelAdmin):
    """Voucher admin - keeping existing configuration"""
    list_display = ['booking_id', 'user_name', 'user_email', 'package_name', 'total_amount', 'booking_date']
    list_filter = ['booking_date', 'package_type', 'payment_mode']
    search_fields = ['booking_id', 'user_name', 'user_email', 'package_name']
    readonly_fields = ['booking_id', 'created_at', 'updated_at']
    
    fieldsets = (
        ('Booking Information', {
            'fields': ('booking_id', 'partner', 'package', 'user', 'booking_date')
        }),
        ('User Details', {
            'fields': ('user_name', 'user_email')
        }),
        ('Package Details', {
            'fields': ('package_name', 'package_type', 'number_of_guests', 'package_start_date', 'package_end_date', 'inclusions')
        }),
        ('Financial Details', {
            'fields': ('total_amount', 'amount_paid', 'payment_mode', 'payment_date')
        }),
        ('Additional Details', {
            'fields': ('special_notes', 'voucher_pdf')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at')
        }),
    )


class BookingForm(forms.ModelForm):
    """Custom form for Booking model to handle edit operations"""
    
    class Meta:
        model = Booking
        fields = '__all__'
        
    def _init_(self, *args, **kwargs):
        super()._init_(*args, **kwargs)
        # Make certain fields readonly
        readonly_fields = ['external_id', 'created_at', 'updated_at']
        for field_name in readonly_fields:
            if field_name in self.fields:
                self.fields[field_name].widget.attrs['readonly'] = True


class BookingItineraryInline(admin.TabularInline):
    """Inline for showing BookingItinerary items in Booking detail view"""
    model = BookingItinerary
    extra = 0
    can_delete = False
    verbose_name = "Booking Itinerary Item"
    verbose_name_plural = "Booking Itinerary Items"
    help_text = "Click on the 'Related Booking' link to view details or upload/edit invoice files for each booking item."
    readonly_fields = [
        'day_number', 'date', 'type', 
        'booking_link', 'invoice_file_display', 'flight_booking', 'hotel_booking', 
        'activity_booking', 'transfer_booking'
    ]
    
    fields = [
        'day_number', 'date', 'type', 'booking_link', 'invoice_file_display'
    ]
    
    def get_queryset(self, request):
        """Order the itinerary items properly"""
        queryset = super().get_queryset(request)
        return queryset.order_by('date', 'day_number', 'order')
    
    def booking_link(self, obj):
        """Create hyperlink to the related booking model based on type"""
        if not obj:
            return '-'
            
        if obj.type == 'Flight' and obj.flight_booking:
            url = reverse('admin:bookings_flightbooking_change', args=[obj.flight_booking.id])
            return format_html('<a href="{}" target="_blank">Flight Booking #{}</a>', 
                             url, obj.flight_booking.id)
        elif obj.type == 'Hotel' and obj.hotel_booking:
            url = reverse('admin:bookings_hotelbooking_change', args=[obj.hotel_booking.id])
            return format_html('<a href="{}" target="_blank">Hotel Booking #{}</a>', 
                             url, obj.hotel_booking.id)
        elif obj.type == 'Activity' and obj.activity_booking:
            url = reverse('admin:bookings_activitybooking_change', args=[obj.activity_booking.id])
            return format_html('<a href="{}" target="_blank">Activity Booking #{}</a>', 
                             url, obj.activity_booking.id)
        elif obj.type == 'Transfer' and obj.transfer_booking:
            url = reverse('admin:bookings_transferbooking_change', args=[obj.transfer_booking.id])
            return format_html('<a href="{}" target="_blank">Transfer Booking #{}</a>', 
                             url, obj.transfer_booking.id)
        else:
            return f'{obj.type} - No booking linked'
    
    booking_link.short_description = 'Related Booking'
    
    def has_add_permission(self, request, obj=None):
        return False
    
    def invoice_file_display(self, obj):
        """Display invoice file information based on booking type"""
        if not obj:
            return '-'
            
        invoice_file = None
        booking_id = None
        
        if obj.type == 'Flight' and obj.flight_booking:
            invoice_file = obj.flight_booking.invoice_file
            booking_id = obj.flight_booking.id
        elif obj.type == 'Hotel' and obj.hotel_booking:
            invoice_file = obj.hotel_booking.invoice_file
            booking_id = obj.hotel_booking.id
        elif obj.type == 'Activity' and obj.activity_booking:
            invoice_file = obj.activity_booking.invoice_file
            booking_id = obj.activity_booking.id
        elif obj.type == 'Transfer' and obj.transfer_booking:
            invoice_file = obj.transfer_booking.invoice_file
            booking_id = obj.transfer_booking.id
        
        if invoice_file:
            # Get the filename from the file path
            filename = invoice_file.name.split('/')[-1] if invoice_file.name else 'invoice.pdf'
            # Use direct URL instead of presigned URL
            url = invoice_file.url
            return format_html(
                '<a href="{}" target="_blank" title="Download {}">{}</a>', 
                url, filename, filename[:20] + '...' if len(filename) > 20 else filename
            )
        else:
            return format_html('<span style="color: #999;">No invoice</span>')
    
    invoice_file_display.short_description = 'Invoice File'


@admin.register(Booking)
class BookingAdmin(admin.ModelAdmin):
    """Enhanced Booking admin with detailed view and itinerary section"""
    
    form = BookingForm
    list_display = [
        'external_id', 'booker_email', 'destination_display', 
        'package_display', 'status', 'total_amount', 'invoice_status', 'created_at'
    ]
    list_filter = ['status', 'created_at', 'destination', 'partner']
    search_fields = ['external_id', 'booker_email', 'booker_phone_number', 'user__email']
    readonly_fields = ['external_id', 'created_at', 'updated_at']
    autocomplete_fields = ['user', 'partner', 'package', 'destination']
    
    fieldsets = (
        ('Booking Information', {
            'fields': (
                'external_id', 'user', 'partner', 'package', 'destination', 'status'
            ),
            'classes': ('wide',)
        }),
        ('Booker Details', {
            'fields': ('booker_email', 'booker_phone_number'),
            'classes': ('wide',)
        }),
        ('Financial Information', {
            'fields': ('total_amount', 'amount_paid'),
            'classes': ('wide',)
        }),
        ('Invoice Information', {
            'fields': ('invoice_file', 'invoice_json'),
            'classes': ('wide', 'collapse'),
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('wide', 'collapse'),
        }),
    )
    
    inlines = [BookingItineraryInline]
    
    def get_readonly_fields(self, request, obj=None):
        """Make external_id and timestamps readonly, allow editing of other fields"""
        readonly = list(self.readonly_fields)
        if obj:  # Editing existing booking
            readonly.extend(['external_id', 'created_at', 'updated_at'])
        return readonly
    
    def has_add_permission(self, request):
        """Don't allow admin to create new bookings"""
        return False
    
    def destination_display(self, obj):
        """Display destination name"""
        return obj.destination.title if obj.destination else '-'
    destination_display.short_description = 'Destination'
    
    def package_display(self, obj):
        """Display package name"""
        return obj.package.title if obj.package else '-'
    package_display.short_description = 'Package'
    
    def invoice_status(self, obj):
        """Display invoice file status"""
        if obj.invoice_file:
            return format_html('<span style="color: green;">✓ Generated</span>')
        else:
            return format_html('<span style="color: red;">✗ Not Generated</span>')
    invoice_status.short_description = 'Invoice Status'
    
    def change_view(self, request, object_id, form_url='', extra_context=None):
        """Override change_view to handle custom button actions BEFORE form validation"""
        # Add detailed logging to understand what's happening
        logger.info(f"BookingAdmin.change_view called for booking {object_id}")
        logger.info(f"Request method: {request.method}")
        logger.info(f"Request POST keys: {list(request.POST.keys()) if request.method == 'POST' else 'N/A (GET)'}")
        
        # Get the booking object
        obj = self.get_object(request, object_id)
        if not obj:
            logger.error(f"Booking with ID {object_id} not found")
            return super().change_view(request, object_id, form_url, extra_context)
        
        # Handle custom button actions BEFORE Django processes the form
        if request.method == 'POST':
            from django.http import HttpResponseRedirect
            from django.urls import reverse
            from django.contrib import messages
            from bookings.services.invoice_email_service import InvoiceEmailService
            
            if '_mail_main_invoice' in request.POST:
                logger.info(f"BookingAdmin - Mail main invoice button clicked for booking {obj.external_id}")
                
                try:
                    # Test basic functionality first
                    logger.info(f"Testing email service for booking {obj.external_id}")
                    logger.info(f"Booking user: {obj.user}")
                    logger.info(f"Booking user email: {obj.user.email if obj.user else 'None'}")
                    logger.info(f"Booking booker email: {obj.booker_email}")
                    logger.info(f"Booking invoice file: {obj.invoice_file}")
                    
                    if not obj.invoice_file:
                        messages.error(request, f'No main invoice file found for booking {obj.external_id}. Please generate the invoice first.')
                    elif not (obj.user and obj.user.email) and not obj.booker_email:
                        messages.error(request, f'No email address found for booking {obj.external_id}. Please add user email or booker email.')
                    else:
                        success = InvoiceEmailService.send_main_booking_invoice(obj)
                        
                        if success:
                            recipient = obj.user.email if obj.user and obj.user.email else obj.booker_email
                            messages.success(request, f'Main booking invoice has been sent successfully to {recipient}.')
                        else:
                            messages.error(request, f'Failed to send main booking invoice. Please check the logs for details.')
                            
                except Exception as e:
                    logger.error(f"Error sending main booking invoice for {obj.external_id}: {str(e)}", exc_info=True)
                    messages.error(request, f'Error sending main booking invoice: {str(e)}')
                    
                return HttpResponseRedirect(
                    reverse('admin:bookings_booking_change', args=[obj.pk])
                )
            
            elif '_mail_hotel_invoices' in request.POST:
                logger.info(f"BookingAdmin - Mail hotel invoices button clicked for booking {obj.external_id}")
                
                try:
                    success = InvoiceEmailService.send_type_invoices(obj, 'Hotel')
                    
                    if success:
                        recipient = obj.user.email if obj.user and obj.user.email else obj.booker_email
                        messages.success(request, f'Hotel invoices have been sent successfully to {recipient}.')
                    else:
                        messages.error(request, f'Failed to send hotel invoices. Please check if hotel invoice files exist.')
                        
                except Exception as e:
                    logger.error(f"Error sending hotel invoices for {obj.external_id}: {str(e)}", exc_info=True)
                    messages.error(request, f'Error sending hotel invoices: {str(e)}')
                    
                return HttpResponseRedirect(
                    reverse('admin:bookings_booking_change', args=[obj.pk])
                )
            
            elif '_mail_flight_invoices' in request.POST:
                logger.info(f"BookingAdmin - Mail flight invoices button clicked for booking {obj.external_id}")
                
                try:
                    success = InvoiceEmailService.send_type_invoices(obj, 'Flight')
                    
                    if success:
                        recipient = obj.user.email if obj.user and obj.user.email else obj.booker_email
                        messages.success(request, f'Flight invoices have been sent successfully to {recipient}.')
                    else:
                        messages.error(request, f'Failed to send flight invoices. Please check if flight invoice files exist.')
                        
                except Exception as e:
                    logger.error(f"Error sending flight invoices for {obj.external_id}: {str(e)}", exc_info=True)
                    messages.error(request, f'Error sending flight invoices: {str(e)}')
                    
                return HttpResponseRedirect(
                    reverse('admin:bookings_booking_change', args=[obj.pk])
                )
            
            elif '_mail_activity_invoices' in request.POST:
                logger.info(f"BookingAdmin - Mail activity invoices button clicked for booking {obj.external_id}")
                
                try:
                    success = InvoiceEmailService.send_type_invoices(obj, 'Activity')
                    
                    if success:
                        recipient = obj.user.email if obj.user and obj.user.email else obj.booker_email
                        messages.success(request, f'Activity invoices have been sent successfully to {recipient}.')
                    else:
                        messages.error(request, f'Failed to send activity invoices. Please check if activity invoice files exist.')
                        
                except Exception as e:
                    logger.error(f"Error sending activity invoices for {obj.external_id}: {str(e)}", exc_info=True)
                    messages.error(request, f'Error sending activity invoices: {str(e)}')
                    
                return HttpResponseRedirect(
                    reverse('admin:bookings_booking_change', args=[obj.pk])
                )
            
            elif '_mail_transfer_invoices' in request.POST:
                logger.info(f"BookingAdmin - Mail transfer invoices button clicked for booking {obj.external_id}")
                
                try:
                    success = InvoiceEmailService.send_type_invoices(obj, 'Transfer')
                    
                    if success:
                        recipient = obj.user.email if obj.user and obj.user.email else obj.booker_email
                        messages.success(request, f'Transfer invoices have been sent successfully to {recipient}.')
                    else:
                        messages.error(request, f'Failed to send transfer invoices. Please check if transfer invoice files exist.')
                        
                except Exception as e:
                    logger.error(f"Error sending transfer invoices for {obj.external_id}: {str(e)}", exc_info=True)
                    messages.error(request, f'Error sending transfer invoices: {str(e)}')
                    
                return HttpResponseRedirect(
                    reverse('admin:bookings_booking_change', args=[obj.pk])
                )
        
        # For normal requests (GET or POST without custom buttons), call the parent method
        logger.info(f"BookingAdmin.change_view - normal request, calling super()")
        return super().change_view(request, object_id, form_url, extra_context)
    

@admin.register(FlightBooking)
class FlightBookingAdmin(admin.ModelAdmin):
    """Flight Booking admin - editable invoice_file only"""
    
    list_display = [
        'id', 'tripjack_booking_id', 'tripjack_pnr', 'status', 
        'amount', 'total_passengers', 'is_international', 'invoice_status'
    ]
    list_filter = ['status', 'is_international', 'created_at']
    search_fields = ['tripjack_booking_id', 'tripjack_pnr']
    readonly_fields = [field.name for field in FlightBooking._meta.fields if field.name != 'invoice_file']
    
    fieldsets = (
        ('Basic Information', {
            'fields': (
                'tripjack_booking_id', 'tripjack_pnr', 'status', 'is_international'
            ),
        }),
        ('Financial Details', {
            'fields': ('amount', 'currency', 'total_passengers'),
        }),
        ('Booking Details', {
            'fields': ('booking_details', 'search_id'),
            'classes': ('collapse',),
        }),
        ('Invoice Information', {
            'fields': ('invoice_file', 'invoice_json'),
            'classes': ('collapse',),
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',),
        }),
    )
    
    def has_add_permission(self, request):
        return False
    

    
    def has_delete_permission(self, request, obj=None):
        return False
    
    def invoice_status(self, obj):
        """Display invoice file status"""
        if obj.invoice_file:
            return format_html('<span style="color: green;">✓ Generated</span>')
        else:
            return format_html('<span style="color: red;">✗ Not Generated</span>')
    invoice_status.short_description = 'Invoice Status'
    
@admin.register(HotelBooking)
class HotelBookingAdmin(admin.ModelAdmin):
    """Hotel Booking admin - editable invoice_file only"""
    
    list_display = [
        'id', 'hotel_name', 'tripjack_booking_id', 'check_in_date', 
        'check_out_date', 'amount', 'status', 'invoice_status'
    ]
    list_filter = ['status', 'check_in_date', 'check_out_date']
    search_fields = ['hotel_name', 'tripjack_booking_id', 'tripjack_hotel_id']
    readonly_fields = [field.name for field in HotelBooking._meta.fields if field.name != 'invoice_file']
    
    fieldsets = (
        ('Basic Information', {
            'fields': (
                'tripjack_booking_id', 'tripjack_hotel_id', 'hotel_name', 'status', 'confirmation_number'
            ),
        }),
        ('Stay Details', {
            'fields': (
                'check_in_date', 'check_out_date', 'number_of_rooms', 
                'number_of_adults', 'number_of_children'
            ),
        }),
        ('Financial Details', {
            'fields': ('amount',),
        }),
        ('Booking Details', {
            'fields': ('booking_details', 'room_data', 'meta_information'),
            'classes': ('collapse',),
        }),
        ('Invoice Information', {
            'fields': ('invoice_file', 'invoice_json'),
            'classes': ('collapse',),
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',),
        }),
    )
    
    def has_add_permission(self, request):
        return False
    

    
    def has_delete_permission(self, request, obj=None):
        return False
    
    def invoice_status(self, obj):
        """Display invoice file status"""
        if obj.invoice_file:
            return format_html('<span style="color: green;">✓ Generated</span>')
        else:
            return format_html('<span style="color: red;">✗ Not Generated</span>')
    invoice_status.short_description = 'Invoice Status'
    
@admin.register(ActivityBooking)
class ActivityBookingAdmin(admin.ModelAdmin):
    """Activity Booking admin - editable invoice_file only"""
    
    list_display = [
        'id', 'activity_name', 'provider', 'date', 
        'amount', 'participants_count', 'invoice_status'
    ]
    list_filter = ['provider', 'date']
    search_fields = ['activity_name', 'provider_booking_id', 'location']
    readonly_fields = [field.name for field in ActivityBooking._meta.fields if field.name != 'invoice_file']
    
    fieldsets = (
        ('Basic Information', {
            'fields': (
                'activity_name', 'provider', 'provider_booking_id'
            ),
        }),
        ('Activity Details', {
            'fields': (
                'location', 'date', 'participants_count', 'timing', 'address'
            ),
        }),
        ('Financial Details', {
            'fields': ('amount',),
        }),
        ('Additional Information', {
            'fields': ('description', 'meta_information'),
            'classes': ('collapse',),
        }),
        ('Invoice Information', {
            'fields': ('invoice_file',),
            'classes': ('collapse',),
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',),
        }),
    )
    
    def has_add_permission(self, request):
        return False
    

    
    def has_delete_permission(self, request, obj=None):
        return False
    
    def invoice_status(self, obj):
        """Display invoice file status"""
        if obj.invoice_file:
            return format_html('<span style="color: green;">✓ Generated</span>')
        else:
            return format_html('<span style="color: red;">✗ Not Generated</span>')
    invoice_status.short_description = 'Invoice Status'
    
@admin.register(TransferBooking)
class TransferBookingAdmin(admin.ModelAdmin):
    """Transfer Booking admin - editable invoice_file only"""
    
    list_display = [
        'id', 'transferz_booking_id', 'pickup_location', 'dropoff_location', 
        'vehicle_type', 'amount', 'status', 'invoice_status'
    ]
    list_filter = ['status', 'pickup_datetime', 'vehicle_type']
    search_fields = ['transferz_booking_id', 'pickup_location', 'dropoff_location']
    readonly_fields = [field.name for field in TransferBooking._meta.fields if field.name != 'invoice_file']
    
    fieldsets = (
        ('Basic Information', {
            'fields': (
                'transferz_booking_id', 'status', 'vehicle_type'
            ),
        }),
        ('Transfer Details', {
            'fields': (
                'pickup_location', 'dropoff_location', 'pickup_datetime', 
                'passenger_count'
            ),
        }),
        ('Financial Details', {
            'fields': ('amount', 'currency'),
        }),
        ('Booking Details', {
            'fields': ('booking_details',),
            'classes': ('collapse',),
        }),
        ('Invoice Information', {
            'fields': ('invoice_file', 'invoice_json'),
            'classes': ('collapse',),
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',),
        }),
    )
    
    def has_add_permission(self, request):
        return False
    

    
    def has_delete_permission(self, request, obj=None):
        return False
    
    def invoice_status(self, obj):
        """Display invoice file status"""
        if obj.invoice_file:
            return format_html('<span style="color: green;">✓ Generated</span>')
        else:
            return format_html('<span style="color: red;">✗ Not Generated</span>')
    invoice_status.short_description = 'Invoice Status'
    
# Keep other models as reference
@admin.register(BookingItinerary)
class BookingItineraryAdmin(admin.ModelAdmin):
    """BookingItinerary admin for reference"""
    list_display = ['id', 'booking', 'day_number', 'date', 'type', 'order', 'booking_reference']
    list_filter = ['type', 'date']
    search_fields = ['booking_booker_email', 'booking_external_id']
    readonly_fields = [field.name for field in BookingItinerary._meta.fields]
    
    def has_add_permission(self, request):
        return False
    

    
    def has_delete_permission(self, request, obj=None):
        return False
    
    def booking_reference(self, obj):
        """Show which specific booking is linked"""
        if obj.flight_booking:
            return f"Flight #{obj.flight_booking.id}"
        elif obj.hotel_booking:
            return f"Hotel #{obj.hotel_booking.id}"
        elif obj.activity_booking:
            return f"Activity #{obj.activity_booking.id}"
        elif obj.transfer_booking:
            return f"Transfer #{obj.transfer_booking.id}"
        else:
            return "No booking linked"
    booking_reference.short_description = 'Linked Booking'


@admin.register(Passenger)
class PassengerAdmin(admin.ModelAdmin):
    """Passenger admin for reference"""
    list_display = ['first_name', 'last_name', 'passenger_type', 'flight_booking']
    list_filter = ['passenger_type', 'gender']
    search_fields = ['first_name', 'last_name', 'passport_number']
    readonly_fields = [field.name for field in Passenger._meta.fields]
    
    def has_add_permission(self, request):
        return False
    

    
    def has_delete_permission(self, request, obj=None):
        return False


@admin.register(BookedFlightSSR)
class BookedFlightSSRAdmin(admin.ModelAdmin):
    """BookedFlightSSR admin for reference"""
    list_display = ['passenger', 'ssr_type', 'segment_key', 'code']
    list_filter = ['ssr_type']
    search_fields = ['passenger_first_name', 'passenger_last_name', 'code']
    readonly_fields = [field.name for field in BookedFlightSSR._meta.fields]
    
    def has_add_permission(self, request):
        return False
    

    
    def has_delete_permission(self, request, obj=None):
        return False
