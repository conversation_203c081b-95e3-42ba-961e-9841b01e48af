from django import forms
from django.core.exceptions import ValidationError
from django.forms.models import BaseInlineFormSet
import logging

from .models import Voucher
from packages.models import Package
from accounts.models import User
from .utils import VoucherPDFGenerator, VoucherEmailService

logger = logging.getLogger(__name__)


class DaywiseItineraryFormSet(BaseInlineFormSet):
    """Custom formset to validate at least one itinerary is required"""
    
    def __init__(self, *args, **kwargs):
        logger.debug(f"[DaywiseItineraryFormSet] __init__ called with {len(args)} args and {len(kwargs)} kwargs")
        super().__init__(*args, **kwargs)
        logger.info(f"[DaywiseItineraryFormSet] Initialized with {len(self.forms)} forms")
    
    def clean(self):
        """Validate that at least one valid itinerary form is present and dates are within package range"""
        logger.info(f"[DaywiseItineraryFormSet] Starting clean() validation for {len(self.forms)} forms")
        
        if any(self.errors):
            # Don't validate if there are already errors
            logger.warning(f"[DaywiseItineraryFormSet] Skipping clean() - existing errors found in forms")
            return
        
        valid_forms = 0
        itinerary_dates = []
        
        # Get package dates from the parent form
        package_start_date = None
        package_end_date = None
        
        logger.debug(f"[DaywiseItineraryFormSet] Attempting to get package dates from instance")
        # Try to get package dates from the parent form's cleaned_data
        if hasattr(self, 'instance') and self.instance:
            # For existing vouchers, get dates from the instance
            package_start_date = getattr(self.instance, 'package_start_date', None)
            package_end_date = getattr(self.instance, 'package_end_date', None)
            logger.debug(f"[DaywiseItineraryFormSet] Got dates from instance - start: {package_start_date}, end: {package_end_date}")
        
        # If not available from instance, try to get from the parent form
        if not package_start_date or not package_end_date:
            logger.debug(f"[DaywiseItineraryFormSet] Attempting to get package dates from form data")
            # Try to access parent form data
            if hasattr(self, 'data'):
                try:
                    from datetime import datetime
                    if 'package_start_date' in self.data:
                        package_start_date = datetime.strptime(self.data['package_start_date'], '%Y-%m-%d').date()
                        logger.debug(f"[DaywiseItineraryFormSet] Parsed package_start_date from data: {package_start_date}")
                    if 'package_end_date' in self.data:
                        package_end_date = datetime.strptime(self.data['package_end_date'], '%Y-%m-%d').date()
                        logger.debug(f"[DaywiseItineraryFormSet] Parsed package_end_date from data: {package_end_date}")
                except (ValueError, TypeError) as e:
                    # If we can't parse the dates, skip itinerary date validation
                    logger.warning(f"[DaywiseItineraryFormSet] Error parsing package dates from data: {str(e)}")
                    pass
        
        logger.info(f"[DaywiseItineraryFormSet] Package date range for validation - start: {package_start_date}, end: {package_end_date}")
        
        for i, form in enumerate(self.forms):
            logger.debug(f"[DaywiseItineraryFormSet] Processing form {i+1}/{len(self.forms)}")
            
            if form.cleaned_data and not form.cleaned_data.get('DELETE', False):
                valid_forms += 1
                logger.debug(f"[DaywiseItineraryFormSet] Form {i+1} is valid (not deleted), total valid: {valid_forms}")
                
                # Validate itinerary date against package dates
                itinerary_date = form.cleaned_data.get('date')
                day_number = form.cleaned_data.get('day_number', 'Unknown')
                
                if itinerary_date and package_start_date and package_end_date:
                    logger.debug(f"[DaywiseItineraryFormSet] Validating day {day_number} date {itinerary_date} against package range")
                    if itinerary_date < package_start_date or itinerary_date > package_end_date:
                        error_msg = (f'Itinerary date ({itinerary_date.strftime("%d %B %Y")}) must be between '
                            f'package start date ({package_start_date.strftime("%d %B %Y")}) and '
                            f'package end date ({package_end_date.strftime("%d %B %Y")}).')
                        logger.error(f"[DaywiseItineraryFormSet] Date validation failed for day {day_number}: {error_msg}")
                        form.add_error('date', error_msg)
                    else:
                        itinerary_dates.append(itinerary_date)
                        logger.debug(f"[DaywiseItineraryFormSet] Date validation passed for day {day_number}")
                else:
                    logger.debug(f"[DaywiseItineraryFormSet] Skipping date validation for day {day_number} - missing dates")
            else:
                logger.debug(f"[DaywiseItineraryFormSet] Form {i+1} is invalid or marked for deletion")
        
        logger.info(f"[DaywiseItineraryFormSet] Validation summary - valid forms: {valid_forms}, valid dates: {len(itinerary_dates)}")
        
        if valid_forms < 1:
            error_msg = 'At least one day-wise itinerary is required for the voucher.'
            logger.error(f"[DaywiseItineraryFormSet] {error_msg}")
            raise ValidationError(error_msg)
        
        logger.info(f"[DaywiseItineraryFormSet] clean() validation completed successfully")

    def full_clean(self):
        """Override to handle stale voucher references and add logging"""
        logger.info(f"[DaywiseItineraryFormSet] Starting full_clean for {len(self.forms)} forms")
        
        # Check for any voucher references in form data
        if hasattr(self, 'data') and self.data:
            logger.debug(f"[DaywiseItineraryFormSet] Checking {len(self.data)} data keys for voucher references")
            voucher_refs = []
            for key, value in self.data.items():
                if 'voucher' in key.lower() and value:
                    voucher_refs.append((key, value))
            if voucher_refs:
                logger.debug(f"[DaywiseItineraryFormSet] Found voucher references in data: {voucher_refs}")
            else:
                logger.debug(f"[DaywiseItineraryFormSet] No voucher references found in form data")
        
        # Check each form instance for voucher references
        for i, form in enumerate(self.forms):
            logger.debug(f"[DaywiseItineraryFormSet] Checking form {i+1} instance for voucher references")
            if hasattr(form, 'instance') and form.instance and hasattr(form.instance, 'voucher_id'):
                voucher_id = form.instance.voucher_id
                if voucher_id:
                    logger.debug(f"[DaywiseItineraryFormSet] Form {i+1} instance has voucher_id: {voucher_id}")
                    # Check if this voucher exists
                    try:
                        voucher_exists = Voucher.objects.filter(pk=voucher_id).exists()
                        logger.debug(f"[DaywiseItineraryFormSet] Voucher {voucher_id} exists in database: {voucher_exists}")
                    except Exception as e:
                        logger.warning(f"[DaywiseItineraryFormSet] Error checking voucher {voucher_id} existence: {str(e)}")
                else:
                    logger.debug(f"[DaywiseItineraryFormSet] Form {i+1} instance has no voucher_id")
        
        try:
            logger.debug(f"[DaywiseItineraryFormSet] Calling super().full_clean()")
            super().full_clean()
            logger.info(f"[DaywiseItineraryFormSet] full_clean completed successfully")
        except Exception as e:
            logger.error(f"[DaywiseItineraryFormSet] Error in full_clean: {str(e)}", exc_info=True)
            # If there's a validation error about non-existent voucher, log it specifically
            if 'does not exist' in str(e) and 'Voucher instance with id' in str(e):
                logger.warning(f"[DaywiseItineraryFormSet] Detected voucher existence validation error: {str(e)}")
            raise e


class VoucherAdminForm(forms.ModelForm):
    """Custom form for Voucher admin with dynamic filtering"""
    
    class Meta:
        model = Voucher
        fields = '__all__'
    
    def __init__(self, *args, **kwargs):
        # Extract request from kwargs if passed by admin
        self.request = kwargs.pop('request', None)
        logger.info(f"[VoucherAdminForm] __init__ called - request: {self.request is not None}, args: {len(args)}, kwargs: {len(kwargs)}")
        
        super().__init__(*args, **kwargs)
        
        # Log form instance details
        if hasattr(self, 'instance') and self.instance:
            logger.debug(f"[VoucherAdminForm] Form instance: {self.instance.pk if self.instance.pk else 'new'}, booking_id: {getattr(self.instance, 'booking_id', 'None')}")
        
        # If we have a request, filter packages and users by partner
        # Only if the fields exist in the form (not readonly)
        if self.request:
            from packages.admin import get_user_effective_partner
            effective_partner = get_user_effective_partner(self.request)
            
            logger.debug(f"[VoucherAdminForm] Effective partner for filtering: {effective_partner}")
            
            if effective_partner:
                # Filter packages for the same partner and active status
                # Only if package field exists (not readonly)
                if 'package' in self.fields:
                    package_count_before = Package.objects.count()
                    self.fields['package'].queryset = Package.objects.filter(
                        partner=effective_partner, is_active=True
                    ).order_by('title')
                    package_count_after = self.fields['package'].queryset.count()
                    logger.debug(f"[VoucherAdminForm] Filtered packages: {package_count_before} -> {package_count_after}")
                
                # Filter users for the same partner and active status
                # Only if user field exists (not readonly)
                if 'user' in self.fields:
                    user_count_before = User.objects.count()
                    self.fields['user'].queryset = User.objects.filter(
                        partner=effective_partner, is_active=True
                    ).order_by('full_name', 'email')
                    user_count_after = self.fields['user'].queryset.count()
                    logger.debug(f"[VoucherAdminForm] Filtered users: {user_count_before} -> {user_count_after}")
            else:
                # No partner context, show no packages/users
                if 'package' in self.fields:
                    self.fields['package'].queryset = Package.objects.none()
                    logger.debug(f"[VoucherAdminForm] No partner - emptied package queryset")
                if 'user' in self.fields:
                    self.fields['user'].queryset = User.objects.none()
                    logger.debug(f"[VoucherAdminForm] No partner - emptied user queryset")
        
        # Add helpful text for auto-generated booking ID
        # Only if booking_id field exists (not readonly)
        if 'booking_id' in self.fields:
            self.fields['booking_id'].help_text = (
                "Leave blank to auto-generate. Format: ZUUMM[8-char-code]"
            )
            self.fields['booking_id'].required = False  # Explicitly mark as not required
            logger.debug(f"[VoucherAdminForm] Set booking_id field as optional with help text")
        
        logger.info(f"[VoucherAdminForm] Initialization completed")
    
    def clean(self):
        """Custom validation including DaywiseItinerary requirement"""
        logger.info(f"[VoucherAdminForm] Starting clean() validation")
        
        cleaned_data = super().clean()
        logger.debug(f"[VoucherAdminForm] super().clean() completed, cleaned_data keys: {list(cleaned_data.keys())}")
        
        # Call the existing validation logic
        logger.debug(f"[VoucherAdminForm] Starting user and package validation")
        cleaned_data = self._validate_user_and_package(cleaned_data)
        
        # Add calendar validations
        logger.debug(f"[VoucherAdminForm] Starting date validation")
        cleaned_data = self._validate_dates(cleaned_data)
        
        # Validate that amount_paid is not greater than total_amount
        total_amount = cleaned_data.get('total_amount')
        amount_paid = cleaned_data.get('amount_paid')
        
        logger.debug(f"[VoucherAdminForm] Amount validation - total: {total_amount}, paid: {amount_paid}")
        
        if total_amount is not None and amount_paid is not None:
            if amount_paid > total_amount:
                error_msg = f'Amount paid (₹{amount_paid}) cannot be greater than total amount (₹{total_amount}).'
                logger.error(f"[VoucherAdminForm] Amount validation failed: {error_msg}")
                raise ValidationError({
                    'amount_paid': error_msg
                })
        
        # Validate DaywiseItinerary requirement
        # Note: This will only work for existing vouchers during edit
        # For new vouchers, the inline formset handles this validation
        if hasattr(self, 'instance') and self.instance.pk:
            logger.debug(f"[VoucherAdminForm] Checking itinerary requirement for existing voucher {self.instance.pk}")
            itinerary_count = self.instance.daywise_itinerary.count()
            if itinerary_count == 0:
                error_msg = 'At least one day-wise itinerary is required for the voucher.'
                logger.error(f"[VoucherAdminForm] Itinerary requirement failed: {error_msg}")
                raise ValidationError({
                    '__all__': error_msg
                })
            else:
                logger.debug(f"[VoucherAdminForm] Itinerary requirement satisfied: {itinerary_count} itineraries found")
        else:
            logger.debug(f"[VoucherAdminForm] Skipping itinerary requirement check for new voucher")
        
        logger.info(f"[VoucherAdminForm] clean() validation completed successfully")
        return cleaned_data
    
    def save(self, commit=True):
        """Override save to handle booking ID generation"""
        logger.info(f"[VoucherForm] Starting save method - commit={commit}")
        
        # Generate booking ID early if not provided
        if not self.instance.booking_id:
            import uuid
            from .choices import BOOKING_ID_PREFIX
            new_booking_id = f"{BOOKING_ID_PREFIX}{str(uuid.uuid4())[:8].upper()}"
            self.instance.booking_id = new_booking_id
            logger.info(f"[VoucherForm] Auto-generated booking ID: {new_booking_id}")
        else:
            logger.debug(f"[VoucherForm] Using existing booking ID: {self.instance.booking_id}")
        
        # Save the voucher
        logger.debug(f"[VoucherForm] Calling super().save() with commit={commit}")
        voucher = super().save(commit=commit)
        logger.info(f"[VoucherForm] Voucher saved - ID: {voucher.pk}, Booking ID: {voucher.booking_id}")
        
        logger.info(f"[VoucherForm] Save method completed for voucher {voucher.booking_id}")
        return voucher
    
    def _validate_dates(self, cleaned_data):
        """Calendar validations for booking and package dates"""
        logger.debug(f"[VoucherAdminForm] Starting date validation")
        
        booking_date = cleaned_data.get('booking_date')
        package_start_date = cleaned_data.get('package_start_date')
        package_end_date = cleaned_data.get('package_end_date')
        
        logger.debug(f"[VoucherAdminForm] Date values - booking: {booking_date}, start: {package_start_date}, end: {package_end_date}")
        
        # 1. Validate package start date < package end date
        if package_start_date and package_end_date:
            if package_start_date >= package_end_date:
                error_msg = 'Package end date must be after the package start date.'
                logger.error(f"[VoucherAdminForm] Package date order validation failed: {error_msg}")
                raise ValidationError({
                    'package_end_date': error_msg
                })
            else:
                logger.debug(f"[VoucherAdminForm] Package date order validation passed")
        
        # 2. Validate booking date should be earlier than package start/end date
        if booking_date and package_start_date:
            booking_date_only = booking_date.date() if hasattr(booking_date, 'date') else booking_date
            if booking_date_only > package_start_date:
                error_msg = 'Booking date must be earlier than the package start date.'
                logger.error(f"[VoucherAdminForm] Booking vs start date validation failed: {error_msg}")
                raise ValidationError({
                    'booking_date': error_msg
                })
            else:
                logger.debug(f"[VoucherAdminForm] Booking vs start date validation passed")
        
        if booking_date and package_end_date:
            booking_date_only = booking_date.date() if hasattr(booking_date, 'date') else booking_date
            if booking_date_only >= package_end_date:
                error_msg = 'Booking date must be earlier than the package end date.'
                logger.error(f"[VoucherAdminForm] Booking vs end date validation failed: {error_msg}")
                raise ValidationError({
                    'booking_date': error_msg
                })
            else:
                logger.debug(f"[VoucherAdminForm] Booking vs end date validation passed")
        
        logger.debug(f"[VoucherAdminForm] All date validations passed")
        return cleaned_data
    
    def _validate_user_and_package(self, cleaned_data):
        """Extracted user and package validation logic"""
        logger.debug(f"[VoucherAdminForm] Starting user and package validation")
        
        user = cleaned_data.get('user')
        user_name = cleaned_data.get('user_name')
        user_email = cleaned_data.get('user_email')
        package = cleaned_data.get('package')
        package_name = cleaned_data.get('package_name')
        inclusions = cleaned_data.get('inclusions')
        total_amount = cleaned_data.get('total_amount')
        
        logger.debug(f"[VoucherAdminForm] User validation - user: {user}, name: {user_name}, email: {user_email}")
        logger.debug(f"[VoucherAdminForm] Package validation - package: {package}, name: {package_name}, inclusions: {len(inclusions) if inclusions else 0}, amount: {total_amount}")
        
        # User validation logic
        # If no user is selected from dropdown, require manual name and email
        if not user:
            logger.debug(f"[VoucherAdminForm] No user selected - validating manual fields")
            if not user_name:
                error_msg = 'User name is required when no user is selected from dropdown.'
                logger.error(f"[VoucherAdminForm] User name validation failed: {error_msg}")
                raise ValidationError({
                    'user_name': error_msg
                })
            if not user_email:
                error_msg = 'User email is required when no user is selected from dropdown.'
                logger.error(f"[VoucherAdminForm] User email validation failed: {error_msg}")
                raise ValidationError({
                    'user_email': error_msg
                })
            logger.debug(f"[VoucherAdminForm] Manual user fields validation passed")
        
        # If user is selected but manual name/email are not provided, auto-populate from user
        if user:
            logger.debug(f"[VoucherAdminForm] User selected - checking auto-population")
            # Auto-populate user_name if not manually entered
            if not user_name:
                if user.full_name:
                    cleaned_data['user_name'] = user.full_name
                    logger.debug(f"[VoucherAdminForm] Auto-populated user_name: {user.full_name}")
                else:
                    error_msg = 'Selected user has no name. Please enter the name manually.'
                    logger.error(f"[VoucherAdminForm] User name auto-population failed: {error_msg}")
                    raise ValidationError({
                        'user_name': error_msg
                    })
            
            # Auto-populate user_email if not manually entered
            if not user_email:
                if user.email:
                    cleaned_data['user_email'] = user.email
                    logger.debug(f"[VoucherAdminForm] Auto-populated user_email: {user.email}")
                else:
                    error_msg = 'Selected user has no email. Please enter the email manually.'
                    logger.error(f"[VoucherAdminForm] User email auto-population failed: {error_msg}")
                    raise ValidationError({
                        'user_email': error_msg
                    })
        
        # Package validation logic
        # If no package is selected from dropdown, require manual package details
        if not package:
            logger.debug(f"[VoucherAdminForm] No package selected - validating manual fields")
            if not package_name:
                error_msg = 'Package name is required when no package is selected from dropdown.'
                logger.error(f"[VoucherAdminForm] Package name validation failed: {error_msg}")
                raise ValidationError({
                    'package_name': error_msg
                })
            if not inclusions:
                error_msg = 'Package inclusions are required when no package is selected from dropdown.'
                logger.error(f"[VoucherAdminForm] Package inclusions validation failed: {error_msg}")
                raise ValidationError({
                    'inclusions': error_msg
                })
            if not total_amount:
                error_msg = 'Total amount is required when no package is selected from dropdown.'
                logger.error(f"[VoucherAdminForm] Total amount validation failed: {error_msg}")
                raise ValidationError({
                    'total_amount': error_msg
                })
            logger.debug(f"[VoucherAdminForm] Manual package fields validation passed")
        
        # If package is selected but manual fields are not provided, auto-populate from package
        if package:
            logger.debug(f"[VoucherAdminForm] Package selected - checking auto-population")
            # Auto-populate package_name if not manually entered
            if not package_name:
                if package.title:
                    cleaned_data['package_name'] = package.title
                    logger.debug(f"[VoucherAdminForm] Auto-populated package_name: {package.title}")
                else:
                    error_msg = 'Selected package has no title. Please enter the package name manually.'
                    logger.error(f"[VoucherAdminForm] Package name auto-population failed: {error_msg}")
                    raise ValidationError({
                        'package_name': error_msg
                    })
            
            # Auto-populate inclusions if not manually entered
            if not inclusions:
                if package.inclusions.exists():
                    # Get all inclusion values from related PackageInclusion objects
                    inclusion_values = list(package.inclusions.values_list('value', flat=True))
                    cleaned_data['inclusions'] = inclusion_values
                    logger.debug(f"[VoucherAdminForm] Auto-populated inclusions: {len(inclusion_values)} items")
                else:
                    error_msg = 'Selected package has no inclusions. Please enter the inclusions manually.'
                    logger.error(f"[VoucherAdminForm] Package inclusions auto-population failed: {error_msg}")
                    raise ValidationError({
                        'inclusions': error_msg
                    })
            
            # Auto-populate total_amount if not manually entered
            if not total_amount:
                if package.price:
                    # Multiply package price by number of guests
                    number_of_guests = cleaned_data.get('number_of_guests', 1)
                    calculated_amount = package.price * number_of_guests
                    cleaned_data['total_amount'] = calculated_amount
                    logger.info(f"[VoucherForm] Auto-calculated total_amount: {package.price} x {number_of_guests} = {calculated_amount}")
                else:
                    error_msg = 'Selected package has no price. Please enter the total amount manually.'
                    logger.error(f"[VoucherAdminForm] Total amount auto-population failed: {error_msg}")
                    raise ValidationError({
                        'total_amount': error_msg
                    })
        
        logger.debug(f"[VoucherAdminForm] User and package validation completed successfully")
        return cleaned_data


class RoomTypeWidget(forms.TextInput):
    """Custom widget that provides both dropdown suggestions and manual entry for room types"""
    
    def __init__(self, attrs=None):
        default_attrs = {
            'list': 'room_type_options',
            'placeholder': 'Select from dropdown or type your own',
            'autocomplete': 'off',
            'style': 'width: 100%;'
        }
        if attrs:
            default_attrs.update(attrs)
        super().__init__(default_attrs)
    
    def render(self, name, value, attrs=None, renderer=None):
        # Get the base input field
        html = super().render(name, value, attrs, renderer)
        
        # Add datalist with room type options
        from .choices import RoomTypeChoices
        datalist_options = []
        for choice_value, choice_label in RoomTypeChoices.choices:
            datalist_options.append(f'<option value="{choice_value}">{choice_label}</option>')
        
        # Use string concatenation to avoid f-string backslash issues
        newline = '\n'
        datalist_html = f'''
        <datalist id="room_type_options">
            {newline.join(datalist_options)}
        </datalist>
        '''
        
        return html + datalist_html


class DaywiseItineraryAdminForm(forms.ModelForm):
    """Custom form for DaywiseItinerary with enhanced room_type field"""
    
    class Meta:
        from .models import DaywiseItinerary
        model = DaywiseItinerary
        fields = '__all__'
        widgets = {
            'room_type': RoomTypeWidget(),
        }
    
    def __init__(self, *args, **kwargs):
        logger.debug(f"[DaywiseItineraryAdminForm] __init__ called")
        super().__init__(*args, **kwargs)
        if 'room_type' in self.fields:
            self.fields['room_type'].help_text = "Select from suggestions or type your own room type"
            
    def clean(self):
        """Override clean to add logging"""
        logger.debug(f"[DaywiseItineraryAdminForm] Starting clean() validation")
        cleaned_data = super().clean()
        day_number = cleaned_data.get('day_number', 'Unknown')
        date = cleaned_data.get('date', 'Unknown')
        logger.debug(f"[DaywiseItineraryAdminForm] Validation completed for day {day_number}, date {date}")
        return cleaned_data
