#!/usr/bin/env python3
"""
Correct TripJack Hotels Fetcher
Following the exact API flow as described:
1. Fetch hotels from static hotels API
2. Match locations with our destinations
3. Use search API for matched hotels
4. Use detail API for full data
5. Save to database with S3 images
"""

import os
import sys
import django
import requests
import time
import json
import argparse
import logging
import base64
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Optional, Any, Tuple
from django.conf import settings

def setup_django():
    """Setup Django environment"""
    project_root = Path(__file__).parent.absolute()
    sys.path.insert(0, str(project_root))
    
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'zuumm.settings')
    
    # Hardcode API credentials  
    os.environ['TRIPJACK_API_KEY'] = settings.TRIPJACK_API_KEY
    os.environ['TRIPJACK_API_URL'] = settings.TRIPJACK_API_URL
    
    django.setup()

# Setup Django first
setup_django()

# Import TripJack components after Django setup
from base.tripjack_hotels_fetcher.api_client import TripJackAPIClient
from base.tripjack_hotels_fetcher.logger import TripJackLogger

# TripJack API configuration
API_KEY = settings.TRIPJACK_API_KEY
BASE_URL = settings.TRIPJACK_API_URL

# Headers for API requests
HEADERS = {
    'Content-Type': 'application/json',
    'apikey': API_KEY,
    'Accept': 'application/json'
}

class CorrectTripJackFetcher:
    """
    Main TripJack Hotels Fetcher with correct API flow
    """
    
    def __init__(self):
        self.platform_destinations = []
        self.platform_destinations_dict = {}
        self.setup_logging()
        
        # Initialize TripJack components
        self.tripjack_logger = TripJackLogger()
        self.api_client = TripJackAPIClient(self.tripjack_logger)
        
    def setup_logging(self):
        """Setup comprehensive logging with timestamped log files"""
        # Create logs directory
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # Create timestamped log file for this session
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        session_log_file = log_dir / f"tripjack_session_{timestamp}.log"
        
        # Setup main logger
        self.logger = logging.getLogger('TripJackFetcher')
        self.logger.setLevel(logging.DEBUG)
        
        # Clear existing handlers
        if self.logger.hasHandlers():
            self.logger.handlers.clear()
        
        # Console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_format = logging.Formatter('%(message)s')
        console_handler.setFormatter(console_format)
        
        # File handler for detailed logs
        file_handler = logging.FileHandler(session_log_file)
        file_handler.setLevel(logging.DEBUG)
        file_format = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(file_format)
        
        self.logger.addHandler(console_handler)
        self.logger.addHandler(file_handler)
        
        self.logger.info(f"🗂️  Session log file: {session_log_file}")

    def load_platform_destinations(self):
        """Load destinations from our platform database"""
        try:
            from packages.models import Destination
            # Get destinations as (id, title) tuples with lowercase normalization
            platform_destinations_list = Destination.objects.filter(is_active=True).values_list("id", "title")
            
            self.platform_destinations = []
            self.platform_destinations_dict = {}  # For quick lookup
            
            for dest_id, dest_title in platform_destinations_list:
                normalized_title = dest_title.lower().strip()
                dest_data = {
                    'id': dest_id,
                    'title': normalized_title,
                    'original_title': dest_title
                }
                self.platform_destinations.append(dest_data)
                self.platform_destinations_dict[normalized_title] = dest_data
            
            self.logger.info(f"🗺️  Loaded {len(self.platform_destinations)} platform destinations")
            self.logger.debug(f"Platform destinations: {[d['original_title'] for d in self.platform_destinations]}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to load platform destinations: {e}")
            return False

    def generate_page_token(self, page_number: int) -> str:
        """
        Generate a page token for direct page access
        
        Args:
            page_number: Target page number (1-based)
            
        Returns:
            str: Base64 encoded token for the page, or None for page 1
        """
        if page_number <= 1:
            return None  # First page doesn't need a token
        
        # Calculate starting record for this page
        # Page 1: records 0-99, Page 2: records 100-199, etc.
        starting_record = (page_number - 1) * 100
        
        # Create token string in format "100 XXXX"
        token_string = f"100 {starting_record}"
        
        # Encode to base64
        token_b64 = base64.b64encode(token_string.encode()).decode()
        
        self.logger.debug(f"🎯 Generated token for page {page_number}: '{token_string}' -> '{token_b64}'")
        return token_b64

    def decode_page_token(self, token: str) -> tuple[int, int]:
        """
        Decode a page token to understand what page and record range it represents
        
        Args:
            token: Base64 encoded page token
            
        Returns:
            tuple: (page_number, starting_record) or (None, None) if invalid
        """
        try:
            if not token:
                return None, None
                
            # Decode base64
            decoded = base64.b64decode(token).decode('utf-8')
            
            # Parse "100 XXXX" format
            parts = decoded.split()
            if len(parts) != 2 or parts[0] != '100':
                self.logger.warning(f"⚠️ Unexpected token format: '{decoded}'")
                return None, None
            
            starting_record = int(parts[1])
            page_number = (starting_record // 100) + 1
            
            self.logger.debug(f"🔍 Decoded token '{token}': '{decoded}' -> Page {page_number} (records {starting_record}-{starting_record+99})")
            return page_number, starting_record
            
        except Exception as e:
            self.logger.error(f"❌ Error decoding token '{token}': {e}")
            return None, None

    def fetch_static_hotels(self, next_token: str = None) -> tuple[List[Dict], str]:
        """
        Step 1: Fetch hotels from TripJack static hotels API with pagination support
        
        Args:
            next_token: Token for next page (None for first page)
            
        Returns:
            tuple: (hotels_list, next_token_for_next_page)
        """
        self.logger.info("🔍 Step 1: Fetching static hotels from TripJack...")
        
        try:
            endpoint = f"{BASE_URL}/hms/v1/fetch-static-hotels"
            
            # Build payload based on whether we're fetching first page or subsequent pages
            if next_token:
                payload = {"next": next_token}
                # Decode token for debugging
                page_num, start_record = self.decode_page_token(next_token)
                if page_num:
                    self.logger.info(f"📄 Fetching page {page_num} with token: {next_token} (records {start_record}-{start_record+99})")
                else:
                    self.logger.info(f"📄 Fetching page with next token: {next_token}")
            else:
                payload = {}
                self.logger.info(f"📄 Fetching first page with empty payload")
            
            self.logger.debug(f"📡 Making request to: {endpoint}")
            self.logger.debug(f"📋 Payload: {payload}")
            
            response = requests.post(
                endpoint,
                headers=HEADERS,
                json=payload,
                timeout=60  # Increased timeout to 60 seconds
            )
            
            self.logger.debug(f"📊 Response status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                
                # Debug: Check the response structure
                self.logger.debug(f"📋 Response keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
                self.logger.debug(f"📋 Response type: {type(data)}")
                
                if isinstance(data, dict):
                    hotels = data.get('hotels', [])
                    if not hotels and 'data' in data:
                        hotels = data['data']
                    if not hotels and 'hotelOpInfos' in data:
                        hotels = data['hotelOpInfos']
                    if not hotels and isinstance(data, dict) and len(data.keys()) == 1:
                        # Sometimes the response is wrapped in a single key
                        first_key = list(data.keys())[0]
                        hotels = data[first_key]
                else:
                    hotels = data if isinstance(data, list) else []
                
                # Get next token for pagination
                next_page_token = data.get('next') if isinstance(data, dict) else None
                
                self.logger.info(f"✅ Fetched {len(hotels)} static hotels")
                if next_page_token:
                    self.logger.info(f"📄 Next page token: {next_page_token}")
                else:
                    self.logger.info("📄 No more pages available")
                
                return hotels, next_page_token
            else:
                self.logger.error(f"❌ API returned status {response.status_code}: {response.text}")
                return [], None
                
        except Exception as e:
            self.logger.error(f"❌ Error fetching static hotels: {e}")
            return [], None

    def match_hotel_with_destinations(self, hotel: Dict) -> Optional[Dict]:
        """
        Step 2: Check if hotel location matches any of our platform destinations
        Uses the new matching logic with normalized case comparison
        """
        try:
            # Extract location info from hotel address
            address = hotel.get('address', {})
            city_title = address.get('city', {}).get('name', '')
            state_title = address.get('state', {}).get('name', '')
            country_title = address.get('country', {}).get('name', '')
            
            # Always log hotel location info for debugging
            self.logger.info(f"   📍 Hotel Location: City='{city_title}' | State='{state_title}' | Country='{country_title}'")
            
            # Normalize titles to lowercase for comparison
            locations_to_check = [
                city_title.lower().strip() if city_title else '',
                state_title.lower().strip() if state_title else '',
                country_title.lower().strip() if country_title else ''
            ]
            
            # Remove empty strings
            locations_to_check = [loc for loc in locations_to_check if loc]
            
            # Check each location against platform destinations
            for location in locations_to_check:
                if location in self.platform_destinations_dict:
                    matched_destination = self.platform_destinations_dict[location]
                    self.logger.info(f"   ✅ Location match found: '{location}' matches destination '{matched_destination['title']}'")
                    self.logger.debug(f"   ✅ Matched destination details: ID={matched_destination['id']}, Title='{matched_destination['original_title']}'")
                    return matched_destination
            
            self.logger.info(f"   ❌ No destination match found for locations: {locations_to_check}")
            return None
            
        except Exception as e:
            self.logger.error(f"   ❌ Error matching hotel destinations: {e}")
            return None

    def search_hotel(self, hotel_id: str) -> Optional[Dict]:
        """Search for a hotel using its ID to get pricing and real hotel ID"""
        try:
            self.logger.info(f"   🔍 Searching for hotel ID: {hotel_id}")
            
            # Use the API client's search method which has retry logic
            search_data = self.api_client.search_hotels_by_ids([hotel_id])
            
            if search_data:
                # Extract hotel data according to user's specification
                his_data = search_data.get("searchResult", {}).get("his", None)
                if his_data and len(his_data) > 0:
                    actual_data = his_data[0]
                    hotel_real_id = actual_data.get("id")
                    
                    self.logger.info(f"   ✅ Search successful - Real hotel ID: {hotel_real_id}")
                    self.logger.debug(f"   📋 Actual data keys: {list(actual_data.keys()) if isinstance(actual_data, dict) else 'Not a dict'}")
                    
                    return {
                        'actual_data': actual_data,
                        'hotel_real_id': hotel_real_id,
                        'search_response': search_data
                    }
                else:
                    self.logger.warning(f"   ⚠️  No hotel data in search response")
                    self.logger.debug(f"   📄 Search response: {json.dumps(search_data, indent=2)}")
                    return None
            else:
                self.logger.error(f"   ❌ Search API failed after all retries")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ Error searching hotel: {e}")
            return None

    def get_hotel_details(self, hotel_real_id: str) -> Optional[Dict]:
        """
        Step 4: Get detailed hotel information using the real hotel ID
        """
        try:
            self.logger.info(f"   🔍 Fetching details for hotel real ID: {hotel_real_id}")
            
            # Use the API client's detail method which has retry logic
            detail_data = self.api_client.get_hotel_detail(hotel_real_id)
            
            if detail_data:
                self.logger.info(f"   ✅ Detail fetch successful for hotel real ID: {hotel_real_id}")
                self.logger.debug(f"   📋 Detail response keys: {list(detail_data.keys()) if isinstance(detail_data, dict) else 'Not a dict'}")
                return detail_data
            else:
                self.logger.error(f"   ❌ Detail API failed after all retries")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ Error fetching hotel details: {e}")
            return None

    def save_hotel_to_database(self, static_hotel: Dict, search_result: Dict, 
                             detail_data: Dict, destination: Dict) -> bool:
        """
        Step 5: Save hotel to database with S3 images using constants for image limits
        """
        try:
            from base.static import Constants
            from base.tripjack_hotels_fetcher.data_processor import TripJackDataProcessor
            from base.tripjack_hotels_fetcher.image_downloader import TripJackImageDownloader
            from packages.models import Destination
            
            # Get the destination object by ID (fixing destination FK issue)
            try:
                destination_obj = Destination.objects.get(id=destination['id'])
                self.logger.debug(f"   📍 Using destination: {destination_obj.title} (ID: {destination_obj.id})")
            except Destination.DoesNotExist:
                self.logger.error(f"   ❌ Destination with ID {destination['id']} not found in database")
                return False
            
            # Initialize processors with image limits from constants
            image_downloader = TripJackImageDownloader(
                max_hotel_images=Constants.TRIPJACK_MAX_HOTEL_IMAGES,
                max_room_images=Constants.TRIPJACK_MAX_ROOM_IMAGES
            )
            data_processor = TripJackDataProcessor()
            
            # Process and save hotel data with correct destination
            hotel_obj = data_processor.process_hotel_data(
                hotel_data=static_hotel,
                destination=destination_obj,  # Pass the actual destination object
                search_result=search_result,
                detail_data=detail_data
            )
            
            if hotel_obj:
                self.logger.info(f"   ✅ Saved hotel: {hotel_obj.name}")
                self.logger.debug(f"   📋 Hotel details: ID={hotel_obj.id}, Destination={hotel_obj.destination.title}")
                return True
            else:
                self.logger.warning(f"   ⚠️  Failed to save hotel to database")
                return False
                
        except Exception as e:
            self.logger.error(f"   ❌ Error saving hotel to database: {e}")
            import traceback
            self.logger.debug(f"   📄 Full traceback: {traceback.format_exc()}")
            return False

    def process_single_hotel(self, static_hotel: Dict, total_hotels_processed: int, 
                           page_number: int, index: int) -> bool:
        """
        Process a single hotel through the complete flow
        Returns True if hotel was successfully saved, False otherwise
        """
        hotel_name = static_hotel.get('name', 'Unknown Hotel')
        hotel_id = static_hotel.get('hotelId', '')
        
        self.logger.info(f"📍 Processing hotel {total_hotels_processed} (Page {page_number}, #{index}): {hotel_name}")
        
        # Step 2: Match hotel with destinations
        matched_destination = self.match_hotel_with_destinations(static_hotel)
        
        if not matched_destination:
            self.logger.debug(f"   ❌ No destination match found")
            self.logger.info(f"   ⏭️  No destination match - skipping")
            return False
        
        self.logger.info(f"   ✅ Matched with destination: {matched_destination['original_title']}")
        
        # Step 3: Search for hotel details
        search_result = self.search_hotel(static_hotel.get('hotelId'))
        
        if not search_result:
            self.logger.info(f"   ⏭️  Search failed - skipping")
            return False
        
        # Extract data from search result
        actual_data = search_result['actual_data']
        hotel_real_id = search_result['hotel_real_id']
        
        # Step 4: Get detailed information
        detail_data = self.get_hotel_details(hotel_real_id)
        
        if not detail_data:
            self.logger.info(f"   ⏭️  Detail fetch failed - skipping")
            return False
        
        # Step 5: Save to database
        if self.save_hotel_to_database(static_hotel, search_result, detail_data, matched_destination):
            self.logger.info(f"   ✅ Saved hotel: {hotel_name}")
            return True
        else:
            self.logger.info(f"   ⏭️  Database save failed - skipping")
            return False

    def run_complete_flow(self, max_hotels: int = 50, test_mode: bool = True,
                          skip_existing: bool = False, skip_count: int = 0,
                          resume_mode: bool = False, start_page: Optional[int] = None,
                          end_page: Optional[int] = None):
        """
        Run the complete TripJack hotel fetching flow with pagination support and resume functionality
        """
        self.logger.info("🚀 Starting Correct TripJack Hotels Fetcher")
        self.logger.info("="*60)
        
        if test_mode:
            self.logger.info(f"🧪 Running in TEST mode - max {max_hotels} hotels")
        else:
            self.logger.info("🔥 Running in PRODUCTION mode - all hotels")
        
        # Log page range if specified
        if start_page is not None and end_page is not None:
            if start_page == end_page:
                self.logger.info(f"📄 PAGE RANGE mode: Processing page {start_page} only")
            else:
                self.logger.info(f"📄 PAGE RANGE mode: Processing pages {start_page} to {end_page} (inclusive)")
        
        # Log resume options
        if skip_existing:
            self.logger.info("⏭️ SKIP EXISTING mode: Will skip hotels already in database")
        elif skip_count > 0:
            self.logger.info(f"⏭️ SKIP COUNT mode: Will skip first {skip_count} hotels")
        elif resume_mode:
            self.logger.info("🔄 RESUME mode: Will resume from last processed hotel")
            skip_count = self._get_resume_skip_count()
            self.logger.info(f"📊 Resume skip count determined: {skip_count} hotels")
        
        # Log initial API health status
        self.api_client.log_api_health_status()
        
        # Load platform destinations
        if not self.load_platform_destinations():
            self.logger.error("❌ Cannot proceed without platform destinations")
            return False
        
        # Initialize pagination variables
        next_token = None
        page_number = 1
        total_hotels_processed = 0
        total_hotels_saved = 0
        total_hotels_skipped = 0
        
        # If start_page is specified, jump directly to it using generated token
        if start_page is not None and start_page > 1:
            self.logger.info(f"🎯 Jumping directly to page {start_page} using generated token...")
            
            # Generate token for direct page access
            next_token = self.generate_page_token(start_page)
            page_number = start_page
            self.logger.info(f"✅ Direct jump to page {start_page}")
        
        # Main pagination loop
        while True:
            # Check if we've reached the end page
            if end_page is not None and page_number > end_page:
                self.logger.info(f"🏁 Reached end page {end_page}, stopping")
                break
            
            self.logger.info(f"📖 Processing Page {page_number}")
            self.logger.info("-" * 40)
            
            # Step 1: Fetch hotels for this page
            hotels_list, next_page_token = self.fetch_static_hotels(next_token)
            
            if not hotels_list:
                self.logger.warning(f"⚠️  No hotels found on page {page_number}, stopping")
                break
            
            self.logger.info(f"🔄 Starting hotel processing pipeline for page {page_number}...")
            
            # Process each hotel on this page
            page_hotels_saved = 0
            page_hotels_skipped = 0
            
            for index, static_hotel in enumerate(hotels_list, 1):
                total_hotels_processed += 1
                hotel_name = static_hotel.get('name', 'Unknown Hotel')
                hotel_id = static_hotel.get('hotelId', '')
                
                # Skip logic for resume functionality
                if skip_count > 0 and total_hotels_processed <= skip_count:
                    self.logger.debug(f"⏭️ Skipping hotel {total_hotels_processed} (skip count: {skip_count})")
                    total_hotels_skipped += 1
                    page_hotels_skipped += 1
                    continue
                
                # Skip existing hotels logic
                if skip_existing and self._hotel_exists_in_database(hotel_id):
                    self.logger.info(f"⏭️ Hotel {hotel_name} (ID: {hotel_id}) already exists, skipping")
                    total_hotels_skipped += 1
                    page_hotels_skipped += 1
                    continue
                
                # Process the hotel
                success = self.process_single_hotel(static_hotel, total_hotels_processed, page_number, index)
                
                if success:
                    total_hotels_saved += 1
                    page_hotels_saved += 1
                else:
                    # Hotel processing failed, but we continue
                    pass
                
                # Check if we've reached max hotels limit in test mode
                if test_mode and total_hotels_saved >= max_hotels:
                    self.logger.info(f"🎯 Reached max hotels limit ({max_hotels}) in test mode")
                    break
            
            # Log page summary
            self.logger.info(f"📋 Page {page_number} Summary: {len(hotels_list)} hotels fetched, {page_hotels_saved + (len(hotels_list) - page_hotels_saved - page_hotels_skipped)} processed, {page_hotels_saved} saved, {page_hotels_skipped} skipped")
            
            # Check if we've reached max hotels limit in test mode
            if test_mode and total_hotels_saved >= max_hotels:
                self.logger.info(f"🎯 Test mode complete - processed {max_hotels} hotels")
                break
            
            # Check if there's a next page
            if not next_page_token:
                self.logger.info("✅ No more pages available, processing complete")
                break
            
            # Prepare for next page
            next_token = next_page_token
            page_number += 1
            
            # Small delay between pages
            self.logger.info("⏳ Waiting before fetching next page...")
            time.sleep(2)
        
        # Final summary
        self.logger.info("=" * 60)
        self.logger.info(f"📊 Final Results:")
        if start_page is not None and end_page is not None:
            if start_page == end_page:
                self.logger.info(f"   📄 Page processed: {start_page}")
            else:
                self.logger.info(f"   📄 Pages processed: {start_page} to {page_number-1}")
        else:
            self.logger.info(f"   📖 Pages processed: {page_number}")
        self.logger.info(f"   🏨 Total hotels fetched: {total_hotels_processed}")
        self.logger.info(f"   ⏭️ Total hotels skipped: {total_hotels_skipped}")
        self.logger.info(f"   🔄 Total hotels processed: {total_hotels_processed - total_hotels_skipped}")
        self.logger.info(f"   ✅ Total hotels saved: {total_hotels_saved}")
        self.logger.info(f"   📈 Success rate: {(total_hotels_saved/(total_hotels_processed - total_hotels_skipped)*100):.1f}%" if total_hotels_processed > total_hotels_skipped else "   📈 Success rate: 0%")
        
        # Log final API health status
        self.api_client.log_api_health_status()
        
        return total_hotels_saved > 0

    def _get_resume_skip_count(self) -> int:
        """Determine how many hotels to skip based on existing hotels in database"""
        try:
            from dynamic_packages.models import Hotel
            
            # Count existing hotels from TripJack
            existing_count = Hotel.objects.filter(
                tripjack_static_id__isnull=False
            ).exclude(tripjack_static_id='').count()
            
            self.logger.info(f"📊 Found {existing_count} existing TripJack hotels in database")
            return existing_count
            
        except Exception as e:
            self.logger.error(f"❌ Error determining resume skip count: {e}")
            return 0
    
    def _hotel_exists_in_database(self, hotel_id: str) -> bool:
        """Check if hotel already exists in database"""
        try:
            from dynamic_packages.models import Hotel
            
            if not hotel_id:
                return False
                
            return Hotel.objects.filter(tripjack_static_id=hotel_id).exists()
            
        except Exception as e:
            self.logger.error(f"❌ Error checking hotel existence for {hotel_id}: {e}")
            return False

def main():
    """Main function with argument parsing"""
    try:
        # Parse command line arguments
        parser = argparse.ArgumentParser(description='TripJack Hotels Fetcher')
        parser.add_argument('--production', action='store_true', 
                           help='Run in production mode (fetch all hotels)')
        parser.add_argument('--max-hotels', type=int, default=10,
                           help='Maximum number of hotels to process in test mode')
        parser.add_argument('--skip-existing', action='store_true',
                           help='Skip hotels that already exist in database (recommended for resume)')
        parser.add_argument('--skip-count', type=int, default=0,
                           help='Skip first N hotels (alternative resume method)')
        parser.add_argument('--resume', action='store_true',
                           help='Resume from last processed hotel based on database')
        parser.add_argument('--page-range', type=str, default=None,
                           help='Process specific page range (e.g., "4-7" for pages 4 to 7, or "5" for page 5 only)')
        
        args = parser.parse_args()
        
        # Parse page range if provided
        start_page = None
        end_page = None
        if args.page_range:
            try:
                if '-' in args.page_range:
                    # Range format like "4-7"
                    start_str, end_str = args.page_range.split('-', 1)
                    start_page = int(start_str.strip())
                    end_page = int(end_str.strip())
                    
                    if start_page < 1 or end_page < 1 or start_page > end_page:
                        print("❌ Error: Invalid page range. Start and end pages must be >= 1 and start <= end")
                        return
                        
                    print(f"📄 Page range mode: Processing pages {start_page} to {end_page} (inclusive)")
                else:
                    # Single page format like "5"
                    start_page = int(args.page_range.strip())
                    end_page = start_page
                    
                    if start_page < 1:
                        print("❌ Error: Page number must be >= 1")
                        return
                        
                    print(f"📄 Single page mode: Processing page {start_page} only")
                    
            except ValueError:
                print("❌ Error: Invalid page range format. Use 'N' for single page or 'N-M' for range")
                return
        
        # Determine mode based on arguments
        if args.production:
            test_mode = False
            max_hotels = 100000  # Large number for production
            print("🔥 Production mode enabled via --production flag")
        else:
            test_mode = True
            max_hotels = args.max_hotels
            print(f"🧪 Test mode enabled - processing max {max_hotels} hotels")
        
        # Resume options
        skip_existing = args.skip_existing
        skip_count = args.skip_count
        resume_mode = args.resume
        
        if skip_existing:
            print("⏭️ Skip existing mode: Will skip hotels already in database")
        elif skip_count > 0:
            print(f"⏭️ Skip count mode: Will skip first {skip_count} hotels")
        elif resume_mode:
            print("🔄 Resume mode: Will resume from last processed hotel")
        
        setup_django()
        
        fetcher = CorrectTripJackFetcher()
        success = fetcher.run_complete_flow(
            max_hotels=max_hotels, 
            test_mode=test_mode,
            skip_existing=skip_existing,
            skip_count=skip_count,
            resume_mode=resume_mode,
            start_page=start_page,
            end_page=end_page
        )
        
        if success:
            print("✅ TripJack fetcher completed successfully!")
        else:
            print("⚠️  TripJack fetcher completed with issues")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 