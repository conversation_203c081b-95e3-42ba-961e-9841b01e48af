"""
Conversation and message models for the chat app.
"""
from django.db import models
from base.models import BaseModel
from accounts.models import User, Partner
from chat.choices import ConversationStatus, SenderType, ChannelType


class Conversation(BaseModel):
    """
    Django model for a conversation session.
    """
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="conversations"
    )
    partner = models.ForeignKey(
        Partner, on_delete=models.CASCADE, related_name="conversations"
    )
    channel = models.CharField(
        max_length=20,
        choices=ChannelType.choices,
        db_index=True,
        null=True,
        blank=True,
    )
    status = models.CharField(
        max_length=20,
        choices=ConversationStatus.choices,
        default=ConversationStatus.ACTIVE,
        db_index=True,
    )
    last_message_preview = models.TextField(null=True, blank=True)
    state_data = models.JSONField(null=True, blank=True)
    title = models.CharField(max_length=255, null=True, blank=True)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "Conversation"
        verbose_name_plural = "Conversations"
        indexes = [
            models.Index(fields=["external_id", "user"]),
            models.Index(fields=["user", "partner"]),
        ]

    def __str__(self):
        return f"Conversation {self.id} for {self.user.email}"


class Message(BaseModel):
    """
    Django model for a single message within a conversation.
    """
    conversation = models.ForeignKey(
        Conversation, on_delete=models.CASCADE, related_name="messages"
    )
    partner = models.ForeignKey(Partner, on_delete=models.CASCADE)
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    sender = models.CharField(
        max_length=20, choices=SenderType.choices, db_index=True
    )
    image_url = models.URLField(max_length=2048, null=True, blank=True)
    message_text = models.TextField()
    message_data = models.JSONField(null=True, blank=True)

    class Meta:
        ordering = ["created_at"]
        verbose_name = "Message"
        verbose_name_plural = "Messages"

    def __str__(self):
        return f"Message {self.id} in Conversation {self.conversation.id}"