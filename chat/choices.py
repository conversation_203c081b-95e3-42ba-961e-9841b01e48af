from django.db import models

class ConversationStatus(models.TextChoices):
    """Enum for the status of a conversation."""
    ACTIVE = "active", "Active"
    ENDED = "ended", "Ended"
    ARCHIVED = "archived", "Archived"
    DELETED = "deleted", "Deleted"

class SenderType(models.TextChoices):
    """Enum for the sender of a message."""
    USER = "user", "User"
    AGENT = "agent", "Agent"
    OPERATOR = "operator", "Operator"

class ChannelType(models.TextChoices):
    """Enum for the origination channel of a conversation."""
    WEB = "web", "Web"
    ANDROID = "android", "Android"
    IOS = "ios", "iOS"
