import requests
import uuid
import mimetypes
import logging
import re
from django.conf import settings
from django.core.files.base import ContentFile
from serpapi import GoogleSearch

logger = logging.getLogger(__name__)
api_key = settings.SERP_API_KEY

if not api_key:
    raise ValueError("Please set the SERP_API_KEY in your Django settings.")

def download_image(url):
    """
    Downloads an image from a URL, gives it a new unique name,
    and returns it as a ContentFile.
    """
    if not url:
        return None

    if url.startswith('//'):
        url = 'https:' + url

    try:
        response = requests.get(url, stream=True, timeout=30)
        response.raise_for_status()

        content_type = response.headers.get('content-type')
        extension = mimetypes.guess_extension(content_type) if content_type else '.jpg'
        if not extension or extension == '.jpe':
            extension = '.jpg'

        file_name = f"{uuid.uuid4()}{extension}"

        return ContentFile(response.content, name=file_name)

    except requests.exceptions.RequestException as e:
        logger.error(f"Error downloading image from {url}: {e}")
    return None

def get_images_from_link(data_id: str):
    """
    Performs a secondary API call specifically to fetch images for a place.
    """
    if not data_id:
        return []

    logger.info(f"--- Performing secondary search for images using data_id: {data_id} ---")

    image_params = {
        "engine": "google_maps_photos",
        "data_id": data_id,
        "api_key": api_key,
    }

    try:
        search = GoogleSearch(image_params)
        image_results = search.get_dict()

        if "photos" in image_results:
            return [photo.get("image") for photo in image_results["photos"]]
        else:
            return []
    except Exception as e:
        logger.error(f"An error occurred during the image fetch: {e}")
        return []


def get_place_details(query: str, location: str, max_images: int = 1):
    """
    Fetches details for a hotel or restaurant using SerpApi's Google Maps API.
    
    Args:
        query (str): The search query (e.g., hotel name)
        location (str): The location to search in
        max_images (int): Maximum number of images to return (default: 1)
    """
    params = {
        "engine": "google_maps",
        "q": query,
        "location": location,
        "api_key": api_key,
    }

    try:
        search = GoogleSearch(params)
        results = search.get_dict()

        place_data_block = None
        if "local_results" in results and results["local_results"]:
            place_data_block = results["local_results"][0]
        elif "place_results" in results and results["place_results"]:
            place_data_block = results["place_results"]

        if place_data_block:
            name = place_data_block.get("title")
            address = place_data_block.get("address")
            phone = place_data_block.get("phone")
            website = place_data_block.get("website")
            rating = place_data_block.get("rating")
            review_count = place_data_block.get("reviews")
            data_id = place_data_block.get("data_id")
            description = place_data_block.get("description")
            amenities = place_data_block.get("amenities") or place_data_block.get("hotel_amenities") or []

            all_image_urls = []

            if data_id:
                # High quality images from Google Maps
                all_image_urls = get_images_from_link(data_id)

            # Fallback if no images from photos link
            if not all_image_urls:
                thumbnail = place_data_block.get("thumbnail")
                if thumbnail:
                    all_image_urls.append(thumbnail)

                if "images" in place_data_block and isinstance(place_data_block.get("images"), list):
                    all_image_urls.extend([img.get("thumbnail") for img in place_data_block["images"] if img.get("thumbnail")])
                elif "photos" in place_data_block and isinstance(place_data_block.get("photos"), list):
                    all_image_urls.extend([photo.get("image") for photo in place_data_block["photos"] if photo.get("image")])

            # Return a unique list of URLs
            unique_urls = list(dict.fromkeys(filter(None, all_image_urls)))
            # Limit images based on max_images parameter
            unique_urls = unique_urls[:max_images]

            return {
                "name": name,
                "address": address,
                "phone": phone,
                "website": website,
                "rating": rating,
                "review_count": review_count,
                "image_urls": unique_urls,
                "description": description,
                "amenities": amenities,
            }
        else:
            logger.warning("No 'local_results' or 'place_results' found in the API response for query: %s", query)
            return None

    except Exception as e:
        logger.error(f"An error occurred while fetching place details for query '{query}': {e}")
        return None


def clean_queries(query: str) -> str:
    """
    Removes " or similar" or ", similar" or " similar" from the end of a string.
    """
    if ':' in query:
        query = query.split(':', 1)[1]

    # Split by comma first to get main entries
    pattern = r'(?:,|\s+or)?\s+similar\b\s*$'

    cleaned_query = re.sub(pattern, '', query, flags=re.IGNORECASE)

    return cleaned_query.strip()