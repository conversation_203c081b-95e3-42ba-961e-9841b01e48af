import logging
import tempfile
import os
from datetime import datetime
from django.conf import settings
from django.utils import timezone
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from packages.models import Package
from packages.utils.pdf_serializers import PackagePDFSerializer
from base.pdf_utils import PDFGenerator

logger = logging.getLogger(__name__)


class PackagePDFHelper:
    """
    Helper class for Package PDF generation functionality.
    Handles PDF creation, S3 upload, and data processing.
    """
    
    def __init__(self, package):
        """
        Initialize the PDF helper with package object
        
        Args:
            package: Package instance that is already validated for is_active and is_published
        """
        self.package = package
        self.errors = []
        self.pdf_data = None
        self.pdf_bytes = None
        
        logger.info(f"PackagePDFHelper initialized with package: {self.package.title}")
    
    def serialize_package_data(self) -> bool:
        """
        Serialize package data for PDF template using PackagePDFSerializer
        
        Returns:
            bool: True if serialization successful
        """
        try:
            serializer = PackagePDFSerializer(self.package)
            self.pdf_data = serializer.data
            
            # Add some additional computed fields for PDF display
            self._enhance_pdf_data()
            
            logger.info(f"Package data serialized successfully for PDF generation")
            return True
            
        except Exception as e:
            error_msg = f"Error serializing package data: {str(e)}"
            self.errors.append(error_msg)
            logger.error(error_msg)
            return False
    
    def _enhance_pdf_data(self):
        """
        Add additional computed fields specifically for PDF display
        """
        try:
            # Add current generation timestamp
            self.pdf_data['generated_at'] = timezone.now().strftime('%B %d, %Y at %I:%M %p')
            
            # Add partner branding info
            if self.package.partner:
                self.pdf_data['partner_name'] = self.package.partner.entity_name
                self.pdf_data['partner_logo'] = self.package.partner.logo.url if self.package.partner.logo else None
                self.pdf_data['partner_primary_color'] = self.package.partner.primary_theme
                self.pdf_data['partner_secondary_color'] = self.package.partner.secondary_theme
            
            # Add fallback text for missing fields
            self._add_fallback_data()
            
        except Exception as e:
            logger.warning(f"Error enhancing PDF data: {str(e)}")
            # Don't fail PDF generation for enhancement errors
    
    def _add_fallback_data(self):
        """
        Add fallback data for missing or empty fields to ensure PDF doesn't break
        """
        fallbacks = {
            'about_this_tour_cleaned': 'Package overview will be updated soon.',
            'rating_description': 'Customer rating details will be updated soon.',
            'destination_safety': 'Safety information will be updated soon.',
            'cultural_info': 'Cultural information will be updated soon.',
            'what_to_shop': 'Shopping recommendations will be updated soon.',
            'best_time_to_visit': 'Best time to visit information will be updated soon.',
        }
        
        for field, fallback in fallbacks.items():
            if not self.pdf_data.get(field):
                self.pdf_data[field] = fallback
        
        # Ensure arrays are never None
        array_fields = [
            'exclusions', 'hotels_processed', 'popular_activities_processed', 
            'popular_restaurants_processed', 'highlights', 'inclusions', 'addons'
        ]
        
        for field in array_fields:
            if not self.pdf_data.get(field):
                self.pdf_data[field] = []
    
    def process(self) -> bool:
        """
        Generate PDF bytes from template and data
        
        Returns:
            bool: True if PDF generation successful
        """
        try:
            if not self.pdf_data:
                raise ValueError("Package data not serialized. Call serialize_package_data() first.")
            
            template_name = 'package_description_pdf/package_pdf.html'
            context = {
                'package': self.pdf_data,
                'debug_mode': settings.DEBUG,
            }
            
            self.pdf_bytes = PDFGenerator.get_pdf_bytes(
                template_name=template_name,
                context=context
            )
            
            logger.info(f"PDF bytes generated successfully")
            return True
            
        except Exception as e:
            error_msg = f"Error generating PDF: {str(e)}"
            self.errors.append(error_msg)
            logger.error(error_msg)
            return False
    
    def generate_pdf(self) -> str:
        """
        Generate PDF file from template and data
        
        Returns:
            str: Path to generated PDF file
        """
        try:
            if not self.pdf_data:
                raise ValueError("Package data not serialized. Call serialize_package_data() first.")
            
            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            safe_title = "".join(c for c in self.package.title if c.isalnum() or c in (' ', '-', '_')).rstrip()
            safe_title = safe_title[:30]  # Limit length
            filename = f"package_{safe_title}_{timestamp}.pdf"
            
            # Generate PDF using PDFGenerator
            template_name = 'package_description_pdf/package_pdf.html'
            context = {
                'package': self.pdf_data,
                'debug_mode': settings.DEBUG,
            }
            
            pdf_path = PDFGenerator.render_to_pdf(
                template_name=template_name,
                context=context,
                file_name=filename
            )
            
            logger.info(f"PDF generated successfully: {pdf_path}")
            return pdf_path
            
        except Exception as e:
            error_msg = f"Error generating PDF: {str(e)}"
            self.errors.append(error_msg)
            logger.error(error_msg)
            raise
    
    def get_pdf_bytes(self) -> bytes:
        """
        Generate PDF and return as bytes for S3 upload
        
        Returns:
            bytes: PDF content as bytes
        """
        try:
            if not self.pdf_data:
                raise ValueError("Package data not serialized. Call serialize_package_data() first.")
            
            template_name = 'package_description_pdf/package_pdf.html'
            context = {
                'package': self.pdf_data,
                'debug_mode': settings.DEBUG,
            }
            
            pdf_bytes = PDFGenerator.get_pdf_bytes(
                template_name=template_name,
                context=context
            )
            
            logger.info(f"PDF bytes generated successfully")
            return pdf_bytes
            
        except Exception as e:
            error_msg = f"Error generating PDF bytes: {str(e)}"
            self.errors.append(error_msg)
            logger.error(error_msg)
            raise
    
    def _upload_pdf_to_s3(self) -> str:
        """
        Upload PDF bytes to S3 and return public URL
        
        Returns:
            str: S3 public URL of the uploaded PDF
        """
        try:
            if not self.pdf_bytes:
                raise ValueError("PDF bytes not generated. Call process() first.")
            
            # Generate unique filename for S3
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            safe_title = "".join(c for c in self.package.title if c.isalnum() or c in (' ', '-', '_')).rstrip()
            safe_title = safe_title[:30]  # Limit length
            filename = f"packages/pdf/{self.package.external_id}_{safe_title}_{timestamp}.pdf"
            
            # Create ContentFile from bytes
            pdf_file = ContentFile(self.pdf_bytes, name=filename)
            
            # Upload to S3 using default storage
            saved_path = default_storage.save(filename, pdf_file)
            
            # Get public URL
            pdf_url = default_storage.url(saved_path)
            
            logger.info(f"PDF uploaded to S3 successfully: {pdf_url}")
            return pdf_url
            
        except Exception as e:
            error_msg = f"Error uploading PDF to S3: {str(e)}"
            self.errors.append(error_msg)
            logger.error(error_msg)
            raise
    
    def process_full_pdf_generation(self) -> dict:
        """
        Complete PDF generation process: serialize -> process -> upload to S3
        
        Returns:
            dict: Result with success status and S3 URL
        """
        try:
            # Step 1: Serialize data
            if not self.serialize_package_data():
                return {
                    'success': False,
                    'error': 'Package data serialization failed',
                    'errors': self.errors,
                    'status_code': 500
                }
            
            # Step 2: Process PDF generation
            if not self.process():
                return {
                    'success': False,
                    'error': 'PDF generation failed',
                    'errors': self.errors,
                    'status_code': 500
                }
            
            # Step 3: Upload to S3
            s3_url = self._upload_pdf_to_s3()
            
            return {
                'success': True,
                'pdf_url': s3_url,
                'package_title': self.package.title,
                'file_size': len(self.pdf_bytes) if self.pdf_bytes else None,
                'generated_at': timezone.now().isoformat()
            }
            
        except Exception as e:
            error_msg = f"PDF generation process failed: {str(e)}"
            self.errors.append(error_msg)
            logger.error(error_msg)
            
            return {
                'success': False,
                'error': error_msg,
                'errors': self.errors,
                'status_code': 500
            }
    
    def generate_package_pdf_file(self) -> dict:
        """
        Generate PDF file for package (local file generation)
        
        Returns:
            dict: Result with success status and file path
        """
        try:
            # Serialize and generate
            if not self.serialize_package_data():
                return {
                    'success': False,
                    'error': 'Package data serialization failed',
                    'errors': self.errors
                }
            
            # Generate PDF file
            pdf_path = self.generate_pdf()
            
            return {
                'success': True,
                'file_path': pdf_path,
                'package_title': self.package.title
            }
            
        except Exception as e:
            error_msg = f"PDF file generation failed: {str(e)}"
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg
            }
    
    def get_errors(self) -> list:
        """Get all errors that occurred during processing"""
        return self.errors
    
    def has_errors(self) -> bool:
        """Check if any errors occurred during processing"""
        return len(self.errors) > 0 