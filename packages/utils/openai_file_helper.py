import re
import json
import requests
import logging
from typing import Dict, Any, Optional, List, Union
from django.conf import settings
from django.core.exceptions import ValidationError
from packages.utils.package_format_helpers import get_sample_package_format
from docx import Document
import io
from base.static import Constants

logger = logging.getLogger(__name__)


class OpenAIFileHelper:
    """
    Helper class for processing package files using OpenAI API with icon mapping support.
    Handles both JSON fixing and document conversion.
    """
    
    def __init__(self, file_content: Any, file_type: str = None):
        """
        Initialize the OpenAI file helper with direct icon class data access
        
        Args:
            file_content: Raw file content (dict for JSON, bytes for DOC/DOCX)
            file_type: Type of file ('json', 'doc', 'docx')
        """
        print(f"[DEBUG] OpenAIFileHelper.__init__ called with file_content type: {type(file_content)}, file_type: {file_type}")
        
        try:
            self.file_content = file_content
            print(f"[DEBUG] OpenAIFileHelper.__init__ set file_content successfully")
            
            self.file_type = file_type.lower() if file_type else 'json'
            print(f"[DEBUG] OpenAIFileHelper.__init__ set file_type: {self.file_type}")
            
            self.api_key = settings.OPENAI_API_KEY
            print(f"[DEBUG] OpenAIFileHelper.__init__ got api_key: {bool(self.api_key)}")
            
            self.api_url = settings.OPENAI_API_URL
            print(f"[DEBUG] OpenAIFileHelper.__init__ got api_url: {self.api_url}")
            
            self.errors = []
            print(f"[DEBUG] OpenAIFileHelper.__init__ initialized errors list")
            
            # Add the missing sample_format attribute
            print(f"[DEBUG] OpenAIFileHelper.__init__ calling get_sample_package_format()")
            self.sample_format = get_sample_package_format()
            print(f"[DEBUG] OpenAIFileHelper.__init__ got sample_format: {len(self.sample_format)} chars")
            
            # Icon class data - directly imported from Constants
            print(f"[DEBUG] OpenAIFileHelper.__init__ accessing Constants.ICON_CLASS")
            self.icon_classes = Constants.ICON_CLASS
            print(f"[DEBUG] OpenAIFileHelper.__init__ got icon_classes: {len(self.icon_classes)} items")
            
            print(f"[DEBUG] OpenAIFileHelper.__init__ setting icon_descriptions")
            self.icon_descriptions = {
                "island": "Islands, archipelago, tropical destinations, island hopping, remote locations",
                "beach": "Beaches, swimming, sunbathing, coastal activities, ocean views, seaside",
                "ship": "Boat rides, cruises, ferry transport, water transportation, sailing, maritime",
                "sunrise": "Scenic views, sunrises/sunsets, viewpoints, panoramic experiences, nature beauty, photography spots",
                "activity": "Adventure activities, sports, recreational activities, experiences, tours, excursions",
                "passenger": "Group activities, passenger limits, capacity-related features, people-focused services, group size",
                "breakfast": "Meals, dining, food services, culinary experiences, restaurants, catering, food included",
                "taxi": "Private transport, transfers, individual transportation, car services, private vehicle", 
                "bus": "Group transport, shared transfers, bus services, group transportation, coach travel",
                "hotel": "Accommodation, lodging, stays, hotels, resorts, housing, room bookings",
                "flight": "Air travel, flights, aviation services, airport transfers, domestic/international flights",
                "visa": "Documentation, visa services, permits, legal requirements, paperwork, travel documents",
                "insurance": "Protection services, safety, insurance coverage, security, travel protection",
                "forex_card": "Currency services, financial services, money exchange, payment options, foreign exchange"
            }
            print(f"[DEBUG] OpenAIFileHelper.__init__ set icon_descriptions: {len(self.icon_descriptions)} items")
            
            logger.info(f"OpenAIFileHelper initialized for {self.file_type} processing with icon mapping")
            print(f"[DEBUG] OpenAIFileHelper.__init__ completed successfully")
            
        except Exception as e:
            print(f"[DEBUG] OpenAIFileHelper.__init__ EXCEPTION: {type(e).__name__}: {str(e)}")
            raise

    def process(self) -> Dict[str, Any]:
        """
        Process the file content based on its type
        
        Returns:
            Processed JSON data with icon mappings
        """
        try:
            if self.file_type == 'json':
                return self._fix_json_data()
            elif self.file_type in ['doc', 'docx']:
                return self._convert_doc_to_json()
            else:
                error_msg = f"Unsupported file type: {self.file_type}"
                self.errors.append(error_msg)
                raise ValueError(error_msg)
                
        except Exception as e:
            error_msg = f"OpenAI processing failed: {str(e)}"
            self.errors.append(error_msg)
            raise Exception(error_msg)
    
    def _fix_json_data(self) -> Dict[str, Any]:
        """
        Fix JSON data using OpenAI GPT
        
        Returns:
            Fixed JSON data as dictionary
        """
        prompt = self._create_json_fix_prompt()
        
        try:
            response = self._call_openai_api(prompt)
            fixed_data = self._extract_json_from_response(response)
            
            # Validate that the response is proper JSON
            if not isinstance(fixed_data, dict):
                raise ValidationError("OpenAI returned invalid JSON structure")
            
            return fixed_data
            
        except Exception as e:
            logger.error(f"JSON fixing failed: {str(e)}")
            raise ValidationError(f"Failed to fix JSON data: {str(e)}")
    
    def _convert_doc_to_json(self) -> Dict[str, Any]:
        """
        Convert DOC/DOCX content to JSON using OpenAI GPT
        
        Returns:
            Converted JSON data as dictionary
        """
        # Convert binary content to text (simplified approach)
        try:
            # For DOC/DOCX files, we need to extract text first
            text_content = self._extract_text_from_doc()
            
            prompt = self._create_doc_conversion_prompt(text_content)
            
            response = self._call_openai_api(prompt)
            json_data = self._extract_json_from_response(response)
            
            return json_data
            
        except Exception as e:
            logger.error(f"Document conversion failed: {str(e)}")
            raise ValidationError(f"Failed to convert document to JSON: {str(e)}")
    
    def _extract_text_from_doc(self) -> str:
        """
        Extract text content from DOC/DOCX files using proper libraries
        """
        try:
            print(f"[DEBUG] _extract_text_from_doc called with file_type: {self.file_type}")
            print(f"[DEBUG] file_content type: {type(self.file_content)}")
            print(f"[DEBUG] file_content length: {len(self.file_content) if self.file_content else 0}")
            
            if self.file_type == 'docx':
                # Use python-docx library for proper DOCX extraction
                try:
                    from io import BytesIO
                    
                    # Ensure we have bytes content for BytesIO
                    if isinstance(self.file_content, str):
                        print(f"[DEBUG] file_content is string, converting to bytes using latin-1 encoding")
                        # If it's a string, it might be binary data encoded as string
                        # Try to convert back to bytes
                        try:
                            binary_content = self.file_content.encode('latin-1')
                            print(f"[DEBUG] Successfully converted string to bytes, length: {len(binary_content)}")
                        except UnicodeEncodeError:
                            print(f"[DEBUG] Failed to convert string to bytes with latin-1, trying utf-8")
                            binary_content = self.file_content.encode('utf-8')
                    elif isinstance(self.file_content, bytes):
                        print(f"[DEBUG] file_content is already bytes")
                        binary_content = self.file_content
                    else:
                        raise ValidationError(f"Invalid file_content type: {type(self.file_content)}. Expected bytes or str.")
                    
                    # Create a BytesIO object from the binary content
                    print(f"[DEBUG] Creating BytesIO stream from {len(binary_content)} bytes")
                    doc_stream = BytesIO(binary_content)
                    
                    print(f"[DEBUG] Loading DOCX document using python-docx")
                    document = Document(doc_stream)
                    
                    print(f"[DEBUG] Extracting text from {len(document.paragraphs)} paragraphs")
                    # Extract text from all paragraphs
                    full_text = '\n'.join([paragraph.text for paragraph in document.paragraphs])
                    
                    print(f"[DEBUG] Extracting text from {len(document.tables)} tables")
                    # Extract text from tables if any
                    for table in document.tables:
                        for row in table.rows:
                            for cell in row.cells:
                                full_text += '\n' + cell.text
                    
                    # Log successful extraction
                    print(f"[DEBUG] Successfully extracted {len(full_text)} characters from DOCX file")
                    logger.info(f"Successfully extracted {len(full_text)} characters from DOCX file")
                    
                    if not full_text.strip():
                        raise ValidationError("The DOCX file appears to be empty or contains no readable text.")
                    
                    return full_text
                    
                except ImportError:
                    raise ValidationError("python-docx library is not available. Please install it: pip install python-docx")
                except Exception as e:
                    print(f"[DEBUG] Exception in DOCX processing: {str(e)} (type: {type(e).__name__})")
                    raise ValidationError(f"Failed to extract text from DOCX file: {str(e)}")
            
            elif self.file_type == 'doc':
                # For .doc files, we need different libraries
                raise ValidationError(
                    "DOC file processing is not yet implemented. "
                    "Please convert your document to DOCX format or use JSON format."
                )
            else:
                # Fallback for other types - try to decode as text
                if isinstance(self.file_content, bytes):
                    return self.file_content.decode('utf-8', errors='ignore')
                return str(self.file_content)
                
        except Exception as e:
            print(f"[DEBUG] Exception in _extract_text_from_doc: {str(e)} (type: {type(e).__name__})")
            if "python-docx" in str(e) or "DOC file processing" in str(e):
                raise  # Re-raise specific library errors
            raise ValidationError(f"Failed to extract text from document: {str(e)}")
    
    def _create_json_fix_prompt(self) -> str:
        """
        Create comprehensive prompt for fixing JSON data with strict preservation of user-provided content and icon mapping
        """
        # Extract destination for dynamic prompt generation
        destination = "the destination"  # Default fallback
        if isinstance(self.file_content, dict):
            destination = self.file_content.get('destination', 'the destination')
            if destination and destination != 'the destination':
                destination = destination.title()  # Capitalize for readability
        
        # Create icon mapping instructions if icon data is available
        icon_mapping_instructions = ""
        if self.icon_classes:
            available_classes = self.icon_classes
            descriptions = self.icon_descriptions
            
            icon_mapping_instructions = f"""

=== ICON MAPPING REQUIREMENTS ===
For highlights, inclusions, and addons, you must change the format from simple arrays to array of objects with icon mapping:

AVAILABLE ICON CLASSES: {', '.join(available_classes)}

ICON CLASS DESCRIPTIONS:
{chr(10).join([f"- {cls}: {desc}" for cls, desc in descriptions.items()])}

**REQUIRED FORMAT CHANGE:**
From: "highlights": ["Dune bashing and sandboarding", "Skydiving over Palm Jumeirah"]
To: "highlights": [
    {{"value": "Dune bashing and sandboarding", "icon_class": "activity"}},
    {{"value": "Skydiving over Palm Jumeirah", "icon_class": "activity"}}
]

From: "inclusions": ["Breakfast", "Airport transfers", "Hotel accommodation"]
To: "inclusions": [
    {{"value": "Breakfast", "icon_class": "breakfast"}},
    {{"value": "Airport transfers", "icon_class": "taxi"}},
    {{"value": "Hotel accommodation", "icon_class": "hotel"}}
]

From: "addons": ["Flight", "Visa", "Insurance"]
To: "addons": [
    {{"value": "Flight", "icon_class": "flight"}},
    {{"value": "Visa", "icon_class": "visa"}},
    {{"value": "Insurance", "icon_class": "insurance"}}
]

**ICON SELECTION RULES:**
- Choose the MOST APPROPRIATE icon class based on the content
- If unsure between options, choose the most specific one
- For transportation, use: taxi (private), bus (group), flight (air travel), ship (water)
- For activities, use: activity (general), beach (coastal), island (islands), sunrise (scenic)
- For services, use: hotel (accommodation), breakfast (meals), visa (documents), insurance (protection)
- For unclear items, default to "activity"

CRITICAL: Keep the "value" field EXACTLY as provided by user - NEVER modify the text content, only add appropriate icon_class.
"""
        
        # Create the base prompt without f-string to avoid format specifier issues
        sample_format_str = str(self.sample_format)
        file_content_str = json.dumps(self.file_content, indent=2)
        
        prompt = f"""
You are an AI travel package data processor. I will provide you with a JSON object that may have errors, missing fields, or incorrect formatting. Your job is to fix and enhance the data to match our required format.

⚠️ CRITICAL: When user provides simple add-ons like "Flight, Visa, Insurance", keep them EXACTLY as provided - DO NOT expand to detailed descriptions like "Flight booking assistance, Visa processing support, Travel insurance"

⚠️ ESPECIALLY IMPORTANT: If user writes "upgrades" keep it as "upgrades" - DO NOT expand to "Hotel upgrades, Airport lounge, Private guide"

⚠️ ABSOLUTELY CRITICAL: If user provides EMPTY ARRAYS [] for any BASIC FIELDS, keep them as EMPTY ARRAYS - DO NOT generate content for empty arrays

REQUIRED JSON FORMAT (with field categorization):
{sample_format_str}

INPUT JSON TO FIX:
{file_content_str}

CRITICAL RULE: PRESERVE ALL USER-PROVIDED CONTENT INCLUDING EMPTY ARRAYS FOR BASIC FIELDS""" + icon_mapping_instructions + """

=== USER-PROVIDED FIELDS (NEVER CHANGE THESE - ONLY FIX STRUCTURE) ===
These fields should be preserved exactly as the user provided them. Only fix field names/structure, not content:

⚠️ IF USER PROVIDED EMPTY ARRAY [] FOR ANY OF THESE BASIC FIELDS, KEEP AS EMPTY ARRAY - DO NOT GENERATE CONTENT

**BASIC FIELDS - PRESERVE CONTENT:**
- title: Keep exact title provided by user
- package_no: Keep exact package number provided by user (DO NOT add destination prefixes like 'DXB')
- destination: Keep exact destination name, only convert to lowercase for consistency
- category: Keep exact categories provided, only convert to lowercase and ensure array format
- activities: Keep exact activities provided, only convert to lowercase and ensure array format  
- owner: Keep exact owner name provided by user
- type: Keep as provided ("Fixed" or "Variable")
- duration: Keep exact duration text provided by user
- price_per_person: ⚠️ CRITICAL - Keep EXACTLY as provided by user (e.g., if user enters "$49000", keep it as "$49000" - DO NOT change to "49000" or any other format)
- visa_type: Keep exact visa types provided, ensure array format
- about_this_tour: Keep exact content provided by user
- highlights: ⚠️ If empty [], keep as []. If has content, keep exact highlights provided, convert to icon-mapped object format
- inclusions: ⚠️ If empty [], keep as []. If has content, keep exact inclusions provided, convert to icon-mapped object format
- exclusions: ⚠️ If empty [], keep as []. If has content, keep exact exclusions provided, ensure array format
- itinerary: CRITICAL - Convert to HTML format with proper structure for rich text display
- hotels: Keep exact hotels provided, ensure array format
- popular_activities: Keep exact activities provided by user, ensure array format
- addons: Keep exact add ons provided, convert to icon-mapped object format - DO NOT expand or enhance simple terms like "Flight, Visa, Insurance, upgrades" into detailed descriptions. If user says "upgrades" keep it as "upgrades", do NOT expand to "Hotel upgrades, Airport lounge, Private guide"

**ITINERARY HTML FORMAT REQUIREMENT:**
Convert itinerary from JSON array format to proper HTML content with day-wise structure:

CONVERT FROM: [{"day": "Day 1", "title": "Arrival", "description": "Arrive and check-in"}]
TO: "<h3>Day 1 - Arrival</h3><ul><li>Arrive and check-in</li></ul><h3>Day 2 - City Tour</h3><ul><li>Visit landmarks</li><li>Lunch at local restaurant</li></ul>"

**HTML STRUCTURE RULES:**
- Use <h3> tags for day headers: "<h3>Day X - Title</h3>"
- Use <ul><li> tags for activities/descriptions in bullet points
- Break down descriptions into logical bullet points
- Maintain chronological day order
- Keep all original content, just restructure as HTML

**ONLY STRUCTURE FIXES ALLOWED:**
- Convert to proper array format if needed
- Fix field name spelling (e.g., "included" → "inclusions")
- Map legacy field names (e.g., "Optional" → "addons", "Add Ons" → "addons", "add_ons" → "addons")
- Convert itinerary from JSON array to HTML content
- Convert highlights, inclusions, addons to icon-mapped object format

=== AUTO-CALCULATED FIELDS (Compute from user data) ===
Calculate these ONLY from the user-provided basic fields:

- duration_in_nights: Extract from user's duration field
- duration_in_days: Extract from user's duration field  
- price: Extract numeric value from user's price_per_person (but keep original price_per_person unchanged)
- currency: Extract currency code from user's price_per_person (but keep original price_per_person unchanged)
- best_time_to_visit_months: Extract from user's best_time_to_visit (if provided)
- important_notes: Generate based on user's destination and visa_type

=== LLM SOURCED FIELDS (Generate ONLY if missing or empty) ===
Generate these fields ONLY if they are missing, empty, or null in the input. If user provided them, keep exactly as provided:

⚠️ CRITICAL: Check both "addons" and "add_ons" keys in input - if either exists with content, use that data and DO NOT generate new addons

⚠️ MOST IMPORTANT FOR best_time_to_visit: The destination is "{destination}". Generate a comprehensive best time to visit description specifically for {destination} that mentions the optimal months/seasons and weather conditions. This field MUST be generated if it's empty or missing.

⚠️ ABSOLUTELY REQUIRED FIELDS (Must always generate if missing/empty):
- rating_description: REQUIRED - Generate a brief 2-line description explaining why this package deserves its rating based on the highlights, inclusions, itinerary quality, and destination appeal. Keep it concise and engaging, focusing on key value propositions that justify the rating. THIS FIELD IS MANDATORY.
- important_notes: REQUIRED - Generate important travel notes array based on destination and visa requirements. THIS FIELD IS MANDATORY.

- best_time_to_visit: Generate only if missing/empty - MUST generate detailed travel timing advice for {destination}
- rating: Generate only if missing/empty/0
- currency_conversion_rate: Generate only if missing/empty/0
- destination_safety: Generate only if missing/empty
- popular_restaurants: Generate only if missing/empty
- what_to_shop: Generate only if missing/empty  
- what_to_pack: Generate only if missing/empty
- cultural_info: Generate only if missing/empty

**LLM GENERATION RULES:**
- Base ALL generated content on the user's specific destination: {destination}
- Use current, accurate information for {destination}
- Ensure currency_conversion_rate reflects real-world rates
- Make ratings realistic (3.5-4.8 range)
- For rating_description field: Generate a brief 2-line description explaining why this package deserves its rating based on the highlights, inclusions, itinerary quality, and destination appeal. Keep it concise and engaging, focusing on key value propositions that justify the rating.
- For what_to_pack field: Generate a well-organized packing list in HTML format for {destination} trip. Group items into 3-5 logical categories with descriptive titles. Use <h3> for category headings and <ul><li> for items. Each category should have 3-6 items max. Make categories practical and intuitive like: Clothing, Health & Safety, Essentials, Electronics, etc. Avoid too many small categories or putting everything in one category.

=== SYSTEM FIELDS ===
- is_published: Keep exactly as provided by user (preserve user's choice)
- is_active: Keep exactly as provided by user (preserve user's choice)

CRITICAL REQUIREMENTS:
1. NEVER change user-provided content - only fix structure and field names
2. NEVER add prefixes to package_no (keep "003" as "003", not "DXB003")
3. Only generate LLM fields if they are truly missing/empty in input
4. Convert destination, category, activities to lowercase for consistency
5. Ensure proper JSON array/object structure
6. CONVERT ITINERARY TO HTML FORMAT - this is critical for display
7. PRESERVE SIMPLE ADD-ONS TERMS: If user writes "Flight, Visa, Insurance" keep it as ["Flight", "Visa", "Insurance"] - DO NOT expand to detailed descriptions like "Flight booking assistance". ESPECIALLY: If user writes "upgrades" keep it as "upgrades" - DO NOT expand to "Hotel upgrades, Airport lounge, Private guide"
8. Convert highlights, inclusions, addons to proper icon-mapped object format
9. ⚠️ MOST IMPORTANT: Keep price_per_person field EXACTLY as user provided it - if they entered "$49000", return exactly "$49000", not "49000" or any modification
10. ⚠️ GENERATE best_time_to_visit field if empty/missing with detailed information for {destination}

Return ONLY the complete, fixed JSON object with user content preserved, missing fields generated, itinerary as HTML, and icon mappings added. No explanations.
"""
        
        return prompt
    
    def _create_doc_conversion_prompt(self, text_content: str) -> str:
        """
        Create comprehensive prompt for converting document content to JSON with preservation of user content and icon mapping
        """
        # Extract destination for dynamic prompt generation from text content
        destination = "the destination"  # Default fallback
        # Try to extract destination from text content
        if "destination" in text_content.lower():
            # Simple extraction - could be enhanced
            lines = text_content.split('\n')
            for line in lines:
                if 'destination' in line.lower() and ':' in line:
                    dest_part = line.split(':')[-1].strip()
                    if dest_part and len(dest_part) < 50:  # Reasonable destination name length
                        destination = dest_part.title()
                        break
        
        # Create icon mapping instructions if icon data is available
        icon_mapping_instructions = ""
        if self.icon_classes:
            available_classes = self.icon_classes
            descriptions = self.icon_descriptions
            
            icon_mapping_instructions = f"""

=== ICON MAPPING REQUIREMENTS ===
For highlights, inclusions, and addons, you must format as array of objects with icon mapping:

AVAILABLE ICON CLASSES: {', '.join(available_classes)}

ICON CLASS DESCRIPTIONS:
{chr(10).join([f"- {cls}: {desc}" for cls, desc in descriptions.items()])}

**REQUIRED FORMAT:**
"highlights": [
    {{"value": "Extracted highlight text", "icon_class": "appropriate_icon"}},
    {{"value": "Another highlight", "icon_class": "appropriate_icon"}}
]

"inclusions": [
    {{"value": "Extracted inclusion text", "icon_class": "appropriate_icon"}},
    {{"value": "Another inclusion", "icon_class": "appropriate_icon"}}
]

"addons": [
    {{"value": "Extracted addon text", "icon_class": "appropriate_icon"}},
    {{"value": "Another addon", "icon_class": "appropriate_icon"}}
]

**ICON SELECTION RULES:**
- Choose the MOST APPROPRIATE icon class based on the content
- If unsure between options, choose the most specific one
- For transportation, use: taxi (private), bus (group), flight (air travel), ship (water)
- For activities, use: activity (general), beach (coastal), island (islands), sunrise (scenic)
- For services, use: hotel (accommodation), breakfast (meals), visa (documents), insurance (protection)
- For unclear items, default to "activity"

CRITICAL: Keep the "value" field EXACTLY as extracted from document - NEVER modify the text content, only add appropriate icon_class.
"""
        
        # Create the base prompt without f-string to avoid format specifier issues
        sample_format_str = str(self.sample_format)
        
        prompt = f"""
You are an AI travel package document processor. Convert the provided document content into a complete JSON structure with strict preservation of extracted user content.

⚠️ CRITICAL: When user provides simple add-ons like "Flight, Visa, Insurance", keep them EXACTLY as provided - DO NOT expand to detailed descriptions like "Flight booking assistance, Visa processing support, Travel insurance"

⚠️ ESPECIALLY IMPORTANT: If user writes "upgrades" keep it as "upgrades" - DO NOT expand to "Hotel upgrades, Airport lounge, Private guide"

⚠️ ABSOLUTELY CRITICAL: If user provides EMPTY ARRAYS [] for any BASIC FIELDS, keep them as EMPTY ARRAYS - DO NOT generate content for empty arrays

REQUIRED JSON FORMAT (with field categorization):
{sample_format_str}

DOCUMENT CONTENT TO CONVERT:
{text_content}

CRITICAL RULE: PRESERVE ALL EXTRACTED CONTENT EXACTLY AS FOUND IN DOCUMENT{icon_mapping_instructions}

=== USER-PROVIDED FIELDS (Extract and preserve exactly) ===
Extract these from the document content and preserve exactly as found:

**BASIC FIELDS - EXTRACT AND PRESERVE:**
- title: Extract exact title from document
- package_no: Extract exact package number (DO NOT add destination prefixes)
- destination: Extract exact destination name, only convert to lowercase
- category: Extract categories and convert to lowercase, ensure array format
- activities: Extract activities and convert to lowercase, ensure array format
- owner: Extract exact owner/operator information from document
- type: Extract package type ("Fixed" or "Variable")
- duration: IMPORTANT - Extract duration and fix format to "4N & 5D" style, correct spelling mistakes (e.g., "4Nught 6 day" → "4N & 6D", "(4 Nights & 5 Days)" → "4N & 5D", "4 nights and 5 days" → "4N & 5D")
- price_per_person: ⚠️ CRITICAL - Extract EXACTLY as found in document (e.g., if document shows "$49000", keep it as "$49000" - DO NOT change to "49000" or any other format)
- visa_type: Extract visa information, ensure array format
- about_this_tour: Extract exact description from document
- highlights: Extract exact highlights, convert to icon-mapped object format
- inclusions: Extract exact inclusions, convert to icon-mapped object format
- exclusions: Extract exact exclusions, ensure array format
- itinerary: Extract exact itinerary content, structure as html content
- hotels: Extract exact hotel information, ensure array format
- popular_activities: Extract exact activities mentioned by user, ensure array format
- addons: Extract exact add ons provided, convert to icon-mapped object format - DO NOT expand or enhance simple terms like "Flight, Visa, Insurance, upgrades" into detailed descriptions. If user says "upgrades" keep it as "upgrades", do NOT expand to "Hotel upgrades, Airport lounge, Private guide"

**ITINERARY HTML FORMAT REQUIREMENT:**
Extract itinerary information and convert to proper HTML content with day-wise structure:

CONVERT TO: "<h3>Day 1 - Arrival</h3><ul><li>Arrive and check-in</li><li>Hotel transfer included</li></ul><h3>Day 2 - City Tour</h3><ul><li>Visit landmarks</li><li>Lunch at local restaurant</li></ul>"

**HTML STRUCTURE RULES:**
- Use <h3> tags for day headers: "<h3>Day X - Title</h3>"
- Use <ul><li> tags for activities/descriptions in bullet points
- Break down descriptions into logical bullet points
- Maintain chronological day order
- Extract all itinerary content from document and structure as HTML

**STRUCTURE CONVERSION ONLY:**
- Convert text lists to proper JSON arrays
- Structure itinerary as HTML content with day-wise format and bullet points
- Fix field name variations (e.g., "included" → "inclusions")
- Map legacy field names (e.g., "Optional" → "addons", "Add Ons" → "addons", "add_ons" → "addons")
- Convert highlights, inclusions, addons to icon-mapped object format

=== AUTO-CALCULATED FIELDS (Compute from extracted data) ===
Calculate these ONLY from the extracted basic fields:

- duration_in_nights: Calculate from extracted duration
- duration_in_days: Calculate from extracted duration
- price: Extract numeric value from extracted price_per_person (but keep original price_per_person unchanged)
- currency: Extract currency code from extracted price_per_person (but keep original price_per_person unchanged)
- best_time_to_visit_months: Extract from document's best time info (if present)
- important_notes: Generate based on extracted destination and visa info

=== LLM SOURCED FIELDS (Generate ONLY if not found in document) ===
Generate these fields ONLY if they are NOT found in the document content:

⚠️ CRITICAL: Check both "addons" and "add_ons" keys in input - if either exists with content, use that data and DO NOT generate new addons

⚠️ MOST IMPORTANT FOR best_time_to_visit: The destination is "{destination}". Generate a comprehensive best time to visit description specifically for {destination} that mentions the optimal months/seasons and weather conditions. This field MUST be generated if not found in the document.

⚠️ ABSOLUTELY REQUIRED FIELDS (Must always generate if not found in document):
- rating_description: REQUIRED - Generate a brief 2-line description explaining why this package deserves its rating based on the highlights, inclusions, itinerary quality, and destination appeal. Keep it concise and engaging, focusing on key value propositions that justify the rating. THIS FIELD IS MANDATORY.
- important_notes: REQUIRED - Generate important travel notes array based on destination and visa requirements. THIS FIELD IS MANDATORY.

- best_time_to_visit: Generate only if not mentioned in document - MUST generate detailed travel timing advice for {destination}
- rating: Generate only if not mentioned in document
- rating_description: Generate only if not mentioned in document
- currency_conversion_rate: Generate only if not mentioned in document
- destination_safety: Generate only if not mentioned in document
- popular_restaurants: Generate only if not mentioned in document
- what_to_shop: Generate only if not mentioned in document
- what_to_pack: Generate only if not mentioned in document
- cultural_info: Generate only if not mentioned in document

**LLM GENERATION RULES:**
- Base ALL generated content on the extracted destination: {destination}
- Use current, accurate information for {destination}
- Ensure currency_conversion_rate reflects real-world rates
- Make ratings realistic (3.5-4.8 range)
- For rating_description field: Generate a brief 2-line description explaining why this package deserves its rating based on the highlights, inclusions, itinerary quality, and destination appeal. Keep it concise and engaging, focusing on key value propositions that justify the rating.
- For what_to_pack field: Generate a well-organized packing list in HTML format for {destination} trip. Group items into 3-5 logical categories with descriptive titles. Use <h3> for category headings and <ul><li> for items. Each category should have 3-6 items max. Make categories practical and intuitive like: Clothing, Health & Safety, Essentials, Electronics, etc. Avoid too many small categories or putting everything in one category.

=== SYSTEM FIELDS ===
- is_published: Keep exactly as provided by user (preserve user's choice)
- is_active: Keep exactly as provided by user (preserve user's choice)

CRITICAL REQUIREMENTS:
1. NEVER modify extracted content - preserve exactly as found in document
2. NEVER add prefixes to package_no (keep "003" as "003", not "DXB003")
3. Only generate LLM fields if they are truly not mentioned in document
4. Convert destination, category, activities to lowercase for consistency
5. Ensure proper JSON array/object structure
6. Extract everything available from document before generating anything
7. PRESERVE SIMPLE ADD-ONS TERMS: If user writes "Flight, Visa, Insurance" keep it as ["Flight", "Visa", "Insurance"] - DO NOT expand to detailed descriptions like "Flight booking assistance". ESPECIALLY: If user writes "upgrades" keep it as "upgrades" - DO NOT expand to "Hotel upgrades, Airport lounge, Private guide"
8. Convert highlights, inclusions, addons to proper icon-mapped object format
9. ⚠️ MOST IMPORTANT: Keep price_per_person field EXACTLY as found in document - if document shows "$49000", return exactly "$49000", not "49000" or any modification
10. ⚠️ GENERATE best_time_to_visit field if not found in document with detailed information for {destination}

Return ONLY the complete JSON object with extracted content preserved, missing fields generated, and icon mappings added. No explanations or additional text.
"""
        
        return prompt
    
    def _call_openai_api(self, prompt: str) -> str:
        """
        Make API call to OpenAI responses endpoint
        
        Args:
            prompt: The prompt to send to OpenAI
            
        Returns:
            Response text from OpenAI
        """
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
        
        payload = {
            'model': 'gpt-4.1-nano',  # OpenAI GPT-4.1-nano model
            'input': prompt,  # Use input field instead of messages for responses endpoint
            'temperature': 0.5  # More consistent responses (user requested 0.5)
        }
        
        try:
            response = requests.post(
                self.api_url,
                headers=headers,
                json=payload,
                timeout=120  # 2 minute timeout for large responses
            )
            
            response.raise_for_status()
            
            response_data = response.json()
            
            # Handle OpenAI responses endpoint structure based on the actual API response format
            # The correct path is: response_data.get("output", [])[0].get("content", [])[0].get("text")
            content = None
            
            try:
                # Try the correct nested structure first
                output_array = response_data.get("output", [])
                if output_array and len(output_array) > 0:
                    content_array = output_array[0].get("content", [])
                    if content_array and len(content_array) > 0:
                        content = content_array[0].get("text")
                        if content:
                            logger.info("Successfully extracted content using nested structure")
                
                # Fallback options if the main structure doesn't work
                if not content:
                    # Try direct response field
                    if 'response' in response_data:
                        content = response_data['response']
                        logger.info("Extracted content using 'response' field")
                    # Try direct output field (if it's a string)
                    elif 'output' in response_data and isinstance(response_data['output'], str):
                        content = response_data['output']
                        logger.info("Extracted content using direct 'output' field")
                    # Try choices structure (fallback for chat completions format)
                    elif 'choices' in response_data and response_data['choices']:
                        choice = response_data['choices'][0]
                        if 'message' in choice and 'content' in choice['message']:
                            content = choice['message']['content']
                            logger.info("Extracted content using 'choices' structure")
                        elif 'text' in choice:
                            content = choice['text']
                            logger.info("Extracted content using 'choices.text' structure")
                
                # If still no content found, log the structure and raise error
                if not content:
                    logger.error(f"Could not extract content from OpenAI response structure: {list(response_data.keys())}")
                    logger.error(f"Full response data: {response_data}")
                    raise ValidationError(f"OpenAI API returned unexpected response structure. Available keys: {list(response_data.keys())}")
                    
            except (KeyError, IndexError, TypeError) as e:
                logger.error(f"Error parsing OpenAI response structure: {str(e)}")
                logger.error(f"Response data: {response_data}")
                raise ValidationError(f"Failed to parse OpenAI response structure: {str(e)}")
            
            # Ensure content is a string before calling strip()
            if isinstance(content, list):
                # If content is a list, join it or take the first element
                if content:
                    content = str(content[0]) if len(content) == 1 else ' '.join(str(item) for item in content)
                else:
                    content = ""
            elif content is None:
                content = ""
            else:
                content = str(content)
            
            return content.strip()
            
        except requests.exceptions.RequestException as e:
            logger.error(f"OpenAI API request failed: {str(e)}")
            raise ValidationError(f"OpenAI API request failed: {str(e)}")
        except KeyError as e:
            logger.error(f"Invalid OpenAI API response format: {str(e)}")
            raise ValidationError(f"Invalid OpenAI API response format: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error in OpenAI API call: {str(e)}")
            raise ValidationError(f"OpenAI API call failed: {str(e)}")
    
    def _extract_json_from_response(self, response_text: str) -> Dict[str, Any]:
        """
        Extract and parse JSON from OpenAI response with robust error handling
        
        Args:
            response_text: Raw response text from OpenAI
            
        Returns:
            Parsed JSON data as dictionary
        """
        try:
            # First, try to parse the entire response as JSON
            try:
                return json.loads(response_text)
            except json.JSONDecodeError:
                pass
            
            # Look for JSON within markdown code blocks (```json ... ```)
            if '```json' in response_text:
                start = response_text.find('```json') + 7
                end = response_text.find('```', start)
                if end != -1:
                    json_text = response_text[start:end].strip()
                    # Try to parse the extracted JSON
                    try:
                        return json.loads(json_text)
                    except json.JSONDecodeError:
                        # Apply robust parsing if extraction fails
                        return self._robust_json_parse(json_text)
            
            # Look for JSON within curly braces
            if '{' in response_text and '}' in response_text:
                start = response_text.find('{')
                end = response_text.rfind('}') + 1
                json_text = response_text[start:end]
                
                # Try to parse the extracted JSON
                try:
                    return json.loads(json_text)
                except json.JSONDecodeError:
                    # Apply robust parsing if extraction fails
                    return self._robust_json_parse(json_text)
            
            # If all else fails, raise an error
            raise ValidationError("No valid JSON found in OpenAI response")
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON from OpenAI response: {str(e)}")
            logger.error(f"Response text: {response_text}")
            # Try robust parsing as last resort
            try:
                return self._robust_json_parse(response_text)
            except Exception:
                raise ValidationError(f"OpenAI returned invalid JSON: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error extracting JSON: {str(e)}")
            logger.error(f"Response text: {response_text}")
            raise ValidationError(f"Failed to extract JSON from response: {str(e)}")

    def _robust_json_parse(self, json_text: str) -> Dict[str, Any]:
        """
        Robust JSON parser that can handle malformed JSON from OpenAI
        
        Args:
            json_text: Raw JSON text that might be malformed
            
        Returns:
            Parsed JSON data as dictionary
        """
        import re
        
        try:
            # Remove any non-printable characters and normalize whitespace
            cleaned_text = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', json_text)
            cleaned_text = re.sub(r'\s+', ' ', cleaned_text).strip()
            
            # Find the main JSON object boundaries
            start_idx = cleaned_text.find('{')
            if start_idx == -1:
                raise ValidationError("No JSON object found in response")
            
            # Find the matching closing brace for the main object
            brace_count = 0
            end_idx = -1
            
            for i in range(start_idx, len(cleaned_text)):
                char = cleaned_text[i]
                if char == '{':
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                    if brace_count == 0:
                        end_idx = i + 1
                        break
            
            if end_idx == -1:
                raise ValidationError("Malformed JSON: No matching closing brace found")
            
            # Extract the main JSON object
            main_json = cleaned_text[start_idx:end_idx]
            
            # Try to parse the extracted main JSON object
            try:
                return json.loads(main_json)
            except json.JSONDecodeError as e:
                # If still failing, try to fix common JSON issues
                return self._fix_common_json_issues(main_json)
                
        except Exception as e:
            logger.error(f"Robust JSON parsing failed: {str(e)}")
            logger.error(f"Input text: {json_text}")
            raise ValidationError(f"Unable to parse malformed JSON: {str(e)}")

    def _fix_common_json_issues(self, json_text: str) -> Dict[str, Any]:
        """
        Fix common JSON formatting issues that OpenAI might introduce
        
        Args:
            json_text: JSON text with potential formatting issues
            
        Returns:
            Parsed JSON data as dictionary
        """
        import re
        
        try:
            # Remove trailing commas before closing braces/brackets
            fixed_text = re.sub(r',(\s*[}\]])', r'\1', json_text)
            
            # Remove any trailing comma at the very end before final brace
            fixed_text = re.sub(r',(\s*}$)', r'\1', fixed_text)
            
            # Fix any potential unicode issues or escape sequences
            fixed_text = fixed_text.encode('utf-8').decode('utf-8')
            
            # Try parsing the fixed text
            try:
                return json.loads(fixed_text)
            except json.JSONDecodeError:
                # If still failing, try a more aggressive approach
                return self._aggressive_json_fix(fixed_text)
                
        except Exception as e:
            logger.error(f"Common JSON fixes failed: {str(e)}")
            raise ValidationError(f"Unable to fix JSON formatting issues: {str(e)}")

    def _aggressive_json_fix(self, json_text: str) -> Dict[str, Any]:
        """
        Aggressive JSON fixing for severely malformed JSON
        
        Args:
            json_text: Severely malformed JSON text
            
        Returns:
            Parsed JSON data as dictionary
        """
        import ast
        
        try:
            # Try to evaluate as Python literal (sometimes works for JSON-like strings)
            try:
                result = ast.literal_eval(json_text)
                if isinstance(result, dict):
                    return result
            except (ValueError, SyntaxError):
                pass
            
            # Last resort: Manual parsing for key fields
            logger.warning("Attempting manual JSON parsing as last resort")
            
            # Extract key-value pairs using regex
            result = {}
            
            # Find string fields with quotes
            string_pattern = r'"([^"]+)":\s*"([^"]*)"'
            for match in re.finditer(string_pattern, json_text):
                key, value = match.groups()
                result[key] = value
            
            # Find array fields
            array_pattern = r'"([^"]+)":\s*\[([^\]]*)\]'
            for match in re.finditer(array_pattern, json_text):
                key, array_content = match.groups()
                # Simple array parsing - split by comma and clean quotes
                items = [item.strip().strip('"') for item in array_content.split(',') if item.strip()]
                result[key] = items
            
            # Find numeric fields
            number_pattern = r'"([^"]+)":\s*(\d+(?:\.\d+)?)'
            for match in re.finditer(number_pattern, json_text):
                key, value = match.groups()
                try:
                    result[key] = float(value) if '.' in value else int(value)
                except ValueError:
                    result[key] = value
            
            # Find boolean fields
            bool_pattern = r'"([^"]+)":\s*(true|false)'
            for match in re.finditer(bool_pattern, json_text):
                key, value = match.groups()
                result[key] = value.lower() == 'true'
            
            if result:
                logger.warning(f"Manual JSON parsing extracted {len(result)} fields")
                return result
            else:
                raise ValidationError("Unable to extract any valid data from malformed JSON")
                
        except Exception as e:
            logger.error(f"Aggressive JSON fixing failed: {str(e)}")
            raise ValidationError(f"Complete JSON parsing failure: {str(e)}")
    
    def get_errors(self) -> list:
        """Get any errors that occurred during processing"""
        return self.errors
    
    def has_errors(self) -> bool:
        """Check if any errors occurred"""
        return len(self.errors) > 0 