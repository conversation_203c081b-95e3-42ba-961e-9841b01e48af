from django.core.exceptions import ValidationError
from django.db import transaction
import json

# Sample package JSON format for reference
SAMPLE_PACKAGE_FORMAT = {
    # ========== BASIC FIELDS (Always present in files/forms) ==========
    "title": "Extreme Dubai Adventure Package",
    "package_no": "DXB003",
    "destination": "dubai",  # Always lowercase for consistency
    "category": ["adventure", "family", "honeymoon"],  # Always lowercase for consistency
    "activities": ["desert safari", "skydiving", "city tour"],  # Always lowercase for consistency
    "owner": "ZUUMM",
    "type": "Fixed",  # Fixed or Variable
    "duration": "4N & 5D",
    "price_per_person": "₹39,999",  # User input format (AI extracts price and currency)
    "visa_type": ["E-Visa", "Visa on Arrival (with valid USA visa)"],
    "about_this_tour": "Unleash your adventurous spirit with the \"Extreme UAE 4N/5D Tour Package,\" a 4-night, 5-day journey through the UAE's most exhilarating experiences. From the towering dunes of Dubai's desert to the urban thrills of its skyscrapers and the cultural wonders of Abu Dhabi, this package is tailored for thrill-seekers.",
    "highlights": [
        "Dune bashing and sandboarding in Dubai's desert with a BBQ dinner",
        "Skydiving over Palm Jumeirah with panoramic views of Dubai",
        "Riding the XLine, the world's longest urban zip-line in Dubai Marina",
        "High-speed thrills at Ferrari World Abu Dhabi",
        "Visit to Burj Khalifa's 124th-floor observation deck",
        "Desert camp experience with camel rides and cultural performances"
    ],
    "inclusions": [
        "4 nights' accommodation in 3-star adventure-friendly hotels",
        "Daily breakfast at the hotel",
        "One adventure-themed BBQ dinner in the desert",
        "All transfers and sightseeing in a private air-conditioned vehicle",
        "English-speaking adventure guide for key activities",
        "Entry fees and equipment for included activities",
        "Bottled water and energy snacks during adventure days",
        "All taxes and service charges"
    ],
    "exclusions": [
        "International airfare to/from UAE",
        "Lunch and additional meals not specified",
        "Personal expenses (souvenirs, tips, etc.)",
        "Travel insurance",
        "UAE visa fees",
        "Optional activities (e.g., hot air balloon ride, jet skiing)",
        "Anything not explicitly mentioned in inclusions"
    ],
    "itinerary": "<h6>Day 1: Arrival in Dubai – Desert Thrills</h6><p>Arrive at Dubai International Airport (DXB). Private transfer to your hotel. Afternoon desert safari with dune bashing, camel ride, BBQ dinner, and entertainment. Overnight in Dubai.</p><h6>Day 2: Dubai – Skydiving & Urban Adventure</h6><p>Breakfast at the hotel. Morning skydiving over Palm Jumeirah (included)—soar at 13,000 feet with stunning views of Dubai's skyline and coastline. Afternoon XLine Dubai Marina (included)—zip across the world's longest urban zip-line at 80 km/h, 170 meters above ground. Evening free to explore Dubai Marina or relax at the hotel. Overnight stay in Dubai.</p><h6>Day 3: Dubai – Burj Khalifa & City Highlights</h6><p>Breakfast at the hotel. Morning visit to Burj Khalifa—ascend to the 124th-floor observation deck for breathtaking city views (included). Afternoon city tour: Drive past Jumeirah Mosque, Atlantis The Palm, and Dubai Frame; stop at Dubai Mall for the Fountain Show. Evening at leisure—option for extreme add-ons like indoor skiing at Ski Dubai (at extra cost). Overnight stay in Dubai.</p><h6>Day 4: Abu Dhabi – Ferrari World & Cultural Wonders</h6><p>Breakfast and check out from Dubai hotel. Transfer to Abu Dhabi (1.5 hours). Visit Ferrari World—experience the world's fastest roller coaster and other high-speed attractions (included). Afternoon visit to Sheikh Zayed Grand Mosque—marvel at the stunning architecture and peaceful ambiance. Check in to Abu Dhabi hotel. Overnight stay in Abu Dhabi.</p><h6>Day 5: Departure</h6><p>Breakfast at the hotel. Morning at leisure for last-minute shopping or relaxation. Check out and transfer to Abu Dhabi or Dubai Airport for departure. Tour ends with unforgettable memories of extreme UAE adventures.</p>",
    "hotels": [
        "Dubai (3 nights): Rove Downtown (3-star)—central location, modern vibe near adventure hubs",
        "Abu Dhabi (1 night): Aloft Abu Dhabi (3-star)—close to Yas Island, adventure-ready"
    ],
    "addons": [
        "Flight",
        "Visa", 
        "Insurance",
        "Hotel upgrades",
        "Airport lounge",
        "Private guide"
    ],
    "popular_activities": [
        "Desert Safari with BBQ dinner",
        "Burj Khalifa observation deck visit",
        "Dubai Mall and Fountain Show",
        "Dhow cruise dinner",
        "Dubai Marina walk",
        "Jumeirah Beach activities",
        "Gold and Spice Souk shopping"
    ],
    
    # ========== LLM SOURCED FIELDS (May be missing - AI generates if needed) ==========
    "best_time_to_visit": "October to March (winter season)",  # LLM sourced - best months to visit
    "rating": 4.5,  # AI-generated rating based on content quality and destination appeal
    "rating_description": "This exceptional adventure package earns its high rating through its perfect blend of extreme activities like skydiving and dune bashing, luxury accommodations, and comprehensive inclusions. The expertly crafted itinerary offers unmatched thrills while ensuring comfort and safety for an unforgettable UAE experience.",  # AI-generated description explaining the rating
    "currency": "INR",  # AI-extracted from price_per_person
    "price": 39999.00,  # AI-extracted numeric value from price_per_person
    "currency_conversion_rate": "1 USD = 83 INR (as of 2024)",  # LLM sourced - current conversion rate
    "destination_safety": "The UAE is one of the safest destinations globally, with low crime rates, strong law enforcement, and excellent tourist infrastructure. Women travelers are safe, though modest dressing is recommended.",
    "popular_restaurants": [
        "Al Fanar Restaurant & Café (Traditional Emirati cuisine)",
        "Ravi Restaurant (Authentic Pakistani food)",
        "Pierchic (Fine dining seafood)",
        "Zuma (Contemporary Japanese)",
        "Din Tai Fung (Taiwanese cuisine)",
        "Arabian Tea House (Local flavors)",
        "Atmosphere (Burj Khalifa dining)"
    ],
    "what_to_shop": "Gold jewelry from Gold Souk, Arabic perfumes (oud), pashmina shawls, spices from Spice Souk, dates, traditional lanterns, designer brands from malls, electronics during sales, camel milk chocolate, Arabic coffee sets",
    "what_to_pack": "<h6>Clothing</h6><ul><li>Light cotton clothing for day</li><li>Jacket for air-conditioned spaces and evenings</li><li>Swimwear</li><li>Modest attire for mosque visits</li></ul><h6>Health & Safety</h6><ul><li>Sunscreen (SPF 50+)</li><li>Sunglasses</li><li>Hat</li><li>Travel insurance documents</li></ul><h6>Essentials</h6><ul><li>Comfortable walking shoes</li><li>Power adapter (Type G)</li><li>Camera</li></ul>",
    "cultural_info": "The UAE blends Islamic traditions with modern life—dress modestly in public areas, avoid public displays of affection, respect local customs during Ramadan, and enjoy the warm Emirati hospitality. Friday is the holy day when many offices are closed.",
    
    # ========== AUTO-CALCULATED FIELDS (AI computes from basic fields) ==========
    "duration_in_nights": 4,  # Extracted from duration field
    "duration_in_days": 5,    # Extracted from duration field
    "best_time_to_visit_months": ["November", "December", "January", "February", "March"],  # Extracted from best_time_to_visit
    "important_notes": [  # Generated based on destination and content
        "Passport must be valid for at least 6 months",
        "UAE visa required for most nationalities",
        "Dress modestly when visiting religious sites",
        "Alcohol consumption only in licensed venues",
        "Tipping is customary (10-15% in restaurants)",
        "Friday is a holy day, some attractions may have different timings",
        "Stay hydrated and use sunscreen",
        "Respect local customs and traditions"
    ],
    
    # ========== SYSTEM FIELDS ==========
    "is_published": False,
    "is_active": True
}

def validate_json_structure(json_data):
    """
    Validate the structure of the JSON data
    
    Args:
        json_data: The parsed JSON data
        
    Raises:
        ValidationError: If the JSON structure is invalid with all errors collected
    """
    errors = []
    
    # Required fields validation
    required_fields = [
        'package_no', 'title', 'destination', 'type', 'duration', 
        'price_per_person', 'about_this_tour', 'highlights', 'addons'
    ]
    
    for field in required_fields:
        if field not in json_data:
            errors.append(f"Missing required field: {field}")
    
    # Array field validation
    array_fields = ['highlights', 'included', 'excluded', 'visa_type', 
                   'hotels', 'popular_restaurants', 'popular_activities']
    
    for field in array_fields:
        if field in json_data and not isinstance(json_data.get(field), list):
            errors.append(f"Field '{field}' must be an array/list")
    
    # Itinerary validation
    if 'itinerary' in json_data:
        if not isinstance(json_data['itinerary'], list):
            errors.append("Itinerary must be an array/list")
        else:
            for i, day in enumerate(json_data['itinerary']):
                if not isinstance(day, dict):
                    errors.append(f"Itinerary day {i+1} must be an object/dictionary")
                else:
                    for req_key in ['day', 'title', 'description']:
                        if req_key not in day:
                            errors.append(f"Itinerary day {i+1} missing required field: {req_key}")
    
    # Category validation
    if 'category' in json_data and not isinstance(json_data['category'], list):
        errors.append("Category must be an array/list")
    
    # Raise all validation errors at once
    if errors:
        raise ValidationError(errors)
    
    # Process and clean data
    # Handle some field name mapping
    if 'included' in json_data:
        json_data['inclusions'] = json_data.pop('included')
    
    if 'excluded' in json_data:
        json_data['exclusions'] = json_data.pop('excluded')
    
    if 'cultural_information' in json_data:
        json_data['cultural_info'] = json_data.pop('cultural_information')
    
    # Extract duration values
    if 'duration' in json_data:
        try:
            # Try to parse duration format like "4N & 5D"
            duration_str = json_data['duration']
            nights = int(''.join(filter(str.isdigit, duration_str.split('&')[0])))
            days = int(''.join(filter(str.isdigit, duration_str.split('&')[1])))
            
            json_data['duration_in_nights'] = nights
            json_data['duration_in_days'] = days
        except Exception:
            errors.append("Invalid duration format. Expected format like '4N & 5D'")
    
    # Clean price format
    if 'price_per_person' in json_data:
        try:
            # Keep the original price_per_person string as user input
            price_str = str(json_data['price_per_person'])
            
            # Extract currency and numeric value
            # Remove currency symbols and letters to get numeric value
            clean_price = ''.join(c for c in price_str if c.isdigit() or c == '.')
            if clean_price:
                price_value = float(clean_price)
                json_data['price'] = price_value
            
            # Extract currency information
            if '₹' in price_str or 'INR' in price_str.upper():
                json_data['currency'] = 'INR'
            elif '$' in price_str or 'USD' in price_str.upper():
                json_data['currency'] = 'USD'
            elif '€' in price_str or 'EUR' in price_str.upper():
                json_data['currency'] = 'EUR'
            else:
                json_data['currency'] = 'INR'  # Default
                
        except Exception:
            errors.append("Invalid price format")
    
    # Raise any processing errors
    if errors:
        raise ValidationError(errors)
    
    return json_data


@transaction.atomic
def create_package_from_json(uploader_instance, json_data):
    """
    Create a Package from the validated JSON data within a transaction
    
    Args:
        uploader_instance: The PackageUploader instance
        json_data: The validated JSON data
        
    Returns:
        The created Package instance
    """
    from packages.models import Package, Category, Destination, Activity, PackageCategory, PackageActivity
    
    # Delete any existing packages associated with this uploader
    uploader_instance.packages.all().delete()
    
    # Handle destination - get or create
    destination_name = json_data.get('destination', '').strip().lower()
    if destination_name:
        destination, _ = Destination.objects.get_or_create(
            title__iexact=destination_name,
            defaults={'title': destination_name}
        )
    else:
        # Create a default destination if none provided
        destination, _ = Destination.objects.get_or_create(
            title='unknown destination',
            defaults={'title': 'unknown destination'}
        )
    
    # Create the package
    package = Package(
        partner=uploader_instance.partner,
        package_uploaded=uploader_instance,
        title=json_data.get('title'),
        package_no=json_data.get('package_no'),
        destination=destination,  # Use the Destination object, not string
        owner=json_data.get('owner', ''),
        type=json_data.get('type'),
        duration=json_data.get('duration'),
        duration_in_nights=json_data.get('duration_in_nights', 0),
        duration_in_days=json_data.get('duration_in_days', 0),
        currency=json_data.get('currency', 'INR'),
        price=json_data.get('price', 0),
        price_per_person=str(json_data.get('price_per_person', '')),
        visa_type=json_data.get('visa_type', []),
        best_time_to_visit_months=json_data.get('best_time_to_visit_months', []),
        best_time_to_visit=json_data.get('best_time_to_visit', ''),
        rating=json_data.get('rating', 0),
        rating_description=json_data.get('rating_description', ''),
        currency_conversion_rate=json_data.get('currency_conversion_rate', 1.0),
        destination_safety=json_data.get('destination_safety', ''),
        about_this_tour=json_data.get('about_this_tour', ''),
        highlights=json_data.get('highlights', []),
        inclusions=json_data.get('inclusions', []),
        exclusions=json_data.get('exclusions', []),
        itinerary=json_data.get('itinerary', []),
        hotels=json_data.get('hotels', []),
        popular_restaurants=json_data.get('popular_restaurants', []),
        popular_activities=json_data.get('popular_activities', []),
        cultural_info=json_data.get('cultural_info', ''),
        what_to_shop=json_data.get('what_to_shop', ''),
        what_to_pack=json_data.get('what_to_pack', ''),
        important_notes=json_data.get('important_notes', []),
        addons=json_data.get('addons', []),
        is_published=False,  # Default to unpublished
        is_active=True
    )
    
    package.save()
    
    # Add categories using the through model
    if 'category' in json_data and isinstance(json_data['category'], list):
        for cat_name in json_data['category']:
            # Get or create category with correct field name
            category, _ = Category.objects.get_or_create(
                title__iexact=cat_name.strip().lower(),
                defaults={'title': cat_name.strip().lower()}
            )
            # Create the relationship through the through model
            PackageCategory.objects.get_or_create(
                package=package,
                category=category
            )
    
    # Handle activities if they exist in JSON data
    if 'activities' in json_data and isinstance(json_data['activities'], list):
        for activity_name in json_data['activities']:
            # Get or create activity
            activity, _ = Activity.objects.get_or_create(
                title__iexact=activity_name.strip().lower(),
                defaults={'title': activity_name.strip().lower()}
            )
            # Create the relationship through the through model
            PackageActivity.objects.get_or_create(
                package=package,
                activity=activity
            )
    
    return package


def get_sample_package_format():
    """
    Returns a formatted JSON sample for display in the admin panel with proper Unicode handling
    
    Returns:
        str: Pretty-printed JSON sample with proper Unicode encoding
    """
    return json.dumps(SAMPLE_PACKAGE_FORMAT, indent=2, ensure_ascii=False)

def get_sample_doc_format():
    """
    Returns a formatted DOC sample for display in the admin panel
    
    Returns:
        str: Sample DOC format text
    """
    return """=== BASIC FIELDS (Always include these in your document) ===

Package Name: Extreme Dubai Adventure Package
Destination: Dubai
Package No: DXB003
Category: Adventure, Family, Honeymoon
Type: Fixed
Duration: 4N & 5D
Price per person: ₹39,999
Visa Type: E Visa, Visa On Arrival (Only with Valid USA Visa)
Owner: ZUUMM

About This Tour
Unleash your adventurous spirit with the "Extreme UAE 4N/5D Tour Package," a 4-night, 5-day journey through the UAE's most exhilarating experiences. From the towering dunes of Dubai's desert to the urban thrills of its skyscrapers and the cultural wonders of Abu Dhabi, this package is tailored for thrill-seekers. Expect heart-pounding activities like dune bashing, skydiving over Palm Jumeirah, and the world's longest urban zip-line, paired with visits to iconic landmarks like Burj Khalifa and Ferrari World. Perfect for adrenaline junkies and explorers, this tour delivers extreme fun with premium comfort and seamless logistics.

Highlights
• Dune bashing and sandboarding in Dubai's desert with a BBQ dinner
• Skydiving over Palm Jumeirah with panoramic views of Dubai
• Riding the XLine, the world's longest urban zip-line in Dubai Marina
• High-speed thrills at Ferrari World Abu Dhabi
• Visit to Burj Khalifa's 124th-floor observation deck
• Desert camp experience with camel rides and cultural performances
• Exploration of Sheikh Zayed Grand Mosque in Abu Dhabi

Inclusions
• 4 nights' accommodation in 3-star adventure-friendly hotels
• Daily breakfast at the hotel
• One adventure-themed BBQ dinner in the desert
• All transfers and sightseeing in a private air-conditioned vehicle
• English-speaking adventure guide for key activities
• Entry fees and equipment for included activities
• Bottled water and energy snacks during adventure days
• All taxes and service charges

Exclusions
• International airfare to/from UAE
• Lunch and additional meals not specified
• Personal expenses (souvenirs, tips, etc.)
• Travel insurance
• UAE visa fees
• Optional activities (e.g., hot air balloon ride, jet skiing)
• Anything not explicitly mentioned in inclusions

Itinerary
Day 1: Arrival in Dubai – Desert Thrills
Arrive at Dubai International Airport (DXB). Private transfer to your hotel. Afternoon desert safari with dune bashing, camel ride, BBQ dinner, and entertainment. Overnight in Dubai.

Day 2: Dubai – Skydiving & Urban Adventure  
Breakfast at the hotel. Morning skydiving over Palm Jumeirah (included)—soar at 13,000 feet with stunning views of Dubai's skyline and coastline. Afternoon XLine Dubai Marina (included)—zip across the world's longest urban zip-line at 80 km/h, 170 meters above ground. Evening free to explore Dubai Marina or relax at the hotel. Overnight stay in Dubai.

Day 3: Dubai – Burj Khalifa & City Highlights
Breakfast at the hotel. Morning visit to Burj Khalifa—ascend to the 124th-floor observation deck for breathtaking city views (included). Afternoon city tour: Drive past Jumeirah Mosque, Atlantis The Palm, and Dubai Frame; stop at Dubai Mall for the Fountain Show. Evening at leisure—option for extreme add-ons like indoor skiing at Ski Dubai (at extra cost). Overnight stay in Dubai.

Day 4: Abu Dhabi – Ferrari World & Cultural Wonders
Breakfast and check out from Dubai hotel. Transfer to Abu Dhabi (1.5 hours). Visit Ferrari World—experience the world's fastest roller coaster and other high-speed attractions (included). Afternoon visit to Sheikh Zayed Grand Mosque—marvel at the stunning architecture and peaceful ambiance. Check in to Abu Dhabi hotel. Overnight stay in Abu Dhabi.

Day 5: Departure
Breakfast at the hotel. Morning at leisure for last-minute shopping or relaxation. Check out and transfer to Abu Dhabi or Dubai Airport for departure. Tour ends with unforgettable memories of extreme UAE adventures.

Hotels
• Dubai (3 nights): Rove Downtown (3-star)—central location, modern vibe near adventure hubs
• Abu Dhabi (1 night): Aloft Abu Dhabi (3-star)—close to Yas Island, adventure-ready

Add On Services
• Flight booking assistance
• Visa processing support
• Travel insurance
• Hotel upgrades to 4-star/5-star
• Airport lounge access
• Private guide upgrades

Popular Activities
• Desert Safari with BBQ dinner
• Burj Khalifa observation deck visit
• Dubai Mall and Fountain Show
• Dhow cruise dinner
• Dubai Marina walk
• Jumeirah Beach activities
• Gold and Spice Souk shopping

=== LLM SOURCED FIELDS (Optional - AI will generate if missing) ===

Best Time to Visit
October to March - The best time to visit Dubai is from October to March when the weather is pleasant, making it ideal for sightseeing, desert safaris, and outdoor activities. This period coincides with the Dubai Shopping Festival, New Year celebrations, and other major events.

Rating
4.5/5 - Dubai is consistently rated as a top global travel destination. Based on aggregated reviews from major travel platforms, Dubai scores an average of 4.7 out of 5 for tourism experience, infrastructure, and attractions.

Currency Conversion Rate to INR
1 AED (United Arab Emirates Dirham) ≈ 22.7 INR (Indian Rupees). Rates may vary slightly depending on the provider.

Destination Safety
Dubai is considered one of the safest cities in the world for tourists. The city maintains strict laws, high surveillance, and a strong police presence. However, travelers should respect local customs and laws to avoid any issues. Petty crimes are rare, and solo female travelers also report feeling safe in public areas.

Popular Restaurants
• Al Fanar Restaurant & Café (Traditional Emirati cuisine)
• Ravi Restaurant (Authentic Pakistani food)
• Pierchic (Fine dining seafood)
• Zuma (Contemporary Japanese)
• Din Tai Fung (Taiwanese cuisine)
• Arabian Tea House (Local flavors)
• Atmosphere (Burj Khalifa dining)

What to Shop
Gold jewelry from Gold Souk, Arabic perfumes (oud), pashmina shawls, spices from Spice Souk, dates, traditional lanterns, designer brands from malls, electronics during sales, camel milk chocolate, Arabic coffee sets.

What to Pack
Light cotton clothing for day, jacket for air-conditioned spaces and evenings, swimwear, comfortable walking shoes, sunglasses, sunscreen (SPF 50+), hat, power adapter (Type G), modest attire for mosque visits, camera, travel insurance documents.

Cultural Info
Dubai blends Islamic traditions with modern life—dress modestly in public areas, avoid public displays of affection, respect local customs during Ramadan, and enjoy the warm Emirati hospitality. Friday is the holy day when many offices are closed.

=== NOTE ===
• BASIC FIELDS are required and should always be included in your document
• LLM SOURCED FIELDS are optional - if not provided, AI will automatically generate appropriate content based on the destination
• AI will also auto-calculate fields like duration in nights/days, price/currency extraction, and additional recommendations"""
