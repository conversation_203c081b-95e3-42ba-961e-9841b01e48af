"""
Custom Package Icon Helper for Django Admin
Lightweight helper for generating icon classes for highlights, inclusions, and addons
in the Django admin interface with progress feedback
"""

import logging
from django.core.exceptions import ValidationError
from .custom_package_helper.openai_icon_mapper import OpenAIIconMapper

logger = logging.getLogger(__name__)


class CustomPackageIconHelper:
    """
    Lightweight helper for generating icon classes in Django admin.
    Focuses specifically on admin CRUD operations with progress feedback.
    """
    
    def __init__(self):
        """Initialize the icon helper"""
        logger.info("CustomPackageIconHelper.__init__ called")
        
        self.icon_mapper = OpenAIIconMapper()
        self.progress_callback = None
        
        logger.info("CustomPackageIconHelper.__init__ completed successfully")
    
    def set_progress_callback(self, callback):
        """Set a callback function to report progress"""
        self.progress_callback = callback
    
    def _report_progress(self, message):
        """Report progress if callback is set"""
        if self.progress_callback:
            self.progress_callback(message)
        logger.info(f"Progress: {message}")
    
    def generate_icons_for_package_data(self, highlights_list, inclusions_list, addons_list):
        """
        Generate icon classes for all package data items.
        
        Args:
            highlights_list: List of highlight strings
            inclusions_list: List of inclusion strings  
            addons_list: List of addon strings
            
        Returns:
            dict: {
                'highlights': [{'value': str, 'icon_class': str}, ...],
                'inclusions': [{'value': str, 'icon_class': str}, ...],
                'addons': [{'value': str, 'icon_class': str}, ...]
            }
        """
        logger.info("CustomPackageIconHelper.generate_icons_for_package_data called")
        logger.debug(f"Processing {len(highlights_list)} highlights, {len(inclusions_list)} inclusions, {len(addons_list)} addons")
        
        try:
            result = {}
            total_items = len(highlights_list) + len(inclusions_list) + len(addons_list)
            processed_items = 0
            
            if total_items == 0:
                logger.warning("No items to process for icon generation")
                return {'highlights': [], 'inclusions': [], 'addons': []}
            
            self._report_progress(f"Starting icon generation for {total_items} items...")
            
            # Process highlights
            if highlights_list:
                self._report_progress(f"Generating icons for {len(highlights_list)} highlights...")
                result['highlights'] = self.icon_mapper.map_items_to_icons(highlights_list, 'highlight')
                processed_items += len(highlights_list)
                self._report_progress(f"Completed highlights ({processed_items}/{total_items})")
            else:
                result['highlights'] = []
            
            # Process inclusions
            if inclusions_list:
                self._report_progress(f"Generating icons for {len(inclusions_list)} inclusions...")
                result['inclusions'] = self.icon_mapper.map_items_to_icons(inclusions_list, 'inclusion')
                processed_items += len(inclusions_list)
                self._report_progress(f"Completed inclusions ({processed_items}/{total_items})")
            else:
                result['inclusions'] = []
            
            # Process addons
            if addons_list:
                self._report_progress(f"Generating icons for {len(addons_list)} addons...")
                result['addons'] = self.icon_mapper.map_items_to_icons(addons_list, 'addon')
                processed_items += len(addons_list)
                self._report_progress(f"Completed addons ({processed_items}/{total_items})")
            else:
                result['addons'] = []
            
            self._report_progress("Icon generation completed successfully!")
            
            logger.info(f"CustomPackageIconHelper.generate_icons_for_package_data completed successfully")
            logger.debug(f"Generated icons for {len(result['highlights'])} highlights, {len(result['inclusions'])} inclusions, {len(result['addons'])} addons")
            
            return result
            
        except Exception as e:
            error_msg = f"Icon generation failed: {str(e)}"
            logger.error(f"CustomPackageIconHelper.generate_icons_for_package_data error: {error_msg}")
            self._report_progress(f"Error: {error_msg}")
            raise ValidationError(error_msg)
    
    def generate_icons_for_highlights(self, highlights_list):
        """Generate icon classes specifically for highlights"""
        logger.info("CustomPackageIconHelper.generate_icons_for_highlights called")
        
        if not highlights_list:
            logger.warning("No highlights provided for icon generation")
            return []
        
        try:
            self._report_progress(f"Generating icons for {len(highlights_list)} highlights...")
            result = self.icon_mapper.map_items_to_icons(highlights_list, 'highlight')
            self._report_progress("Highlights icon generation completed!")
            
            logger.info(f"CustomPackageIconHelper.generate_icons_for_highlights completed successfully for {len(result)} items")
            return result
            
        except Exception as e:
            error_msg = f"Highlights icon generation failed: {str(e)}"
            logger.error(f"CustomPackageIconHelper.generate_icons_for_highlights error: {error_msg}")
            self._report_progress(f"Error: {error_msg}")
            raise ValidationError(error_msg)
    
    def generate_icons_for_inclusions(self, inclusions_list):
        """Generate icon classes specifically for inclusions"""
        logger.info("CustomPackageIconHelper.generate_icons_for_inclusions called")
        
        if not inclusions_list:
            logger.warning("No inclusions provided for icon generation")
            return []
        
        try:
            self._report_progress(f"Generating icons for {len(inclusions_list)} inclusions...")
            result = self.icon_mapper.map_items_to_icons(inclusions_list, 'inclusion')
            self._report_progress("Inclusions icon generation completed!")
            
            logger.info(f"CustomPackageIconHelper.generate_icons_for_inclusions completed successfully for {len(result)} items")
            return result
            
        except Exception as e:
            error_msg = f"Inclusions icon generation failed: {str(e)}"
            logger.error(f"CustomPackageIconHelper.generate_icons_for_inclusions error: {error_msg}")
            self._report_progress(f"Error: {error_msg}")
            raise ValidationError(error_msg)
    
    def generate_icons_for_addons(self, addons_list):
        """Generate icon classes specifically for addons"""
        logger.info("CustomPackageIconHelper.generate_icons_for_addons called")
        
        if not addons_list:
            logger.warning("No addons provided for icon generation")
            return []
        
        try:
            self._report_progress(f"Generating icons for {len(addons_list)} addons...")
            result = self.icon_mapper.map_items_to_icons(addons_list, 'addon')
            self._report_progress("Addons icon generation completed!")
            
            logger.info(f"CustomPackageIconHelper.generate_icons_for_addons completed successfully for {len(result)} items")
            return result
            
        except Exception as e:
            error_msg = f"Addons icon generation failed: {str(e)}"
            logger.error(f"CustomPackageIconHelper.generate_icons_for_addons error: {error_msg}")
            self._report_progress(f"Error: {error_msg}")
            raise ValidationError(error_msg)
    
    def has_openai_api_key(self):
        """Check if OpenAI API key is available"""
        return bool(self.icon_mapper.api_key)
    
    def get_fallback_icons_for_package_data(self, highlights_list, inclusions_list, addons_list):
        """
        Generate fallback icon classes without OpenAI (faster, local processing).
        
        Args:
            highlights_list: List of highlight strings
            inclusions_list: List of inclusion strings  
            addons_list: List of addon strings
            
        Returns:
            dict: Same format as generate_icons_for_package_data
        """
        logger.info("CustomPackageIconHelper.get_fallback_icons_for_package_data called")
        logger.debug(f"Processing {len(highlights_list)} highlights, {len(inclusions_list)} inclusions, {len(addons_list)} addons using fallback")
        
        try:
            result = {}
            
            self._report_progress("Using fast local icon generation...")
            
            # Process highlights
            if highlights_list:
                result['highlights'] = self.icon_mapper._get_fallback_mapping(highlights_list, 'highlight')
            else:
                result['highlights'] = []
            
            # Process inclusions
            if inclusions_list:
                result['inclusions'] = self.icon_mapper._get_fallback_mapping(inclusions_list, 'inclusion')
            else:
                result['inclusions'] = []
            
            # Process addons
            if addons_list:
                result['addons'] = self.icon_mapper._get_fallback_mapping(addons_list, 'addon')
            else:
                result['addons'] = []
            
            self._report_progress("Local icon generation completed!")
            
            logger.info(f"CustomPackageIconHelper.get_fallback_icons_for_package_data completed successfully using fallback")
            return result
            
        except Exception as e:
            error_msg = f"Fallback icon generation failed: {str(e)}"
            logger.error(f"CustomPackageIconHelper.get_fallback_icons_for_package_data error: {error_msg}")
            self._report_progress(f"Error: {error_msg}")
            raise ValidationError(error_msg)
