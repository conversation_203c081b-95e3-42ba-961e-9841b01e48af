"""
CustomPackage Creation Helper for CustomPackage Admin Flow
Handles all logic for creating and updating CustomPackages with comprehensive related entry support
"""

import re
import logging
from decimal import Decimal, InvalidOperation
from django.core.exceptions import ValidationError
from django.db import transaction
from django.conf import settings

from packages.models import (
    Package, CustomActivity, Destination, Category, PackageHighlight, 
    PackageInclusion, PackageAddon, PackageCategory, PackageMedia,
    Itinerary, ItineraryDayItem, ItineraryHotel, ItineraryActivity
)
from dynamic_packages.models import Hotel
from accounts.models import Partner
from packages.choices import PackageTypeChoices, ItineraryDayItemType
from .openai_icon_mapper import OpenAIIconMapper

logger = logging.getLogger(__name__)


class CustomPackageHelper:
    """
    Helper class for CustomPackage creation and management.
    Handles data validation, OpenAI processing for icon mapping, and database operations.
    
    This class is designed to be reusable for both Admin Panel flow and API flow.
    """
    
    def __init__(self, cleaned_data=None, package_type=None):
        logger.info("CustomPackageHelper.__init__ called")
        logger.debug(f"CustomPackageHelper.__init__ cleaned_data provided: {cleaned_data is not None}")
        logger.debug(f"CustomPackageHelper.__init__ package_type: {package_type}")
        
        self.cleaned_data = cleaned_data or {}
        self.package_type = package_type or PackageTypeChoices.CUSTOM_ADMIN  # Default to admin type
        self.processed_data = {}
        self.errors = []
        self.warnings = []

        logger.debug(f"CustomPackageHelper.__init__ cleaned_data keys: {list(self.cleaned_data.keys())}")
        logger.debug(f"CustomPackageHelper.__init__ package_type set to: {self.package_type}")
        logger.debug(f"CustomPackageHelper.__init__ initializing OpenAIIconMapper")

        self.icon_mapper = OpenAIIconMapper() # Initialize the new icon mapper

        logger.info(f"CustomPackageHelper.__init__ completed successfully")
    
    def prepare_data_for_creation(self):
        """
        Validate and process the cleaned data for package creation.
        This includes OpenAI processing for icon mapping.
        """
        logger.info("CustomPackageHelper.prepare_data_for_creation called")
        
        try:
            logger.info("CustomPackageHelper.prepare_data_for_creation starting data preparation")

            logger.debug("CustomPackageHelper.prepare_data_for_creation validating basic fields")
            self._validate_basic_fields()
            if self.errors:
                logger.error(f"CustomPackageHelper.prepare_data_for_creation validation errors: {', '.join(self.errors)}")
                raise ValidationError(f"Validation errors: {', '.join(self.errors)}")

            logger.debug("CustomPackageHelper.prepare_data_for_creation processing price and currency")
            self._process_price_and_currency()
            
            logger.debug("CustomPackageHelper.prepare_data_for_creation processing duration")
            self._process_duration()

            # Use the new icon mapper for highlights, inclusions, and addons
            logger.info("CustomPackageHelper.prepare_data_for_creation processing highlights with OpenAI")
            self._process_highlights_with_openai()
            
            logger.info("CustomPackageHelper.prepare_data_for_creation processing inclusions with OpenAI")
            self._process_inclusions_with_openai()
            
            logger.info("CustomPackageHelper.prepare_data_for_creation processing addons with OpenAI")
            self._process_addons_with_openai()

            logger.debug("CustomPackageHelper.prepare_data_for_creation setting system fields")
            self._set_system_fields()

            logger.info("CustomPackageHelper.prepare_data_for_creation completed successfully")
            return self.processed_data

        except Exception as e:
            logger.error(f"CustomPackageHelper.prepare_data_for_creation error: {str(e)}")
            raise ValidationError(f"Data preparation failed: {str(e)}")
    
    def create_custom_package(self, fixed_data):
        """
        Create a new CustomPackage with the processed data.
        """
        logger.info("CustomPackageHelper.create_custom_package called")
        logger.debug(f"CustomPackageHelper.create_custom_package fixed_data keys: {list(fixed_data.keys())}")
        logger.debug(f"CustomPackageHelper.create_custom_package package_type: {self.package_type}")
        
        try:
            logger.info("CustomPackageHelper.create_custom_package starting package creation")

            with transaction.atomic():
                logger.debug("CustomPackageHelper.create_custom_package creating Package instance")
                package = Package.objects.create(
                    title=fixed_data['title'],
                    package_no=fixed_data['package_no'],
                    destination=fixed_data['destination'],
                    type=self.package_type,  # Use dynamic package type
                    partner=fixed_data['partner'],
                    owner=fixed_data['owner'],
                    duration=fixed_data['duration'],
                    duration_in_nights=fixed_data['duration_in_nights'],
                    duration_in_days=fixed_data['duration_in_days'],
                    price=fixed_data['price'],
                    price_per_person=fixed_data['price_per_person'],
                    currency=fixed_data['currency'],
                    currency_conversion_rate=fixed_data.get('currency_conversion_rate', ''),
                    about_this_tour=fixed_data['about_this_tour'],
                    exclusions=fixed_data.get('exclusions', []),
                    visa_type=fixed_data.get('visa_type', []),
                    best_time_to_visit=fixed_data.get('best_time_to_visit', ''),
                    destination_safety=fixed_data.get('destination_safety', ''),
                    rating=fixed_data.get('rating'),
                    rating_description=fixed_data.get('rating_description', ''),
                    hotels=fixed_data.get('hotels', []),
                    popular_restaurants=fixed_data.get('popular_restaurants', []),
                    popular_activities=fixed_data.get('popular_activities', []),
                    cultural_info=fixed_data.get('cultural_info', ''),
                    what_to_shop=fixed_data.get('what_to_shop', ''),
                    what_to_pack=fixed_data.get('what_to_pack', ''),
                    important_notes=fixed_data.get('important_notes', []),
                    is_published=fixed_data.get('is_published', False),
                    is_active=fixed_data.get('is_active', True),
                    explore_order=fixed_data.get('explore_order', 0)
                )

                logger.info(f"CustomPackageHelper.create_custom_package package created with ID: {package.id}, type: {package.type}")
                return package

        except Exception as e:
            logger.error(f"CustomPackageHelper.create_custom_package error: {str(e)}")
            raise ValidationError(f"Package creation failed: {str(e)}")
    
    def add_related_entries(self, package, fixed_data):
        """
        Add all related entries to the package including highlights, inclusions, addons,
        media files, itineraries, and itinerary day items.
        """
        logger.info(f"CustomPackageHelper.add_related_entries called for package ID: {package.id}")
        logger.debug(f"CustomPackageHelper.add_related_entries fixed_data keys: {list(fixed_data.keys())}")

        try:
            logger.info("CustomPackageHelper.add_related_entries starting related entries creation")

            with transaction.atomic():
                logger.debug("CustomPackageHelper.add_related_entries adding highlights")
                self._add_highlights(package, fixed_data)
                
                logger.debug("CustomPackageHelper.add_related_entries adding inclusions")
                self._add_inclusions(package, fixed_data)
                
                logger.debug("CustomPackageHelper.add_related_entries adding addons")
                self._add_addons(package, fixed_data)
                
                logger.debug("CustomPackageHelper.add_related_entries adding package media")
                self._add_package_media(package, fixed_data)
                
                logger.debug("CustomPackageHelper.add_related_entries adding itineraries")
                self._add_itineraries(package, fixed_data)

                logger.info("CustomPackageHelper.add_related_entries all related entries added successfully")

        except Exception as e:
            logger.error(f"CustomPackageHelper.add_related_entries error: {str(e)}")
            raise ValidationError(f"Failed to add related entries: {str(e)}")
    
    def _add_highlights(self, package, fixed_data):
        """Add highlights with icon mapping"""
        if 'highlights' in fixed_data and fixed_data['highlights']:
            for highlight_data in fixed_data['highlights']:
                PackageHighlight.objects.create(
                    package=package,
                    value=highlight_data['value'],
                    icon_class=highlight_data['icon_class']
                )
            logger.info(f"Added {len(fixed_data['highlights'])} highlights")
    
    def _add_inclusions(self, package, fixed_data):
        """Add inclusions with icon mapping"""
        if 'inclusions' in fixed_data and fixed_data['inclusions']:
            for inclusion_data in fixed_data['inclusions']:
                PackageInclusion.objects.create(
                    package=package,
                    value=inclusion_data['value'],
                    icon_class=inclusion_data['icon_class']
                )
            logger.info(f"Added {len(fixed_data['inclusions'])} inclusions")
    
    def _add_addons(self, package, fixed_data):
        """Add addons with icon mapping"""
        if 'addons' in fixed_data and fixed_data['addons']:
            for addon_data in fixed_data['addons']:
                PackageAddon.objects.create(
                    package=package,
                    value=addon_data['value'],
                    icon_class=addon_data['icon_class']
                )
            logger.info(f"Added {len(fixed_data['addons'])} addons")
    
    def _add_package_media(self, package, fixed_data):
        """Add package media files"""
        if 'package_media' in fixed_data and fixed_data['package_media']:
            for media_data in fixed_data['package_media']:
                PackageMedia.objects.create(
                    package=package,
                    title=media_data.get('title', ''),
                    file_type=media_data.get('file_type', 'IMAGE'),
                    file=media_data['file']
                )
            logger.info(f"Added {len(fixed_data['package_media'])} media files")
    
    def _add_itineraries(self, package, fixed_data):
        """Add itineraries and their day items"""
        if 'itineraries' in fixed_data and fixed_data['itineraries']:
            for itinerary_data in fixed_data['itineraries']:
                # Create itinerary
                itinerary = Itinerary.objects.create(
                    package=package,
                    partner=package.partner,
                    day_number=itinerary_data['day_number'],
                    date=itinerary_data.get('date'),
                    day_title=itinerary_data['day_title'],
                    description=itinerary_data['description'],
                    order=itinerary_data['order'],
                    inclusions=itinerary_data.get('inclusions', []),
                    meta_information=itinerary_data.get('meta_information', {})
                )
                
                # Add day items for this itinerary
                if 'day_items' in itinerary_data:
                    for day_item_data in itinerary_data['day_items']:
                        self._add_itinerary_day_item(itinerary, day_item_data)
            
            logger.info(f"Added {len(fixed_data['itineraries'])} itineraries")
    
    def _add_itinerary_day_item(self, itinerary, day_item_data):
        """Add a single itinerary day item with related hotel/activity"""
        day_item = ItineraryDayItem.objects.create(
            itinerary=itinerary,
            partner=itinerary.partner,
            type=day_item_data['type'],
            title=day_item_data.get('title', ''),
            description=day_item_data['description'],
            order=day_item_data['order'],
            duration=day_item_data.get('duration', ''),
            inclusions=day_item_data.get('inclusions', []),
            meta_information=day_item_data.get('meta_information', {})
        )
        
        # Add hotel if this is a hotel type day item
        if day_item_data['type'] == ItineraryDayItemType.HOTEL and 'hotel_data' in day_item_data:
            hotel_data = day_item_data['hotel_data']
            
            # Get the Hotel instance from the ID
            hotel_id = hotel_data.get('hotel')
            hotel_instance = None
            
            if hotel_id:
                try:
                    from dynamic_packages.models import Hotel
                    hotel_instance = Hotel.objects.filter(id=hotel_id).first()
                    logger.debug(f"CustomPackageHelper._add_itinerary_day_item found hotel: {hotel_instance}")
                except Hotel.DoesNotExist:
                    logger.warning(f"CustomPackageHelper._add_itinerary_day_item hotel with ID {hotel_id} not found")
                    hotel_instance = None
            
            itinerary_hotel = ItineraryHotel.objects.create(
                hotel=hotel_instance,  # Use the Hotel instance, not the ID
                check_in_time=hotel_data.get('check_in_time'),
                check_out_time=hotel_data.get('check_out_time'),
                meta_information=hotel_data.get('meta_information', {})
            )
            day_item.hotel = itinerary_hotel
            day_item.save()
        
        # Add activity if this is an activity type day item
        if day_item_data['type'] == ItineraryDayItemType.ACTIVITY and 'activity_data' in day_item_data:
            activity_data = day_item_data['activity_data']
            
            # Get the CustomActivity instance from the ID
            activity_id = activity_data.get('activity')
            activity_instance = None
            
            if activity_id:
                try:
                    from packages.models import CustomActivity
                    activity_instance = CustomActivity.objects.filter(id=activity_id).first()
                    logger.debug(f"CustomPackageHelper._add_itinerary_day_item found activity: {activity_instance}")
                except CustomActivity.DoesNotExist:
                    logger.warning(f"CustomPackageHelper._add_itinerary_day_item activity with ID {activity_id} not found")
                    activity_instance = None
            
            itinerary_activity = ItineraryActivity.objects.create(
                activity=activity_instance,  # Use the CustomActivity instance, not the ID
                start_time=activity_data.get('start_time'),
                end_time=activity_data.get('end_time'),
                meta_information=activity_data.get('meta_information', {})
            )
            day_item.activity = itinerary_activity
            day_item.save()
    
    def _validate_basic_fields(self):
        """Validate required basic fields"""
        required_fields = [
            'title', 'package_no', 'destination', 'owner', 'duration', 
            'price_per_person', 'about_this_tour', 'partner'
        ]
        
        for field in required_fields:
            if not self.cleaned_data.get(field):
                self.errors.append(f"{field.replace('_', ' ').title()} is required")
        
        # Validate duration format
        duration = self.cleaned_data.get('duration', '')
        if duration and not self._validate_duration_format(duration):
            self.errors.append("Duration must be in format '4N & 5D' (4 Nights & 5 Days)")
    
    def _validate_duration_format(self, duration):
        """Validate duration format (4N & 5D)"""
        pattern = r'^\d+N\s*&\s*\d+D$'
        return bool(re.match(pattern, duration.strip()))
    
    def _process_price_and_currency(self):
        """Extract currency and price from price_per_person field"""
        price_per_person = self.cleaned_data.get('price_per_person', '')
        
        # Extract currency symbol/code and numeric value
        currency_patterns = {
            'INR': r'[₹]|INR',
            'USD': r'[\$]|USD',
            'EUR': r'[€]|EUR', 
            'GBP': r'[£]|GBP'
        }
        
        currency = 'INR'  # Default
        price = 0
        
        for curr_code, pattern in currency_patterns.items():
            if re.search(pattern, price_per_person):
                currency = curr_code
                break
        
        # Extract numeric value
        numeric_match = re.search(r'[\d,]+', price_per_person.replace(',', ''))
        if numeric_match:
            try:
                price = Decimal(numeric_match.group().replace(',', ''))
            except (ValueError, InvalidOperation):
                price = 0
        
        self.processed_data.update({
            'price_per_person': price_per_person,
            'currency': currency,
            'price': price
        })
    
    def _process_duration(self):
        """Extract duration_in_nights and duration_in_days from duration field"""
        duration = self.cleaned_data.get('duration', '')
        
        # Extract nights and days from format like "4N & 5D"
        nights_match = re.search(r'(\d+)N', duration)
        days_match = re.search(r'(\d+)D', duration)
        
        duration_in_nights = int(nights_match.group(1)) if nights_match else 0
        duration_in_days = int(days_match.group(1)) if days_match else 0
        
        self.processed_data.update({
            'duration': duration,
            'duration_in_nights': duration_in_nights,
            'duration_in_days': duration_in_days
        })
    
    def _process_highlights_with_openai(self):
        """Process highlights with OpenAI icon mapping"""
        logger.info("CustomPackageHelper._process_highlights_with_openai called")
        
        highlights = self.cleaned_data.get('highlights', [])
        logger.debug(f"CustomPackageHelper._process_highlights_with_openai processing {len(highlights)} highlights")
        logger.debug(f"CustomPackageHelper._process_highlights_with_openai highlights: {highlights}")
        
        if highlights:
            logger.info("CustomPackageHelper._process_highlights_with_openai calling icon mapper")
            mapped_highlights = self.icon_mapper.map_items_to_icons(highlights, 'highlight')
            logger.info(f"CustomPackageHelper._process_highlights_with_openai received {len(mapped_highlights)} mapped highlights")
            self.processed_data['highlights'] = mapped_highlights
        else:
            logger.warning("CustomPackageHelper._process_highlights_with_openai no highlights to process")
            self.processed_data['highlights'] = []
        
        logger.info("CustomPackageHelper._process_highlights_with_openai completed")

    def _process_inclusions_with_openai(self):
        """Process inclusions with OpenAI icon mapping"""
        logger.info("CustomPackageHelper._process_inclusions_with_openai called")
        
        inclusions = self.cleaned_data.get('inclusions', [])
        logger.debug(f"CustomPackageHelper._process_inclusions_with_openai processing {len(inclusions)} inclusions")
        logger.debug(f"CustomPackageHelper._process_inclusions_with_openai inclusions: {inclusions}")
        
        if inclusions:
            logger.info("CustomPackageHelper._process_inclusions_with_openai calling icon mapper")
            mapped_inclusions = self.icon_mapper.map_items_to_icons(inclusions, 'inclusion')
            logger.info(f"CustomPackageHelper._process_inclusions_with_openai received {len(mapped_inclusions)} mapped inclusions")
            self.processed_data['inclusions'] = mapped_inclusions
        else:
            logger.warning("CustomPackageHelper._process_inclusions_with_openai no inclusions to process")
            self.processed_data['inclusions'] = []
        
        logger.info("CustomPackageHelper._process_inclusions_with_openai completed")

    def _process_addons_with_openai(self):
        """Process addons with OpenAI icon mapping"""
        logger.info("CustomPackageHelper._process_addons_with_openai called")
        
        addons = self.cleaned_data.get('addons', [])
        logger.debug(f"CustomPackageHelper._process_addons_with_openai processing {len(addons)} addons")
        logger.debug(f"CustomPackageHelper._process_addons_with_openai addons: {addons}")
        
        if addons:
            logger.info("CustomPackageHelper._process_addons_with_openai calling icon mapper")
            mapped_addons = self.icon_mapper.map_items_to_icons(addons, 'addon')
            logger.info(f"CustomPackageHelper._process_addons_with_openai received {len(mapped_addons)} mapped addons")
            self.processed_data['addons'] = mapped_addons
        else:
            logger.info("CustomPackageHelper._process_addons_with_openai no addons to process (optional field)")
            self.processed_data['addons'] = []
        
        logger.info("CustomPackageHelper._process_addons_with_openai completed")
    
    def _set_system_fields(self):
        """Set system fields and copy remaining cleaned data"""
        # Copy all other fields from cleaned_data
        for key, value in self.cleaned_data.items():
            if key not in self.processed_data:
                self.processed_data[key] = value
        
        # Ensure system fields are set
        self.processed_data.setdefault('is_published', False)
        self.processed_data.setdefault('is_active', True)
        self.processed_data.setdefault('explore_order', 0) 