"""
OpenAI Icon Mapper for CustomPackage Helper
Handles mapping of package highlights, inclusions, and addons to appropriate icon classes using OpenAI
"""

import json
import logging
import requests
from django.conf import settings
from base.static import Constants

logger = logging.getLogger(__name__)


class OpenAIIconMapper:
    """
    Dedicated OpenAI icon mapper for CustomPackage elements.
    Maps highlights, inclusions, and addons to appropriate icon classes.
    """
    
    def __init__(self):
        """Initialize the OpenAI icon mapper"""
        logger.info("OpenAIIconMapper.__init__ called")
        
        self.api_key = getattr(settings, 'OPENAI_API_KEY', None)
        self.api_url = getattr(settings, 'OPENAI_API_URL', 'https://api.openai.com/v1/responses')

        logger.debug(f"OpenAIIconMapper.__init__ api_key configured: {bool(self.api_key)}")
        logger.debug(f"OpenAIIconMapper.__init__ api_url: {self.api_url}")

        # Available icon classes (from Constants.ICON_CLASS)
        self.icon_classes = Constants.ICON_CLASS

        logger.debug(f"OpenAIIconMapper.__init__ available icon classes: {len(self.icon_classes)} classes")

        # Icon class descriptions for better mapping
        self.icon_descriptions = {
            "island": "Islands, archipelago, tropical destinations, island hopping, remote locations",
            "beach": "Beaches, swimming, sunbathing, coastal activities, ocean views, seaside",
            "ship": "Boat rides, cruises, ferry transport, water transportation, sailing, maritime",
            "sunrise": "Scenic views, sunrises/sunsets, viewpoints, panoramic experiences, nature beauty, photography spots",
            "activity": "Adventure activities, sports, recreational activities, experiences, tours, excursions",
            "passenger": "Group activities, passenger limits, capacity-related features, people-focused services, group size",
            "breakfast": "Meals, dining, food services, culinary experiences, restaurants, catering, food included",
            "taxi": "Private transport, transfers, individual transportation, car services, private vehicle",
            "bus": "Group transport, shared transfers, bus services, group transportation, coach travel",
            "hotel": "Accommodation, lodging, stays, hotels, resorts, housing, room bookings",
            "flight": "Air travel, flights, aviation services, airport transfers, domestic/international flights",
            "visa": "Documentation, visa services, permits, legal requirements, paperwork, travel documents",
            "insurance": "Protection services, safety, insurance coverage, security, travel protection",
            "forex_card": "Currency services, financial services, money exchange, payment options, foreign exchange"
        }

        logger.info("OpenAIIconMapper.__init__ completed successfully")
    
    def map_items_to_icons(self, items, item_type):
        """
        Map a list of items to appropriate icon classes using OpenAI
        """
        logger.info(f"OpenAIIconMapper.map_items_to_icons called for {item_type}")
        logger.debug(f"OpenAIIconMapper.map_items_to_icons items count: {len(items) if items else 0}")
        logger.debug(f"OpenAIIconMapper.map_items_to_icons items: {items}")
        
        if not items:
            logger.warning("OpenAIIconMapper.map_items_to_icons no items provided, returning empty list")
            return []

        try:
            logger.info(f"OpenAIIconMapper.map_items_to_icons starting mapping for {len(items)} {item_type}s")

            if not self.api_key:
                logger.warning("OpenAIIconMapper.map_items_to_icons OpenAI API key not configured, using fallback mapping")
                fallback_result = self._get_fallback_mapping(items, item_type)
                logger.info(f"OpenAIIconMapper.map_items_to_icons fallback mapping completed, returning {len(fallback_result)} items")
                return fallback_result

            logger.debug("OpenAIIconMapper.map_items_to_icons API key available, attempting OpenAI mapping")
            mapped_items = self._get_openai_mapping(items, item_type)
            logger.info(f"OpenAIIconMapper.map_items_to_icons OpenAI mapping successful, mapped {len(mapped_items)} {item_type}s")
            return mapped_items

        except Exception as e:
            logger.error(f"OpenAIIconMapper.map_items_to_icons OpenAI mapping failed for {item_type}s: {str(e)}")
            logger.info(f"OpenAIIconMapper.map_items_to_icons falling back to local mapping for {item_type}s")
            fallback_result = self._get_fallback_mapping(items, item_type)
            logger.info(f"OpenAIIconMapper.map_items_to_icons fallback mapping completed, returning {len(fallback_result)} items")
            return fallback_result
    
    def _get_openai_mapping(self, items, item_type):
        """
        Get icon mapping from OpenAI API
        """
        logger.info(f"OpenAIIconMapper._get_openai_mapping called for {item_type}")
        logger.debug(f"OpenAIIconMapper._get_openai_mapping processing {len(items)} items")
        
        prompt = self._create_mapping_prompt(items, item_type)
        logger.debug(f"OpenAIIconMapper._get_openai_mapping prompt created, length: {len(prompt)} chars")

        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
        logger.debug("OpenAIIconMapper._get_openai_mapping headers prepared")

        data = {
            'model': 'gpt-4o-mini',
            'messages': [
                {
                    'role': 'system',
                    'content': 'You are an expert at mapping travel package content to appropriate icon classes. You must ONLY use the icon classes provided in the prompt. Never create new icon classes or use CSS classes like "fas fa-star". Always respond with valid JSON only.'
                },
                {
                    'role': 'user',
                    'content': prompt
                }
            ],
            'temperature': 0.1,
            'max_tokens': 2000
        }
        logger.debug("OpenAIIconMapper._get_openai_mapping request data prepared")

        logger.info(f"OpenAIIconMapper._get_openai_mapping making API request to {self.api_url}")
        response = requests.post(self.api_url, headers=headers, json=data, timeout=30)
        logger.debug(f"OpenAIIconMapper._get_openai_mapping API response status: {response.status_code}")
        
        response.raise_for_status()
        logger.debug("OpenAIIconMapper._get_openai_mapping API request successful")

        result = response.json()
        logger.debug("OpenAIIconMapper._get_openai_mapping response JSON parsed")
        
        content = result['choices'][0]['message']['content'].strip()
        logger.debug(f"OpenAIIconMapper._get_openai_mapping extracted content length: {len(content)} chars")

        try:
            logger.debug("OpenAIIconMapper._get_openai_mapping parsing JSON response")
            mapped_items = json.loads(content)
            
            if not isinstance(mapped_items, list):
                logger.error("OpenAIIconMapper._get_openai_mapping response is not a list")
                raise ValueError("Response is not a list")

            logger.info(f"OpenAIIconMapper._get_openai_mapping parsed {len(mapped_items)} mapped items")

            # Validate and fix icon classes
            fixed_count = 0
            for item in mapped_items:
                if not isinstance(item, dict) or 'value' not in item or 'icon_class' not in item:
                    logger.error(f"OpenAIIconMapper._get_openai_mapping invalid item format: {item}")
                    raise ValueError("Invalid item format in response")

                if item['icon_class'] not in self.icon_classes:
                    logger.warning(f"OpenAIIconMapper._get_openai_mapping invalid icon class '{item['icon_class']}' for '{item['value']}', using fallback")
                    item['icon_class'] = self._get_fallback_icon_class(item['value'], item_type)
                    fixed_count += 1

            if fixed_count > 0:
                logger.info(f"OpenAIIconMapper._get_openai_mapping fixed {fixed_count} invalid icon classes using fallback")

            logger.info("OpenAIIconMapper._get_openai_mapping validation completed successfully")
            return mapped_items

        except (json.JSONDecodeError, ValueError) as e:
            logger.error(f"OpenAIIconMapper._get_openai_mapping invalid JSON response: {str(e)}")
            logger.debug(f"OpenAIIconMapper._get_openai_mapping problematic content: {content[:500]}...")
            raise Exception("Invalid JSON response from OpenAI")
    
    def _create_mapping_prompt(self, items, item_type):
        """Create the prompt for OpenAI icon mapping"""
        available_classes = ', '.join(self.icon_classes)
        descriptions = '\n'.join([f"- {cls}: {desc}" for cls, desc in self.icon_descriptions.items()])
        items_text = '\n'.join([f"- {item}" for item in items])
        
        return f"""
You must map the following {item_type} items to the EXACT icon classes provided below. 

⚠️ CRITICAL RULES:
1. ONLY use icon classes from the list below - DO NOT create new ones
2. DO NOT use CSS classes like "fas fa-star", "fa-plane", etc.
3. ONLY use the exact strings from AVAILABLE ICON CLASSES list
4. Each icon_class must be one of: {available_classes}

AVAILABLE ICON CLASSES (USE ONLY THESE):
{available_classes}

ICON CLASS DESCRIPTIONS:
{descriptions}

{item_type.upper()} ITEMS TO MAP:
{items_text}

MAPPING GUIDELINES:
- Transportation: taxi (private transfers), bus (group transport), flight (air travel), ship (water transport)
- Accommodation: hotel (stays, rooms, lodging)
- Food: breakfast (meals, dining, food services)
- Activities: activity (general activities), beach (coastal activities), island (island destinations), sunrise (scenic views)
- Services: visa (documents, permits), insurance (protection, coverage), forex_card (currency, money)
- Default: Use "activity" if unsure

REQUIRED JSON FORMAT:
[
    {{"value": "exact item text", "icon_class": "one_of_the_available_classes"}},
    {{"value": "exact item text", "icon_class": "one_of_the_available_classes"}}
]

IMPORTANT: 
- Keep "value" field EXACTLY as provided
- Use ONLY icon classes from the available list above
- Return valid JSON only, no explanations
"""
    
    def _get_fallback_mapping(self, items, item_type):
        """
        Get fallback icon mapping based on content analysis
        """
        logger.info(f"OpenAIIconMapper._get_fallback_mapping called for {item_type}")
        logger.debug(f"OpenAIIconMapper._get_fallback_mapping processing {len(items)} items")
        
        mapped_items = []
        for i, item in enumerate(items):
            logger.debug(f"OpenAIIconMapper._get_fallback_mapping processing item {i+1}/{len(items)}: '{item}'")
            icon_class = self._get_fallback_icon_class(item, item_type)
            logger.debug(f"OpenAIIconMapper._get_fallback_mapping mapped '{item}' to '{icon_class}'")
            
            mapped_items.append({
                'value': item,
                'icon_class': icon_class
            })
        
        logger.info(f"OpenAIIconMapper._get_fallback_mapping completed, mapped {len(mapped_items)} items")
        return mapped_items
    
    def _get_fallback_icon_class(self, value, item_type):
        """Get fallback icon class based on content analysis"""
        logger.debug(f"OpenAIIconMapper._get_fallback_icon_class called for value: '{value}', type: {item_type}")
        
        value_lower = value.lower()
        logger.debug(f"OpenAIIconMapper._get_fallback_icon_class analyzing lowercase value: '{value_lower}'")

        # Transportation keywords
        if any(word in value_lower for word in ['flight', 'air', 'airplane', 'airport']):
            logger.debug(f"OpenAIIconMapper._get_fallback_icon_class matched flight keywords")
            return 'flight'
        elif any(word in value_lower for word in ['taxi', 'car', 'private transport', 'transfer']):
            logger.debug(f"OpenAIIconMapper._get_fallback_icon_class matched taxi keywords")
            return 'taxi'
        elif any(word in value_lower for word in ['bus', 'coach', 'group transport']):
            logger.debug(f"OpenAIIconMapper._get_fallback_icon_class matched bus keywords")
            return 'bus'
        elif any(word in value_lower for word in ['ship', 'boat', 'cruise', 'ferry']):
            logger.debug(f"OpenAIIconMapper._get_fallback_icon_class matched ship keywords")
            return 'ship'
        
        # Accommodation keywords
        elif any(word in value_lower for word in ['hotel', 'accommodation', 'room', 'stay']):
            logger.debug(f"OpenAIIconMapper._get_fallback_icon_class matched hotel keywords")
            return 'hotel'
        
        # Food keywords
        elif any(word in value_lower for word in ['breakfast', 'lunch', 'dinner', 'meal', 'food']):
            logger.debug(f"OpenAIIconMapper._get_fallback_icon_class matched food keywords")
            return 'breakfast'
        
        # Documentation keywords
        elif any(word in value_lower for word in ['visa', 'document', 'passport']):
            logger.debug(f"OpenAIIconMapper._get_fallback_icon_class matched visa keywords")
            return 'visa'
        
        # Services keywords
        elif any(word in value_lower for word in ['insurance', 'protection', 'coverage']):
            logger.debug(f"OpenAIIconMapper._get_fallback_icon_class matched insurance keywords")
            return 'insurance'
        elif any(word in value_lower for word in ['forex', 'currency', 'money']):
            logger.debug(f"OpenAIIconMapper._get_fallback_icon_class matched forex keywords")
            return 'forex_card'
        
        # Location/Activity keywords
        elif any(word in value_lower for word in ['beach', 'swimming', 'coastal']):
            logger.debug(f"OpenAIIconMapper._get_fallback_icon_class matched beach keywords")
            return 'beach'
        elif any(word in value_lower for word in ['island', 'archipelago']):
            logger.debug(f"OpenAIIconMapper._get_fallback_icon_class matched island keywords")
            return 'island'
        elif any(word in value_lower for word in ['sunset', 'sunrise', 'view', 'scenic']):
            logger.debug(f"OpenAIIconMapper._get_fallback_icon_class matched scenic keywords")
            return 'sunrise'
        
        # Default based on item type
        elif item_type == 'inclusion':
            logger.debug(f"OpenAIIconMapper._get_fallback_icon_class using inclusion default: breakfast")
            return 'breakfast'
        elif item_type == 'addon':
            logger.debug(f"OpenAIIconMapper._get_fallback_icon_class using addon default: activity")
            return 'activity'
        else:
            logger.debug(f"OpenAIIconMapper._get_fallback_icon_class using general default: activity")
            return 'activity' 