"""
Document processor for handling DOC and DOCX files
"""
import os
import re
from django.core.exceptions import ValidationError

try:
    from docx import Document
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False

try:
    DOC_AVAILABLE = True
except ImportError:
    DOC_AVAILABLE = False


def process_document_file(file):
    """
    Process DOC or DOCX file and extract package information
    
    Args:
        file: The uploaded file object
        
    Returns:
        dict: Extracted package data in JSON format
        
    Raises:
        ValidationError: If the file cannot be processed or required libraries are missing
    """
    file_extension = os.path.splitext(file.name)[1].lower()
    
    if file_extension == '.docx':
        return process_docx_file(file)
    elif file_extension == '.doc':
        return process_doc_file(file)
    else:
        raise ValidationError(f"Unsupported file extension: {file_extension}")


def process_docx_file(file):
    """
    Process DOCX file using python-docx library
    
    Args:
        file: The uploaded DOCX file
        
    Returns:
        dict: Extracted package data
    """
    if not DOCX_AVAILABLE:
        raise ValidationError(
            "DOCX processing is not available. Please install python-docx: pip install python-docx"
        )
    
    try:
        # Read the document
        document = Document(file)
        
        # Extract text from all paragraphs
        full_text = '\n'.join([paragraph.text for paragraph in document.paragraphs])
        
        # Extract text from tables if any
        for table in document.tables:
            for row in table.rows:
                for cell in row.cells:
                    full_text += '\n' + cell.text
        
        # Parse the extracted text to create package data
        package_data = parse_document_text(full_text)
        
        return package_data
        
    except Exception as e:
        raise ValidationError(f"Error processing DOCX file: {str(e)}")


def process_doc_file(file):
    """
    Process DOC file - basic text extraction
    Note: For full DOC support, you might need additional libraries like python-docx2txt or win32com
    
    Args:
        file: The uploaded DOC file
        
    Returns:
        dict: Extracted package data
    """
    # For now, we'll provide a basic implementation
    # In production, you might want to use libraries like:
    # - python-docx2txt for simple text extraction
    # - win32com.client (Windows only) for full functionality
    # - antiword (Linux) for text extraction
    
    raise ValidationError(
        "DOC file processing is not yet implemented. "
        "Please convert your document to DOCX format or use JSON format for now."
    )


def parse_document_text(text):
    """
    Parse document text and extract package information using regex patterns
    
    Args:
        text: The extracted text from the document
        
    Returns:
        dict: Package data in the expected JSON format
    """
    package_data = {}
    
    # Define regex patterns for extracting different fields
    patterns = {
        'package_no': r'package\s*(?:no|number)\s*:?\s*([^\n\r]+)',
        'title': r'(?:title|package\s*name)\s*:?\s*([^\n\r]+)',
        'destination': r'destination\s*:?\s*([^\n\r]+)',
        'price_per_person': r'price\s*(?:per\s*person)?\s*:?\s*([^\n\r]+)',
        'duration': r'duration\s*:?\s*([^\n\r]+)',
        'type': r'(?:package\s*)?type\s*:?\s*([^\n\r]+)',
        'best_time_to_visit': r'best\s*time\s*(?:to\s*visit)?\s*:?\s*([^\n\r]+)',
        'about_this_tour': r'(?:about|description)\s*:?\s*((?:[^\n\r]+\n?)+?)(?:\n\s*\n|\n\s*[A-Z][a-z]+\s*:)',
    }
    
    # Extract basic fields using regex
    for field, pattern in patterns.items():
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            package_data[field] = match.group(1).strip()
    
    # Extract lists (highlights, inclusions, exclusions)
    list_patterns = {
        'highlights': r'highlights?\s*:?\s*((?:[-•*]\s*[^\n\r]+\n?)+)',
        'inclusions': r'(?:inclusions?|included)\s*:?\s*((?:[-•*]\s*[^\n\r]+\n?)+)',
        'exclusions': r'(?:exclusions?|excluded)\s*:?\s*((?:[-•*]\s*[^\n\r]+\n?)+)',
        'visa_type': r'visa\s*(?:type|requirement)?\s*:?\s*((?:[-•*]\s*[^\n\r]+\n?)+)',
    }
    
    for field, pattern in list_patterns.items():
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            # Split by bullet points and clean up
            items = re.split(r'[-•*]\s*', match.group(1))
            package_data[field] = [item.strip() for item in items if item.strip()]
    
    # Set default values for required fields if not found
    defaults = {
        'package_no': 'AUTO-GENERATED',
        'title': 'Package Title',
        'destination': 'Unknown Destination',
        'type': 'Fixed',
        'duration': '1N & 2D',
        'price_per_person': '₹0',
        'about_this_tour': 'Package description not provided',
        'highlights': ['Package highlights not specified'],
        'inclusions': [],
        'exclusions': [],
        'visa_type': [],
        'rating': '0',
        'currency_conversion_rate': '1',
        'destination_safety': 'Safety information not provided',
        'itinerary': [],
        'hotels': [],
        'popular_restaurants': [],
        'popular_activities': [],
        'what_to_shop': '',
        'what_to_pack': '',
        'cultural_information': '',
    }
    
    # Apply defaults for missing fields
    for field, default_value in defaults.items():
        if field not in package_data:
            package_data[field] = default_value
    
    return package_data 