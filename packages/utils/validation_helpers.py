from django.core.exceptions import ValidationError
from decimal import Decimal
import re
import json
from typing import Dict, List, Any, Tuple, Optional


class PackageJsonValidationHelper:
    """
    OOP-based validation helper for package JSON data.
    Handles validation and cleaning of package data for both form input and file uploads.
    """
    
    def __init__(self, json_data: Dict[str, Any]):
        """Initialize with JSON data to validate"""
        self.json_data = json_data.copy() if json_data else {}
        self.errors = []
        self.cleaned_data = {}
    
    def validate(self) -> None:
        """
        Main validation method that runs all validations.
        Collects all errors and raises ValidationError if any exist.
        """
        self.errors = []
        
        # Run all validation methods
        self._validate_required_fields()
        self._validate_package_no()
        self._validate_title()
        self._validate_duration()
        self._validate_price_per_person()
        self._validate_rating()
        self._validate_array_fields()
        self._validate_itinerary()
        self._validate_data_types()
        
        # Raise all errors at once if any exist
        if self.errors:
            raise ValidationError(self.errors)
    
    def clean(self) -> Dict[str, Any]:
        """
        Clean and normalize the data.
        Returns the cleaned data dictionary.
        """
        self.cleaned_data = self.json_data.copy()
        
        # Clean each field
        self._clean_package_no()
        self._clean_title()
        self._clean_duration()
        self._clean_price_per_person()
        self._clean_rating()
        self._clean_array_fields()
        self._clean_itinerary()
        self._clean_field_mappings()
        self._clean_best_time_to_visit_months()
        
        return self.cleaned_data
    
    # ===================
    # VALIDATION METHODS
    # ===================
    
    def _validate_required_fields(self) -> None:
        """Validate that all required fields are present"""
        required_fields = [
            'package_no', 'title', 'destination', 'type', 'duration', 
            'price_per_person', 'about_this_tour', 'highlights'
        ]
        
        # Fields that should be treated as lists
        list_fields = ['highlights']
        
        for field in required_fields:
            if field not in self.json_data:
                self.errors.append(f"Missing required field: {field}")
            elif field in list_fields:
                # Special handling for list fields
                value = self.json_data[field]
                if not isinstance(value, list):
                    self.errors.append(f"Required field '{field}' must be a list")
                elif len(value) == 0:
                    self.errors.append(f"Required field '{field}' cannot be empty")
                else:
                    # Check if all items in the list are empty/whitespace
                    non_empty_items = [str(item).strip() for item in value if str(item).strip()]
                    if len(non_empty_items) == 0:
                        self.errors.append(f"Required field '{field}' cannot contain only empty items")
            elif not str(self.json_data[field]).strip():
                self.errors.append(f"Required field '{field}' cannot be empty")
    
    def _validate_package_no(self) -> None:
        """Validate package number format"""
        package_no = self.json_data.get('package_no')
        if not package_no:
            return
        
        package_no = str(package_no).strip()
        
        if not re.match(r'^[A-Za-z0-9\-\_]+$', package_no):
            self.errors.append("Package number can only contain letters, numbers, hyphens, and underscores")
        
        if len(package_no) > 50:
            self.errors.append("Package number cannot exceed 50 characters")
        
        if len(package_no) < 1:
            self.errors.append("Package number is required")
    
    def _validate_title(self) -> None:
        """Validate package title"""
        title = self.json_data.get('title')
        if not title:
            return
        
        title = str(title).strip()
        
        if len(title) < 3:
            self.errors.append("Package title must be at least 3 characters long")
        
        if len(title) > 255:
            self.errors.append("Package title cannot exceed 255 characters")
    
    def _validate_duration(self) -> None:
        """Validate duration format"""
        duration = self.json_data.get('duration')
        if not duration:
            return
        
        duration = str(duration).strip()
        duration_clean = duration.upper().replace('&', ' ').replace(',', ' ')
        
        # Pattern to match numbers followed by N/NIGHT or D/DAY
        nights_match = re.search(r'(\d+)\s*[N|NIGHT]', duration_clean)
        days_match = re.search(r'(\d+)\s*[D|DAY]', duration_clean)
        
        if not (nights_match and days_match):
            self.errors.append("Duration must include both nights and days. Examples: '4N & 5D', '3 Nights 4 Days'")
            return
        
        try:
            nights = int(nights_match.group(1))
            days = int(days_match.group(1))
            
            if nights < 0 or days < 0:
                self.errors.append("Nights and days must be positive numbers")
            
            if abs(days - nights) > 2:
                self.errors.append("Days should typically be nights + 1. Please verify your duration")
        except ValueError:
            self.errors.append("Invalid duration format")
    
    def _validate_price_per_person(self) -> None:
        """Validate price format and basic constraints"""
        price = self.json_data.get('price_per_person')
        if not price:
            return
        
        price_str = str(price)
        
        # Extract numeric part
        numeric_part = re.sub(r'[^\d\.]', '', price_str.replace(',', ''))
        
        if not numeric_part:
            self.errors.append("Price must contain a numeric value")
            return
        
        try:
            price_value = Decimal(numeric_part)
            if price_value < 0:
                self.errors.append("Price cannot be negative")
            if price_value == 0:
                self.errors.append("Price must be greater than zero")
        except Exception:
            self.errors.append("Invalid price format. Example: '₹39,999', '$500', '25000'")
    
    def _validate_rating(self) -> None:
        """Validate rating field"""
        rating = self.json_data.get('rating')
        if rating is None:
            return
        
        try:
            rating_value = Decimal(str(rating))
            if rating_value < 0 or rating_value > 5:
                self.errors.append("Rating must be between 0.0 and 5.0")
        except Exception:
            self.errors.append("Invalid rating format. Example: '4.5', '3.0'")
    
    def _validate_array_fields(self) -> None:
        """Validate all array fields"""
        array_field_configs = {
            'highlights': {'min_items': 1, 'max_items': 20, 'required': True},
            'inclusions': {'max_items': 50},
            'exclusions': {'max_items': 50},
            'visa_type': {'max_items': 10},
            'hotels': {'max_items': 20},
            'popular_restaurants': {'max_items': 30},
            'popular_activities': {'max_items': 30},
            'addons': {'max_items': 20},
            'important_notes': {'max_items': 10},
            'category': {'max_items': 10}
        }
        
        for field_name, config in array_field_configs.items():
            self._validate_single_array_field(field_name, **config)
    
    def _validate_single_array_field(self, field_name: str, min_items: int = None, 
                                   max_items: int = None, required: bool = False) -> None:
        """Validate a single array field"""
        value = self.json_data.get(field_name)
        
        if value is None:
            if required:
                self.errors.append(f"{field_name} is required")
            return
        
        if not isinstance(value, list):
            self.errors.append(f"{field_name} must be a list")
            return
        
        # Clean empty items for validation
        cleaned_items = [str(item).strip() for item in value if str(item).strip()]
        
        if min_items and len(cleaned_items) < min_items:
            self.errors.append(f"{field_name} must have at least {min_items} item(s)")
        
        if max_items and len(cleaned_items) > max_items:
            self.errors.append(f"{field_name} cannot have more than {max_items} items")
        
        # # Check for duplicates
        # if len(cleaned_items) != len(set(cleaned_items)):
        #     # For inclusions, allow some legitimate duplicates (like "Breakfast at the hotel" on different days)
        #     if field_name == 'inclusions':
        #         # Only flag as error if there are exact duplicates that seem problematic
        #         # Allow duplicates for common travel terms that legitimately repeat
        #         common_repeatable_terms = [
        #             'breakfast', 'lunch', 'dinner', 'overnight stay', 'check in', 'check out',
        #             'transfer', 'accommodation', 'hotel', 'morning', 'afternoon', 'evening'
        #         ]
                
        #         # Check if duplicates are legitimate (contain repeatable terms)
        #         duplicates = []
        #         seen = set()
        #         for item in cleaned_items:
        #             if item in seen:
        #                 item_lower = item.lower()
        #                 # Check if this duplicate contains common repeatable terms
        #                 is_legitimate = any(term in item_lower for term in common_repeatable_terms)
        #                 if not is_legitimate:
        #                     duplicates.append(item)
        #             seen.add(item)
                
        #         # Only raise error if there are non-legitimate duplicates
        #         if duplicates:
        #             self.errors.append(f"{field_name} contains problematic duplicate items: {duplicates}")
        #     else:
        #         # For other fields, keep strict duplicate checking
        #         self.errors.append(f"{field_name} contains duplicate items")
    
    def _validate_itinerary(self) -> None:
        """Validate itinerary structure - now expects HTML content for RichTextField"""
        itinerary = self.json_data.get('itinerary')
        if not itinerary:
            return
        
        # Itinerary should now be HTML content (string) for RichTextField
        if not isinstance(itinerary, str):
            self.errors.append("Itinerary must be HTML content (string) for rich text display")
            return
        
        # Basic validation that it contains some content
        if not itinerary.strip():
            self.errors.append("Itinerary content cannot be empty")
            return
        
        # Optional: Validate that it contains some basic HTML structure for days
        # This is a basic check to ensure it's properly formatted HTML with day structure
        if '<h3>' not in itinerary.lower():
            self.errors.append("Itinerary should contain day headers with <h3> tags")
    
    def _validate_data_types(self) -> None:
        """Validate data types for specific fields"""
        type_validations = {
            'type': {'allowed': ['Fixed', 'Variable'], 'error': "Type must be 'Fixed' or 'Variable'"},
            'is_published': {'type': bool, 'error': "is_published must be a boolean"},
            'is_active': {'type': bool, 'error': "is_active must be a boolean"},
            'is_international': {'type': bool, 'error': "is_international must be a boolean"},
        }
        
        for field, config in type_validations.items():
            value = self.json_data.get(field)
            if value is None:
                continue
            
            if 'allowed' in config:
                # Special case-insensitive handling for 'type' field
                if field == 'type':
                    value_lower = str(value).lower()
                    allowed_lower = [v.lower() for v in config['allowed']]
                    if value_lower not in allowed_lower:
                        self.errors.append(config['error'])
                    else:
                        # Normalize the value to proper case for consistency
                        if value_lower == 'fixed':
                            self.json_data['type'] = 'Fixed'
                        elif value_lower == 'variable':
                            self.json_data['type'] = 'Variable'
                else:
                    if str(value) not in config['allowed']:
                        self.errors.append(config['error'])
            elif 'type' in config:
                if not isinstance(value, config['type']):
                    self.errors.append(config['error'])
    
    # =================
    # CLEANING METHODS
    # =================
    
    def _clean_package_no(self) -> None:
        """Clean package number"""
        package_no = self.cleaned_data.get('package_no')
        if package_no:
            self.cleaned_data['package_no'] = str(package_no).strip()
    
    def _clean_title(self) -> None:
        """Clean title"""
        title = self.cleaned_data.get('title')
        if title:
            self.cleaned_data['title'] = str(title).strip()
    
    def _clean_duration(self) -> None:
        """Clean duration and extract nights/days"""
        duration = self.cleaned_data.get('duration')
        if not duration:
            return
        
        duration = str(duration).strip()
        duration_clean = duration.upper().replace('&', ' ').replace(',', ' ')
        
        # Extract nights and days
        nights_match = re.search(r'(\d+)\s*[N|NIGHT]', duration_clean)
        days_match = re.search(r'(\d+)\s*[D|DAY]', duration_clean)
        
        if nights_match and days_match:
            self.cleaned_data['duration_in_nights'] = int(nights_match.group(1))
            self.cleaned_data['duration_in_days'] = int(days_match.group(1))
        
        self.cleaned_data['duration'] = duration
    
    def _clean_price_per_person(self) -> None:
        """Clean price and extract currency info while preserving original format"""
        price = self.cleaned_data.get('price_per_person')
        if not price:
            return
        
        price_str = str(price)
        
        # PRESERVE the original price_per_person format exactly as provided
        self.cleaned_data['price_per_person'] = price_str
        
        # Extract currency symbol
        currency_symbol = re.sub(r'[\d\.,\s]', '', price_str).strip()
        
        # Extract numeric value for separate price field
        numeric_part = re.sub(r'[^\d\.]', '', price_str.replace(',', ''))
        
        if numeric_part:
            try:
                price_value = Decimal(numeric_part)
                # Create separate price field with just the numeric value
                self.cleaned_data['price'] = float(price_value)
                
                # Determine currency
                currency = 'INR'  # Default
                if '$' in currency_symbol:
                    currency = 'USD'
                elif '€' in currency_symbol:
                    currency = 'EUR'
                elif '£' in currency_symbol:
                    currency = 'GBP'
                elif '₹' in currency_symbol:
                    currency = 'INR'
                
                # Set default symbol if none provided
                if not currency_symbol:
                    currency_symbol = '₹'
                    currency = 'INR'
                    # AUTOMATICALLY ADD ₹ SYMBOL TO price_per_person WHEN NO SYMBOL IS PRESENT
                    self.cleaned_data['price_per_person'] = f'₹{price_str}'
                else:
                    # PRESERVE the original price_per_person format when symbol is already present
                    self.cleaned_data['price_per_person'] = price_str
                
                self.cleaned_data['currency'] = currency
                self.cleaned_data['currency_symbol'] = currency_symbol
                
            except Exception:
                pass  # Error will be caught in validation
    
    def _clean_rating(self) -> None:
        """Clean rating field"""
        rating = self.cleaned_data.get('rating')
        if rating is not None:
            self.cleaned_data['rating'] = float(Decimal(str(rating)))
    
    def _clean_array_fields(self) -> None:
        """Clean all array fields"""
        array_fields = [
            'highlights', 'inclusions', 'exclusions', 'visa_type', 
            'hotels', 'popular_restaurants', 'popular_activities', 
            'addons', 'important_notes', 'category'
        ]
        
        for field in array_fields:
            value = self.cleaned_data.get(field)
            if isinstance(value, list):
                # Clean empty items and remove duplicates while preserving order
                cleaned_items = []
                seen = set()
                for item in value:
                    item_str = str(item).strip()
                    if item_str and item_str not in seen:
                        cleaned_items.append(item_str)
                        seen.add(item_str)
                self.cleaned_data[field] = cleaned_items
            elif value is None:
                self.cleaned_data[field] = []
    
    def _clean_itinerary(self) -> None:
        """Clean itinerary structure - now handles HTML content"""
        itinerary = self.cleaned_data.get('itinerary')
        
        if isinstance(itinerary, str):
            # It's already HTML content, just clean it up
            self.cleaned_data['itinerary'] = itinerary.strip()
        elif isinstance(itinerary, list):
            # Legacy format - convert JSON array to HTML
            html_content = self._convert_itinerary_json_to_html(itinerary)
            self.cleaned_data['itinerary'] = html_content
        elif itinerary is None:
            self.cleaned_data['itinerary'] = ""
        else:
            # Fallback - convert to string
            self.cleaned_data['itinerary'] = str(itinerary).strip()
    
    def _convert_itinerary_json_to_html(self, itinerary_list: list) -> str:
        """Convert legacy JSON array format to HTML content"""
        if not itinerary_list:
            return ""
        
        html_parts = []
        for day_data in itinerary_list:
            if isinstance(day_data, dict):
                day = day_data.get('day', '')
                title = day_data.get('title', '')
                description = day_data.get('description', '')
                
                # Create day header
                if day and title:
                    html_parts.append(f"<h3>{day} - {title}</h3>")
                elif day:
                    html_parts.append(f"<h3>{day}</h3>")
                
                # Create description as list items
                if description:
                    # Split description into logical bullet points if it's long
                    desc_parts = [desc.strip() for desc in description.split('.') if desc.strip()]
                    if len(desc_parts) > 1:
                        html_parts.append("<ul>")
                        for part in desc_parts:
                            if part:
                                html_parts.append(f"<li>{part}</li>")
                        html_parts.append("</ul>")
                    else:
                        html_parts.append(f"<ul><li>{description}</li></ul>")
        
        return "".join(html_parts)
    
    def _clean_field_mappings(self) -> None:
        """Handle field name mappings from JSON to model fields"""
        field_mappings = {
            'included': 'inclusions',
            'excluded': 'exclusions',
            'cultural_information': 'cultural_info',
            'optional': 'addons'  # Legacy field name mapping
        }
        
        for old_field, new_field in field_mappings.items():
            if old_field in self.cleaned_data:
                self.cleaned_data[new_field] = self.cleaned_data.pop(old_field)
    
    def _clean_best_time_to_visit_months(self) -> None:
        """Extract month names from best_time_to_visit text and populate best_time_to_visit_months"""
        best_time_to_visit = self.cleaned_data.get('best_time_to_visit')
        
        # If best_time_to_visit_months is already populated, don't override it
        if self.cleaned_data.get('best_time_to_visit_months'):
            return
            
        if not best_time_to_visit:
            self.cleaned_data['best_time_to_visit_months'] = []
            return
        
        text_lower = best_time_to_visit.lower()
        
        # Month mappings - both full names and abbreviations
        month_mappings = {
            'january': 'January', 'jan': 'January',
            'february': 'February', 'feb': 'February',
            'march': 'March', 'mar': 'March',
            'april': 'April', 'apr': 'April',
            'may': 'May',
            'june': 'June', 'jun': 'June',
            'july': 'July', 'jul': 'July',
            'august': 'August', 'aug': 'August',
            'september': 'September', 'sep': 'September', 'sept': 'September',
            'october': 'October', 'oct': 'October',
            'november': 'November', 'nov': 'November',
            'december': 'December', 'dec': 'December'
        }
        
        # Season mappings
        season_mappings = {
            'winter': ['December', 'January', 'February'],
            'spring': ['March', 'April', 'May'],
            'summer': ['June', 'July', 'August'],
            'autumn': ['September', 'October', 'November'],
            'fall': ['September', 'October', 'November'],
            'monsoon': ['June', 'July', 'August', 'September'],
            'post-monsoon': ['October', 'November']
        }
        
        extracted_months = set()
        
        # Look for individual months
        for month_key, month_name in month_mappings.items():
            if month_key in text_lower:
                extracted_months.add(month_name)
        
        # Look for seasons
        for season_key, season_months in season_mappings.items():
            if season_key in text_lower:
                extracted_months.update(season_months)
        
        # Look for month ranges like "October to March"
        range_pattern = r'(\w+)\s+to\s+(\w+)'
        matches = re.findall(range_pattern, text_lower)
        for start_month, end_month in matches:
            start_found = month_mappings.get(start_month)
            end_found = month_mappings.get(end_month)
            if start_found and end_found:
                # Get month numbers for range calculation
                month_order = ['January', 'February', 'March', 'April', 'May', 'June',
                              'July', 'August', 'September', 'October', 'November', 'December']
                try:
                    start_idx = month_order.index(start_found)
                    end_idx = month_order.index(end_found)
                    
                    # Handle year wrap-around (e.g., October to March)
                    if start_idx <= end_idx:
                        # Same year range
                        for i in range(start_idx, end_idx + 1):
                            extracted_months.add(month_order[i])
                    else:
                        # Year wrap-around
                        for i in range(start_idx, len(month_order)):
                            extracted_months.add(month_order[i])
                        for i in range(0, end_idx + 1):
                            extracted_months.add(month_order[i])
                except ValueError:
                    pass
        
        # Set the extracted months
        self.cleaned_data['best_time_to_visit_months'] = sorted(list(extracted_months))
    
    # =================
    # UTILITY METHODS
    # =================
    
    def get_cleaned_data(self) -> Dict[str, Any]:
        """Get the cleaned data"""
        return self.cleaned_data
    
    def get_errors(self) -> List[str]:
        """Get validation errors"""
        return self.errors
    
    def has_errors(self) -> bool:
        """Check if there are validation errors"""
        return len(self.errors) > 0


# Convenience functions for backwards compatibility
def validate_package_data(json_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Convenience function to validate and clean package data.
    Returns cleaned data or raises ValidationError.
    """
    helper = PackageJsonValidationHelper(json_data)
    try:
        helper.validate()
        return helper.clean()
    except ValidationError:
        raise


def create_package_validation_helper(json_data: Dict[str, Any]) -> PackageJsonValidationHelper:
    """Create and return a validation helper instance"""
    return PackageJsonValidationHelper(json_data) 