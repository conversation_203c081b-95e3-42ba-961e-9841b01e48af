from rest_framework import serializers
from packages.models import CustomPackageItinerary, CustomActivity, TripjackHotels, Package
from packages.choices import ItineraryDayItemType


class CustomActivityDataSerializer(serializers.ModelSerializer):
    """Serializer for activity data in itinerary items"""
    images = serializers.SerializerMethodField()
    
    class Meta:
        model = CustomActivity
        fields = [
            'external_id', 'title', 'description', 'overall_rating', 'images'
        ]

    def get_images(self, obj):
        """Get activity images from related media"""
        try:
            media_objects = obj.media.filter(is_active=True)
            return [media.media.name if media.media else None for media in media_objects if media.media]
        except:
            return []


class TripjackHotelDataSerializer(serializers.ModelSerializer):
    """Serializer for hotel data in itinerary items"""
    
    class Meta:
        model = TripjackHotels
        fields = [
            'external_id', 'name', 'description', 'star_rating',
            'address_line', 'city_name', 'country_name', 'amenities', 'images'
        ]


class CustomPackageItinerarySerializer(serializers.ModelSerializer):
    """Serializer for the simplified CustomPackageItinerary model"""
    activity_data = serializers.SerializerMethodField()
    hotel_data = serializers.SerializerMethodField()
    
    class Meta:
        model = CustomPackageItinerary
        fields = [
            'day_number', 'type', 'activity_data', 'hotel_data'
        ]
    
    def get_activity_data(self, obj):
        """Return detailed activity data if this is an activity type item"""
        if obj.type == ItineraryDayItemType.ACTIVITY and obj.activity:
            return CustomActivityDataSerializer(obj.activity).data
        return None
    
    def get_hotel_data(self, obj):
        """Return detailed hotel data if this is a hotel type item"""
        if obj.type == ItineraryDayItemType.HOTEL and obj.hotel:
            return TripjackHotelDataSerializer(obj.hotel).data
        return None


def _generate_day_title(day_number, day_items):
    """Generate a meaningful title for the day based on its activities and hotels"""
    if not day_items:
        return "Explore & Experience"
    
    # Collect activities and hotels for this day
    activities = []
    hotels = []
    
    for item in day_items:
        if item.type == 'Activity' and item.activity:
            activities.append(item.activity.title)
        elif item.type == 'Hotel' and item.hotel:
            hotels.append(item.hotel.name)
    
    # Build title components
    title_parts = []
    
    # Handle activities
    if activities:
        if len(activities) == 1:
            # Single activity - use full title
            title_parts.append(activities[0])
        elif len(activities) == 2:
            # Two activities - list both
            title_parts.append(f"{activities[0]} and {activities[1]}")
        elif len(activities) == 3:
            # Three activities - list all with commas
            title_parts.append(f"{activities[0]}, {activities[1]} and {activities[2]}")
        else:
            # Multiple activities (4+) - list first 2 and summarize rest
            remaining_count = len(activities) - 2
            title_parts.append(f"{activities[0]}, {activities[1]} and {remaining_count} more activities")
    
    # Handle hotels
    if hotels:
        if len(hotels) == 1:
            # Single hotel - use full name
            hotel_part = f"Stay at {hotels[0]}"
        elif len(hotels) == 2:
            # Two hotels - list both
            hotel_part = f"Stay at {hotels[0]} and {hotels[1]}"
        elif len(hotels) == 3:
            # Three hotels - list all with commas
            hotel_part = f"Stay at {hotels[0]}, {hotels[1]} and {hotels[2]}"
        else:
            # Multiple hotels (4+) - list first 2 and summarize rest
            remaining_count = len(hotels) - 2
            hotel_part = f"Stay at {hotels[0]}, {hotels[1]} and {remaining_count} more properties"

        title_parts.append(hotel_part)
    
    # If no specific content, use generic title
    if not title_parts:
        return "Explore & Experience"
    
    # Join with "and" for natural flow
    if len(title_parts) == 1:
        return title_parts[0]
    else:
        return " and ".join(title_parts)


def get_itinerary_data(itineraries):
    """
    Get detailed itinerary data for a package using the new simplified structure
    
    Args:
        itineraries: QuerySet of CustomPackageItinerary objects
        
    Returns:
        List of detailed itinerary data organized by day
    """
    if not itineraries:
        return []
    
    # Group itineraries by day number
    itinerary_dict = {}
    for item in itineraries.filter(is_active=True).order_by('day_number'):
        day = item.day_number
        if day not in itinerary_dict:
            itinerary_dict[day] = {
                'day_number': day,
                'day_items': [],
                'raw_items': []  # Keep raw items for title generation
            }
        
        # Serialize the itinerary item
        serializer = CustomPackageItinerarySerializer(item)
        itinerary_dict[day]['day_items'].append(serializer.data)
        itinerary_dict[day]['raw_items'].append(item)
    
    # Generate titles and clean up
    result = []
    for day in sorted(itinerary_dict.keys()):
        day_data = itinerary_dict[day]
        title = _generate_day_title(day, day_data['raw_items'])
        
        result.append({
            'day_number': day,
            'title': title,
            'day_items': day_data['day_items']
        })
    
    return result


def get_simple_itinerary_data(itineraries):
    """
    Get simplified itinerary data (for backward compatibility)
    
    Args:
        itineraries: QuerySet of CustomPackageItinerary objects
        
    Returns:
        List of basic itinerary data
    """
    if not itineraries:
        return []
    
    itinerary_data = []
    current_day = None
    day_items = []
    
    for item in itineraries.filter(is_active=True).order_by('day_number'):
        if current_day != item.day_number:
            # Save previous day if exists
            if current_day is not None:
                # Generate title for the previous day
                raw_items = []
                for item_data in day_items:
                    # Create a mock object for title generation
                    class MockItem:
                        def __init__(self, type_val, activity_data, hotel_data):
                            self.type = type_val
                            if activity_data:
                                self.activity = type('obj', (object,), {'title': activity_data.get('title', '')})()
                            else:
                                self.activity = None
                            if hotel_data:
                                self.hotel = type('obj', (object,), {'name': hotel_data.get('name', '')})()
                            else:
                                self.hotel = None
                    
                    raw_items.append(MockItem(
                        item_data['type'],
                        item_data.get('activity_data'),
                        item_data.get('hotel_data')
                    ))
                
                title = _generate_day_title(current_day, raw_items)
                
                itinerary_data.append({
                    'day_number': current_day,
                    'title': title,
                    'day_items': day_items
                })
            
            # Start new day
            current_day = item.day_number
            day_items = []
        
        # Add item to current day
        item_data = {
            'type': item.type,
            'day_number': item.day_number,
        }

        # Initialize both data fields as null
        item_data['activity_data'] = None
        item_data['hotel_data'] = None

        if item.type == ItineraryDayItemType.ACTIVITY and item.activity:
            # Get activity images
            try:
                images = [media.media.name for media in item.activity.media.filter(is_active=True) if media.media]
            except:
                images = []

            item_data['activity_data'] = {
                'external_id': item.activity.external_id,
                'title': item.activity.title,
                'description': item.activity.description,
                'overall_rating': item.activity.overall_rating,
                'images': images
            }
        elif item.type == ItineraryDayItemType.HOTEL and item.hotel:
            item_data['hotel_data'] = {
                'external_id': item.hotel.external_id,
                'name': item.hotel.name,
                'description': item.hotel.description,
                'star_rating': item.hotel.star_rating,
                'address_line': item.hotel.address_line,
                'city_name': item.hotel.city_name,
                'country_name': item.hotel.country_name,
                'amenities': item.hotel.amenities,
                'images': item.hotel.images
            }

        day_items.append(item_data)

    # Don't forget the last day
    if current_day is not None:
        # Generate title for the last day
        raw_items = []
        for item_data in day_items:
            # Create a mock object for title generation
            class MockItem:
                def __init__(self, type_val, activity_data, hotel_data):
                    self.type = type_val
                    if activity_data:
                        self.activity = type('obj', (object,), {'title': activity_data.get('title', '')})()
                    else:
                        self.activity = None
                    if hotel_data:
                        self.hotel = type('obj', (object,), {'name': hotel_data.get('name', '')})()
                    else:
                        self.hotel = None
            
            raw_items.append(MockItem(
                item_data['type'],
                item_data.get('activity_data'),
                item_data.get('hotel_data')
            ))
        
        title = _generate_day_title(current_day, raw_items)

        itinerary_data.append({
            'day_number': current_day,
            'title': title,
            'day_items': day_items
        })

    return itinerary_data