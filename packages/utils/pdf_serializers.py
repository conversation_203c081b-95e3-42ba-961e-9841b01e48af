from rest_framework import serializers
from django.utils.html import strip_tags
from packages.models import Package, ActivityMedia, CategoryMedia, DestinationMedia, Activity, PackageMedia
from packages.api.v1.serializers import (
    PackageHighlightSerializer, 
    PackageInclusionSerializer, 
    PackageAddonSerializer,
)
import re
import json


class ActivityMediaPDFSerializer(serializers.ModelSerializer):
    """PDF-specific serializer for Activity Media that returns full URLs"""
    
    class Meta:
        model = ActivityMedia
        fields = ['external_id', 'media', 'created_at']
    
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        if instance.media:
            # Return full URL for PDF generation with AWS S3
            representation['media'] = instance.media.url
        return representation


class CategoryMediaPDFSerializer(serializers.ModelSerializer):
    """PDF-specific serializer for Category Media that returns full URLs"""
    
    class Meta:
        model = CategoryMedia
        fields = ['external_id', 'media', 'created_at']
    
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        if instance.media:
            # Return full URL for PDF generation with AWS S3
            representation['media'] = instance.media.url
        return representation


class DestinationMediaPDFSerializer(serializers.ModelSerializer):
    """PDF-specific serializer for Destination Media that returns full URLs"""
    
    class Meta:
        model = DestinationMedia
        fields = ['external_id', 'media', 'created_at']
    
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        if instance.media:
            # Return full URL for PDF generation with AWS S3
            representation['media'] = instance.media.url
        return representation


class PackageMediaPDFSerializer(serializers.ModelSerializer):
    """PDF-specific serializer for Package Media that returns full URLs"""
    
    class Meta:
        model = PackageMedia
        fields = ['external_id', 'file']
    
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        if instance.file:
            # Return full URL for PDF generation with AWS S3
            representation['file'] = instance.file.url
        return representation


class PackageActivityPDFSerializer(serializers.ModelSerializer):
    """PDF-specific serializer for Package Activities with full image URLs"""
    media = serializers.SerializerMethodField()
    
    class Meta:
        model = Activity
        fields = ['external_id', 'title', 'media']
    
    def get_media(self, obj):
        """Get main display image for activity with full URLs, fallback to first image if no main display"""
        # First try to get main display media
        main_media = obj.media.filter(main_display=True, is_active=True)
        if main_media.exists():
            return ActivityMediaPDFSerializer(main_media, many=True).data
        
        # Fallback to first available media if no main display
        first_media = obj.media.filter(is_active=True).first()
        if first_media:
            return ActivityMediaPDFSerializer([first_media], many=True).data
        
        return None


class PackagePDFSerializer(serializers.ModelSerializer):
    """
    Comprehensive serializer for Package PDF generation
    Includes all data needed for dynamic PDF template rendering
    """
    # Basic package info
    destination_title = serializers.SerializerMethodField()
    media = PackageMediaPDFSerializer(many=True, read_only=True)
    
    # Dynamic lists
    highlights = serializers.SerializerMethodField()
    inclusions = serializers.SerializerMethodField()
    addons = serializers.SerializerMethodField()
    
    # Processed text fields
    about_this_tour_cleaned = serializers.SerializerMethodField()
    what_to_pack_parsed = serializers.SerializerMethodField()
    itinerary_parsed = serializers.SerializerMethodField()
    
    # Rating display
    rating_stars = serializers.SerializerMethodField()
    
    # Duration display
    duration_display = serializers.SerializerMethodField()
    
    # Price display
    price_display = serializers.SerializerMethodField()
    
    # Visa type display
    visa_type_display = serializers.SerializerMethodField()
    
    # Hotel list processed
    hotels_processed = serializers.SerializerMethodField()
    
    # Popular activities and restaurants
    popular_activities_processed = serializers.SerializerMethodField()
    popular_restaurants_processed = serializers.SerializerMethodField()
    
    # Package activities with media
    activities = serializers.SerializerMethodField()
    
    class Meta:
        model = Package
        fields = [
            'external_id',
            'title',
            'package_no',
            'destination_title',
            'owner',
            'type',
            'duration',
            'duration_display',
            'duration_in_days',
            'duration_in_nights',
            'price_per_person',
            'price_display',
            'visa_type',
            'visa_type_display',
            'rating',
            'rating_stars',
            'rating_description',
            'about_this_tour',
            'about_this_tour_cleaned',
            'media',
            'highlights',
            'inclusions',
            'addons',
            'exclusions',
            'itinerary',
            'itinerary_parsed',
            'hotels',
            'hotels_processed',
            'popular_activities',
            'popular_activities_processed',
            'popular_restaurants',
            'popular_restaurants_processed',
            'best_time_to_visit',
            'destination_safety',
            'cultural_info',
            'what_to_shop',
            'what_to_pack',
            'what_to_pack_parsed',
            'activities',
            'created_at'
        ]
    
    def get_destination_title(self, obj):
        """Get destination title"""
        return obj.destination.title if obj.destination else "Unknown Destination"
    
    def get_highlights(self, obj):
        """Get highlights using separate serializer"""
        highlights = obj.highlights.filter(is_active=True)
        return PackageHighlightSerializer(highlights, many=True).data
    
    def get_inclusions(self, obj):
        """Get inclusions using separate serializer"""
        inclusions = obj.inclusions.filter(is_active=True)
        return PackageInclusionSerializer(inclusions, many=True).data
    
    def get_addons(self, obj):
        """Get addons using separate serializer"""
        addons = obj.addons.filter(is_active=True)
        return PackageAddonSerializer(addons, many=True).data
    
    def get_about_this_tour_cleaned(self, obj):
        """Clean HTML tags from about_this_tour if present"""
        if not obj.about_this_tour:
            return ""
        return strip_tags(obj.about_this_tour).strip()
    
    def get_what_to_pack_parsed(self, obj):
        """Parse what_to_pack rich text field into structured data"""
        if not obj.what_to_pack:
            return []
        
        try:
            # Remove HTML tags and parse into categories
            cleaned_text = strip_tags(obj.what_to_pack)
            
            # Split by common category indicators
            categories = []
            current_category = None
            current_items = []
            
            lines = cleaned_text.split('\n')
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                
                # Check if line is a category header (ends with colon or is capitalized)
                if line.endswith(':') or (line.isupper() and len(line.split()) <= 3):
                    # Save previous category
                    if current_category and current_items:
                        categories.append({
                            'title': current_category,
                            'items': current_items
                        })
                    
                    # Start new category
                    current_category = line.replace(':', '')
                    current_items = []
                elif line.startswith('•') or line.startswith('-') or line.startswith('*'):
                    # Item in current category
                    item = line.replace('•', '').replace('-', '').replace('*', '').strip()
                    if item:
                        current_items.append(item)
                elif current_category:
                    # Regular line - add as item
                    current_items.append(line)
            
            # Add last category
            if current_category and current_items:
                categories.append({
                    'title': current_category,
                    'items': current_items
                })
            
            return categories
            
        except Exception:
            # Fallback: return as single category
            return [{
                'title': 'What To Pack',
                'items': [strip_tags(obj.what_to_pack)] if obj.what_to_pack else []
            }]
    
    def get_itinerary_parsed(self, obj):
        """Parse itinerary rich text field into day-wise structure"""
        if not obj.itinerary:
            return []
        
        try:
            # Remove HTML and parse into days
            cleaned_text = strip_tags(obj.itinerary)
            
            days = []
            current_day = None
            current_activities = []
            
            lines = cleaned_text.split('\n')
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                
                # Check if line is a day header
                day_match = re.match(r'day\s*(\d+)', line.lower())
                if day_match or 'day' in line.lower()[:10]:
                    # Save previous day
                    if current_day and current_activities:
                        days.append({
                            'title': current_day,
                            'activities': current_activities
                        })
                    
                    # Start new day
                    current_day = line
                    current_activities = []
                else:
                    # Activity for current day
                    if line.startswith('•') or line.startswith('-') or line.startswith('*'):
                        activity = line.replace('•', '').replace('-', '').replace('*', '').strip()
                        if activity:
                            current_activities.append(activity)
                    elif current_day:
                        current_activities.append(line)
            
            # Add last day
            if current_day and current_activities:
                days.append({
                    'title': current_day,
                    'activities': current_activities
                })
            
            return days
            
        except Exception:
            # Fallback: return raw content
            return [{
                'title': 'Itinerary',
                'activities': [strip_tags(obj.itinerary)] if obj.itinerary else []
            }]
    
    def get_rating_stars(self, obj):
        """Generate star display data for CSS-based half stars"""
        if not obj.rating:
            return {'full': 0, 'half': 0, 'empty': 5, 'rating_value': 0}
        
        rating = float(obj.rating)
        full_stars = int(rating)  # Number of full stars
        has_half = (rating - full_stars) >= 0.5  # Whether to show half star
        
        # Calculate empty stars
        total_filled = full_stars + (1 if has_half else 0)
        empty_stars = 5 - total_filled
        
        return {
            'full': full_stars,
            'half': 1 if has_half else 0,
            'empty': empty_stars,
            'rating_value': rating
        }
    
    def get_duration_display(self, obj):
        """Format duration for display"""
        if obj.duration_in_nights and obj.duration_in_days:
            return f"{obj.duration_in_nights}N/{obj.duration_in_days}D"
        return obj.duration or "Duration not specified"
    
    def get_price_display(self, obj):
        """Format price for display"""
        if obj.price_per_person:
            return f"{obj.price_per_person}/per person"
        return "Price on request"
    
    def get_visa_type_display(self, obj):
        """Format visa type for display"""
        if not obj.visa_type:
            return "Visa information not available"
        
        if isinstance(obj.visa_type, list):
            return " • ".join(obj.visa_type)
        return str(obj.visa_type)
    
    def get_hotels_processed(self, obj):
        """Process hotels list into structured format"""
        if not obj.hotels:
            return []
        
        hotels = []
        for i, hotel in enumerate(obj.hotels):
            # You can add more processing here like extracting star ratings, descriptions etc.
            hotels.append({
                'name': hotel,
                'day_range': f"Day {i+1} - Day {i+2}",  # Example day range
                'rating': '★★★★',  # Default rating, can be parsed from hotel name
                'description': 'Centrally located with modern amenities.'  # Default description
            })
        return hotels
    
    def get_popular_activities_processed(self, obj):
        """Process popular activities into structured format"""
        if not obj.popular_activities:
            return []
        
        return [{'name': activity, 'description': 'Popular local activity'} 
                for activity in obj.popular_activities]
    
    def get_popular_restaurants_processed(self, obj):
        """Process popular restaurants into structured format"""
        if not obj.popular_restaurants:
            return []
        
        return [{'name': restaurant, 'description': 'Highly recommended dining'} 
                for restaurant in obj.popular_restaurants] 

    def get_activities(self, obj):
        """Get activities with media using PDF serializer for full URLs"""
        # Get activities through the package_activities relationship
        activities = [pa.activity for pa in obj.package_activities.select_related('activity').filter(is_active=True)]
        return PackageActivityPDFSerializer(activities, many=True).data 