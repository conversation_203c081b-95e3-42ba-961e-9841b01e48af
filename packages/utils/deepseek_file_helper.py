import json
import requests
import logging
from typing import Dict, Any
from django.conf import settings
from django.core.exceptions import ValidationError
from packages.utils.package_format_helpers import get_sample_package_format

logger = logging.getLogger(__name__)


class DeepSeekFileHelper:
    """
    OOP-based helper for processing and fixing files using DeepSeek AI.
    Handles both JSON fixing and DOC/DOCX to JSON conversion.
    """
    
    def __init__(self, file_content: Any, file_type: str = None):
        """
        Initialize with file content and type
        
        Args:
            file_content: Raw file content (dict for JSON, binary for docs)
            file_type: Type of file ('json', 'doc', 'docx'). Defaults to 'json' for manual data.
        """
        self.file_content = file_content
        # Default to 'json' if file_type is None (for manual data entry)
        self.file_type = (file_type or 'json').lower()
        self.api_key = settings.DEEPSEEK_API_KEY
        self.api_url = settings.DEEPSEEK_API_URL
        self.sample_format = get_sample_package_format()
        self.processed_data = None
        self.errors = []
        
    def process(self) -> Dict[str, Any]:
        """
        Main processing method that handles the complete flow
        
        Returns:
            Dict containing the processed and fixed JSON data
        """
        try:
            if self.file_type == 'json':
                # Fix existing JSON data
                self.processed_data = self._fix_json_data()
            elif self.file_type in ['doc', 'docx']:
                # Convert DOC/DOCX to JSON, then fix it
                json_data = self._convert_doc_to_json()
                self.file_content = json_data
                self.processed_data = self._fix_json_data()
            else:
                raise ValidationError(f"Unsupported file type: {self.file_type}")
            
            return self.processed_data
            
        except Exception as e:
            error_msg = f"DeepSeek processing failed: {str(e)}"
            logger.error(error_msg)
            raise ValidationError(error_msg)
    
    def _fix_json_data(self) -> Dict[str, Any]:
        """
        Fix JSON data using DeepSeek AI
        
        Returns:
            Fixed JSON data as dictionary
        """
        prompt = self._create_json_fix_prompt()
        
        try:
            response = self._call_deepseek_api(prompt)
            fixed_data = self._extract_json_from_response(response)
            
            # Validate that the response is proper JSON
            if not isinstance(fixed_data, dict):
                raise ValidationError("DeepSeek returned invalid JSON structure")
            
            return fixed_data
            
        except Exception as e:
            logger.error(f"JSON fixing failed: {str(e)}")
            raise ValidationError(f"Failed to fix JSON data: {str(e)}")
    
    def _convert_doc_to_json(self) -> Dict[str, Any]:
        """
        Convert DOC/DOCX content to JSON using DeepSeek AI
        
        Returns:
            Converted JSON data as dictionary
        """
        # Convert binary content to text (simplified approach)
        try:
            # For DOC/DOCX files, we need to extract text first
            text_content = self._extract_text_from_doc()
            
            prompt = self._create_doc_conversion_prompt(text_content)
            
            response = self._call_deepseek_api(prompt)
            json_data = self._extract_json_from_response(response)
            
            return json_data
            
        except Exception as e:
            logger.error(f"Document conversion failed: {str(e)}")
            raise ValidationError(f"Failed to convert document to JSON: {str(e)}")
    
    def _extract_text_from_doc(self) -> str:
        """
        Extract text content from DOC/DOCX files using proper libraries
        """
        try:
            if self.file_type == 'docx':
                # Use python-docx library for proper DOCX extraction
                try:
                    from docx import Document
                    from io import BytesIO
                    
                    # Create a BytesIO object from the binary content
                    doc_stream = BytesIO(self.file_content)
                    document = Document(doc_stream)
                    
                    # Extract text from all paragraphs
                    full_text = '\n'.join([paragraph.text for paragraph in document.paragraphs])
                    
                    # Extract text from tables if any
                    for table in document.tables:
                        for row in table.rows:
                            for cell in row.cells:
                                full_text += '\n' + cell.text
                    
                    # Log successful extraction
                    logger.info(f"Successfully extracted {len(full_text)} characters from DOCX file")
                    
                    return full_text
                    
                except ImportError:
                    raise ValidationError("python-docx library is not available. Please install it: pip install python-docx")
                except Exception as e:
                    raise ValidationError(f"Failed to extract text from DOCX file: {str(e)}")
            
            elif self.file_type == 'doc':
                # For .doc files, we need different libraries
                raise ValidationError(
                    "DOC file processing is not yet implemented. "
                    "Please convert your document to DOCX format or use JSON format."
                )
            else:
                # Fallback for other types - try to decode as text
                if isinstance(self.file_content, bytes):
                    return self.file_content.decode('utf-8', errors='ignore')
                return str(self.file_content)
                
        except Exception as e:
            if "python-docx" in str(e) or "DOC file processing" in str(e):
                raise  # Re-raise specific library errors
            raise ValidationError(f"Failed to extract text from document: {str(e)}")
    
    def _create_json_fix_prompt(self) -> str:
        """
        Create comprehensive prompt for fixing JSON data with strict preservation of user-provided content
        """
        return f"""
You are an AI travel package data processor. I will provide you with a JSON object that may have errors, missing fields, or incorrect formatting. Your job is to fix and enhance the data to match our required format.

⚠️ CRITICAL: When user provides simple add-ons like "Flight, Visa, Insurance", keep them EXACTLY as provided - DO NOT expand to detailed descriptions like "Flight booking assistance, Visa processing support, Travel insurance"

⚠️ ESPECIALLY IMPORTANT: If user writes "upgrades" keep it as "upgrades" - DO NOT expand to "Hotel upgrades, Airport lounge, Private guide"

REQUIRED JSON FORMAT (with field categorization):
{self.sample_format}

INPUT JSON TO FIX:
{json.dumps(self.file_content, indent=2)}

CRITICAL RULE: PRESERVE ALL USER-PROVIDED CONTENT 

=== USER-PROVIDED FIELDS (NEVER CHANGE THESE - ONLY FIX STRUCTURE) ===
These fields should be preserved exactly as the user provided them. Only fix field names/structure, not content:

**BASIC FIELDS - PRESERVE CONTENT:**
- title: Keep exact title provided by user
- package_no: Keep exact package number provided by user (DO NOT add destination prefixes like 'DXB')
- destination: Keep exact destination name, only convert to lowercase for consistency
- category: Keep exact categories provided, only convert to lowercase and ensure array format
- activities: Keep exact activities provided, only convert to lowercase and ensure array format  
- owner: Keep exact owner name provided by user
- type: Keep as provided ("Fixed" or "Variable")
- duration: Keep exact duration text provided by user
- price_per_person: Keep exact price format provided by user
- visa_type: Keep exact visa types provided, ensure array format
- about_this_tour: Keep exact content provided by user
- highlights: Keep exact highlights provided, ensure array format
- inclusions: Keep exact inclusions provided, ensure array format
- exclusions: Keep exact exclusions provided, ensure array format
- itinerary: Keep exact itinerary content provided, ensure proper array of objects format
- hotels: Keep exact hotels provided, ensure array format
- popular_activities: Keep exact activities provided by user, ensure array format
- addons: Keep exact add ons provided, ensure array format - DO NOT expand or enhance simple terms like "Flight, Visa, Insurance, upgrades" into detailed descriptions. If user says "upgrades" keep it as "upgrades", do NOT expand to "Hotel upgrades, Airport lounge, Private guide"

**ONLY STRUCTURE FIXES ALLOWED:**
- Convert to proper array format if needed
- Fix field name spelling (e.g., "included" → "inclusions")
- Map legacy field names (e.g., "Optional" → "addons", "Add Ons" → "addons")
- Ensure itinerary is array of objects with day, title, description

=== AUTO-CALCULATED FIELDS (Compute from user data) ===
Calculate these ONLY from the user-provided basic fields:

- duration_in_nights: Extract from user's duration field
- duration_in_days: Extract from user's duration field  
- price: Extract numeric value from user's price_per_person
- currency: Extract currency code from user's price_per_person
- best_time_to_visit_months: Extract from user's best_time_to_visit (if provided)
- important_notes: Generate based on user's destination and visa_type

=== LLM SOURCED FIELDS (Generate ONLY if missing or empty) ===
Generate these fields ONLY if they are missing, empty, or null in the input. If user provided them, keep exactly as provided:

- best_time_to_visit: Generate only if missing/empty
- rating: Generate only if missing/empty/0
- rating_description: Generate only if missing/empty
- currency_conversion_rate: Generate only if missing/empty/0
- destination_safety: Generate only if missing/empty
- popular_restaurants: Generate only if missing/empty
- what_to_shop: Generate only if missing/empty  
- what_to_pack: Generate only if missing/empty
- cultural_info: Generate only if missing/empty

**LLM GENERATION RULES:**
- Base ALL generated content on the user's specific destination
- Use current, accurate information for the destination
- Ensure currency_conversion_rate reflects real-world rates
- Make ratings realistic (3.5-4.8 range)
- For rating_description field: Generate a brief 2-line description explaining why this package deserves its rating based on the highlights, inclusions, itinerary quality, and destination appeal. Keep it concise and engaging, focusing on key value propositions that justify the rating.

=== SYSTEM FIELDS (PRESERVE USER VALUES) ===
- is_published: Keep exactly as provided by user (true or false)
- is_active: Keep exactly as provided by user (true or false)

CRITICAL REQUIREMENTS:
1. NEVER change user-provided content - only fix structure and field names
2. NEVER add prefixes to package_no (keep "003" as "003", not "DXB003")
3. Only generate LLM fields if they are truly missing/empty in input
4. Convert destination, category, activities to lowercase for consistency
5. Ensure proper JSON array/object structure
6. PRESERVE USER'S is_published AND is_active VALUES EXACTLY AS PROVIDED
7. PRESERVE SIMPLE ADD-ONS TERMS: If user writes "Flight, Visa, Insurance" keep it as ["Flight", "Visa", "Insurance"] - DO NOT expand to detailed descriptions like "Flight booking assistance". ESPECIALLY: If user writes "upgrades" keep it as "upgrades" - DO NOT expand to "Hotel upgrades, Airport lounge, Private guide"

Return ONLY the complete, fixed JSON object with user content preserved and missing fields generated. No explanations.
"""
    
    def _create_doc_conversion_prompt(self, text_content: str) -> str:
        """
        Create comprehensive prompt for converting document content to JSON with preservation of user content
        """
        return f"""
You are an AI travel package document processor. Convert the provided document content into a complete JSON structure with strict preservation of extracted user content.

⚠️ CRITICAL: When user provides simple add-ons like "Flight, Visa, Insurance", keep them EXACTLY as provided - DO NOT expand to detailed descriptions like "Flight booking assistance, Visa processing support, Travel insurance"

⚠️ ESPECIALLY IMPORTANT: If user writes "upgrades" keep it as "upgrades" - DO NOT expand to "Hotel upgrades, Airport lounge, Private guide"

REQUIRED JSON FORMAT (with field categorization):
{self.sample_format}

DOCUMENT CONTENT TO CONVERT:
{text_content}

CRITICAL RULE: PRESERVE ALL EXTRACTED CONTENT EXACTLY AS FOUND IN DOCUMENT

=== USER-PROVIDED FIELDS (Extract and preserve exactly) ===
Extract these from the document content and preserve exactly as found:

**BASIC FIELDS - EXTRACT AND PRESERVE:**
- title: Extract exact title from document
- package_no: Extract exact package number (DO NOT add destination prefixes)
- destination: Extract exact destination name, only convert to lowercase
- category: Extract categories and convert to lowercase, ensure array format
- activities: Extract activities and convert to lowercase, ensure array format
- owner: Extract exact owner/operator information from document
- type: Extract package type ("Fixed" or "Variable")
- duration: IMPORTANT - Extract duration and fix format to "4N & 5D" style, correct spelling mistakes (e.g., "4Nught 6 day" → "4N & 6D", "(4 Nights & 5 Days)" → "4N & 5D", "4 nights and 5 days" → "4N & 5D")
- price_per_person: Extract exact price format from document
- visa_type: Extract visa information, ensure array format
- about_this_tour: Extract exact description from document
- highlights: Extract exact highlights, ensure array format
- inclusions: Extract exact inclusions, ensure array format
- exclusions: Extract exact exclusions, ensure array format
- itinerary: Extract exact itinerary content, structure as array of objects
- hotels: Extract exact hotel information, ensure array format
- popular_activities: Extract exact activities mentioned by user, ensure array format
- addons: Extract exact add ons provided, ensure array format - DO NOT expand or enhance simple terms like "Flight, Visa, Insurance, upgrades" into detailed descriptions. If user says "upgrades" keep it as "upgrades", do NOT expand to "Hotel upgrades, Airport lounge, Private guide"

**STRUCTURE CONVERSION ONLY:**
- Convert text lists to proper JSON arrays
- Structure itinerary as array of objects with day, title, description
- Fix field name variations (e.g., "included" → "inclusions")
- Map legacy field names (e.g., "Optional" → "addons", "Add Ons" → "addons")

=== AUTO-CALCULATED FIELDS (Compute from extracted data) ===
Calculate these ONLY from the extracted basic fields:

- duration_in_nights: Calculate from extracted duration
- duration_in_days: Calculate from extracted duration
- price: Extract numeric value from extracted price_per_person
- currency: Extract currency code from extracted price_per_person
- best_time_to_visit_months: Extract from document's best time info (if present)
- important_notes: Generate based on extracted destination and visa info

=== LLM SOURCED FIELDS (Generate ONLY if not found in document) ===
Generate these fields ONLY if they are NOT found in the document content:

- best_time_to_visit: Generate only if not mentioned in document
- rating: Generate only if not mentioned in document
- rating_description: Generate only if not mentioned in document
- currency_conversion_rate: Generate only if not mentioned in document
- destination_safety: Generate only if not mentioned in document
- popular_restaurants: Generate only if not mentioned in document
- what_to_shop: Generate only if not mentioned in document
- what_to_pack: Generate only if not mentioned in document
- cultural_info: Generate only if not mentioned in document

**LLM GENERATION RULES:**
- Base ALL generated content on the extracted destination
- Use current, accurate information for the destination
- Ensure currency_conversion_rate reflects real-world rates
- Make ratings realistic (3.5-4.8 range)
- For rating_description field: Generate a brief 2-line description explaining why this package deserves its rating based on the highlights, inclusions, itinerary quality, and destination appeal. Keep it concise and engaging, focusing on key value propositions that justify the rating.
- Only generate if truly not found in document

=== SYSTEM FIELDS (PRESERVE OR SET DEFAULTS) ===
- is_published: Extract from document if mentioned, otherwise set to false (for new packages)
- is_active: Extract from document if mentioned, otherwise set to true

CRITICAL REQUIREMENTS:
1. NEVER modify extracted content - preserve exactly as found in document
2. NEVER add prefixes to package_no (keep "003" as "003", not "DXB003")
3. Only generate LLM fields if they are truly not mentioned in document
4. Convert destination, category, activities to lowercase for consistency
5. Ensure proper JSON array/object structure
6. Extract everything available from document before generating anything
7. PRESERVE is_published AND is_active VALUES IF FOUND IN DOCUMENT
8. PRESERVE SIMPLE ADD-ONS TERMS: If user writes "Flight, Visa, Insurance" keep it as ["Flight", "Visa", "Insurance"] - DO NOT expand to detailed descriptions like "Flight booking assistance". ESPECIALLY: If user writes "upgrades" keep it as "upgrades" - DO NOT expand to "Hotel upgrades, Airport lounge, Private guide"

Return ONLY the complete JSON object with extracted content preserved and missing fields generated. No explanations or additional text.
"""
    
    def _call_deepseek_api(self, prompt: str) -> str:
        """
        Make API call to DeepSeek v3
        
        Args:
            prompt: The prompt to send to DeepSeek
            
        Returns:
            Response text from DeepSeek
        """
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
        
        payload = {
            'model': 'deepseek-chat',  # DeepSeek-V3 model
            'messages': [
                {
                    'role': 'user',
                    'content': prompt
                }
            ],
            'temperature': 0.7  # Updated temperature for v3
        }
        
        try:
            response = requests.post(
                self.api_url,
                headers=headers,
                json=payload,
                timeout=120  # 2 minute timeout for large responses
            )
            
            response.raise_for_status()
            
            response_data = response.json()
            
            # Handle DeepSeek v3 response structure
            if 'choices' not in response_data or not response_data['choices']:
                raise ValidationError("DeepSeek API returned empty response")
            
            # Extract content from the response
            choice = response_data['choices'][0]
            if 'message' not in choice or 'content' not in choice['message']:
                raise ValidationError("Invalid DeepSeek API response structure")
            
            content = choice['message']['content']
            
            return content.strip()
            
        except requests.exceptions.RequestException as e:
            logger.error(f"DeepSeek API request failed: {str(e)}")
            raise ValidationError(f"DeepSeek API request failed: {str(e)}")
        except KeyError as e:
            logger.error(f"Invalid DeepSeek API response format: {str(e)}")
            raise ValidationError(f"Invalid DeepSeek API response format: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error in DeepSeek API call: {str(e)}")
            raise ValidationError(f"DeepSeek API call failed: {str(e)}")
    
    def _extract_json_from_response(self, response_text: str) -> Dict[str, Any]:
        """
        Extract and parse JSON from DeepSeek v3 response
        
        Args:
            response_text: Raw response text from DeepSeek
            
        Returns:
            Parsed JSON data as dictionary
        """
        try:
            # Try to find JSON in the response
            
            # First, try to parse the entire response as JSON
            try:
                return json.loads(response_text)
            except json.JSONDecodeError:
                pass
            
            # Look for JSON within markdown code blocks (```json ... ```)
            if '```json' in response_text:
                start = response_text.find('```json') + 7
                end = response_text.find('```', start)
                if end != -1:
                    json_text = response_text[start:end].strip()
                    return json.loads(json_text)
            
            # Look for JSON within curly braces
            if '{' in response_text and '}' in response_text:
                start = response_text.find('{')
                end = response_text.rfind('}') + 1
                json_text = response_text[start:end]
                return json.loads(json_text)
            
            # If all else fails, raise an error
            raise ValidationError("No valid JSON found in DeepSeek response")
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON from DeepSeek response: {str(e)}")
            logger.error(f"Response text: {response_text}")
            raise ValidationError(f"DeepSeek returned invalid JSON: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error extracting JSON: {str(e)}")
            logger.error(f"Response text: {response_text}")
            raise ValidationError(f"Failed to extract JSON from response: {str(e)}")
    
    def get_errors(self) -> list:
        """Get any errors that occurred during processing"""
        return self.errors
    
    def has_errors(self) -> bool:
        """Check if any errors occurred"""
        return len(self.errors) > 0 
