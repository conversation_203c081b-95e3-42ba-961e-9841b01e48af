"""
AI Description Helper for TripJack Hotels
Handles description generation and rephrasing using Google Generative AI.

Features:
- Generate new descriptions from hotel data.
- Rephrase existing descriptions to improve them.
- Uses Google's native generativeai package with Pydantic for structured output.
- Google Search grounding for accurate and factual information using the latest best practices.
- Comprehensive error handling and logging.

Example usage:
    helper = AIDescriptionHelper()
    description = helper.generate_or_rephrase_description(hotel_instance)
"""

import logging
from typing import Optional, Dict, Any

from pydantic import BaseModel, Field
from google import genai
from google.genai.types import Tool, GoogleSearch, GenerateContentConfig

from zuumm import settings

logger = logging.getLogger(__name__)


class HotelDescriptionResponse(BaseModel):
    """Pydantic model for the AI's structured response, focusing only on the description."""
    description: str = Field(..., description="A compelling and informative hotel description.")


class AIDescriptionHelper:
    """Helper class for generating and rephrasing hotel descriptions using Google Generative AI."""

    def __init__(self, model_name: str = "gemini-2.5-flash-lite"):
        self.api_key = settings.GOOGLE_API_KEY
        if not self.api_key:
            raise ValueError("GOOGLE_API_KEY environment variable not set.")

        # Initialize the client using the new Google GenAI structure
        self.client = genai.Client(api_key=self.api_key)
        self.model_name = model_name
        self.config = self._initialize_config()

    def _initialize_config(self) -> GenerateContentConfig:
        """Initializes and returns the GenerateContentConfig with tools and settings."""
        # Set up grounding tools as per the latest best practices
        grounding_tool = Tool(google_search=GoogleSearch())

        return GenerateContentConfig(
            temperature=1.0,
            max_output_tokens=1024,
            tools=[grounding_tool]
        )

    def generate_or_rephrase_description(self, hotel) -> Optional[str]:
        """
        Main method to generate a new description or rephrase an existing one.

        Args:
            hotel: A hotel data object or instance.

        Returns:
            A generated or rephrased description string, or None if it fails.
        """
        try:
            hotel_info = self._extract_hotel_info(hotel)
            hotel_name = hotel_info.get('name', 'Unknown Hotel')

            if hasattr(hotel, 'description') and hotel.description and hotel.description.strip():
                logger.info(f"Rephrasing existing description for hotel: {hotel_name}")
                prompt = self._build_rephrase_prompt(hotel.description, hotel_info)
            else:
                logger.info(f"Generating new description for hotel: {hotel_name}")
                prompt = self._build_generate_prompt(hotel_info)

            return self._call_ai_with_validation(prompt, hotel_name)
        except Exception as e:
            logger.error(f"Error processing description for hotel: {e}")
            return None

    def _build_generate_prompt(self, hotel_info: Dict[str, Any]) -> str:
        """Builds the prompt for generating a new hotel description."""
        return f"""
        Generate a compelling and informative hotel description (50-80 words) for the following hotel.
        Use both your internal knowledge and Google Search capabilities to provide accurate, appealing information 
        that would help a traveler make a booking decision.

        Hotel Details:
        - Name: {hotel_info['name']}
        - Location: {hotel_info['city']}, {hotel_info['country']}
        - Star Rating: {hotel_info['star_rating']} stars
        - Property Type: {hotel_info['property_type']}
        - Address: {hotel_info['address']}
        - Amenities: {', '.join(hotel_info['amenities']) if hotel_info['amenities'] else 'Not specified'}

        Create a description that:
        1. Highlights the hotel's key features and location benefits.
        2. Mentions notable amenities or services.
        3. Appeals to potential guests and focuses on their experience.
        4. Is factual, professional, and engaging.
        """

    def _build_rephrase_prompt(self, current_description: str, hotel_info: Dict[str, Any]) -> str:
        """Builds the prompt for rephrasing an existing hotel description."""
        return f"""
        Rephrase and improve the following hotel description to be more engaging and informative.
        Keep the core information accurate but enhance the appeal for travelers.

        Current Description: "{current_description}"

        Hotel Context (use this to enhance the description):
        - Name: {hotel_info['name']}
        - Location: {hotel_info['city']}, {hotel_info['country']}
        - Star Rating: {hotel_info['star_rating']} stars
        - Property Type: {hotel_info['property_type']}
        - Amenities: {', '.join(hotel_info['amenities']) if hotel_info['amenities'] else 'Not specified'}

        Guidelines for rephrasing:
        1. Target a word count between 50 and 80 words.
        2. Make it more engaging and appealing to a potential guest.
        3. Maintain factual accuracy and a professional tone.
        4. Focus on guest benefits and the overall experience.
        5. Improve the flow and readability of the text.
        """

    def _call_ai_with_validation(self, prompt: str, hotel_name: str) -> Optional[str]:
        """
        Calls the Google Generative AI API and validates the response using a Pydantic model.
        """
        try:
            # Generate content using the new client structure
            response = self.client.models.generate_content(
                model=self.model_name,
                contents=prompt,
                config=self.config
            )

            # Extract text from response
            text_content = None
            if hasattr(response, 'text') and response.text:
                text_content = response.text.strip()
            elif hasattr(response, 'candidates') and response.candidates:
                candidate = response.candidates[0]
                if hasattr(candidate, 'content') and candidate.content:
                    if hasattr(candidate.content, 'parts') and candidate.content.parts:
                        for part in candidate.content.parts:
                            if hasattr(part, 'text') and part.text:
                                text_content = part.text.strip()
                                break
                    elif hasattr(candidate.content, 'text') and candidate.content.text:
                        text_content = candidate.content.text.strip()

            if not text_content:
                logger.warning(f"Empty response from AI for hotel: {hotel_name}")
                return None

            # Simple validation and return
            if text_content and 20 <= len(text_content) <= 1000:  # Increased max length
                word_count = len(text_content.split())
                logger.info(f"Successfully generated description for '{hotel_name}' ({word_count} words).")
                return text_content
            else:
                length = len(text_content) if text_content else 0
                logger.warning(f"Description validation failed for '{hotel_name}' (length: {length})")
                return None

        except Exception as e:
            logger.error(f"AI API call failed for hotel {hotel_name}: {e}")
            return None

    def _extract_hotel_info(self, hotel) -> Dict[str, Any]:
        """Extracts and normalizes hotel information for AI processing."""
        amenities = getattr(hotel, 'amenities', [])
        if not isinstance(amenities, list):
            amenities = []

        return {
            'name': getattr(hotel, 'name', 'Unknown Hotel'),
            'city': getattr(hotel, 'city_name', 'Unknown City'),
            'country': getattr(hotel, 'country_name', 'Unknown Country'),
            'star_rating': getattr(hotel, 'star_rating', 'Not rated'),
            'property_type': getattr(hotel, 'property_type', 'Hotel'),
            'address': getattr(hotel, 'address_line', 'Address not available'),
            'amenities': amenities,
        }
