import ast
from django.db import transaction
from rest_framework import serializers
from packages.models import (
    Category, 
    CategoryMedia, 
    CustomActivity,
    CustomPackageItinerary,
    Destination, 
    DestinationMedia, 
    Activity, 
    ActivityMedia,
    Package, 
    PackageMedia,
    DestinationFaq,
    PackageHighlight,
    PackageInclusion,
    PackageAddon,
    PackageHotel,
    PackageRestaurant,
    TripjackHotels,
)
from base.utils import destination_best_time_to_visit_display, package_best_time_to_visit_display
from packages.choices import ItineraryDayItemType, PackageTypeChoices
from packages.utils.custom_package_icon_helper import CustomPackageIconHelper
from packages.choices import PackageMediaTypes



class PackageHotelSerializer(serializers.ModelSerializer):
    class Meta:
        model = PackageHotel
        fields = [
            'external_id', 'name', 'address', 'phone', 'website',
            'rating', 'review_count', 'image_urls', 'description', 'amenities'
        ]

    def get_description(self, obj):
        raw_description = obj.description

        if not raw_description:
            return None

        # Check if the description is a string representation of a dictionary.
        try:
            evaluated_data = ast.literal_eval(raw_description)
            if isinstance(evaluated_data, dict):
                return None

            return raw_description
        except (ValueError, SyntaxError):
            return None

class PackageRestaurantSerializer(serializers.ModelSerializer):
    class Meta:
        model = PackageRestaurant
        fields = [
            'external_id', 'name', 'address', 'phone', 'website',
            'rating', 'review_count', 'image_urls', 'description', 'amenities'
        ]

class CategoryMediaSerializer(serializers.ModelSerializer):
    """Serializer for Category Media"""
    
    class Meta:
        model = CategoryMedia
        fields = ['external_id', 'media', 'created_at']
    
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        if instance.media:
            representation['media'] = instance.media.name
        return representation


class DestinationMediaSerializer(serializers.ModelSerializer):
    """Serializer for Destination Media"""
    
    class Meta:
        model = DestinationMedia
        fields = ['external_id', 'media', 'created_at']
    
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        if instance.media:
            representation['media'] = instance.media.name
        return representation


class ActivityMediaSerializer(serializers.ModelSerializer):
    """Serializer for Activity Media"""
    
    class Meta:
        model = ActivityMedia
        fields = ['external_id', 'media', 'created_at']
    
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        if instance.media:
            representation['media'] = instance.media.name
        return representation


class PackageMediaSerializer(serializers.ModelSerializer):
    """Serializer for Package Media"""
    
    class Meta:
        model = PackageMedia
        fields = ['external_id', 'file', 'created_at']
    
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        if instance.file:
            representation['file'] = instance.file.name
        return representation


class PackageHighlightSerializer(serializers.ModelSerializer):
    """Serializer for Package Highlight"""
    
    class Meta:
        model = PackageHighlight
        fields = ['external_id', 'value', 'icon_class',]


class PackageInclusionSerializer(serializers.ModelSerializer):
    """Serializer for Package Inclusion"""
    
    class Meta:
        model = PackageInclusion
        fields = ['external_id', 'value', 'icon_class',]


class PackageAddonSerializer(serializers.ModelSerializer):
    """Serializer for Package Addon"""
    
    class Meta:
        model = PackageAddon
        fields = ['external_id', 'value', 'icon_class',]


class CategoryLandingPageSerializer(serializers.ModelSerializer):
    """Serializer for Category with media"""
    media = CategoryMediaSerializer(many=True, read_only=True)
    
    class Meta:
        model = Category
        fields = [ 'external_id', 'title', 'description', 'media', 'created_at',]


class DestinationLandingPageSerializer(serializers.ModelSerializer):
    """Serializer for Destination with media and starting price"""
    media = DestinationMediaSerializer(many=True, read_only=True)
    starting_price = serializers.DecimalField(
        max_digits=10, 
        decimal_places=2, 
        allow_null=True, 
        read_only=True
    )
    other_packages_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Destination
        fields = [
            'external_id', 'title', 'description', 'is_international', 'is_trending',
            'best_time_to_visit', 'media', 'starting_price', 'explore_order', 'other_packages_count', 'created_at'
        ]
        read_only_fields = ['external_id', 'created_at', 'updated_at', 'starting_price']

    def get_other_packages_count(self, obj):
        """Get the count of other packages for the destination"""
        # TODO: Implement this
        return 100


class CategoryExplorePageSerializer(serializers.ModelSerializer):
    """Serializer for Category with media"""
    media = CategoryMediaSerializer(many=True, read_only=True)
    
    class Meta:
        model = Category
        fields = [ 'external_id', 'title', 'description', 'media', 'created_at',]


class PopularActivityExplorePageSerializer(serializers.ModelSerializer):
    """Serializer for Popular Activity with main display media only"""
    media = serializers.SerializerMethodField()
    
    class Meta:
        model = Activity
        fields = [ 'external_id', 'title', 'description', 'media', 'is_featured', 'explore_order', 'created_at',]
    
    def get_media(self, obj):
        """Get only the media with main_display=True"""
        main_media = obj.media.filter(main_display=True)
        if main_media.exists():
            return ActivityMediaSerializer(main_media, many=True).data

        return None


class DestinationDashboardSerializer(serializers.ModelSerializer):
    """Comprehensive serializer for Destination dashboard API with all required fields"""
    media = DestinationMediaSerializer(many=True, read_only=True)
    starting_price = serializers.DecimalField(
        max_digits=10, 
        decimal_places=2, 
        allow_null=True, 
        read_only=True
    )
    best_time_to_visit_display = serializers.SerializerMethodField()
    best_time_to_visit_months = serializers.SerializerMethodField()
    
    class Meta:
        model = Destination
        fields = [
            'external_id', 'title', 'description', 'is_international', 'is_trending',
            'best_time_to_visit_display', 'best_time_to_visit_months',
            'media', 'starting_price', 'explore_order', 'created_at'
        ]

    def get_best_time_to_visit_display(self, obj):
        """Get display names for best time to visit months in range format"""
        return destination_best_time_to_visit_display(obj)
    
    def get_best_time_to_visit_months(self, obj):
        """Get month names for filtering/processing"""
        if not obj.best_time_to_visit:
            return []
        
        from packages.choices import MonthChoices
        month_names = []
        for month_num in obj.best_time_to_visit:
            try:
                month_choice = MonthChoices(str(month_num))
                month_names.append(month_choice.label)
            except ValueError:
                continue
        return month_names


class PopularDestinationDashboardSerializer(serializers.ModelSerializer):
    """Serializer for Popular Destination dashboard API with all required fields"""
    media = DestinationMediaSerializer(many=True, read_only=True)
    
    class Meta:
        model = Destination
        fields = ['external_id', 'title', 'description', 'is_international', 'media', 'created_at']


class PackageDashboardSerializer(serializers.ModelSerializer):
    """Comprehensive serializer for Package dashboard API with all required fields"""
    # media = PackageMediaSerializer(many=True, read_only=True)
    media = serializers.SerializerMethodField()
    starting_price = serializers.DecimalField(source='price', max_digits=10, decimal_places=2, read_only=True)
    inclusions = serializers.SerializerMethodField()
    
    class Meta:
        model = Package
        fields = [
            'external_id', 
            'title',
            'inclusions',
            'media', 
            'starting_price', 
            'rating',
            'rating_description',
            'duration', 
            'duration_in_nights', 
            'duration_in_days', 
            'explore_order', 
            'created_at'
        ]

    def get_inclusions(self, obj):
        """Get inclusions using separate serializer"""
        inclusions = obj.inclusions.filter(is_active=True)
        return PackageInclusionSerializer(inclusions, many=True).data

    def get_media(self, obj):
        ordered_media = obj.media.filter(is_active=True).order_by('created_at')  # order here
        return PackageMediaSerializer(ordered_media, many=True).data


class DestinationActivityDashboardSerializer(serializers.ModelSerializer):
    """Serializer for Destination Activity dashboard API"""
    media = ActivityMediaSerializer(many=True, read_only=True)
    
    class Meta:
        model = Activity
        fields = ['external_id', 'title', 'description', 'media', 'explore_order', 'created_at']



class VisaOnArrivalDestinationDashboardSerializer(serializers.ModelSerializer):
    """Serializer for Visa on arrival destination dashboard"""
    media = DestinationMediaSerializer(many=True, read_only=True)
    has_visa_on_arrival = serializers.BooleanField(read_only=True)
    
    class Meta:
        model = Destination
        fields = ['external_id', 'title', 'media', 'has_visa_on_arrival', 'created_at']


class PackageCategorySerializer(serializers.ModelSerializer):
    """Serializer for Package Categories - just titles"""
    
    class Meta:
        model = Category
        fields = ['title']


class PackageActivitySerializer(serializers.ModelSerializer):
    """Serializer for Package Activities with main display image"""
    media = serializers.SerializerMethodField()
    
    class Meta:
        model = Activity
        fields = ['external_id', 'title', 'media']
    
    def get_media(self, obj):
        """Get main display image for activity, fallback to first image if no main display"""
        # First try to get main display media
        main_media = obj.media.filter(main_display=True, is_active=True)
        if main_media.exists():
            return ActivityMediaSerializer(main_media, many=True).data
        
        # Fallback to first available media if no main display
        first_media = obj.media.filter(is_active=True).first()
        if first_media:
            return ActivityMediaSerializer([first_media], many=True).data
        
        return None


class PackageBasicDetailsSerializer(serializers.ModelSerializer):
    """Serializer for Package Basic Details API - includes all basic info, gallery, highlights, inclusions/exclusions"""
    media = serializers.SerializerMethodField()
    destination = serializers.SerializerMethodField()
    categories = serializers.SerializerMethodField()
    activities = serializers.SerializerMethodField()
    best_time_to_visit_display = serializers.SerializerMethodField()
    highlights = serializers.SerializerMethodField()
    inclusions = serializers.SerializerMethodField()
    restaurants = serializers.SerializerMethodField()
    
    class Meta:
        model = Package
        fields = [
            'external_id', 
            'destination', 
            'title', 
            'package_no', 
            'rating',
            'rating_description',
            'media', 
            'owner', 
            'type', 
            'visa_type', 
            'best_time_to_visit_display', 
            'best_time_to_visit_months',
            'best_time_to_visit',
            'duration', 
            'duration_in_days', 
            'duration_in_nights', 
            'price',
            'price_per_person', 
            'about_this_tour', 
            'highlights', 
            'inclusions', 
            'exclusions',
            'popular_restaurants',
            'restaurants',
            'categories',
            'activities', 
            'created_at'
        ]
    
    def get_destination(self, obj):
        """Get destination details"""
        return obj.destination.title if obj.destination else None
    
    def get_categories(self, obj):
        """Get categories using separate serializer - just titles"""
        categories = [pc.category for pc in obj.package_categories.select_related('category').filter(is_active=True)]
        return [category.title for category in categories]
    
    def get_activities(self, obj):
        """Get activities using separate serializer with main display image"""
        activities = [pa.activity for pa in obj.package_activities.select_related('activity').filter(is_active=True)]
        return PackageActivitySerializer(activities, many=True).data
    
    def get_best_time_to_visit_display(self, obj):
        """Get best time to visit using utility function"""
        return package_best_time_to_visit_display(obj)

    def get_inclusions(self, obj):
        """Get inclusions using separate serializer"""
        inclusions = obj.inclusions.filter(is_active=True)
        return PackageInclusionSerializer(inclusions, many=True).data

    def get_highlights(self, obj):
        """Get highlights using separate serializer"""
        highlights = obj.highlights.filter(is_active=True)
        return PackageHighlightSerializer(highlights, many=True).data

    def get_restaurants(self, obj):
        """Get restaurants using separate serializer"""
        return PackageRestaurantSerializer(
            obj.package_restaurants.filter(
                is_active=True,
                packagerestaurantrelation__deleted_at__isnull=True,
                packagerestaurantrelation__is_active=True
            ),
            many=True
        ).data

    def get_media(self, obj):
        ordered_media = obj.media.filter(is_active=True).order_by('created_at')  # order here
        return PackageMediaSerializer(ordered_media, many=True).data


class PackageItenaryDetailsSerializer(serializers.ModelSerializer):
    """Serializer for Package Itinerary Details API - includes itinerary, hotels, add-ons, and destination info"""
    addons = serializers.SerializerMethodField()
    hotels = serializers.SerializerMethodField()
    itinerary = serializers.SerializerMethodField()

    class Meta:
        model = Package
        fields = [
            'external_id',
            'type',
            'itinerary',
            'hotels',
            'addons',
            'best_time_to_visit',
            'destination_safety',
            'cultural_info',
            'what_to_shop',
            'what_to_pack',
            'important_notes',
            'created_at'
        ]

    def get_addons(self, obj):
        """Get add-ons using separate serializer"""
        addons = obj.addons.filter(is_active=True)
        return PackageAddonSerializer(addons, many=True).data

    def get_hotels(self, obj):
        """Get hotels using separate serializer"""
        return PackageHotelSerializer(
            obj.package_hotels.filter(
                is_active=True,
                packagehotelrelation__deleted_at__isnull=True,
                packagehotelrelation__is_active=True
            ),
            many=True
        ).data

    def get_itinerary(self, obj):
        """Get itinerary using separate serializer"""
        # Return itinerary for custom packages using related itineraries
        if obj.type in PackageTypeChoices.custom_package_choices():
            from packages.utils.serializer_utils import get_itinerary_data
            itineraries = obj.itineraries.filter(is_active=True)
            itinerary_data = get_itinerary_data(itineraries)
            return itinerary_data
        
        # Return itinerary for non-custom packages using itinerary field
        return obj.itinerary


class DestinationFaqSerializer(serializers.ModelSerializer):
    """Serializer for Destination FAQ"""
    
    class Meta:
        model = DestinationFaq
        fields = [
            'external_id',
            'question',
            'answer', 
            'priority',
            'created_at'
        ]


class PackageDestinationFaqSerializer(serializers.ModelSerializer):
    """Serializer for Package Destination FAQ API - returns destination info and FAQs"""
    destination_title = serializers.SerializerMethodField()
    destination_external_id = serializers.SerializerMethodField()
    faqs = serializers.SerializerMethodField()
    
    class Meta:
        model = Package
        fields = [
            'external_id',
            'destination_title',
            'destination_external_id', 
            'faqs',
            'created_at'
        ]
    
    def get_destination_title(self, obj):
        """Get destination title"""
        return obj.destination.title if obj.destination else None
    
    def get_destination_external_id(self, obj):
        """Get destination external_id"""
        return obj.destination.external_id if obj.destination else None
    
    def get_faqs(self, obj):
        """Get published FAQs for the destination ordered by priority"""
        if not obj.destination:
            return []
        
        faqs = obj.destination.faqs.filter(
            is_published=True,
            is_active=True
        ).order_by('priority', '-created_at')
        
        return DestinationFaqSerializer(faqs, many=True).data


# ========== CUSTOM PACKAGE CREATION SERIALIZERS ==========

class ItineraryHotelDataSerializer(serializers.Serializer):
    """Serializer for hotel data in itinerary day items"""
    hotel = serializers.IntegerField()  # Fixed: was hotel_id
    check_in_time = serializers.TimeField(required=False, allow_null=True)
    check_out_time = serializers.TimeField(required=False, allow_null=True)
    meta_information = serializers.JSONField(required=False, default=dict)


class ItineraryActivityDataSerializer(serializers.Serializer):
    """Serializer for activity data in itinerary day items"""
    activity = serializers.IntegerField()  # Fixed: was activity_id
    start_time = serializers.TimeField(required=False, allow_null=True)
    end_time = serializers.TimeField(required=False, allow_null=True)
    meta_information = serializers.JSONField(required=False, default=dict)


class ItineraryDayItemSerializer(serializers.Serializer):
    """Serializer for individual day items within an itinerary"""
    type = serializers.ChoiceField(choices=ItineraryDayItemType.choices)  # Fixed: use proper choices
    title = serializers.CharField(max_length=255, required=False, allow_blank=True)
    description = serializers.CharField()
    order = serializers.IntegerField()
    duration = serializers.CharField(max_length=255, required=False, allow_blank=True)
    inclusions = serializers.ListField(
        child=serializers.CharField(max_length=500),
        required=False,
        default=list
    )
    meta_information = serializers.JSONField(required=False, default=dict)
    # hotel_data = ItineraryHotelDataSerializer(required=False, allow_null=True)
    # activity_data = ItineraryActivityDataSerializer(required=False, allow_null=True)
    
    def validate(self, data):
        """Validate that hotel_data is provided for hotel type and activity_data for activity type"""
        item_type = data.get('type')
        hotel_data = data.get('hotel_data')
        activity_data = data.get('activity_data')
        
        if item_type == ItineraryDayItemType.HOTEL and not hotel_data:
            raise serializers.ValidationError("hotel_data is required for Hotel type items")
        
        if item_type == ItineraryDayItemType.ACTIVITY and not activity_data:
            raise serializers.ValidationError("activity_data is required for Activity type items")
        
        if item_type not in [ItineraryDayItemType.HOTEL, ItineraryDayItemType.ACTIVITY] and (hotel_data or activity_data):
            raise serializers.ValidationError("hotel_data and activity_data are only allowed for Hotel and Activity types")
        
        return data


class ItinerarySerializer(serializers.Serializer):
    """Serializer for itinerary within a custom package"""
    day_number = serializers.IntegerField()
    date = serializers.DateField(required=False, allow_null=True)
    day_title = serializers.CharField(max_length=255)
    description = serializers.CharField()
    order = serializers.IntegerField()
    inclusions = serializers.ListField(
        child=serializers.CharField(max_length=500),
        required=False,
        default=list
    )
    meta_information = serializers.JSONField(required=False, default=dict)
    # day_items = ItineraryDayItemSerializer(many=True)
    
    def validate_day_items(self, value):
        """Validate that at least one day item is provided"""
        if not value or len(value) == 0:
            raise serializers.ValidationError("At least one day item is required per itinerary")
        return value


class PackageMediaSerializer(serializers.Serializer):
    """Serializer for package media that accepts both file uploads and file paths"""
    title = serializers.CharField(max_length=255, required=False, allow_blank=True)
    file_type = serializers.ChoiceField(choices=PackageMediaTypes.choices)
    file = serializers.CharField()  # Fixed: Accept file paths as strings
    
    def validate_file_type(self, value):
        """Ensure file_type matches PackageMediaTypes choices"""
        if value not in [choice[0] for choice in PackageMediaTypes.choices]:
            # Try lowercase
            value_lower = value.lower()
            if value_lower in [choice[0] for choice in PackageMediaTypes.choices]:
                return value_lower
        return value


class CustomPackageCreateSerializer(serializers.ModelSerializer):
    """ModelSerializer for creating custom packages with helper integration"""
    
    # Override the reverse FK fields to accept arrays of strings
    highlights = serializers.ListField(
        child=serializers.CharField(max_length=500),
        required=True,
        help_text='Package highlights (array of strings)'
    )
    
    inclusions = serializers.ListField(
        child=serializers.CharField(max_length=500),
        required=True,
        help_text='What is included in the package (array of strings)'
    )
    
    addons = serializers.ListField(
        child=serializers.CharField(max_length=500),
        required=False,
        help_text='Add On services (array of strings)'
    )
    
    # Nested serializers for complex data
    itineraries = serializers.ListField(
        child=serializers.DictField(),
        required=False,
        help_text='List of itinerary items with day_number, type, activity_id or hotel_id'
    )
    package_media = PackageMediaSerializer(many=True, required=False)
    
    class Meta:
        model = Package
        fields = [
            'title', 'package_no', 'destination', 'duration', 'price_per_person',
            'currency_conversion_rate', 'owner', 'about_this_tour', 'highlights',
            'inclusions', 'exclusions', 'addons', 'visa_type', 'best_time_to_visit',
            'destination_safety', 'rating', 'rating_description', 'hotels',
            'popular_restaurants', 'popular_activities', 'cultural_info',
            'what_to_shop', 'what_to_pack', 'important_notes', 'explore_order',
            'is_published', 'is_active', 'itineraries', 'package_media'
        ]
    
    def validate(self, attrs):
        """Validate data and prepare for creation"""
        attrs["partner"] = self.context.get("partner")
        attrs["type"] = PackageTypeChoices.CUSTOM_AI.value  # Set API type
        return attrs
    
    def create(self, validated_data):
        """Create custom package with simplified structure"""

        # Extract related data
        highlights_data = validated_data.pop('highlights', [])
        inclusions_data = validated_data.pop('inclusions', [])
        addons_data = validated_data.pop('addons', [])
        itineraries_data = validated_data.pop('itineraries', [])
        package_media_data = validated_data.pop('package_media', [])
        
        with transaction.atomic():
            # Create the main package
            package = Package.objects.create(**validated_data)
            
            try:
                icon_helper = CustomPackageIconHelper()
                icon_data = icon_helper.generate_icons_for_package_data(
                    highlights_data, inclusions_data, addons_data
                )
                
                for highlight_data in icon_data['highlights']:
                    PackageHighlight.objects.create(
                        package=package,
                        value=highlight_data['value'],
                        icon_class=highlight_data['icon_class']
                    )
                
                for inclusion_data in icon_data['inclusions']:
                    PackageInclusion.objects.create(
                        package=package,
                        value=inclusion_data['value'],
                        icon_class=inclusion_data['icon_class']
                    )
                
                for addon_data in icon_data['addons']:
                    PackageAddon.objects.create(
                        package=package,
                        value=addon_data['value'],
                        icon_class=addon_data['icon_class']
                    )
                    
            except Exception:
                for highlight_text in highlights_data:
                    if highlight_text.strip():
                        PackageHighlight.objects.create(
                            package=package,
                            value=highlight_text.strip(),
                            icon_class='activity'  # Default fallback
                        )

                for inclusion_text in inclusions_data:
                    if inclusion_text.strip():
                        PackageInclusion.objects.create(
                            package=package,
                            value=inclusion_text.strip(),
                            icon_class='breakfast'  # Default fallback
                        )

                for addon_text in addons_data:
                    if addon_text.strip():
                        PackageAddon.objects.create(
                            package=package,
                            value=addon_text.strip(),
                            icon_class='activity'  # Default fallback
                        )

            for itinerary_item in itineraries_data:
                day_number = itinerary_item.get('day_number')
                item_type = itinerary_item.get('type')

                if not day_number or not item_type:
                    continue

                itinerary_obj = CustomPackageItinerary(
                    package=package,
                    day_number=day_number,
                    type=item_type
                )

                if item_type == ItineraryDayItemType.ACTIVITY:
                    activity_id = itinerary_item.get('activity_id')
                    if activity_id:
                        try:
                            activity = CustomActivity.objects.get(id=activity_id, is_active=True)
                            itinerary_obj.activity = activity
                        except CustomActivity.DoesNotExist:
                            continue
                elif item_type == ItineraryDayItemType.HOTEL:
                    hotel_id = itinerary_item.get('hotel_id')
                    if hotel_id:
                        try:
                            hotel = TripjackHotels.objects.get(id=hotel_id, is_active=True)
                            itinerary_obj.hotel = hotel
                        except TripjackHotels.DoesNotExist:
                            continue

                itinerary_obj.save()

        return package
