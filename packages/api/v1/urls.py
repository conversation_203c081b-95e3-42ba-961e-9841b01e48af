from django.urls import path
from packages.api.v1 import views


urlpatterns = [
    # Landing Page APIs

    # Landing Page Category APIs
    path(
        "landing/categories/", 
        views.CategoryLandingPageView.as_view(),
        name="categories"
    ),
    # Landing Page Destination APIs
    path(
        "landing/destinations/", 
        views.DestinationLandingPageView.as_view(),
        name="destinations"
    ),

    # NOTE:Explore Page APIs

    # Dashboard Dropdown APIs
    path(
        "dashboard/dropdowns/", 
        views.DashboardDropdownView.as_view(),
        name="dashboard-dropdowns"
    ),

    # NOTE: Page 1 API's
    path(
        "explore/categories/", 
        views.CategoryExplorePageView.as_view(),
        name="explore-categories"
    ),
    # Explore Page Popular Destinations
    path(
        "explore/popular-activities/", 
        views.PopularActivityExplorePageView.as_view(),
        name="explore-destinations"
    ),
    # Explore Page Trending Destinations 
    path(
        "explore/trending-destinations/", 
        views.TrendingDestinationExplorePageView.as_view(),
        name="explore-destinations"
    ),

    # NOTE: Page 2 API's
    path(
        "dashboard/destinations/", 
        views.DestinationDashboardView.as_view(),
        name="dashboard-destinations"
    ), 
    path(
        "dashboard/popular-destinations/", 
        views.PopularDestinationDashboardView.as_view(),
        name="dashboard-popular-destinations"
    ), 
    path(
        "dashboard/visa-on-arrival-destinations/", 
        views.VisaOnArrivalDestinationDashboardView.as_view(),
        name="dashboard-visa-on-arrival-destinations"
    ),

    # NOTE: Page 3 API's
    path(
        "dashboard/packages/", 
        views.PackageDashboardView.as_view(),
        name="dashboard-packages"
    ),
    path(
        "dashboard/destination-activities/", 
        views.DestinationActivityDashboardView.as_view(),
        name="dashboard-destination-activities"
    ),

    # NOTE: Page 4 API's
    path(
        "package/<external_id>/basic-details/", 
        views.PackageBasicDetailsView.as_view(),
        name="package-basic-details"
    ),
    path(
        "package/<external_id>/itinerary-details/", 
        views.PackageItineraryDetailsView.as_view(),
        name="package-itinerary-details"
    ),
    path(
        "package/<external_id>/destination-faq/", 
        views.PackageDestinationFaqView.as_view(),
        name="package-destination-faq"
    ),

    # Package PDF
    path(
        "package/<external_id>/generate-pdf/", 
        views.PackagePDFGenerationView.as_view(),
        name="package-generate-pdf"
    ),

    # NOTE: Redirection from Page 1
    path(
        "activity-gallery/<external_id>/", 
        views.ActivityGalleryView.as_view(),
        name="activity-gallery"
    ),

    # CustomPackage API routes
    path(
        'custom-package-ai/',
        views.CustomPackageView.as_view(),
        name='custom-package-ai'
    )
]
