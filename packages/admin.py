from django.contrib import admin
from django import forms
from decimal import Decimal, ROUND_HALF_UP
from django.utils.html import format_html
from django.contrib.gis import admin as gis_admin
from django.contrib.gis.admin import GISModelAdmin
from packages.models import (
    PackageUploader, Package, PackageMedia, Category, Destination, Activity,
    CategoryMedia, DestinationMedia, ActivityMedia,
    PackageCategory, PackageActivity, DestinationCategory,
    DestinationActivity, DestinationFaq, CustomActivity,
    CustomActivityMedia, CustomActivityCategory, CustomActivityLocation,
    CustomActivityCategoryRelation, CustomActivityLocationRelation, CustomActivitySystemCategoryRelation,
    TripjackHotels, TripjackHotelDestinationMapping, CustomPackage, CustomPackageItinerary
)
import ast
from datetime import datetime
from base.static import Constants
from accounts.choices import PackageTypeChoices as AccountPackageTypeChoices
from accounts.choices import UserTypeChoices
from packages.choices import PackageTypeChoices
from packages.forms import (
    PackageUploaderForm, DestinationAdminForm, CategoryAdminForm, ActivityAdminForm, 
    PackageAdminForm, DestinationFaqAdminForm, ActivityMediaAdminForm, CustomActivityAdminForm,
    TripjackHotelsAdminForm, CustomPackageAdminForm, CustomPackageItineraryForm
)
from django_better_admin_arrayfield.admin.mixins import DynamicArrayMixin
from packages.services.package_creation_service import PackageCreationService
from base.admin_filters import (
    ActiveStatusFilter, 
    InternationalDestinationFilter, 
    TrendingDestinationFilter, 
    FeaturedActivityFilter,
    PublishStatusFilter,
    TripjackHotelDestinationFilter
)
from django.urls import reverse
from django import forms
import logging
import re
import decimal

logger = logging.getLogger(__name__)


# Helper function to get user's effective partner
def get_user_effective_partner(request):
    """Get the effective partner for the user - more efficient than multiple queries"""
    # Check if user is authenticated to prevent AnonymousUser errors
    if not request.user.is_authenticated:
        return None
        
    if request.user.is_superuser:
        # For superadmins, return ZUUMM partner (should be cached in middleware)
        return getattr(request, 'zuumm_partner', None) or request.user.partner
    else:
        # For partner admins, return their own partner
        return request.user.partner


def user_can_manage_content(request):
    """Check if user can manage Category/Destination/Activity based on package_type"""
    # Check if user is authenticated to prevent AnonymousUser errors
    if not request.user.is_authenticated:
        return False
        
    if request.user.is_superuser:
        return True
    
    if request.user.user_type == UserTypeChoices.PARTNER_ADMIN.value:
        user_partner = request.user.partner
        if user_partner and user_partner.package_type == AccountPackageTypeChoices.OWN_PACKAGE.value:
            return True
    
    return False


def user_can_manage_packages(request):
    """Check if user can manage Packages based on package_type"""
    # Check if user is authenticated to prevent AnonymousUser errors
    if not request.user.is_authenticated:
        return False
        
    if request.user.is_superuser:
        return True
    
    if request.user.user_type == UserTypeChoices.PARTNER_ADMIN.value:
        user_partner = request.user.partner
        if user_partner and user_partner.package_type == AccountPackageTypeChoices.OWN_PACKAGE.value:
            return True
    
    return False


# Inline admins for M2M relationships  
class DestinationCategoryInline(admin.TabularInline):
    """Inline to add categories to destinations"""
    model = DestinationCategory
    extra = 1
    show_change_link = False  # Hide pencil/eye/plus icons
    autocomplete_fields = ['category']
    fields = ['destination', 'category']
    exclude = ['deleted_at', 'restored_at', 'is_active', 'external_id', 'created_at', 'updated_at']

    def get_formset(self, request, obj=None, **kwargs):
        formset = super().get_formset(request, obj, **kwargs)
        for field_name in self.autocomplete_fields:
            widget = formset.form.base_fields[field_name].widget
            widget.can_add_related = False
            widget.can_change_related = False
            widget.can_view_related = False
        return formset

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """Filter categories by user's effective partner and is_active=True"""
        if db_field.name == "category":
            effective_partner = get_user_effective_partner(request)
            if effective_partner:
                kwargs["queryset"] = Category.objects.filter(partner=effective_partner, is_active=True)
            else:
                kwargs["queryset"] = Category.objects.none()
        return super().formfield_for_foreignkey(db_field, request, **kwargs)
    
    def has_add_permission(self, request, obj=None):
        """Allow adding M2M relationships if user can manage content"""
        return user_can_manage_content(request)
    
    def has_change_permission(self, request, obj=None):
        """Allow changing M2M relationships if user can manage content"""
        return user_can_manage_content(request)
    
    def has_delete_permission(self, request, obj=None):
        """Allow deleting M2M relationships if user can manage content"""
        return user_can_manage_content(request)
    
    def has_view_permission(self, request, obj=None):
        """Allow viewing M2M relationships if user can manage content"""
        return user_can_manage_content(request)


class DestinationActivityInline(admin.TabularInline):
    """Inline to add activities to destinations"""
    model = DestinationActivity
    extra = 1
    show_change_link = False  # Hide pencil/eye/plus icons
    autocomplete_fields = ['activity']
    fields = ['destination', 'activity']
    exclude = ['deleted_at', 'restored_at', 'is_active', 'external_id', 'created_at', 'updated_at']

    def get_formset(self, request, obj=None, **kwargs):
        formset = super().get_formset(request, obj, **kwargs)
        for field_name in self.autocomplete_fields:
            widget = formset.form.base_fields[field_name].widget
            widget.can_add_related = False
            widget.can_change_related = False
            widget.can_view_related = False
        return formset

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """Filter activities by user's effective partner and is_active=True"""
        if db_field.name == "activity":
            effective_partner = get_user_effective_partner(request)
            if effective_partner:
                kwargs["queryset"] = Activity.objects.filter(partner=effective_partner, is_active=True)
            else:
                kwargs["queryset"] = Activity.objects.none()
        return super().formfield_for_foreignkey(db_field, request, **kwargs)
    
    def has_add_permission(self, request, obj=None):
        """Allow adding M2M relationships if user can manage content"""
        return user_can_manage_content(request)
    
    def has_change_permission(self, request, obj=None):
        """Allow changing M2M relationships if user can manage content"""
        return user_can_manage_content(request)
    
    def has_delete_permission(self, request, obj=None):
        """Allow deleting M2M relationships if user can manage content"""
        return user_can_manage_content(request)
    
    def has_view_permission(self, request, obj=None):
        """Allow viewing M2M relationships if user can manage content"""
        return user_can_manage_content(request)


# Media inlines
class CategoryMediaInline(admin.StackedInline):
    """Inline admin for CategoryMedia - one media per category"""
    model = CategoryMedia
    extra = 1
    show_change_link = False  # Hide pencil/eye/plus icons
    max_num = Constants.CATEGORY_MAX_MEDIA_COUNT
    fields = ('media',)
    can_delete = False
    
    def get_formset(self, request, obj=None, **kwargs):
        """Customize formset to make media field required and add validation"""
        formset = super().get_formset(request, obj, **kwargs)
        formset.form.base_fields['media'].required = True
        formset.form.base_fields['media'].help_text = "Media file is required (jpg, jpeg, png, webp)"
        
        # Override formset clean method to validate at least one media
        original_clean = formset.clean
        
        def clean_with_media_validation(self):
            if hasattr(original_clean, '__call__'):
                original_clean(self)
            
            # Check if at least one valid media file is provided
            valid_media_count = 0
            for form in self.forms:
                if not form.cleaned_data.get('DELETE', False) and form.cleaned_data.get('media'):
                    valid_media_count += 1
            
            if valid_media_count == 0:
                from django.core.exceptions import ValidationError
                raise ValidationError("At least one media file is required for this category. Please upload an image.")
        
        formset.clean = clean_with_media_validation
        return formset
    
    def get_extra(self, request, obj=None, **kwargs):
        """Only show extra form if no media exists"""
        if obj and obj.media.exists():
            return 0
        return 1
    
    def has_add_permission(self, request, obj=None):
        """Allow adding media if user can manage content"""
        return user_can_manage_content(request)
    
    def has_change_permission(self, request, obj=None):
        """Allow changing media if user can manage content"""
        return user_can_manage_content(request)
    
    def has_delete_permission(self, request, obj=None):
        """Allow deleting media if user can manage content"""
        return user_can_manage_content(request)
    
    def has_view_permission(self, request, obj=None):
        """Allow viewing media if user can manage content"""
        return user_can_manage_content(request)


class DestinationMediaInline(admin.StackedInline):
    """Inline admin for DestinationMedia - one media per destination"""
    model = DestinationMedia
    extra = 1
    show_change_link = False  # Hide pencil/eye/plus icons
    max_num = Constants.DESTINATION_MAX_MEDIA_COUNT 
    fields = ('media',)
    can_delete = False
    
    def get_formset(self, request, obj=None, **kwargs):
        """Customize formset to make media field required and add validation"""
        kwargs['can_delete'] = False
        formset = super().get_formset(request, obj, **kwargs)
        formset.form.base_fields['media'].required = True
        formset.form.base_fields['media'].help_text = "Media file is required (jpg, jpeg, png, webp)"
        
        # Override formset clean method to validate at least one media
        original_clean = formset.clean
        
        def clean_with_media_validation(self):
            if hasattr(original_clean, '__call__'):
                original_clean(self)
            
            # Check if at least one valid media file is provided
            valid_media_count = 0
            for form in self.forms:
                if not form.cleaned_data.get('DELETE', False) and form.cleaned_data.get('media'):
                    valid_media_count += 1
            
            if valid_media_count == 0:
                from django.core.exceptions import ValidationError
                raise ValidationError("At least one media file is required for this destination. Please upload an image.")
        
        formset.clean = clean_with_media_validation
        return formset
    
    def get_extra(self, request, obj=None, **kwargs):
        """Only show extra form if no media exists"""
        if obj and obj.media.exists():
            return 0
        return 1
    
    def has_add_permission(self, request, obj=None):
        """Allow adding media if user can manage content"""
        return user_can_manage_content(request)
    
    def has_change_permission(self, request, obj=None):
        """Allow changing media if user can manage content"""
        return user_can_manage_content(request)
    
    def has_delete_permission(self, request, obj=None):
        """Allow deleting media if user can manage content"""
        return user_can_manage_content(request)
    
    def has_view_permission(self, request, obj=None):
        """Allow viewing media if user can manage content"""
        return user_can_manage_content(request)
    
    def get_formset(self, request, obj=None, **kwargs):
        """Customize formset to make media field required and add validation"""
        kwargs['can_delete'] = False
        formset = super().get_formset(request, obj, **kwargs)
        formset.form.base_fields['media'].required = True
        formset.form.base_fields['media'].help_text = "Media file is required (jpg, jpeg, png, webp)"
        
        # Override formset clean method to validate at least one media
        original_clean = formset.clean
        
        def clean_with_media_validation(self):
            if hasattr(original_clean, '__call__'):
                original_clean(self)
            
            # Check if at least one valid media file is provided
            valid_media_count = 0
            for form in self.forms:
                if not form.cleaned_data.get('DELETE', False) and form.cleaned_data.get('media'):
                    valid_media_count += 1
            
            if valid_media_count == 0:
                from django.core.exceptions import ValidationError
                raise ValidationError("At least one media file is required for this destination. Please upload an image.")
        
        formset.clean = clean_with_media_validation
        return formset
    
    def get_extra(self, request, obj=None, **kwargs):
        """Only show extra form if no media exists"""
        if obj and obj.media.exists():
            return 0
        return 1
    
    def has_add_permission(self, request, obj=None):
        """Allow adding media if user can manage content"""
        return user_can_manage_content(request)
    
    def has_change_permission(self, request, obj=None):
        """Allow changing media if user can manage content"""
        return user_can_manage_content(request)
    
    def has_delete_permission(self, request, obj=None):
        """Allow deleting media if user can manage content"""
        return user_can_manage_content(request)
    
    def has_view_permission(self, request, obj=None):
        """Allow viewing media if user can manage content"""
        return user_can_manage_content(request)


class ActivityMediaInline(admin.TabularInline):
    """Inline admin for ActivityMedia - multiple media files per activity (up to 10)"""
    model = ActivityMedia
    form = ActivityMediaAdminForm
    extra = 1
    show_change_link = False  # Hide pencil/eye/plus icons
    max_num = Constants.ACTIVITY_MAX_MEDIA_COUNT
    fields = ('media', 'main_display')
    
    def get_formset(self, request, obj=None, **kwargs):
        """Customize formset to make media field required and add validation"""
        formset = super().get_formset(request, obj, **kwargs)
        formset.form.base_fields['media'].required = True
        formset.form.base_fields['media'].help_text = "Media file is required (jpg, jpeg, png, webp)"
        formset.form.base_fields['main_display'].help_text = "Check to set as main display image (only one allowed per activity)"
        
        # Override formset clean method to validate at least one media and main_display logic
        original_clean = formset.clean
        
        def clean_with_media_validation(self):
            if hasattr(original_clean, '__call__'):
                original_clean(self)
            
            # Check if at least one valid media file is provided
            valid_media_count = 0
            main_display_count = 0
            
            for form in self.forms:
                if not form.cleaned_data.get('DELETE', False):
                    if form.cleaned_data.get('media'):
                        valid_media_count += 1
                    if form.cleaned_data.get('main_display'):
                        main_display_count += 1
            
            if valid_media_count == 0:
                from django.core.exceptions import ValidationError
                raise ValidationError("At least one media file is required for this activity. Please upload an image.")
            
            if main_display_count > 1:
                from django.core.exceptions import ValidationError
                raise ValidationError("Only one media file can be set as main display per activity.")
        
        formset.clean = clean_with_media_validation
        return formset
    
    def get_extra(self, request, obj=None, **kwargs):
        """Show extra forms based on current media count"""
        if obj and obj.media.count() >= Constants.ACTIVITY_MAX_MEDIA_COUNT:
            return 0
        return 1
    
    def has_add_permission(self, request, obj=None):
        """Allow adding media if user can manage content"""
        return user_can_manage_content(request)
    
    def has_change_permission(self, request, obj=None):
        """Allow changing media if user can manage content"""
        return user_can_manage_content(request)
    
    def has_delete_permission(self, request, obj=None):
        """Allow deleting media if user can manage content"""
        return user_can_manage_content(request)
    
    def has_view_permission(self, request, obj=None):
        """Allow viewing media if user can manage content"""
        return user_can_manage_content(request)


class PackageMediaInline(admin.TabularInline):
    """Inline admin for PackageMedia to add media directly to packages"""
    model = PackageMedia
    extra = 1
    show_change_link = False  # Hide pencil/eye/plus icons
    max_num = Constants.PACKAGE_MAX_MEDIA_COUNT
    fields = ('file',)

    def get_formset(self, request, obj=None, **kwargs):
        """Customize formset to make media field required and add validation"""
        formset = super().get_formset(request, obj, **kwargs)
        formset.form.base_fields['file'].required = True
        formset.form.base_fields['file'].help_text = "Image file is required (jpg, jpeg, png, webp)"

        # Customize file field error messages
        formset.form.base_fields['file'].error_messages = {
            'required': 'Please select an image file to upload.',
            'invalid': 'Please upload a valid image file.',
        }

        # Override formset clean method to validate at least one media
        original_clean = formset.clean

        def clean_with_media_validation(self):
            # Only run original clean if no forms have file uploads
            has_uploaded_files = any(
                form.cleaned_data.get('file') and not form.cleaned_data.get('DELETE', False)
                for form in self.forms
                if hasattr(form, 'cleaned_data') and form.cleaned_data
            )

            if not has_uploaded_files:
                if hasattr(original_clean, '__call__'):
                    original_clean(self)

                # Check if at least one valid media file is provided
                valid_media_count = 0
                for form in self.forms:
                    if (hasattr(form, 'cleaned_data') and form.cleaned_data and
                        not form.cleaned_data.get('DELETE', False) and
                        form.cleaned_data.get('file')):
                        valid_media_count += 1

                if valid_media_count == 0:
                    from django.core.exceptions import ValidationError
                    raise ValidationError("At least one image file is required for this package.")

        formset.clean = clean_with_media_validation
        return formset

    def get_extra(self, request, obj=None, **kwargs):
        """Only show extra form if less than max media exists"""
        if obj and obj.media.count() >= Constants.PACKAGE_MAX_MEDIA_COUNT:
            return 0
        return 1

    def has_add_permission(self, request, obj=None):
        """Allow adding media if user can manage packages"""
        return user_can_manage_packages(request)
    
    def has_change_permission(self, request, obj=None):
        """Allow changing media if user can manage packages"""
        return user_can_manage_packages(request)
    
    def has_delete_permission(self, request, obj=None):
        """Allow deleting media if user can manage packages"""
        return user_can_manage_packages(request)
    
    def has_view_permission(self, request, obj=None):
        """Allow viewing media if user can manage packages"""
        return user_can_manage_packages(request)


class PackageCategoryInline(admin.TabularInline):
    """Inline to add categories to packages"""
    model = PackageCategory
    extra = 1
    show_change_link = False  # Hide pencil/eye/plus icons
    autocomplete_fields = ['category']
    fields = ['package', 'category']
    exclude = ['deleted_at', 'restored_at', 'is_active', 'external_id', 'created_at', 'updated_at']

    def get_formset(self, request, obj=None, **kwargs):
        formset = super().get_formset(request, obj, **kwargs)
        for field_name in self.autocomplete_fields:
            widget = formset.form.base_fields[field_name].widget
            widget.can_add_related = False
            widget.can_change_related = False
            widget.can_view_related = False
        return formset

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """Filter categories by user's effective partner and is_active=True"""
        if db_field.name == "category":
            effective_partner = get_user_effective_partner(request)
            if effective_partner:
                kwargs["queryset"] = Category.objects.filter(partner=effective_partner, is_active=True)
            else:
                kwargs["queryset"] = Category.objects.none()
        return super().formfield_for_foreignkey(db_field, request, **kwargs)
    
    def has_add_permission(self, request, obj=None):
        """Allow adding M2M relationships if user can manage packages"""
        return user_can_manage_packages(request)
    
    def has_change_permission(self, request, obj=None):
        """Allow changing M2M relationships if user can manage packages"""
        return user_can_manage_packages(request)
    
    def has_delete_permission(self, request, obj=None):
        """Allow deleting M2M relationships if user can manage packages"""
        return user_can_manage_packages(request)
    
    def has_view_permission(self, request, obj=None):
        """Allow viewing M2M relationships if user can manage packages"""
        return user_can_manage_packages(request)


class PackageActivityInline(admin.TabularInline):
    """Inline to add activities to packages"""
    model = PackageActivity
    extra = 1
    show_change_link = False  # Hide pencil/eye/plus icons
    autocomplete_fields = ['activity']
    fields = ['package', 'activity']
    exclude = ['deleted_at', 'restored_at', 'is_active', 'external_id', 'created_at', 'updated_at']

    def get_formset(self, request, obj=None, **kwargs):
        formset = super().get_formset(request, obj, **kwargs)
        for field_name in self.autocomplete_fields:
            widget = formset.form.base_fields[field_name].widget
            widget.can_add_related = False
            widget.can_change_related = False
            widget.can_view_related = False
        return formset

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """Filter activities by user's effective partner and is_active=True"""
        if db_field.name == "activity":
            effective_partner = get_user_effective_partner(request)
            if effective_partner:
                kwargs["queryset"] = Activity.objects.filter(partner=effective_partner, is_active=True)
            else:
                kwargs["queryset"] = Activity.objects.none()
        return super().formfield_for_foreignkey(db_field, request, **kwargs)
    
    def has_add_permission(self, request, obj=None):
        """Allow adding M2M relationships if user can manage packages"""
        return user_can_manage_packages(request)
    
    def has_change_permission(self, request, obj=None):
        """Allow changing M2M relationships if user can manage packages"""
        return user_can_manage_packages(request)
    
    def has_delete_permission(self, request, obj=None):
        """Allow deleting M2M relationships if user can manage packages"""
        return user_can_manage_packages(request)
    
    def has_view_permission(self, request, obj=None):
        """Allow viewing M2M relationships if user can manage packages"""
        return user_can_manage_packages(request)


# Main model admins
@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    """Admin for Category model with improved partner logic"""
    form = CategoryAdminForm
    add_form_template = 'admin/packages/category/add_form.html'
    list_display = ('title', 'description', 'has_media', 'is_active', 'created_at')
    search_fields = ('title', 'description')
    list_filter = (ActiveStatusFilter, 'created_at')
    
    fieldsets = (
        ('General', {
            'fields': ('title', 'description', 'explore_order', 'is_active'),
        }),
    )
    
    inlines = [CategoryMediaInline]
    
    def get_queryset(self, request):
        """Filter by user's effective partner"""
        qs = super().get_queryset(request)
        effective_partner = get_user_effective_partner(request)
        if effective_partner:
            return qs.filter(partner=effective_partner)
        return qs.none()

    def save_model(self, request, obj, form, change):
        """Auto-assign partner based on user"""
        if not change:  # Only for new categories
            obj.partner = get_user_effective_partner(request)
        super().save_model(request, obj, form, change)

    def get_form(self, request, obj=None, **kwargs):
        """Pass request to form for partner context"""
        form = super().get_form(request, obj, **kwargs)
        
        class RequestAwareForm(form):
            def __init__(self, *args, **kwargs):
                kwargs['request'] = request
                super().__init__(*args, **kwargs)
        
        return RequestAwareForm

    def has_media(self, obj):
        """Check if category has media"""
        if not obj or not obj.pk:
            return False
        return obj.media.exists()
    has_media.boolean = True
    has_media.short_description = 'Has Media'
    
    # Permission methods based on package_type
    def has_module_permission(self, request):
        """Only allow users who can manage content"""
        return request.user.is_active and user_can_manage_content(request)
    
    def has_view_permission(self, request, obj=None):
        """Only allow users who can manage content"""
        return request.user.is_active and user_can_manage_content(request)
    
    def has_add_permission(self, request):
        """Only allow users who can manage content"""
        return request.user.is_active and user_can_manage_content(request)
    
    def has_change_permission(self, request, obj=None):
        """Only allow users who can manage content"""
        return request.user.is_active and user_can_manage_content(request)
    
    def has_delete_permission(self, request, obj=None):
        """Remove delete permission for all users"""
        return False


@admin.register(Destination)
class DestinationAdmin(admin.ModelAdmin):
    """Admin for Destination model with improved partner logic"""
    form = DestinationAdminForm
    add_form_template = 'admin/packages/destination/add_form.html'
    
    list_display = ('title', 'description', 'is_international', 'is_trending', 'best_months_display', 'has_media', 'category_count', 'activity_count', 'hotels_link', 'is_active', 'created_at')
    search_fields = ('title', 'description')
    list_filter = (InternationalDestinationFilter, TrendingDestinationFilter, ActiveStatusFilter, 'created_at')
    readonly_fields = ('hotels_link', 'category_count', 'activity_count', 'has_media', 'best_months_display')
    
    fieldsets = (
        ('General', {
            'fields': ('title', 'description', 'is_international', 'is_trending',  'explore_order', 'hotels_link', 'is_active',),
        }),
        ('Best Time to Visit', {
            'fields': ('best_time_to_visit',),
            'classes': ('wide',),
            'description': 'Select the months when it\'s best to visit this destination'
        }),
    )
    
    inlines = [DestinationMediaInline, DestinationCategoryInline, DestinationActivityInline]
    
    def get_queryset(self, request):
        """Filter by user's effective partner"""
        qs = super().get_queryset(request)
        effective_partner = get_user_effective_partner(request)
        if effective_partner:
            return qs.filter(partner=effective_partner)
        return qs.none()

    def save_model(self, request, obj, form, change):
        """Auto-assign partner based on user"""
        if not change:  # Only for new destinations
            obj.partner = get_user_effective_partner(request)
        super().save_model(request, obj, form, change)

    def get_form(self, request, obj=None, **kwargs):
        """Pass request to form for partner context"""
        form = super().get_form(request, obj, **kwargs)
        
        class RequestAwareForm(form):
            def __init__(self, *args, **kwargs):
                kwargs['request'] = request
                super().__init__(*args, **kwargs)
        
        return RequestAwareForm

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """Filter destination dropdown by partner and is_active=True for Package forms"""
        if db_field.name == "destination":
            effective_partner = get_user_effective_partner(request)
            if effective_partner:
                kwargs["queryset"] = Destination.objects.filter(partner=effective_partner, is_active=True)
            else:
                kwargs["queryset"] = Destination.objects.none()
        return super().formfield_for_foreignkey(db_field, request, **kwargs)

    def has_media(self, obj):
        """Check if destination has media - boolean indicator"""
        if not obj or not obj.pk:
            return False
        return obj.media.exists()
    has_media.short_description = 'Has Media'
    has_media.boolean = True
    
    def category_count(self, obj):
        """Count categories - safe for unsaved objects"""
        if not obj or not obj.pk:
            return 0
        return obj.categories.count()
    category_count.short_description = 'Categories'
    
    def activity_count(self, obj):
        """Count activities - safe for unsaved objects"""
        if not obj or not obj.pk:
            return 0
        return obj.activities.count()
    activity_count.short_description = 'Activities'
    
    def hotels_link(self, obj):
        """Display hyperlink to TripJack hotels for this destination with count"""
        if not obj or not obj.pk:
            return format_html('<span style="color: gray;">No hotels</span>')
        
        try:
            # Count TripJack hotels for this destination using M2M relationship
            hotel_count = obj.tripjack_hotels.filter(is_active=True).count()

            if hotel_count > 0:
                try:
                    # Create URL to filtered TripJack hotels list
                    url = reverse('admin:packages_tripjackhotels_changelist') + f'?destinations__id__exact={obj.id}'
                    return format_html(
                        '<a href="{}" target="_blank" style="color: #417690; text-decoration: none;">'
                        '{} Hotels</a>',
                        url, hotel_count
                    )
                except Exception:
                    # If reverse fails, just show count without link
                    return format_html('<span style="color: #417690;">{} Hotels</span>', hotel_count)
            else:
                return format_html('<span style="color: gray;">No hotels</span>')
        except Exception:
            # Handle any other errors gracefully
            return format_html('<span style="color: gray;">Hotels unavailable</span>')
    hotels_link.short_description = 'Hotels'
    
    def best_months_display(self, obj):
        """Display best months to visit"""
        if not obj or not obj.pk:
            return "-"
        return obj.get_best_months_display()
    best_months_display.short_description = 'Best Time to Visit'
    
    # Permission methods based on package_type
    def has_module_permission(self, request):
        """Only allow users who can manage content"""
        return request.user.is_active and user_can_manage_content(request)
    
    def has_view_permission(self, request, obj=None):
        """Only allow users who can manage content"""
        return request.user.is_active and user_can_manage_content(request)
    
    def has_add_permission(self, request):
        """Only allow users who can manage content"""
        return request.user.is_active and user_can_manage_content(request)
    
    def has_change_permission(self, request, obj=None):
        """Only allow users who can manage content"""
        return request.user.is_active and user_can_manage_content(request)
    
    def has_delete_permission(self, request, obj=None):
        """Remove delete permission for all users"""
        return False


@admin.register(Activity)
class ActivityAdmin(admin.ModelAdmin):
    """Admin for Activity model with improved partner logic"""
    form = ActivityAdminForm
    list_display = ('title', 'description', 'is_featured', 'has_media', 'is_active', 'created_at')
    search_fields = ('title', 'description')
    list_filter = (FeaturedActivityFilter, ActiveStatusFilter, 'created_at')
    
    fieldsets = (
        ('General', {
            'fields': ('title', 'description', 'is_featured', 'explore_order', 'is_active'),
        }),
    )
    
    inlines = [ActivityMediaInline]
    
    def get_queryset(self, request):
        """Filter by user's effective partner"""
        qs = super().get_queryset(request)
        effective_partner = get_user_effective_partner(request)
        if effective_partner:
            return qs.filter(partner=effective_partner)
        return qs.none()

    def save_model(self, request, obj, form, change):
        """Auto-assign partner based on user"""
        if not change:  # Only for new activities
            obj.partner = get_user_effective_partner(request)
        super().save_model(request, obj, form, change)

    def get_form(self, request, obj=None, **kwargs):
        """Pass request to form for partner context"""
        form = super().get_form(request, obj, **kwargs)
        
        class RequestAwareForm(form):
            def __init__(self, *args, **kwargs):
                kwargs['request'] = request
                super().__init__(*args, **kwargs)
        
        return RequestAwareForm

    def has_media(self, obj):
        """Check if activity has media - boolean indicator"""
        if not obj or not obj.pk:
            return False
        return obj.media.exists()
    has_media.short_description = 'Has Media'
    has_media.boolean = True
    
    # Permission methods based on package_type
    def has_module_permission(self, request):
        """Only allow users who can manage content"""
        return request.user.is_active and user_can_manage_content(request)
    
    def has_view_permission(self, request, obj=None):
        """Only allow users who can manage content"""
        return request.user.is_active and user_can_manage_content(request)
    
    def has_add_permission(self, request):
        """Only allow users who can manage content"""
        return request.user.is_active and user_can_manage_content(request)
    
    def has_change_permission(self, request, obj=None):
        """Only allow users who can manage content"""
        return request.user.is_active and user_can_manage_content(request)
    
    def has_delete_permission(self, request, obj=None):
        """Remove delete permission for all users"""
        return False


@admin.register(Package)
class PackageAdmin(admin.ModelAdmin, DynamicArrayMixin):
    """Admin interface for travel packages with simplified single form"""
    form = PackageAdminForm
    list_display = ('title', 'package_no', 'destination', 'price_per_person', 'duration', 'is_published', 'created_at')
    search_fields = ('title', 'package_no', 'destination__title')
    list_filter = (PublishStatusFilter, ActiveStatusFilter, 'type', 'created_at')
    readonly_fields = ('created_at', 'updated_at', 'json_file_link')  # Removed type from readonly since form handles it
    inlines = [PackageMediaInline, PackageCategoryInline, PackageActivityInline]
    autocomplete_fields = ['destination']
    
    # Custom template for change list to add custom button - specific to Package model
    change_list_template = 'admin/packages/package/change_list.html'

    def get_form(self, request, obj=None, **kwargs):
        """Pass request to form for partner handling and customize field labels"""
        form = super().get_form(request, obj, **kwargs)
        
        # Create a subclass that has access to request and customized labels
        class RequestAwareForm(form):
            def __init__(self, *args, **kwargs):
                super().__init__(*args, **kwargs)
                self.request = request
                
                # Customize field labels
                if 'package_no' in self.fields:
                    self.fields['package_no'].label = 'Package No.'
            
            def full_clean(self):
                """Override to show only first error per field"""
                super().full_clean()
                
                # If there are field errors, keep only the first error for each field
                if hasattr(self, '_errors') and self._errors:
                    from django.forms.utils import ErrorList
                    for field_name, error_list in self._errors.items():
                        if len(error_list) > 1:
                            # Keep only the first error message but maintain ErrorList type
                            first_error = error_list[0]
                            self._errors[field_name] = ErrorList([first_error])
            
            def save_m2m(self):
                """Save many-to-many relationships - delegate to parent form"""
                if hasattr(super(), 'save_m2m'):
                    return super().save_m2m()
                # If parent doesn't have save_m2m, it's likely because our PackageAdminForm
                # handles M2M relationships differently (through PackageCreationService)
                # In that case, do nothing as M2M relationships are already handled
                pass
        
        return RequestAwareForm
    
    def get_queryset(self, request):
        """Filter by user's effective partner with optimized queries"""
        qs = super().get_queryset(request)
        effective_partner = get_user_effective_partner(request)
        qs = qs.filter(type=PackageTypeChoices.FIXED.value)
        if effective_partner:
            return qs.filter(partner=effective_partner).select_related(
                'destination', 'partner', 'package_uploaded'
            )
        return qs.none()
    
    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """Filter destination dropdown by partner and is_active=True"""
        if db_field.name == "destination":
            effective_partner = get_user_effective_partner(request)
            if effective_partner:
                kwargs["queryset"] = Destination.objects.filter(partner=effective_partner, is_active=True)
            else:
                kwargs["queryset"] = Destination.objects.none()
        return super().formfield_for_foreignkey(db_field, request, **kwargs)
    
    def get_fields(self, request, obj=None):
        """Single form with all fields except auto-calculated and system fields"""
        fields = [
            'title', 'package_no', 'destination', 'type', 'explore_order', 'is_published', 'is_active',
            'duration',  # AI will calculate duration_in_nights and duration_in_days
            'price_per_person', 'currency_conversion_rate',
            'owner', 'about_this_tour', 'highlights', 'inclusions', 'exclusions',
            'itinerary',
            'visa_type', 'best_time_to_visit', 'destination_safety', 'rating', 'rating_description',
            'hotels', 'popular_restaurants', 'popular_activities',
            'addons',  # New field for add on services
            'cultural_info', 'what_to_shop', 'what_to_pack', 'important_notes',
        ]
        
        # Add readonly fields for existing objects
        if obj:
            fields.extend(['json_file_link', 'created_at', 'updated_at'])
        
        # Partner, currency, currency_symbol, duration_in_nights, duration_in_days, best_time_to_visit_months are handled automatically
        return fields
    
    def save_model(self, request, obj, form, change):
        """Set partner automatically based on user and ensure type is FIXED"""
        if not change:  # Only set partner when creating new package
            obj.partner = get_user_effective_partner(request)
            obj.type = PackageTypeChoices.FIXED.value  # Always set to FIXED
        super().save_model(request, obj, form, change)
    
    def save_related(self, request, form, formsets, change):
        """Override to ensure formsets have the correct package instance"""
        if not change and hasattr(form, '_processed_package') and form._processed_package:
            for formset in formsets:
                if hasattr(formset, 'instance'):
                    formset.instance = form._processed_package
                    for inline_form in formset.forms:
                        if hasattr(inline_form, 'instance') and inline_form.instance:
                            if hasattr(inline_form.instance, 'package'):
                                inline_form.instance.package = form._processed_package
        super().save_related(request, form, formsets, change)
    
    def json_file_link(self, obj):
        """Show link to view PackageUploader details for file-uploaded packages"""
        if obj and obj.pk and obj.package_uploaded:
            # Package was created from file upload - show link to PackageUploader admin
            uploader_url = reverse('admin:packages_packageuploader_change', args=[obj.package_uploaded.pk])
            return format_html('<a href="{}" target="_blank">📄 View Upload Details</a>', uploader_url)
        return "-"  # Manual package or no package_uploaded relationship
    json_file_link.short_description = 'Upload Details'
    
    # Permission methods based on package_type
    def has_module_permission(self, request):
        """Only allow users who can manage packages"""
        return request.user.is_active and user_can_manage_packages(request)
    
    def has_view_permission(self, request, obj=None):
        """Only allow users who can manage packages"""
        return request.user.is_active and user_can_manage_packages(request)
    
    def has_add_permission(self, request):
        """Only allow users who can manage packages"""
        return request.user.is_active and user_can_manage_packages(request)
    
    def has_change_permission(self, request, obj=None):
        """Only allow users who can manage packages"""
        return request.user.is_active and user_can_manage_packages(request)
    
    def has_delete_permission(self, request, obj=None):
        """Remove delete permission for all users"""
        return False


@admin.register(PackageUploader)
class PackageUploaderAdmin(admin.ModelAdmin):
    """Admin interface for package JSON uploads - Hidden from admin panel"""
    form = PackageUploaderForm
    list_display = ('file', 'created_at')  # Remove file_type from display
    readonly_fields = ()
    
    def get_form(self, request, obj=None, **kwargs):
        """Use different form for readonly vs editable views"""
        if obj:  # Existing object - use a simple ModelForm without custom __init__
            from django import forms
            
            class ReadOnlyPackageUploaderForm(forms.ModelForm):
                class Meta:
                    model = PackageUploader
                    fields = ('file',)  # Remove file_type from fields
                
                def __init__(self, *args, **kwargs):
                    super().__init__(*args, **kwargs)
                    # Don't add complex help text or custom logic for readonly views
            
            form = ReadOnlyPackageUploaderForm
        else:  # New object - use the full PackageUploaderForm
            form = super().get_form(request, obj, **kwargs)
            
            # Create a subclass that has access to request
            class RequestAwareForm(form):
                def __init__(self, *args, **kwargs):
                    super().__init__(*args, **kwargs)
                    self.request = request
            
            return RequestAwareForm
        
        return form
    
    def changelist_view(self, request, extra_context=None):
        """Redirect to Package admin with message instead of showing changelist"""
        from django.shortcuts import redirect
        from django.contrib import messages
        
        messages.info(request, 'This section is not accessible. Use the "Upload Package File" button to add new packages.')
        return redirect('admin:packages_package_changelist')
    
    def change_view(self, request, object_id, form_url='', extra_context=None):
        """Allow viewing individual PackageUploader objects but make fields readonly"""
        extra_context = extra_context or {}
        extra_context['title'] = 'Package Upload Details'
        
        return super().change_view(request, object_id, form_url, extra_context)
    
    def get_readonly_fields(self, request, obj=None):
        """Make all fields readonly when viewing existing objects"""
        if obj:  # Existing object (change view)
            return ('file', 'file_type', 'partner', 'file_download_link', 'created_package_link', 'created_at', 'updated_at')
        else:  # New object (add view)
            return ('partner', 'created_at', 'updated_at')
    
    def file_download_link(self, obj):
        """Show download link for the uploaded file"""
        if obj and obj.file:
            file_url = obj.file.url
            file_name = obj.file.name.split('/')[-1]  # Get just the filename
            return format_html('<a href="{}" download="{}">📥 Download Original File</a>', file_url, file_name)
        return "-"
    file_download_link.short_description = 'Download File'
    
    def created_package_link(self, obj):
        """Show link to the package that was created from this upload"""
        if obj and obj.pk:
            try:
                # Find the package that was created from this uploader
                package = Package.objects.get(package_uploaded=obj)
                package_url = reverse('admin:packages_package_change', args=[package.pk])
                return format_html('<a href="{}" target="_blank">📦 View Created Package: {}</a>', package_url, package.title)
            except Package.DoesNotExist:
                return "No package created yet"
        return "-"
    created_package_link.short_description = 'Created Package'
    
    def get_list_filter(self, request):
        """Remove file_type filter - only show created_at"""
        return ('created_at',)
    
    def get_fields(self, request, obj=None):
        """Show only file field - partner and file_type are set automatically"""
        return ('file',)
    
    def get_queryset(self, request):
        """Filter by user's effective partner"""
        qs = super().get_queryset(request)
        effective_partner = get_user_effective_partner(request)
        if effective_partner:
            return qs.filter(partner=effective_partner)
        return qs.none()
    
    def save_model(self, request, obj, form, change):
        """Set partner and file_type automatically based on user"""
        if not change:  # Only set partner and file_type when creating new uploader
            obj.partner = get_user_effective_partner(request)
            obj.file_type = PackageTypeChoices.FIXED.value  # Always set to FIXED
        super().save_model(request, obj, form, change)
    
    def response_add(self, request, obj, post_url_continue=None):
        """Redirect to created Package after successful upload with simplified success message"""
        from django.shortcuts import redirect
        from django.contrib import messages
        
        # Always show success message for file upload
        messages.success(
            request, 
            f'Package file "{obj.file.name.split("/")[-1]}" was uploaded successfully.'
        )
        
        # Try to find and redirect to created package if it exists
        try:
            package = Package.objects.get(package_uploaded=obj)
            messages.success(
                request, 
                f'Package "{package.title}" was created successfully.'
            )
            return redirect('admin:packages_package_change', package.pk)
        except Package.DoesNotExist:
            # Just redirect to package list if no package was created
            return redirect('admin:packages_package_changelist')
    
    def response_change(self, request, obj):
        """Redirect to associated Package after successful update with simplified success message"""
        from django.shortcuts import redirect
        from django.contrib import messages
        
        # Always show success message for file update
        messages.success(
            request, 
            f'Package file "{obj.file.name.split("/")[-1]}" was updated successfully.'
        )
        
        # Try to find and redirect to associated package if it exists
        try:
            package = Package.objects.get(package_uploaded=obj)
            return redirect('admin:packages_package_change', package.pk)
        except Package.DoesNotExist:
            # Just redirect to package list if no package is associated
            return redirect('admin:packages_package_changelist')
    
    def has_change_permission(self, request, obj=None):
        """Only allow users who can manage packages"""
        return request.user.is_active and user_can_manage_packages(request)
    
    def has_delete_permission(self, request, obj=None):
        """Disable delete permission"""
        return False
    
    def has_module_permission(self, request):
        """Hide from admin panel sidebar"""
        return False
    
    def has_add_permission(self, request):
        """Allow adding through direct URL if user can manage packages"""
        return request.user.is_active and user_can_manage_packages(request)
    
    def has_view_permission(self, request, obj=None):
        """Allow viewing through direct URL if user can manage packages"""
        return request.user.is_active and user_can_manage_packages(request)


@admin.register(DestinationFaq)
class DestinationFaqAdmin(admin.ModelAdmin):
    """Admin for DestinationFaq model with improved partner logic"""
    form = DestinationFaqAdminForm
    list_display = ('destination', 'question_preview', 'is_published', 'priority', 'created_at')
    search_fields = ('destination__title', 'question', 'answer')
    list_filter = ('is_published', 'created_at')
    ordering = ('-priority', '-created_at')
    
    fieldsets = (
        ('General', {
            'fields': ('destination', 'question', 'answer', 'is_published', 'priority'),
        }),
    )
    
    def question_preview(self, obj):
        """Show first 50 characters of question"""
        if obj.question and len(obj.question) > 50:
            return obj.question[:50] + "..."
        return obj.question or "-"
    question_preview.short_description = 'Question'
    
    def get_queryset(self, request):
        """Filter by user's effective partner"""
        qs = super().get_queryset(request)
        effective_partner = get_user_effective_partner(request)
        if effective_partner:
            return qs.filter(partner=effective_partner)
        return qs.none()

    def save_model(self, request, obj, form, change):
        """Auto-assign partner based on user"""
        if not change:  # Only for new FAQs
            obj.partner = get_user_effective_partner(request)
        super().save_model(request, obj, form, change)

    def get_form(self, request, obj=None, **kwargs):
        """Pass request to form for partner context"""
        form = super().get_form(request, obj, **kwargs)
        
        class RequestAwareForm(form):
            def __init__(self, *args, **kwargs):
                kwargs['request'] = request
                super().__init__(*args, **kwargs)
        
        return RequestAwareForm

    def has_module_permission(self, request):
        """Only allow users who can manage content"""
        return request.user.is_active and user_can_manage_content(request)
    
    def has_view_permission(self, request, obj=None):
        """Only allow users who can manage content"""
        return request.user.is_active and user_can_manage_content(request)
    
    def has_add_permission(self, request):
        """Only allow users who can manage content"""
        return request.user.is_active and user_can_manage_content(request)
    
    def has_change_permission(self, request, obj=None):
        """Only allow users who can manage content"""
        return request.user.is_active and user_can_manage_content(request)
    
    def has_delete_permission(self, request, obj=None):
        """Allow delete permission for FAQs"""
        return request.user.is_active and user_can_manage_content(request)

    def delete_model(self, request, obj):
        """Use hard_delete instead of soft delete"""
        # Use the hard_delete method from django-softdelete
        obj.hard_delete()
    
    def delete_queryset(self, request, queryset):
        """Hard delete multiple objects"""
        # Use hard_delete for bulk deletions
        for obj in queryset:
            obj.hard_delete()


# =============================================================================
# CUSTOM ACTIVITY MODELS ADMIN (GetYourGuide Data)
# =============================================================================

class CustomActivityCategoryInlineFormSet(forms.models.BaseInlineFormSet):
    """Custom formset for CustomActivityCategoryInline to handle M2M relationships properly"""
    
    def clean(self):
        """Validate the formset data"""
        super().clean()
        
        if any(self.errors):
            return
        
        # Check for duplicate categories
        categories = []
        for form in self.forms:
            if form.cleaned_data and not form.cleaned_data.get('DELETE', False):
                category = form.cleaned_data.get('category')
                if category:
                    if category in categories:
                        raise forms.ValidationError("Cannot add the same category multiple times.")
                    categories.append(category)


# Inline admins for Custom Activity M2M relationships
class CustomActivityCategoryInline(admin.TabularInline):
    """Inline to add categories to custom activities"""
    model = CustomActivityCategoryRelation
    formset = CustomActivityCategoryInlineFormSet
    extra = 0  # Don't show extra empty forms by default
    show_change_link = False
    autocomplete_fields = ['category']
    fields = ['category']  # Remove 'activity' field as it's automatically set
    exclude = ['deleted_at', 'restored_at', 'is_active', 'external_id', 'created_at', 'updated_at']

    def get_formset(self, request, obj=None, **kwargs):
        kwargs['formset'] = self.formset
        formset = super().get_formset(request, obj, **kwargs)
        for field_name in self.autocomplete_fields:
            widget = formset.form.base_fields[field_name].widget
            widget.can_add_related = False
            widget.can_change_related = False
            widget.can_view_related = False
        return formset

    def get_extra(self, request, obj=None, **kwargs):
        """Control number of extra empty forms"""
        if obj and obj.pk:
            # For existing objects, check if they have any categories
            existing_count = obj.categories.count()
            if existing_count > 0:
                return 0  # Don't show extra forms if there are existing categories
            else:
                return 1  # Show one empty form if no categories exist
        # For new objects, show one empty form
        return 1

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """Filter categories by CustomActivityCategory objects (not system Category objects)"""
        if db_field.name == "category":
            # Show CustomActivityCategory objects, not system Category objects
            from packages.models import CustomActivityCategory
            kwargs["queryset"] = CustomActivityCategory.objects.all().order_by('name')
        return super().formfield_for_foreignkey(db_field, request, **kwargs)

    def has_add_permission(self, request, obj=None):
        return request.user.is_staff
    
    def has_change_permission(self, request, obj=None):
        return request.user.is_staff
    
    def has_delete_permission(self, request, obj=None):
        return request.user.is_staff
    
    def has_view_permission(self, request, obj=None):
        return request.user.is_staff


class CustomActivityLocationInline(admin.TabularInline):
    """Inline to add locations to custom activities"""
    model = CustomActivityLocationRelation
    extra = 1
    show_change_link = False
    autocomplete_fields = ['location']
    fields = ['activity', 'location']
    exclude = ['deleted_at', 'restored_at', 'is_active', 'external_id', 'created_at', 'updated_at']

    def get_formset(self, request, obj=None, **kwargs):
        """Customize formset to handle partner-specific logic"""
        formset = super().get_formset(request, obj, **kwargs)
        
        # Add custom validation or logic here if needed
        return formset

    def has_add_permission(self, request, obj=None):
        return request.user.is_staff

    def has_change_permission(self, request, obj=None):
        return request.user.is_staff

    def has_delete_permission(self, request, obj=None):
        return request.user.is_staff

    def has_view_permission(self, request, obj=None):
        return request.user.is_staff


class CustomActivityMediaInline(admin.TabularInline):
    """Inline to add media files to custom activities"""
    model = CustomActivityMedia
    fields = ['media']
    extra = 1
    show_change_link = False
    max_num = 10  # Set a reasonable limit
    fk_name = 'custom_activity'  # Explicitly specify the foreign key field name
    
    def get_formset(self, request, obj=None, **kwargs):
        logger.info(f"CustomActivityMediaInline.get_formset START - obj: {obj}")
        logger.info(f"CustomActivityMediaInline.get_formset - request.method: {request.method}")
        logger.info(f"CustomActivityMediaInline.get_formset - kwargs: {kwargs}")
        
        if obj:
            logger.info(f"CustomActivityMediaInline.get_formset - obj.pk: {obj.pk}")
            logger.info(f"CustomActivityMediaInline.get_formset - obj.title: {getattr(obj, 'title', 'No title')}")
            try:
                media_count = obj.media.count()
                logger.info(f"CustomActivityMediaInline.get_formset - obj.media.count(): {media_count}")
                
                # Log existing media
                for i, media in enumerate(obj.media.all()):
                    logger.info(f"CustomActivityMediaInline.get_formset - existing media {i}: {media} (pk: {media.pk}, file: {media.media})")
            except Exception as e:
                logger.error(f"CustomActivityMediaInline.get_formset - error getting media count: {e}")
        else:
            logger.info(f"CustomActivityMediaInline.get_formset - obj is None (new object)")
        
        try:
            logger.info(f"CustomActivityMediaInline.get_formset - calling super().get_formset()")
            formset = super().get_formset(request, obj, **kwargs)
            logger.info(f"CustomActivityMediaInline.get_formset - super().get_formset() successful")
            logger.info(f"CustomActivityMediaInline.get_formset - formset type: {type(formset)}")
            logger.info(f"CustomActivityMediaInline.get_formset - formset: {formset}")
            
            # Log formset attributes
            if hasattr(formset, 'model'):
                logger.info(f"CustomActivityMediaInline.get_formset - formset.model: {formset.model}")
            if hasattr(formset, 'fk'):
                logger.info(f"CustomActivityMediaInline.get_formset - formset.fk: {formset.fk}")
            if hasattr(formset, 'prefix'):
                logger.info(f"CustomActivityMediaInline.get_formset - formset.prefix: {formset.prefix}")
            
            return formset
            
        except Exception as e:
            logger.error(f"CustomActivityMediaInline.get_formset - ERROR: {e}")
            logger.error(f"CustomActivityMediaInline.get_formset - ERROR type: {type(e)}")
            import traceback
            logger.error(f"CustomActivityMediaInline.get_formset - ERROR traceback: {traceback.format_exc()}")
            raise
    
    def get_extra(self, request, obj=None, **kwargs):
        logger.info(f"CustomActivityMediaInline.get_extra - obj: {obj}")
        if obj and obj.pk:
            try:
                media_count = obj.media.count()
                logger.info(f"CustomActivityMediaInline.get_extra - media_count: {media_count}")
                if media_count >= 10:  # max_num limit
                    logger.info(f"CustomActivityMediaInline.get_extra - returning 0 (reached max)")
                    return 0
                else:
                    logger.info(f"CustomActivityMediaInline.get_extra - returning 1")
                    return 1
            except Exception as e:
                logger.error(f"CustomActivityMediaInline.get_extra - error: {e}")
                return 1
        logger.info(f"CustomActivityMediaInline.get_extra - returning 1 (new object or no pk)")
        return 1
    
    def has_add_permission(self, request, obj=None):
        result = request.user.is_staff
        logger.info(f"CustomActivityMediaInline.has_add_permission - result: {result}")
        return result
    
    def has_change_permission(self, request, obj=None):
        result = request.user.is_staff
        logger.info(f"CustomActivityMediaInline.has_change_permission - result: {result}")
        return result
    
    def has_delete_permission(self, request, obj=None):
        result = request.user.is_staff
        logger.info(f"CustomActivityMediaInline.has_delete_permission - result: {result}")
        return result
    
    def has_view_permission(self, request, obj=None):
        result = request.user.is_staff
        logger.info(f"CustomActivityMediaInline.has_view_permission - result: {result}")
        return result


class CustomActivitySystemCategoryInline(admin.TabularInline):
    """Inline to add system categories to custom activities"""
    model = CustomActivitySystemCategoryRelation
    extra = 1
    show_change_link = False
    autocomplete_fields = ['category']
    fields = ['activity', 'category']
    exclude = ['deleted_at', 'restored_at', 'is_active', 'external_id', 'created_at', 'updated_at']

    def get_formset(self, request, obj=None, **kwargs):
        formset = super().get_formset(request, obj, **kwargs)
        for field_name in self.autocomplete_fields:
            widget = formset.form.base_fields[field_name].widget
            widget.can_add_related = False
            widget.can_change_related = False
            widget.can_view_related = False
        return formset

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """Filter categories by partner (same as activity's destination partner) and active status"""
        if db_field.name == "category":
            # Show all active system categories
            kwargs["queryset"] = Category.objects.filter(is_active=True).order_by('title')
        return super().formfield_for_foreignkey(db_field, request, **kwargs)

    def has_add_permission(self, request, obj=None):
        return request.user.is_staff
    
    def has_change_permission(self, request, obj=None):
        return request.user.is_staff
    
    def has_delete_permission(self, request, obj=None):
        return request.user.is_staff
    
    def has_view_permission(self, request, obj=None):
        return request.user.is_staff


class BestsellerFilter(admin.SimpleListFilter):
    """Custom filter for bestseller status"""
    title = 'Bestseller Status'
    parameter_name = 'bestseller_status'

    def lookups(self, request, model_admin):
        return (
            ('yes', 'Bestseller'),
            ('no', 'Not Bestseller'),
            ('null', 'Unknown'),
        )

    def queryset(self, request, queryset):
        if self.value() == 'yes':
            return queryset.filter(bestseller=True)
        if self.value() == 'no':
            return queryset.filter(bestseller=False)
        if self.value() == 'null':
            return queryset.filter(bestseller__isnull=True)
        return queryset


class CertifiedFilter(admin.SimpleListFilter):
    """Custom filter for certified status"""
    title = 'Certified Status'
    parameter_name = 'certified_status'

    def lookups(self, request, model_admin):
        return (
            ('yes', 'Certified'),
            ('no', 'Not Certified'),
            ('null', 'Unknown'),
        )

    def queryset(self, request, queryset):
        if self.value() == 'yes':
            return queryset.filter(certified=True)
        if self.value() == 'no':
            return queryset.filter(certified=False)
        if self.value() == 'null':
            return queryset.filter(certified__isnull=True)
        return queryset


class OverallRatingFilter(admin.SimpleListFilter):
    """Custom filter for overall rating ranges"""
    title = 'Overall Rating'
    parameter_name = 'overall_rating_range'

    def lookups(self, request, model_admin):
        return (
            ('5_stars', '5.0 Stars (Perfect)'),
            ('4_5_plus', '4.5+ Stars (Excellent)'),
            ('4_plus', '4.0+ Stars (Very Good)'),
            ('3_5_plus', '3.5+ Stars (Good)'),
            ('3_plus', '3.0+ Stars (Average)'),
            ('below_3', 'Below 3.0 Stars (Poor)'),
            ('no_rating', 'No Rating'),
        )

    def queryset(self, request, queryset):
        if self.value() == '5_stars':
            return queryset.filter(overall_rating=5.0)
        if self.value() == '4_5_plus':
            return queryset.filter(overall_rating__gte=4.5)
        if self.value() == '4_plus':
            return queryset.filter(overall_rating__gte=4.0)
        if self.value() == '3_5_plus':
            return queryset.filter(overall_rating__gte=3.5)
        if self.value() == '3_plus':
            return queryset.filter(overall_rating__gte=3.0)
        if self.value() == 'below_3':
            return queryset.filter(overall_rating__lt=3.0, overall_rating__isnull=False)
        if self.value() == 'no_rating':
            return queryset.filter(overall_rating__isnull=True)
        return queryset


class NumberOfRatingsFilter(admin.SimpleListFilter):
    """Custom filter for number of ratings ranges"""
    title = 'Number of Ratings'
    parameter_name = 'number_of_ratings_range'

    def lookups(self, request, model_admin):
        return (
            ('1000_plus', '1000+ Reviews (Very Popular)'),
            ('500_plus', '500+ Reviews (Popular)'),
            ('100_plus', '100+ Reviews (Well-reviewed)'),
            ('50_plus', '50+ Reviews (Moderate)'),
            ('10_plus', '10+ Reviews (Some feedback)'),
            ('1_plus', '1+ Reviews (Has reviews)'),
            ('no_reviews', 'No Reviews'),
        )

    def queryset(self, request, queryset):
        if self.value() == '1000_plus':
            return queryset.filter(number_of_ratings__gte=1000)
        if self.value() == '500_plus':
            return queryset.filter(number_of_ratings__gte=500)
        if self.value() == '100_plus':
            return queryset.filter(number_of_ratings__gte=100)
        if self.value() == '50_plus':
            return queryset.filter(number_of_ratings__gte=50)
        if self.value() == '10_plus':
            return queryset.filter(number_of_ratings__gte=10)
        if self.value() == '1_plus':
            return queryset.filter(number_of_ratings__gte=1)
        if self.value() == 'no_reviews':
            return queryset.filter(number_of_ratings__isnull=True)
        return queryset


class BestsellerFilter(admin.SimpleListFilter):
    """Custom filter for bestseller status"""
    title = 'Bestseller Status'
    parameter_name = 'bestseller_status'

    def lookups(self, request, model_admin):
        return (
            ('yes', 'Bestseller'),
            ('no', 'Not Bestseller'),
            ('null', 'Unknown'),
        )

    def queryset(self, request, queryset):
        if self.value() == 'yes':
            return queryset.filter(bestseller=True)
        if self.value() == 'no':
            return queryset.filter(bestseller=False)
        if self.value() == 'null':
            return queryset.filter(bestseller__isnull=True)
        return queryset


class CertifiedFilter(admin.SimpleListFilter):
    """Custom filter for certified status"""
    title = 'Certified Status'
    parameter_name = 'certified_status'

    def lookups(self, request, model_admin):
        return (
            ('yes', 'Certified'),
            ('no', 'Not Certified'),
            ('null', 'Unknown'),
        )

    def queryset(self, request, queryset):
        if self.value() == 'yes':
            return queryset.filter(certified=True)
        if self.value() == 'no':
            return queryset.filter(certified=False)
        if self.value() == 'null':
            return queryset.filter(certified__isnull=True)
        return queryset


class OverallRatingFilter(admin.SimpleListFilter):
    """Custom filter for overall rating ranges"""
    title = 'Overall Rating'
    parameter_name = 'overall_rating_range'

    def lookups(self, request, model_admin):
        return (
            ('5_stars', '5.0 Stars (Perfect)'),
            ('4_5_plus', '4.5+ Stars (Excellent)'),
            ('4_plus', '4.0+ Stars (Very Good)'),
            ('3_5_plus', '3.5+ Stars (Good)'),
            ('3_plus', '3.0+ Stars (Average)'),
            ('below_3', 'Below 3.0 Stars (Poor)'),
            ('no_rating', 'No Rating'),
        )

    def queryset(self, request, queryset):
        if self.value() == '5_stars':
            return queryset.filter(overall_rating=5.0)
        if self.value() == '4_5_plus':
            return queryset.filter(overall_rating__gte=4.5)
        if self.value() == '4_plus':
            return queryset.filter(overall_rating__gte=4.0)
        if self.value() == '3_5_plus':
            return queryset.filter(overall_rating__gte=3.5)
        if self.value() == '3_plus':
            return queryset.filter(overall_rating__gte=3.0)
        if self.value() == 'below_3':
            return queryset.filter(overall_rating__lt=3.0, overall_rating__isnull=False)
        if self.value() == 'no_rating':
            return queryset.filter(overall_rating__isnull=True)
        return queryset


class NumberOfRatingsFilter(admin.SimpleListFilter):
    """Custom filter for number of ratings ranges"""
    title = 'Number of Ratings'
    parameter_name = 'number_of_ratings_range'

    def lookups(self, request, model_admin):
        return (
            ('1000_plus', '1000+ Reviews (Very Popular)'),
            ('500_plus', '500+ Reviews (Popular)'),
            ('100_plus', '100+ Reviews (Well-reviewed)'),
            ('50_plus', '50+ Reviews (Moderate)'),
            ('10_plus', '10+ Reviews (Some feedback)'),
            ('1_plus', '1+ Reviews (Has reviews)'),
            ('no_reviews', 'No Reviews'),
        )

    def queryset(self, request, queryset):
        if self.value() == '1000_plus':
            return queryset.filter(number_of_ratings__gte=1000)
        if self.value() == '500_plus':
            return queryset.filter(number_of_ratings__gte=500)
        if self.value() == '100_plus':
            return queryset.filter(number_of_ratings__gte=100)
        if self.value() == '50_plus':
            return queryset.filter(number_of_ratings__gte=50)
        if self.value() == '10_plus':
            return queryset.filter(number_of_ratings__gte=10)
        if self.value() == '1_plus':
            return queryset.filter(number_of_ratings__gte=1)
        if self.value() == 'no_reviews':
            return queryset.filter(number_of_ratings__isnull=True)
        return queryset


@admin.register(CustomActivityCategory)
class CustomActivityCategoryAdmin(admin.ModelAdmin):
    """Admin interface for Custom Activity Categories from GetYourGuide"""
    
    list_display = ('name', 'activity_count', 'created_at', 'updated_at')
    search_fields = ('name',)
    list_filter = ('created_at', 'updated_at')
    ordering = ('name',)
    
    # Remove fieldsets to show all fields in single form
    fields = ('name',)
    
    readonly_fields = ('created_at', 'updated_at', 'activity_count')
    
    def activity_count(self, obj):
        """Show count of activities using this category"""
        if obj and obj.pk:
            from django.urls import reverse
            
            # Use the M2M relationship to count activities
            count = obj.activities.count()
            
            if count > 0:
                # Create clickable link to filtered activities
                url = reverse('admin:packages_customactivity_changelist')
                filter_params = f"?categories__exact={obj.pk}"
                return format_html(
                    '<a href="{}{}" style="color: #417690; text-decoration: none;">{}</a>',
                    url, filter_params, count
                )
            return count
        return 0
    activity_count.short_description = 'Activities Count'
    
    def has_module_permission(self, request):
        """Allow access to all users with admin permissions"""
        return request.user.is_staff
    
    def has_view_permission(self, request, obj=None):
        """Allow viewing for all staff users"""
        return request.user.is_staff
    
    def has_add_permission(self, request):
        """Allow adding for all staff users"""
        return request.user.is_staff
    
    def has_change_permission(self, request, obj=None):
        """Allow editing for all staff users"""
        return request.user.is_staff
    
    def has_delete_permission(self, request, obj=None):
        """Allow deletion for all staff users"""
        return False


@admin.register(CustomActivityLocation)
class CustomActivityLocationAdmin(GISModelAdmin):
    """Admin interface for Custom Activity Locations from GetYourGuide"""
    
    list_display = ('city', 'country', 'google_place_id', 'has_coordinates', 'activity_count', 'created_at')
    search_fields = ('city', 'country', 'google_place_id')
    list_filter = ('country', 'created_at', 'updated_at')
    ordering = ('country', 'city')
    
    # Remove fieldsets to show all fields in single form
    fields = ('city', 'country', 'google_place_id', 'location_coordinates')
    
    readonly_fields = ('created_at', 'updated_at', 'has_coordinates', 'activity_count')
    
    # GIS-specific settings
    default_lon = 0.0
    default_lat = 0.0
    default_zoom = 2
    map_width = 800
    map_height = 500
    map_srid = 4326
    display_srid = 4326
    
    def has_coordinates(self, obj):
        """Show if location has coordinates"""
        if obj.location_coordinates:
            try:
                x_coord = float(obj.location_coordinates.x)
                y_coord = float(obj.location_coordinates.y)
                return format_html(
                    '<span style="color: green;">✓ ({})</span>',
                    f'{x_coord:.6f}, {y_coord:.6f}'
                )
            except (ValueError, TypeError, AttributeError):
                return format_html('<span style="color: red;">✗ Invalid coordinates</span>')
        return format_html('<span style="color: red;">✗ No coordinates</span>')
    has_coordinates.short_description = 'Coordinates'
    
    def activity_count(self, obj):
        """Show count of activities in this location"""
        if obj and obj.pk:
            from django.urls import reverse
            
            # Use the M2M relationship to count activities
            count = obj.activities.count()
            
            if count > 0:
                # Create clickable link to filtered activities
                url = reverse('admin:packages_customactivity_changelist')
                filter_params = f"?locations__exact={obj.pk}"
                return format_html(
                    '<a href="{}{}" style="color: #417690; text-decoration: none;">{}</a>',
                    url, filter_params, count
                )
            return count
        return 0
    activity_count.short_description = 'Activities Count'
    
    def has_module_permission(self, request):
        """Allow access to all users with admin permissions"""
        return request.user.is_staff
    
    def has_view_permission(self, request, obj=None):
        """Allow viewing for all staff users"""
        return request.user.is_staff
    
    def has_add_permission(self, request):
        """Allow adding for all staff users"""
        return request.user.is_staff
    
    def has_change_permission(self, request, obj=None):
        """Allow editing for all staff users"""
        return request.user.is_staff
    
    def has_delete_permission(self, request, obj=None):
        """Allow deletion for all staff users"""
        return False


@admin.register(CustomActivity)
class CustomActivityAdmin(DynamicArrayMixin, GISModelAdmin):
    """Admin interface for Custom Activities from GetYourGuide with hyperlink relations"""
    
    form = CustomActivityAdminForm
    
    # Add custom template for change form to include the Reframe Highlights With AI button
    change_form_template = 'admin/packages/customactivity_change_form.html'
    
    list_display = (
        'title_display', 'destination', 'tour_id', 'activity_type', 
        'price_display', 'rating_display', 'persona_display',  'bestseller', 'certified',
        'is_active', 'cancellation_policy_display', 'opening_hours_display',
        'category_count', 'location_count',  'system_category_count', 'created_at'
    )
    
    search_fields = ('title', 'tour_id', 'destination__title', 'activity_type', 'description')
    
    list_filter = (
        BestsellerFilter, CertifiedFilter, OverallRatingFilter, NumberOfRatingsFilter,
        ActiveStatusFilter, 'has_pick_up', 'activity_type', 'destination', 'categories', 'locations', 
        'created_at', 'updated_at'
    )
    
    ordering = ('-created_at',)
    
    readonly_fields = (
        'created_at', 'updated_at', 'coordinates_display',
        'related_categories_link', 'related_locations_link', 'rating_display',
        'category_count', 'location_count', 'system_category_count', 'persona_display'
    )
    
    # Add inlines for M2M relationships
    inlines = [CustomActivityMediaInline, CustomActivityCategoryInline, CustomActivityLocationInline, CustomActivitySystemCategoryInline]
    
    # Remove fieldsets to show all fields in single form
    fields = (
        'destination', 'tour_id', 'title', 'abstract', 'description', 'activity_type',
        'additional_information', 'items_to_bring', 'not_allowed', 'not_suitable_for',
        'bestseller', 'certified', 'has_pick_up', 'is_active',
        'overall_rating', 'number_of_ratings', 'price', 'rating_display',
        'highlights', 'inclusions', 'exclusions', 'durations',
        'coordinates', 'coordinates_display', 'location_id',
        'opening_hours', 'cancellation_policy_text', 'cancellation_policy', 'persona', 'persona_display',
        'preferred_start_time', 'preferred_end_time',
        'addons', 'adult_price', 'child_price', 'infant_price',
        'category_count', 'location_count', 'system_category_count', 'related_categories_link', 'related_locations_link'
    )
    
    autocomplete_fields = ['destination']
    
    # GIS-specific settings
    default_lon = 0.0
    default_lat = 0.0
    default_zoom = 2
    map_width = 800
    map_height = 500
    map_srid = 4326
    display_srid = 4326

    def get_search_results(self, request, queryset, search_term):
        """
        Override to filter results based on the 'destination_id' GET parameter.
        """
        queryset, use_distinct = super().get_search_results(request, queryset, search_term)

        queryset = queryset.filter(is_active=True)
        destination_id = request.GET.get('destination_id')

        if destination_id:
            try:
                # Filter activities to the specified destination
                queryset = queryset.filter(destination__id=destination_id)
            except (ValueError, TypeError):
                pass

        return queryset, use_distinct

    def change_view(self, request, object_id, form_url='', extra_context=None):
        """Override change_view to add comprehensive logging"""
        logger.info(f"=== CustomActivityAdmin.change_view START ===")
        logger.info(f"CustomActivityAdmin.change_view - object_id: {object_id}")
        logger.info(f"CustomActivityAdmin.change_view - request.method: {request.method}")
        logger.info(f"CustomActivityAdmin.change_view - request.user: {request.user}")
        logger.info(f"CustomActivityAdmin.change_view - form_url: {form_url}")
        
        # Handle custom AI button BEFORE any formset processing
        if request.method == 'POST' and '_reframe_highlights_ai' in request.POST:
            logger.info(f"CustomActivityAdmin.change_view - AI button detected, handling early...")
            
            from django.http import HttpResponseRedirect
            from django.urls import reverse
            from django.contrib import messages
            from packages.utils.openai_activity_helper import OpenAIActivityHelper
            
            try:
                # Get the object
                obj = self.get_object(request, object_id)
                if not obj:
                    messages.error(request, 'Activity not found.')
                    return HttpResponseRedirect(reverse('admin:packages_customactivity_changelist'))
                
                logger.info(f"CustomActivityAdmin.change_view - processing AI for obj: {obj}")
                
                # Initialize OpenAI helper
                ai_helper = OpenAIActivityHelper()
                
                # Check if activity has highlights to reframe or needs new ones
                if obj.highlights and obj.highlights.strip():
                    # Reframe existing highlights
                    new_highlights = ai_helper.reframe_highlights(obj.highlights, obj.title)
                    action_type = "reframed"
                else:
                    # Generate new highlights from title
                    if not obj.title:
                        messages.error(request, f'Cannot generate highlights: Activity title is required.')
                        return HttpResponseRedirect(
                            reverse('admin:packages_customactivity_change', args=[obj.pk])
                        )
                    
                    new_highlights = ai_helper.generate_highlights(obj.title)
                    action_type = "generated"
                
                # Save the new highlights if generation was successful
                if new_highlights:
                    obj.highlights = new_highlights
                    obj.save(update_fields=['highlights'])
                    
                    # Show success message with preview of highlights
                    highlight_preview = new_highlights[:100] + "..." if len(new_highlights) > 100 else new_highlights
                    messages.success(
                        request, 
                        f'🤖 AI successfully {action_type} highlights for activity "{obj.title}". '
                    )
                else:
                    # Handle AI processing failure
                    error_details = ""
                    if ai_helper.has_errors():
                        error_details = f" Error details: {'; '.join(ai_helper.get_errors())}"
                    
                    messages.error(
                        request, 
                        f'Failed to {action_type.replace("ed", "")} highlights for activity "{obj.title}". '
                        f'Please try again later.{error_details}'
                    )
                
            except Exception as e:
                # Handle any unexpected errors
                logger.error(f"CustomActivityAdmin.change_view - AI processing error: {e}")
                messages.error(
                    request, 
                    f'An error occurred while processing highlights: {str(e)}'
                )
            
            logger.info(f"CustomActivityAdmin.change_view - AI processing complete, redirecting...")
            # Redirect back to the same page to show the message and updated content
            return HttpResponseRedirect(
                reverse('admin:packages_customactivity_change', args=[object_id])
            )
        
        if request.method == 'POST':
            logger.info(f"CustomActivityAdmin.change_view - request.POST keys: {list(request.POST.keys())}")
            # Log management form data specifically
            for key, value in request.POST.items():
                if 'TOTAL_FORMS' in key or 'INITIAL_FORMS' in key or 'media-' in key:
                    logger.info(f"CustomActivityAdmin.change_view - POST[{key}]: {value}")
            
            # Log ALL POST data for debugging
            logger.info(f"CustomActivityAdmin.change_view - FULL POST DATA:")
            for key, value in request.POST.items():
                logger.info(f"CustomActivityAdmin.change_view - POST[{key}]: {value}")
        
        try:
            # Get the object to check if it exists and has data
            obj = self.get_object(request, object_id)
            if obj:
                logger.info(f"CustomActivityAdmin.change_view - obj found: {obj} (pk: {obj.pk})")
                logger.info(f"CustomActivityAdmin.change_view - obj.title: {getattr(obj, 'title', 'No title')}")
                try:
                    media_count = obj.media.count()
                    logger.info(f"CustomActivityAdmin.change_view - obj.media.count(): {media_count}")
                    
                    # Log existing media details
                    for i, media in enumerate(obj.media.all()):
                        logger.info(f"CustomActivityAdmin.change_view - existing media {i}: {media} (pk: {media.pk}, file: {media.media})")
                except Exception as e:
                    logger.error(f"CustomActivityAdmin.change_view - error getting media: {e}")
            else:
                logger.warning(f"CustomActivityAdmin.change_view - obj NOT found for object_id: {object_id}")
        except Exception as e:
            logger.error(f"CustomActivityAdmin.change_view - error getting object: {e}")
        
        try:
            logger.info(f"CustomActivityAdmin.change_view - calling super().change_view()")
            result = super().change_view(request, object_id, form_url, extra_context)
            logger.info(f"CustomActivityAdmin.change_view - super().change_view() completed successfully")
            logger.info(f"=== CustomActivityAdmin.change_view END ===")
            return result
        except Exception as e:
            logger.error(f"CustomActivityAdmin.change_view - ERROR in super().change_view(): {e}")
            import traceback
            logger.error(f"CustomActivityAdmin.change_view - ERROR traceback: {traceback.format_exc()}")
            logger.info(f"=== CustomActivityAdmin.change_view END (WITH ERROR) ===")
            raise
    
    def get_formsets_with_inlines(self, request, obj=None):
        """Override to add logging for inline formsets"""
        logger.info(f"CustomActivityAdmin.get_formsets_with_inlines START - obj: {obj}")
        logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - request.method: {request.method}")
        
        if obj:
            logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - obj.pk: {obj.pk}")
            logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - obj.title: {getattr(obj, 'title', 'No title')}")
        
        # Log configured inlines first
        logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - configured inlines: {[inline.__name__ for inline in self.inlines]}")
        
        try:
            formsets_with_inlines = []
            
            logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - calling super().get_formsets_with_inlines()")
            for i, (formset, inline) in enumerate(super().get_formsets_with_inlines(request, obj)):
                logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - processing inline {i}: {inline.__class__.__name__}")
                logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - formset {i} type: {type(formset)}")
                logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - inline {i} model: {getattr(inline, 'model', 'No model')}")
                
                # Check permissions for each inline
                try:
                    has_view = inline.has_view_permission(request, obj)
                    has_add = inline.has_add_permission(request, obj)
                    has_change = inline.has_change_permission(request, obj)
                    has_delete = inline.has_delete_permission(request, obj)
                    logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - inline {i} permissions: view={has_view}, add={has_add}, change={has_change}, delete={has_delete}")
                    
                    # If no permissions, this inline might be skipped
                    if not (has_view or has_add or has_change or has_delete):
                        logger.warning(f"CustomActivityAdmin.get_formsets_with_inlines - inline {i} has NO permissions, might be skipped!")
                except Exception as e:
                    logger.error(f"CustomActivityAdmin.get_formsets_with_inlines - error checking permissions for inline {i}: {e}")
                
                # Log specific details for media inline
                if hasattr(inline, 'model') and inline.model.__name__ == 'CustomActivityMedia':
                    logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - MEDIA INLINE DETECTED")
                    logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - inline.fk_name: {getattr(inline, 'fk_name', 'Not set')}")
                    logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - inline.fields: {getattr(inline, 'fields', 'Not set')}")
                    
                    # Create an instance of the formset to check management form
                    try:
                        # Create formset instance with the object
                        formset_instance = formset(request.POST or None, instance=obj, prefix=f"customactivitymedia_set")
                        logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - formset_instance created: {formset_instance}")
                        logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - formset_instance.prefix: {formset_instance.prefix}")
                        
                        # Now access management form from the instance
                        mgmt_form = formset_instance.management_form
                        logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - management_form instance: {mgmt_form}")
                        logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - management_form.prefix: {mgmt_form.prefix}")
                        logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - management_form fields: {list(mgmt_form.fields.keys())}")
                        
                        # Check if management form data is in POST
                        if request.method == 'POST':
                            expected_total_forms_key = f"{mgmt_form.prefix}-TOTAL_FORMS"
                            expected_initial_forms_key = f"{mgmt_form.prefix}-INITIAL_FORMS"
                            logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - expected TOTAL_FORMS key: {expected_total_forms_key}")
                            logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - expected INITIAL_FORMS key: {expected_initial_forms_key}")
                            logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - TOTAL_FORMS in POST: {expected_total_forms_key in request.POST}")
                            logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - INITIAL_FORMS in POST: {expected_initial_forms_key in request.POST}")
                            
                            if expected_total_forms_key in request.POST:
                                logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - POST[{expected_total_forms_key}]: {request.POST[expected_total_forms_key]}")
                            if expected_initial_forms_key in request.POST:
                                logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - POST[{expected_initial_forms_key}]: {request.POST[expected_initial_forms_key]}")
                                
                    except Exception as e:
                        logger.error(f"CustomActivityAdmin.get_formsets_with_inlines - ERROR creating formset instance: {e}")
                        import traceback
                        logger.error(f"CustomActivityAdmin.get_formsets_with_inlines - ERROR traceback: {traceback.format_exc()}")
                
                # Try to instantiate the formset to check for errors
                try:
                    test_formset = formset(instance=obj)
                    logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - inline {i} formset instantiated successfully")
                except Exception as e:
                    logger.error(f"CustomActivityAdmin.get_formsets_with_inlines - ERROR instantiating formset for inline {i}: {e}")
                    import traceback
                    logger.error(f"CustomActivityAdmin.get_formsets_with_inlines - formset error traceback: {traceback.format_exc()}")
                
                formsets_with_inlines.append((formset, inline))
                logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - successfully added inline {i} to list")
            
            logger.info(f"CustomActivityAdmin.get_formsets_with_inlines - returning {len(formsets_with_inlines)} formsets")
            return formsets_with_inlines
            
        except Exception as e:
            logger.error(f"CustomActivityAdmin.get_formsets_with_inlines - ERROR: {e}")
            import traceback
            logger.error(f"CustomActivityAdmin.get_formsets_with_inlines - ERROR traceback: {traceback.format_exc()}")
            raise
    
    def title_display(self, obj):
        """Display title with truncation"""
        if obj.title:
            return obj.title[:100] + '...' if len(obj.title) > 100 else obj.title
        return 'No Title'
    title_display.short_description = 'Title'
    
    def price_display(self, obj):
        """Display formatted price"""
        if obj.price:
            try:
                price_value = float(obj.price)
                return format_html('<strong>₹{}</strong>', f'{price_value:.2f}')
            except (ValueError, TypeError):
                return format_html('<span style="color: gray;">Invalid Price</span>')
        return format_html('<span style="color: gray;">No Price</span>')
    price_display.short_description = 'Price'
    
    def rating_display(self, obj):
        """Display rating with stars"""
        if obj.overall_rating and obj.number_of_ratings:
            try:
                rating_value = float(obj.overall_rating)
                rating_count = int(obj.number_of_ratings)
                stars = '★' * int(rating_value)
                empty_stars = '☆' * (5 - int(rating_value))
                return format_html(
                    '<span style="color: gold;">{}</span><span style="color: lightgray;">{}</span> '
                    '({} reviews)',
                    stars, empty_stars, f'{rating_value:.1f}/5 -', rating_count
                )
            except (ValueError, TypeError):
                return format_html('<span style="color: gray;">Invalid Rating</span>')
        elif obj.overall_rating:
            try:
                rating_value = float(obj.overall_rating)
                stars = '★' * int(rating_value)
                empty_stars = '☆' * (5 - int(rating_value))
                return format_html(
                    '<span style="color: gold;">{}</span><span style="color: lightgray;">{}</span> '
                    '({})',
                    stars, empty_stars, f'{rating_value:.1f}/5'
                )
            except (ValueError, TypeError):
                return format_html('<span style="color: gray;">Invalid Rating</span>')
        return format_html('<span style="color: gray;">No Rating</span>')
    rating_display.short_description = 'Rating'
    
    def cancellation_policy_display(self, obj):
        """Display cancellation policy text with truncation and formatting"""
        if obj.cancellation_policy_text:
            policy_text = obj.cancellation_policy_text.strip()
            if len(policy_text) > 50:
                truncated = policy_text[:50] + '...'
                return format_html(
                    '<span title="{}" style="color: #666;">{}</span>',
                    policy_text, truncated
                )
            return format_html('<span style="color: #666;">{}</span>', policy_text)
        elif obj.cancellation_policy and isinstance(obj.cancellation_policy, dict):
            # Try to extract meaningful info from JSON policy
            policy_keys = list(obj.cancellation_policy.keys())
            if policy_keys:
                policy_info = f"JSON: {', '.join(policy_keys[:2])}"
                if len(policy_keys) > 2:
                    policy_info += f" (+{len(policy_keys) - 2} more)"
                return format_html('<span style="color: #666; font-style: italic;">{}</span>', policy_info)
        return format_html('<span style="color: gray;">No Policy</span>')
    cancellation_policy_display.short_description = 'Cancellation Policy'


    def opening_hours_display(self, obj):
        """Display opening hours from first schedule entry"""
        if obj.opening_hours:
            try:
                opening_hours = obj.opening_hours

                # If it's a string in Python list/dict format (with single quotes), parse it
                if isinstance(opening_hours, str):
                    opening_hours = ast.literal_eval(opening_hours)

                if not opening_hours:
                    return "No hours"

                first = opening_hours[0]

                opening = datetime.fromisoformat(first['opening_time'])
                closing = datetime.fromisoformat(first['closing_time'])

                return f"{opening.strftime('%H:%M')} - {closing.strftime('%H:%M')}"
            except Exception as e:
                return "Invalid data"
        return "No hours"
    opening_hours_display.short_description = 'Opening Hours'
    
    def persona_display(self, obj):
        """Display persona values from ArrayField"""
        if obj.persona and len(obj.persona) > 0:
            # Format each persona value and join them
            formatted_personas = [persona.replace('_', ' ').title() for persona in obj.persona]
            return ', '.join(formatted_personas)
        return format_html('<span style="color: gray;">No Persona</span>')
    persona_display.short_description = 'Persona'
    
    def coordinates_display(self, obj):
        """Display coordinates in a readable format"""
        if obj.coordinates:
            try:
                x_coord = float(obj.coordinates.x)
                y_coord = float(obj.coordinates.y)
                return format_html(
                    '<span style="color: blue;">📍 {}</span>',
                    f'{x_coord:.6f}, {y_coord:.6f}'
                )
            except (ValueError, TypeError, AttributeError):
                return format_html('<span style="color: gray;">Invalid Coordinates</span>')
        return format_html('<span style="color: gray;">No Coordinates</span>')
    coordinates_display.short_description = 'Coordinates'
    
    def category_count(self, obj):
        """Show count of categories for this activity"""
        if obj and obj.pk:
            count = obj.categories.count()
            if count > 0:
                return format_html('<span style="color: green;">{} categories</span>', count)
            return format_html('<span style="color: gray;">No categories</span>')
        return 0
    category_count.short_description = 'Categories'
    
    def location_count(self, obj):
        """Show count of locations for this activity"""
        if obj and obj.pk:
            count = obj.locations.count()
            if count > 0:
                return format_html('<span style="color: green;">{} locations</span>', count)
            return format_html('<span style="color: gray;">No locations</span>')
        return 0
    location_count.short_description = 'Locations'
    
    def system_category_count(self, obj):
        """Show count of system categories for this activity"""
        if obj and obj.pk:
            count = obj.system_categories.count()
            if count > 0:
                if obj.destination and obj.destination.partner:
                    partner_name = obj.destination.partner.entity_name[:20]  # Truncate if too long
                    return format_html(
                        '<span style="color: green;">{} categories</span><br>'
                        '<small style="color: gray;">Partner: {}</small>', 
                        count, partner_name
                    )
                else:
                    return format_html('<span style="color: green;">{} categories</span>', count)
            return format_html('<span style="color: gray;">No categories</span>')
        return 0
    system_category_count.short_description = 'System Categories'
    
    def pictures_display(self, obj):
        """Display simple picture links"""
        if obj.media.exists():
            links = []
            for i, media_item in enumerate(obj.media.all()[:5]):  # Show max 5 links
                if media_item.media:
                    links.append('<a href="{}" target="_blank">Image {}</a>'.format(media_item.media.url, i+1))
            if links:
                return format_html('<br>'.join(links))
        return 'No Images'
    pictures_display.short_description = 'Pictures'
    
    def related_categories_link(self, obj):
        """Display hyperlink to view categories for this activity"""
        if obj and obj.pk:
            category_count = obj.categories.count()
            url = reverse('admin:packages_customactivitycategory_changelist')
            
            if category_count > 0:
                # Show the actual categories this activity belongs to
                categories = obj.categories.all()[:3]  # Show first 3
                category_names = [cat.name for cat in categories]
                display_text = ', '.join(category_names)
                if category_count > 3:
                    display_text += f" (+{category_count - 3} more)"
                
                # Add filter parameter to show only categories for this activity
                filter_params = f"?activities__exact={obj.pk}"
                
                return format_html(
                    '<a href="{}{}" target="_blank" style="color: #417690; text-decoration: none;">'
                    '🔗 {} ({})</a><br>'
                    '<small style="color: gray;">{}</small>',
                    url, filter_params, f"{category_count} Categories", category_count, display_text
                )
            else:
                return format_html(
                    '<a href="{}" target="_blank" style="color: #417690; text-decoration: none;">'
                    '🔗 View All Categories</a><br>'
                    '<small style="color: gray;">No categories assigned</small>',
                    url
                )
        else:
            url = reverse('admin:packages_customactivitycategory_changelist')
            return format_html(
                '<a href="{}" target="_blank" style="color: #417690; text-decoration: none;">'
                '🔗 View All Categories</a>',
                url
            )
    related_categories_link.short_description = 'Related Categories'
    
    def related_locations_link(self, obj):
        """Display hyperlink to view locations for this activity"""
        if obj and obj.pk:
            location_count = obj.locations.count()
            url = reverse('admin:packages_customactivitylocation_changelist')
            
            if location_count > 0:
                # Show the actual locations this activity belongs to
                locations = obj.locations.all()[:3]  # Show first 3
                location_names = [str(loc) for loc in locations]
                display_text = ', '.join(location_names)
                if location_count > 3:
                    display_text += f" (+{location_count - 3} more)"
                
                # Add filter parameter to show only locations for this activity
                filter_params = f"?activities__exact={obj.pk}"
                
                return format_html(
                    '<a href="{}{}" target="_blank" style="color: #417690; text-decoration: none;">'
                    '🔗 {} ({})</a><br>'
                    '<small style="color: gray;">{}</small>',
                    url, filter_params, f"{location_count} Locations", location_count, display_text
                )
            else:
                return format_html(
                    '<a href="{}" target="_blank" style="color: #417690; text-decoration: none;">'
                    '🔗 View All Locations</a><br>'
                    '<small style="color: gray;">No locations assigned</small>',
                    url
                )
        else:
            url = reverse('admin:packages_customactivitylocation_changelist')
            return format_html(
                '<a href="{}" target="_blank" style="color: #417690; text-decoration: none;">'
                '🔗 View All Locations</a>',
                url
            )
    related_locations_link.short_description = 'Related Locations'
    
    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """Filter destinations for dropdown"""
        if db_field.name == "destination":
            # Show all destinations but you could filter based on user permissions
            kwargs["queryset"] = Destination.objects.filter(is_active=True).order_by('title')
        return super().formfield_for_foreignkey(db_field, request, **kwargs)
    
    def get_queryset(self, request):
        """Optimize queryset with select_related"""
        return super().get_queryset(request).select_related('destination')
    
    def has_module_permission(self, request):
        """Allow access to all users with admin permissions"""
        return request.user.is_staff
    
    def has_view_permission(self, request, obj=None):
        """Allow viewing for all staff users"""
        return request.user.is_staff
    
    def has_add_permission(self, request):
        """Allow adding for all staff users"""
        return request.user.is_staff
    
    def has_change_permission(self, request, obj=None):
        """Allow editing for all staff users"""
        return request.user.is_staff
    
    def has_delete_permission(self, request, obj=None):
        """Allow deletion for all staff users"""
        return False
    
    def change_view(self, request, object_id, form_url='', extra_context=None):
        """Override to handle custom AI button actions before form processing"""
        
        # Handle custom AI button actions BEFORE Django processes forms/formsets
        if request.method == 'POST' and object_id:
            try:
                obj = self.get_object(request, object_id)
                if obj:
                    # Handle highlights AI reframing
                    if '_reframe_highlights_ai' in request.POST:
                        return self._handle_highlights_ai_reframing(request, obj)
                    
                    # Handle description AI reframing  
                    if '_reframe_description_ai' in request.POST:
                        return self._handle_description_ai_reframing(request, obj)
            except Exception as e:
                from django.contrib import messages
                messages.error(request, f'Error processing AI request: {str(e)}')
        
        # For normal requests, call the parent method
        return super().change_view(request, object_id, form_url, extra_context)
    
    def _handle_highlights_ai_reframing(self, request, obj):
        """Handle highlights AI reframing logic"""
        from django.http import HttpResponseRedirect
        from django.urls import reverse
        from django.contrib import messages
        from packages.utils.openai_activity_helper import OpenAIActivityHelper
        
        logger.info(f"CustomActivityAdmin._handle_highlights_ai_reframing - AI button clicked, processing...")
        
        try:
            # Initialize OpenAI helper
            ai_helper = OpenAIActivityHelper()
            
            # Check if activity has highlights to reframe or needs new ones
            if obj.highlights and obj.highlights.strip():
                # Reframe existing highlights
                new_highlights = ai_helper.reframe_highlights(obj.highlights, obj.title)
                action_type = "reframed"
            else:
                # Generate new highlights from title
                if not obj.title:
                    messages.error(request, f'Cannot generate highlights: Activity title is required.')
                    return HttpResponseRedirect(
                        reverse('admin:packages_customactivity_change', args=[obj.pk])
                    )
                
                new_highlights = ai_helper.generate_highlights(obj.title)
                action_type = "generated"
            
            # Save the new highlights if generation was successful
            if new_highlights:
                obj.highlights = new_highlights
                obj.save(update_fields=['highlights'])
                
                # Show success message with preview of highlights
                highlight_preview = new_highlights[:100] + "..." if len(new_highlights) > 100 else new_highlights
                messages.success(
                    request, 
                    f'🤖 AI successfully {action_type} highlights for activity "{obj.title}". '
                )
            else:
                # Handle AI processing failure
                error_details = ""
                if ai_helper.has_errors():
                    error_details = f" Error details: {'; '.join(ai_helper.get_errors())}"
                
                messages.error(
                    request, 
                    f'Failed to {action_type.replace("ed", "")} highlights for activity "{obj.title}". '
                    f'Please try again later.{error_details}'
                )
            
        except Exception as e:
            # Handle any unexpected errors
            messages.error(
                request, 
                f'An error occurred while processing highlights for activity "{obj.title}": {str(e)}'
            )
        
        logger.info(f"CustomActivityAdmin._handle_highlights_ai_reframing - AI highlights processing complete, redirecting...")
        # Redirect back to the same page to show the message and updated content
        return HttpResponseRedirect(
            reverse('admin:packages_customactivity_change', args=[obj.pk])
        )
    
    def _handle_description_ai_reframing(self, request, obj):
        """Handle description AI reframing logic"""
        from django.http import HttpResponseRedirect
        from django.urls import reverse
        from django.contrib import messages
        from packages.utils.openai_activity_helper import OpenAIActivityHelper
        
        logger.info(f"CustomActivityAdmin._handle_description_ai_reframing - AI button clicked, processing...")
        
        try:
            # Initialize OpenAI helper
            ai_helper = OpenAIActivityHelper()
            
            # Check if activity has description to reframe or needs new one
            if obj.description and obj.description.strip():
                # Reframe existing description
                new_description = ai_helper.reframe_description(obj.description, obj.title)
                action_type = "reframed"
            else:
                # Generate new description from title
                if not obj.title:
                    messages.error(request, f'Cannot generate description: Activity title is required.')
                    return HttpResponseRedirect(
                        reverse('admin:packages_customactivity_change', args=[obj.pk])
                    )
                
                new_description = ai_helper.generate_description(obj.title)
                action_type = "generated"
            
            # Save the new description if generation was successful
            if new_description:
                obj.description = new_description
                obj.save(update_fields=['description'])
                
                # Show success message with preview of description
                description_preview = new_description[:100] + "..." if len(new_description) > 100 else new_description
                messages.success(
                    request, 
                    f'🤖 AI successfully {action_type} description for activity "{obj.title}". '
                )
            else:
                # Handle AI processing failure
                error_details = ""
                if ai_helper.has_errors():
                    error_details = f" Error details: {'; '.join(ai_helper.get_errors())}"
                
                messages.error(
                    request, 
                    f'Failed to {action_type.replace("ed", "")} description for activity "{obj.title}". '
                    f'Please try again later.{error_details}'
                )
            
        except Exception as e:
            # Handle any unexpected errors
            messages.error(
                request, 
                f'An error occurred while processing description for activity "{obj.title}": {str(e)}'
            )
        
        logger.info(f"CustomActivityAdmin._handle_description_ai_reframing - AI description processing complete, redirecting...")
        # Redirect back to the same page to show the message and updated content
        return HttpResponseRedirect(
            reverse('admin:packages_customactivity_change', args=[obj.pk])
        )
    
    # def formfield_for_dbfield(self, db_field, request, **kwargs):
    #     """Ensure array fields are properly handled"""
    #     # Let DynamicArrayMixin handle array fields
    #     formfield = super().formfield_for_dbfield(db_field, request, **kwargs)
    #     return formfield

# ===============================================
# TRIPJACK HOTELS ADMIN CLASSES
# ===============================================

class TripjackHotelDestinationMappingInline(admin.TabularInline):
    """Inline admin for TripJack hotel destination mappings"""
    model = TripjackHotelDestinationMapping
    extra = 1
    fields = ('destination', 'created_at')
    readonly_fields = ('created_at',)
    autocomplete_fields = ['destination']


@admin.register(TripjackHotels)
class TripjackHotelsAdmin(admin.ModelAdmin, DynamicArrayMixin):
    """Admin interface for TripJack Hotels"""
    form = TripjackHotelsAdminForm
    list_display = (
        'name', 'hotel_id', 'star_rating', 'base_price', 'city_name',
        'country_name', 'destinations_display', 'images_count', 'is_active', 'created_at'
    )
    list_filter = (
        TripjackHotelDestinationFilter, 'star_rating', 'country_name', 'city_name', 'property_type',
        'is_active', 'created_at'
    )
    search_fields = (
        'name', 'hotel_id', 'description', 'city_name', 'country_name',
        'address_line', 'destinations__title'
    )
    ordering = ('-created_at',)

    # Define fieldsets for the change view (editing an existing hotel)
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'hotel_id', 'description', 'star_rating', 'property_type', 'is_active')
        }),
        ('Pricing and Timings', {
            'fields': ('base_price', 'check_in_time', 'check_out_time')
        }),
        ('Location', {
            'fields': ('latitude', 'longitude', 'address_line', 'postal_code',
                       'city_name', 'country_name', 'full_address_display')
        }),
        ('Amenities', {
            'fields': ('amenities',)
        }),
        ('Media', {
            'fields': ('manage_images',)
        }),
        ('System Information', {
            'fields': ('external_id', 'created_at', 'updated_at', 'destinations_count'),
            'classes': ('collapse',)
        }),
    )

    # Define fieldsets for the add view (creating a new hotel)
    add_fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'hotel_id', 'description', 'star_rating', 'property_type', 'is_active')
        }),
        ('Pricing and Timings', {
            'fields': ('base_price', 'check_in_time', 'check_out_time')
        }),
        ('Location', {
            'fields': ('latitude', 'longitude', 'address_line', 'postal_code',
                       'city_name', 'country_name')
        }),
        ('Amenities', {
            'fields': ('amenities',)
        }),
        ('Media', {
            'fields': ('manage_images',)
        }),
    )

    inlines = [TripjackHotelDestinationMappingInline]
    actions = ['activate_hotels', 'deactivate_hotels']

    def get_search_results(self, request, queryset, search_term):
        """
        Override to filter results based on the 'destination_id' GET parameter.
        """
        queryset, use_distinct = super().get_search_results(request, queryset, search_term)

        queryset = queryset.filter(is_active=True)
        destination_id = request.GET.get('destination_id')

        if destination_id:
            try:
                # Filter hotels to the specified destination
                queryset = queryset.filter(destinations__id=destination_id)
            except (ValueError, TypeError):
                pass

        return queryset, use_distinct

    def get_queryset(self, request):
        from django.db.models import Min
        """
        Overrides the default queryset to show distinct hotel names by default,
        while ensuring filters and search work correctly on the full dataset.
        """
        # Start with the default queryset
        qs = super().get_queryset(request)

        # We need to determine if any filters or a search query are active.
        # The admin uses 'q' for the search parameter. Other keys are for filters.
        # We can inspect the request's GET parameters.
        query_params = request.GET.copy()

        # We ignore parameters that are for pagination and ordering, as they
        # don't represent a filter on the data itself.
        query_params.pop('p', None)  # Page number
        query_params.pop('o', None)  # Ordering
        query_params.pop('all', None) # "Show all" link

        # If any other parameters are left, it means a filter or a search is active.
        # In this case, we MUST return the full queryset so that the filter/search
        # can operate on the entire set of records.
        if query_params:
            return qs

        # If we are here, it means no filters or search are active. This is the
        # default changelist view, so we can now safely de-duplicate.
        #
        # This subquery finds the ID of the first record for each unique 'name'.
        distinct_name_ids = (
            self.model.objects.values('name')
            .annotate(min_id=Min('id'))
            .values_list('min_id', flat=True)
        )

        # Filter the main queryset to only include these unique records for display.
        return qs.filter(id__in=distinct_name_ids)

    def render_change_form(self, request, context, add=False, change=False, form_url='', obj=None):
        context.update({'has_file_field': True})
        
        # Add fetch images and generate description buttons only on change form (not add form)
        if obj and change:
            context.update({
                'show_fetch_images_button': True,
                'fetch_images_url': f'../fetch-images/',
                'generate_description_url': f'../generate-description/'
            })
        
        return super().render_change_form(request, context, add=add, change=change, form_url=form_url, obj=obj)

    def get_fieldsets(self, request, obj=None):
        """
        Return add_fieldsets for add view and default fieldsets for change view.
        """
        if not obj:
            return self.add_fieldsets
        return super().get_fieldsets(request, obj)

    def get_readonly_fields(self, request, obj=None):
        """Make property_type readonly on change"""
        base_readonly = [
            'external_id', 'created_at', 'updated_at', 'destinations_count',
            'full_address_display'
        ]
        if obj:  # obj is not None, so this is a change page
            return base_readonly + ['property_type']
        return base_readonly

    def destinations_display(self, obj):
        """Show comma-separated destination names with ellipsis if many"""
        destinations = obj.destinations.all()
        if not destinations:
            return "No destinations"

        dest_names = [dest.title for dest in destinations]
        if len(dest_names) <= 2:
            return ", ".join(dest_names)
        else:
            return f"{', '.join(dest_names[:2])}... (+{len(dest_names)-2} more)"
    destinations_display.short_description = "Destinations"

    def destinations_count(self, obj):
        """Show count of destinations this hotel is mapped to"""
        count = obj.destinations.count()
        if count > 0:
            return f"{count} destination{'s' if count != 1 else ''}"
        return "No destinations"
    destinations_count.short_description = "Destinations Count"

    def images_count(self, obj):
        """Show count of images"""
        if obj.images and isinstance(obj.images, list):
            return f"{len(obj.images)} image{'s' if len(obj.images) != 1 else ''}"
        return "No images"
    images_count.short_description = "Images"

    def amenities_count(self, obj):
        """Show count of amenities"""
        if obj.amenities and isinstance(obj.amenities, list):
            return f"{len(obj.amenities)} amenity/ies"
        return "No amenities"
    amenities_count.short_description = "Amenities"

    def full_address_display(self, obj):
        """Display formatted full address"""
        return obj.full_address
    full_address_display.short_description = "Full Address"

    def activate_hotels(self, request, queryset):
        """Bulk activate hotels"""
        updated = queryset.update(is_active=True)
        self.message_user(request, f"Successfully activated {updated} hotel(s).")
    activate_hotels.short_description = "Activate selected hotels"

    def deactivate_hotels(self, request, queryset):
        """Bulk deactivate hotels"""
        updated = queryset.update(is_active=False)
        self.message_user(request, f"Successfully deactivated {updated} hotel(s).")
    deactivate_hotels.short_description = "Deactivate selected hotels"

    def formfield_for_dbfield(self, db_field, request, **kwargs):
        """Customize form fields"""
        if db_field.name == 'property_type':
            # Get property types, strip spaces, and normalize case
            property_types = (
                TripjackHotels.objects
                .values_list('property_type', flat=True)
                .exclude(property_type__isnull=True)
            )

            # Use a set for uniqueness after normalizing
            normalized = sorted(
                {pt.strip() for pt in property_types if pt and pt.strip()}
            )

            # Build choices
            choices = [('', '-- Select Property Type --')] + [(pt, pt) for pt in normalized]
            kwargs['widget'] = forms.Select(choices=choices)

        return super().formfield_for_dbfield(db_field, request, **kwargs)
    
    def get_urls(self):
        """Add custom URL for fetching images from SerpAPI"""
        from django.urls import path
        urls = super().get_urls()
        custom_urls = [
            path(
                '<int:object_id>/fetch-images/',
                self.admin_site.admin_view(self.fetch_images_view),
                name='packages_tripjackhotels_fetch_images',
            ),
            path(
                '<int:object_id>/generate-description/',
                self.admin_site.admin_view(self.generate_description_view),
                name='packages_tripjackhotels_generate_description',
            ),
        ]
        return custom_urls + urls
    
    def fetch_images_view(self, request, object_id):
        """Handle SerpAPI image fetching for a specific hotel"""
        import json
        from django.http import JsonResponse
        from django.core.files.storage import default_storage
        from packages.utils.establishment_utils import get_place_details, download_image
        from base.storage_utils import tripjack_hotel_media_upload_path
        import logging
        
        logger = logging.getLogger(__name__)
        
        if request.method != 'POST':
            return JsonResponse({'success': False, 'error': 'Only POST method allowed'})
        
        try:
            # Get the hotel object
            hotel = self.get_object(request, object_id)
            if not hotel:
                return JsonResponse({'success': False, 'error': 'Hotel not found'})
            
            # Build query for SerpAPI using hotel name and location
            query = hotel.name
            location = f"{hotel.city_name}, {hotel.country_name}" if hotel.city_name and hotel.country_name else hotel.country_name or "India"
            
            logger.info(f"Fetching images for hotel: {hotel.name} in location: {location}")
            
            # Get place details from SerpAPI (fetch up to 5 images)
            details = get_place_details(query, location, max_images=5)
            
            if not details or not details.get('image_urls'):
                return JsonResponse({'success': False, 'error': 'No images found from SerpAPI'})
            
            # Get image URLs (already limited by max_images parameter)
            image_urls = details.get('image_urls', [])
            
            if not image_urls:
                return JsonResponse({'success': False, 'error': 'No valid image URLs returned'})
            
            # Get current images
            current_images = hotel.images or []
            new_images = []
            uploaded_count = 0
            
            # Download and upload each image
            for i, image_url in enumerate(image_urls):
                try:
                    # Download image
                    image_content = download_image(image_url)
                    if not image_content:
                        logger.warning(f"Failed to download image {i+1} from {image_url}")
                        continue
                    
                    # Create upload path similar to TripjackHotelsAdminForm
                    temp_media_instance = type('TempMedia', (object,), {'hotel': hotel})()
                    file_path = tripjack_hotel_media_upload_path(temp_media_instance, image_content.name)
                    
                    # Save to S3
                    s3_key = default_storage.save(file_path, image_content)
                    new_images.append(s3_key)
                    uploaded_count += 1
                    logger.info(f"Successfully uploaded image {i+1} to S3: {s3_key}")
                    
                except Exception as e:
                    logger.error(f"Error processing image {i+1} from {image_url}: {e}")
                    continue
            
            if uploaded_count == 0:
                return JsonResponse({'success': False, 'error': 'Failed to upload any images'})
            
            # Update hotel images (append to existing)
            updated_images = current_images + new_images
            hotel.images = updated_images
            hotel.save()
            
            return JsonResponse({
                'success': True, 
                'message': f'Successfully fetched and uploaded {uploaded_count} images',
                'uploaded_count': uploaded_count,
                'total_images': len(updated_images)
            })
            
        except Exception as e:
            logger.error(f"Error in fetch_images_view: {e}")
            return JsonResponse({'success': False, 'error': str(e)})
    
    def generate_description_view(self, request, object_id):
        """Handle AI description generation for a specific hotel"""
        from django.http import JsonResponse
        from packages.utils.ai_description_helper import AIDescriptionHelper
        import logging
        
        logger = logging.getLogger(__name__)
        
        if request.method != 'POST':
            return JsonResponse({'success': False, 'error': 'Only POST method allowed'})
        
        try:
            # Get the hotel object
            hotel = self.get_object(request, object_id)
            if not hotel:
                return JsonResponse({'success': False, 'error': 'Hotel not found'})
            
            # Initialize AI helper
            try:
                ai_helper = AIDescriptionHelper()
            except ValueError as e:
                return JsonResponse({'success': False, 'error': str(e)})
            
            # Determine action based on existing description
            action_type = "rephrase" if (hotel.description and hotel.description.strip()) else "generate"
            logger.info(f"Starting AI description {action_type} for hotel: {hotel.name}")
            
            # Generate or rephrase description
            description = ai_helper.generate_or_rephrase_description(hotel)
            
            if not description:
                return JsonResponse({
                    'success': False, 
                    'error': f'Failed to {action_type} description using AI'
                })
            
            # Save the original description for comparison
            original_description = hotel.description
            
            # Update hotel description
            hotel.description = description
            hotel.save(update_fields=['description'])
            
            # Build response message
            if action_type == "rephrase":
                message = f'Successfully rephrased description: "{description[:100]}..."'
            else:
                message = f'Successfully generated new description: "{description[:100]}..."'
            
            return JsonResponse({
                'success': True, 
                'message': message,
                'description': description,
                'action_type': action_type,
                'original_description': original_description
            })
            
        except Exception as e:
            logger.error(f"Error in generate_description_view: {e}")
            return JsonResponse({'success': False, 'error': str(e)})


class CustomPackageItineraryInline(admin.StackedInline):
    """Inline admin for CustomPackageItinerary"""
    model = CustomPackageItinerary
    form = CustomPackageItineraryForm
    extra = 0
    autocomplete_fields = ['hotel', 'activity']
    verbose_name = "Itinerary Item"
    verbose_name_plural = "Package Itinerary (Add Activities & Hotels)"
    
    fieldsets = (
        (None, {
            'fields': ('day_number', 'type'),
            'classes': ('wide',),
        }),
        ('Activity/Hotel Selection', {
            'fields': ('activity', 'hotel'),
            'classes': ('wide',),
            'description': 'Select either an activity OR a hotel for this day based on the type selected above.'
        }),
    )

    class Media:
        js = ('admin/js/custom_package_admin.js',)
        css = {
            'all': ('admin/css/custom_package_admin.css',)
        }

    def get_formset(self, request, obj=None, **kwargs):
        """Customize widget actions for activity and hotel fields and add validation"""
        formset = super().get_formset(request, obj, **kwargs)

        # Configure activity field to only show view (eye) icon
        if 'activity' in formset.form.base_fields:
            activity_widget = formset.form.base_fields['activity'].widget
            activity_widget.can_add_related = False     # Remove add (plus) icon
            activity_widget.can_change_related = False  # Remove edit (pencil) icon
            activity_widget.can_delete_related = False  # Remove delete (cross) icon
            activity_widget.can_view_related = True     # Keep view (eye) icon
        # Configure hotel field to only show view (eye) icon
        if 'hotel' in formset.form.base_fields:
            hotel_widget = formset.form.base_fields['hotel'].widget
            hotel_widget.can_add_related = False     # Remove add (plus) icon
            hotel_widget.can_change_related = False  # Remove edit (pencil) icon
            hotel_widget.can_delete_related = False  # Remove delete (cross) icon
            hotel_widget.can_view_related = True     # Keep view (eye) icon

        # Override formset clean method to validate at least one itinerary item
        original_clean = formset.clean

        def clean_with_itinerary_validation(self):
            if hasattr(original_clean, '__call__'):
                original_clean(self)

            # Check if at least one valid itinerary item is provided
            valid_itinerary_count = 0
            for form in self.forms:
                if (not form.cleaned_data.get('DELETE', False) and
                    (form.cleaned_data.get('activity') or form.cleaned_data.get('hotel'))):
                    valid_itinerary_count += 1

            if valid_itinerary_count == 0:
                from datetime import datetime
                print(f"[{datetime.now()}] Package itinerary validation failed: No itinerary items provided")
                from django.core.exceptions import ValidationError
                raise ValidationError("At least one itinerary item (activity or hotel) is required for this package.")

        formset.clean = clean_with_itinerary_validation
        return formset

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """Use empty querysets for performance - JavaScript and validation handle the rest"""
        if db_field.name == 'activity':
            # Empty queryset for performance - JavaScript populates options
            kwargs["queryset"] = CustomActivity.objects.none()
                
        elif db_field.name == 'hotel':
            # Empty queryset for performance - JavaScript populates options
            kwargs["queryset"] = TripjackHotels.objects.none()
                
        return super().formfield_for_foreignkey(db_field, request, **kwargs)


@admin.register(CustomPackage)
class CustomPackageAdmin(admin.ModelAdmin):
    """Admin for CustomPackage with dynamic itinerary based on destination selection"""
    
    form = CustomPackageAdminForm
    list_display = ['title', 'destination', 'type', 'duration_nights_display', 'price', 'is_published', 'created_at']
    list_filter = ['type', 'is_published', 'destination', 'created_at']
    search_fields = ['title', 'package_no', 'destination__title']
    autocomplete_fields = ['destination']
    
    fieldsets = (
        ('Destination Selection', {
            'fields': ('destination',),
            'description': 'REQUIRED: Select the destination first! This will filter the activities and hotels available in the itinerary section.',
            'classes': ('wide', 'destination-section'),
        }),
        ('Package Information', {
            'fields': (
                # Basic package details
                'title', 'type', 'package_no', 'owner', 'explore_order',
                # Duration and pricing
                'duration_in_nights', 'calculated_price_display',
                # Rating and reviews  
                'rating', 'rating_description',
                # Auto-generated displays
                'package_category_display', 'package_best_time_display', 'package_persona_display'
            ),
            'classes': ('wide',),
        }),
        ('Package Details & Status', {
            'fields': ('about_this_tour', 'highlights', 'inclusions', 'exclusions', 'addons', 'important_notes', 'visa_type', 'is_active', 'is_published'),
            'classes': ('wide', 'collapse'),
        }),
        ('Highlights & Attractions', {
            'fields': ('destination_safety', 'popular_restaurants', 'cultural_info', 'what_to_shop', 'what_to_pack'),
            'classes': ('wide', 'collapse'),
        }),
    )
    
    inlines = [PackageMediaInline, CustomPackageItineraryInline]
    
    class Media:
        js = (
            'admin/js/jquery.init.js',  # Ensure jQuery is loaded first
            'admin/js/custom_package_admin.js',
        )
        css = {
            'all': ('admin/css/custom_package_admin.css',)
        }

    def get_form(self, request, obj=None, **kwargs):
        """Override for any custom form logic"""
        return super().get_form(request, obj, **kwargs)

    def get_readonly_fields(self, request, obj=None):
        """Make certain fields readonly based on context"""
        readonly_fields = ['created_at', 'updated_at']
        
        # Add auto-generated fields only when editing existing packages
        if obj and obj.pk:
            readonly_fields.extend([
                'destination',  # Make destination readonly for existing packages
                'package_category_display', 
                'package_best_time_display', 
                'package_persona_display',
                'calculated_price_display'  # Make calculated price readonly
            ])
        
        return readonly_fields

    def get_fieldsets(self, request, obj=None):
        """Conditionally modify fieldsets based on whether we're creating or editing"""
        fieldsets = list(super().get_fieldsets(request, obj))
        
        # Modify fieldsets when creating new package
        if not obj or not obj.pk:
            for i, (name, options) in enumerate(fieldsets):
                if name == 'Package Information':
                    # Remove auto-generated fields from Package Information when creating
                    fields = list(options['fields'])
                    fields_to_remove = ['package_category_display', 'package_best_time_display', 'package_persona_display', 'calculated_price_display']
                    for field in fields_to_remove:
                        if field in fields:
                            fields.remove(field)
                    
                    # Update the fieldset
                    new_options = options.copy()
                    new_options['fields'] = tuple(fields)
                    fieldsets[i] = (name, new_options)
        
        return fieldsets

    def save_model(self, request, obj, form, change):
        """Override save to handle admin-specific logic"""

        # Auto-assign partner from current user for ALL objects that don't have one
        # Only assign partner if it's not already set
        if not obj.partner_id:
            if obj.destination and hasattr(obj.destination, 'partner_id') and obj.destination.partner_id:
                # Get the Partner object using the partner_id from destination
                from accounts.models import Partner
                try:
                    partner = Partner.objects.get(id=obj.destination.partner_id, is_active=True)
                    obj.partner = partner
                except Partner.DoesNotExist:
                    raise ValueError(f"Partner with ID {obj.destination.partner_id} not found or not active. Please contact administrator.")
            else:
                # This should not happen in production, but handle gracefully
                raise ValueError("Destination has no partner assigned. Please contact administrator.")

        # Call parent save - the model's save method will handle all the auto-generation logic
        super().save_model(request, obj, form, change)

        # Handle related model fields (highlights, inclusions, addons) from the form
        if hasattr(form, '_save_related_fields'):
            form._save_related_fields(obj)
    
    def save_related(self, request, form, formsets, change):
        """Override to recalculate price after saving inline formsets"""
        # Save the related objects first
        super().save_related(request, form, formsets, change)
        
        # After saving inlines, recalculate the price
        obj = form.instance
        if obj.pk:
            # Force price recalculation
            obj.save(update_fields=['price', 'price_per_person'])

    def formfield_for_choice_field(self, db_field, request, **kwargs):
        """Override for choice fields - type field is handled by custom widget"""
        return super().formfield_for_choice_field(db_field, request, **kwargs)
    
    def formfield_for_dbfield(self, db_field, request, **kwargs):
        """Override to handle ArrayFields properly"""
        # Let DynamicArrayMixin handle ArrayFields
        if hasattr(db_field, 'base_field'):  # ArrayField detection
            return super().formfield_for_dbfield(db_field, request, **kwargs)
        return super().formfield_for_dbfield(db_field, request, **kwargs)

    def package_category_display(self, obj):
        """Display package categories"""
        return obj.get_package_category_display() if obj else "No categories assigned"
    
    package_category_display.short_description = "Package Categories"
    
    def package_best_time_display(self, obj):
        """Display best time to visit from destination"""
        return obj.get_package_best_time_display() if obj else "Not specified"
    
    package_best_time_display.short_description = "Best Time to Visit"
    
    def package_persona_display(self, obj):
        """Display personas from custom activities"""
        return obj.get_package_persona_display() if obj else "No personas identified"
    
    package_persona_display.short_description = "Package Personas"
    
    def calculated_price_display(self, obj):
        """Display calculated price from itinerary"""
        if obj and obj.pk:
            return obj.get_calculated_price_display()
        return "Save package and add itinerary to calculate price"
    
    calculated_price_display.short_description = "Calculated Price (from Itinerary)"

    def duration_nights_display(self, obj):
        """Display duration in nights as 'xN' format"""
        if obj and obj.duration_in_nights is not None:
            return f"{obj.duration_in_nights}N"
        return "-"

    duration_nights_display.short_description = "Duration"

    def get_queryset(self, request):
        """Filter to show only Custom Admin and Custom AI packages"""
        qs = super().get_queryset(request)

        # Filter for only Custom Admin and Custom AI packages
        custom_types = PackageTypeChoices.custom_package_choices()
        qs = qs.filter(type__in=custom_types)

        # Also apply partner filtering like other admins
        effective_partner = get_user_effective_partner(request)
        if effective_partner:
            return qs.filter(partner=effective_partner).select_related(
                'destination', 'partner'
            )
        return qs.none()
