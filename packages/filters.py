from django_filters import rest_framework as filters
from django.db.models import Q, Min, Max
from .models import Category, Destination, Package
from .choices import MonthChoices


class CategoryFilter(filters.FilterSet):
    """Filter class for Category model"""
    search = filters.CharFilter(method='filter_search', help_text="Search in title and description")
    
    class Meta:
        model = Category
        fields = ['search']
    
    def filter_search(self, queryset, name, value):
        """Custom search filter for title and description"""
        if value:
            return queryset.filter(
                Q(title__icontains=value)
            )
        return queryset


class DashboardDestinationFilter(filters.FilterSet):
    """Comprehensive filter class for Destination dashboard API"""
    
    # Search functionality
    search = filters.CharFilter(method='filter_search', help_text="Search in destination title")
    
    # Categories filter - multiple select using external_id
    categories = filters.CharFilter(
        method='filter_categories',
        help_text="Filter by multiple category external_ids (comma-separated) - ALL categories must match (AND condition)"
    )
    
    # Activities filter - multiple select using external_id
    activities = filters.Char<PERSON>ilter(
        method='filter_activities', 
        help_text="Filter by multiple activity external_ids (comma-separated)"
    )
    
    # Visiting months filter - comma-separated values
    visiting_months = filters.CharFilter(
        method='filter_visiting_months',
        help_text="Filter by best time to visit months (comma-separated: 1,2,3)"
    )
    
    # Price range filters
    min_price = filters.NumberFilter(
        method='filter_min_price',
        help_text="Filter by minimum starting price"
    )
    max_price = filters.NumberFilter(
        method='filter_max_price',
        help_text="Filter by maximum starting price"
    )
    
    # Duration range filters (in nights)
    min_duration = filters.NumberFilter(
        method='filter_min_duration',
        help_text="Filter by minimum duration in nights"
    )
    max_duration = filters.NumberFilter(
        method='filter_max_duration',
        help_text="Filter by maximum duration in nights"
    )
    
    # Ordering with custom default
    ordering = filters.OrderingFilter(
        fields=(
            ('starting_price', 'starting_price'),
            ('explore_order', 'explore_order'),
        ),
        field_labels={
            'starting_price': 'Price (Low to High)',
            '-starting_price': 'Price (High to Low)',
            'explore_order': 'Default Order',
        },
        help_text="Order by: starting_price, explore_order (add '-' for descending)"
    )
    
    class Meta:
        model = Destination
        fields = [
            'search', 'categories', 'activities', 'visiting_months',
            'min_price', 'max_price', 'min_duration', 'max_duration', 'ordering'
        ]
    
    def filter_search(self, queryset, name, value):
        """Custom search filter for destination title"""
        if value:
            return queryset.filter(title__icontains=value)
        return queryset
    
    def filter_categories(self, queryset, name, value):
        """Filter destinations by category external_ids (comma-separated) using AND condition"""
        if value:
            try:
                external_ids = [id.strip() for id in value.split(',') if id.strip()]
                if external_ids:
                    # Apply each category filter sequentially to create AND condition
                    # This ensures destinations have ALL specified categories, not just ANY
                    for external_id in external_ids:
                        queryset = queryset.filter(categories__category__external_id=external_id)
                    return queryset.distinct()
            except (ValueError, AttributeError):
                pass
        return queryset
    
    def filter_activities(self, queryset, name, value):
        """Filter destinations by activity external_ids (comma-separated)"""
        if value:
            try:
                external_ids = [id.strip() for id in value.split(',') if id.strip()]
                if external_ids:
                    return queryset.filter(activities__activity__external_id__in=external_ids).distinct()
            except (ValueError, AttributeError):
                pass
        return queryset
    
    def filter_visiting_months(self, queryset, name, value):
        """Filter destinations by best time to visit months (comma-separated)"""
        if value:
            # Convert comma-separated values to integers
            try:
                month_numbers = [int(month.strip()) for month in value.split(',') if month.strip()]
                # Filter destinations that have any of the selected months
                month_filters = Q()
                for month in month_numbers:
                    month_filters |= Q(best_time_to_visit__contains=[month])
                return queryset.filter(month_filters)
            except (ValueError, TypeError):
                pass
        return queryset
    
    def filter_min_price(self, queryset, name, value):
        """Filter by minimum starting price using annotated field"""
        if value is not None:
            return queryset.filter(starting_price__gte=value)
        return queryset
    
    def filter_max_price(self, queryset, name, value):
        """Filter by maximum starting price using annotated field"""
        if value is not None:
            return queryset.filter(starting_price__lte=value)
        return queryset
    
    def filter_min_duration(self, queryset, name, value):
        """Filter destinations that have at least one package with duration >= value"""
        if value is not None:
            return queryset.filter(
                packages__duration_in_nights__gte=value,
                packages__is_published=True,
                packages__is_active=True
            ).distinct()
        return queryset
    
    def filter_max_duration(self, queryset, name, value):
        """Filter destinations that have at least one package with duration <= value"""
        if value is not None:
            return queryset.filter(
                packages__duration_in_nights__lte=value,
                packages__is_published=True,
                packages__is_active=True
            ).distinct()
        return queryset


class PopularDestinationDashboardFilter(filters.FilterSet):
    """Filter class for Popular Destination dashboard API"""
    
    # Categories filter - multiple select using external_id (same as dashboard destinations)
    categories = filters.CharFilter(
        method='filter_categories',
        help_text="Filter by multiple category external_ids (comma-separated) - ALL categories must match (AND condition)"
    )
    
    class Meta:
        model = Destination
        fields = ['categories']
    
    def filter_categories(self, queryset, name, value):
        """Filter destinations by category external_ids (comma-separated) using AND condition"""
        if value:
            try:
                external_ids = [id.strip() for id in value.split(',') if id.strip()]
                if external_ids:
                    # Apply each category filter sequentially to create AND condition
                    # This ensures destinations have ALL specified categories, not just ANY
                    for external_id in external_ids:
                        queryset = queryset.filter(categories__category__external_id=external_id)
                    return queryset.distinct()
            except (ValueError, AttributeError):
                pass
        return queryset


class DashboardPackageFilter(filters.FilterSet):
    """Comprehensive filter class for Package dashboard API"""
    
    # Search functionality
    search = filters.CharFilter(method='filter_search', help_text="Search in package title")
    
    # Destination filter - single select using external_id
    destination = filters.CharFilter(
        method='filter_destination',
        help_text="Filter by destination external_id"
    )
    
    # Categories filter - multiple select using external_id
    categories = filters.CharFilter(
        method='filter_categories',
        help_text="Filter by multiple category external_ids (comma-separated) - ALL categories must match (AND condition)"
    )
    
    # Activities filter - multiple select using external_id
    activities = filters.CharFilter(
        method='filter_activities', 
        help_text="Filter by multiple activity external_ids (comma-separated)"
    )
    
    # Visiting months filter - comma-separated values
    visiting_months = filters.CharFilter(
        method='filter_visiting_months',
        help_text="Filter by best time to visit months (1-12)"
    )
    
    # Price range filters
    min_price = filters.NumberFilter(
        method='filter_min_price',
        help_text="Filter by minimum starting price"
    )
    max_price = filters.NumberFilter(
        method='filter_max_price',
        help_text="Filter by maximum starting price"
    )
    
    # Duration range filters (in nights)
    min_duration = filters.NumberFilter(
        method='filter_min_duration',
        help_text="Filter by minimum duration in nights"
    )
    max_duration = filters.NumberFilter(
        method='filter_max_duration',
        help_text="Filter by maximum duration in nights"
    )
    
    # Ordering with custom default
    ordering = filters.OrderingFilter(
        fields=(
            ('price', 'price'),
            ('explore_order', 'explore_order'),
        ),
        field_labels={
            'price': 'Price (Low to High)',
            '-price': 'Price (High to Low)',
            'explore_order': 'Default Order',
        },
        help_text="Order by: price, explore_order (add '-' for descending)"
    )
    
    class Meta:
        model = Package
        fields = [
            'search', 'destination', 'categories', 'activities', 'visiting_months',
            'min_price', 'max_price', 'min_duration', 'max_duration', 'ordering'
        ]
    
    def filter_search(self, queryset, name, value):
        """Custom search filter for package title"""
        if value:
            return queryset.filter(title__icontains=value)
        return queryset
    
    def filter_destination(self, queryset, name, value):
        """Filter packages by destination external_id"""
        if value:
            try:
                return queryset.filter(destination__external_id=value.strip())
            except (ValueError, AttributeError):
                pass
        return queryset
    
    def filter_categories(self, queryset, name, value):
        """Filter packages by category external_ids (comma-separated) using AND condition"""
        if value:
            try:
                external_ids = [id.strip() for id in value.split(',') if id.strip()]
                if external_ids:
                    # Apply each category filter sequentially to create AND condition
                    # This ensures packages have ALL specified categories, not just ANY
                    for external_id in external_ids:
                        queryset = queryset.filter(categories__external_id=external_id)
                    return queryset.distinct()
            except (ValueError, AttributeError):
                pass
        return queryset
    
    def filter_activities(self, queryset, name, value):
        """Filter packages by activity external_ids (comma-separated)"""
        if value:
            try:
                external_ids = [id.strip() for id in value.split(',') if id.strip()]
                if external_ids:
                    return queryset.filter(activities__external_id__in=external_ids).distinct()
            except (ValueError, AttributeError):
                pass
        return queryset
    
    def filter_visiting_months(self, queryset, name, value):
        """Filter packages by best time to visit months (comma-separated)"""
        if value:
            # Convert comma-separated values to integers
            try:
                month_numbers = [int(month.strip()) for month in value.split(',') if month.strip()]
                
                # Convert month numbers to month names using the mapping from validation helpers
                month_number_to_name = {
                    1: 'January', 2: 'February', 3: 'March', 4: 'April',
                    5: 'May', 6: 'June', 7: 'July', 8: 'August',
                    9: 'September', 10: 'October', 11: 'November', 12: 'December'
                }
                
                month_names = []
                for month_num in month_numbers:
                    if month_num in month_number_to_name:
                        month_names.append(month_number_to_name[month_num])
                
                if month_names:
                    # Filter packages that have any of the selected month names
                    month_filters = Q()
                    for month_name in month_names:
                        month_filters |= Q(best_time_to_visit_months__contains=[month_name])
                    return queryset.filter(month_filters)
                    
            except (ValueError, TypeError):
                pass
        return queryset
    
    def filter_min_price(self, queryset, name, value):
        """Filter by minimum price"""
        if value is not None:
            return queryset.filter(price__gte=value)
        return queryset
    
    def filter_max_price(self, queryset, name, value):
        """Filter by maximum price"""
        if value is not None:
            return queryset.filter(price__lte=value)
        return queryset
    
    def filter_min_duration(self, queryset, name, value):
        """Filter by minimum duration in nights"""
        if value is not None:
            return queryset.filter(duration_in_nights__gte=value)
        return queryset
    
    def filter_max_duration(self, queryset, name, value):
        """Filter by maximum duration in nights"""
        if value is not None:
            return queryset.filter(duration_in_nights__lte=value)
        return queryset
