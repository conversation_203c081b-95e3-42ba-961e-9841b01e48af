from django.db import models
from django.core.validators import FileExtensionValidator, MinValueValidator, MaxValueValidator
from django.core.exceptions import ValidationError
from django_better_admin_arrayfield.models.fields import ArrayField
from django.conf import settings
from ckeditor.fields import RichTextField
from django.contrib.gis.db import models as gis_models
from django.contrib.postgres.indexes import OpClass, GinIndex, GistIndex
from django.contrib.postgres.operations import TrigramExtension
from django.db.models.functions import Lower

from base.models import BaseModel
from base.storage_utils import (
    package_upload_path, 
    package_media_upload_path, 
    category_upload_path,
    destination_upload_path, 
    activity_upload_path,
    custom_activity_media_upload_path,
)
from base.static import Constants
from accounts.models import Partner
from packages.choices import PackageMediaTypes, PackageTypeChoices, PersonaChoices, ItineraryDayItemType
from packages.choices import MonthChoices

operations = [
    TrigramExtension(),
]

class Category(BaseModel):
    """
    Category model for package categorization (HONEYMOON, FAMILY, RELIGIOUS, etc.)
    """
    partner = models.ForeignKey(Partner, on_delete=models.CASCADE, related_name='categories')
    title = models.CharField(max_length=100)
    description = models.TextField(help_text="Description is required")
    explore_order = models.PositiveIntegerField(default=0)

    def save(self, *args, **kwargs):
        """Override save to ensure title is lowercase"""
        if self.title:
            self.title = self.title.lower().strip()
        super().save(*args, **kwargs)

    def __str__(self):
        return self.title
    
    class Meta:
        verbose_name = 'Category'
        verbose_name_plural = 'Categories'
        unique_together = ('partner', 'title')


class Destination(BaseModel):
    """
    Destination model for travel destinations (DARJEELING, GOA, KERALA, etc.)
    """
    partner = models.ForeignKey(Partner, on_delete=models.CASCADE, related_name='destinations')
    title = models.CharField(max_length=100)
    description = models.TextField(help_text="Description is required")
    is_international = models.BooleanField(default=False)
    is_trending = models.BooleanField(default=False)
    best_time_to_visit = models.JSONField(
        default=list, 
        blank=True,
        help_text="List of month numbers when it's best to visit this destination"
    )
    explore_order = models.PositiveIntegerField(default=0)

    def save(self, *args, **kwargs):
        """Override save to ensure title is lowercase"""
        if self.title:
            self.title = self.title.lower().strip()
        super().save(*args, **kwargs)

    def __str__(self):
        return self.title
    
    def _format_best_time(self, best_time) -> list:
        """Convert best_time data to list of month names"""
        import calendar
        
        if isinstance(best_time, dict):
            months = best_time.get("months")
            if isinstance(months, list):
                # Convert numbers to month names, ignoring invalid ones
                month_names = [
                    calendar.month_name[m]
                    for m in months
                    if isinstance(m, int) and 1 <= m <= 12
                ]
                return month_names

        elif isinstance(best_time, list):
            # If it's a raw list like [1, 2, 3]
            month_names = [
                calendar.month_name[m]
                for m in best_time
                if isinstance(m, int) and 1 <= m <= 12
            ]
            return month_names

        return []

    def get_best_months_display(self):
        """Get display names for best time to visit months"""
        if not self.best_time_to_visit:
            return "Not specified"
        
        month_names = self._format_best_time(self.best_time_to_visit)
        return ", ".join(month_names) if month_names else "Not specified"
    
    def clean(self):
        """Validate best_time_to_visit field"""
        super().clean()
        if self.best_time_to_visit:
            valid_months = MonthChoices.get_month_list()
            for month in self.best_time_to_visit:
                if str(month) not in valid_months:
                    raise ValidationError(f"Invalid month: {month}. Must be between 1-12.")
    
    class Meta:
        verbose_name = 'Destination'
        verbose_name_plural = 'Destinations'
        unique_together = ('partner', 'title')
        indexes = [
            GistIndex(
                OpClass(Lower('title'), name='gist_trgm_ops'),
                name='destination_title_gist_idx'
            )
        ]


class Activity(BaseModel):
    """
    Activity model for travel activities (Sightseeing, Scuba Diving, etc.)
    """
    partner = models.ForeignKey(Partner, on_delete=models.CASCADE, related_name='activities')
    title = models.CharField(max_length=100)
    description = models.TextField(help_text="Description is required")
    is_featured = models.BooleanField(default=False)
    explore_order = models.PositiveIntegerField(default=0)

    def save(self, *args, **kwargs):
        """Override save to ensure title is lowercase"""
        if self.title:
            self.title = self.title.lower().strip()
        super().save(*args, **kwargs)

    def __str__(self):
        return self.title
    
    class Meta:
        verbose_name = 'Activity'
        verbose_name_plural = 'Activities'
        unique_together = ('partner', 'title')


class CategoryMedia(BaseModel):
    """
    Media model for Category - multiple media file per category
    """
    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='media')
    media = models.FileField(upload_to=category_upload_path, help_text="Media file is required")
    
    def __str__(self):
        return f"Media for {self.category.title}"

    def clean(self):
        # Check if maximum media count is reached - only for existing categories
        if not self.pk and self.category and self.category.pk and self.category.media.count() >= Constants.CATEGORY_MAX_MEDIA_COUNT:
            raise ValidationError(f"Maximum of {Constants.CATEGORY_MAX_MEDIA_COUNT} media files allowed per category")
        
        # Validate file extension
        if self.media:
            ext = self.media.name.split('.')[-1].lower()
            if ext not in Constants.CATEGORY_MEDIA_EXTENSIONS:
                raise ValidationError(f"Invalid file extension. Allowed: {', '.join(Constants.CATEGORY_MEDIA_EXTENSIONS)}")
        
        super().clean()

    class Meta:
        verbose_name = 'Category Media'
        verbose_name_plural = 'Category Media'


class DestinationMedia(BaseModel):
    """
    Media model for Destination - multiple media files per destination
    """
    destination = models.ForeignKey(Destination, on_delete=models.CASCADE, related_name='media')
    media = models.FileField(upload_to=destination_upload_path, help_text="Media file is required")
    
    def __str__(self):
        return f"Media for {self.destination.title}"

    def clean(self):
        # Check if maximum media count is reached - only for existing destinations
        if not self.pk and self.destination and self.destination.pk and self.destination.media.count() >= Constants.DESTINATION_MAX_MEDIA_COUNT:
            raise ValidationError(f"Maximum of {Constants.DESTINATION_MAX_MEDIA_COUNT} media files allowed per destination")

        # Validate file extension
        if self.media:
            ext = self.media.name.split('.')[-1].lower()
            if ext not in Constants.DESTINATION_MEDIA_EXTENSIONS:
                raise ValidationError(f"Invalid file extension. Allowed: {', '.join(Constants.DESTINATION_MEDIA_EXTENSIONS)}")

        super().clean()

    class Meta:
        verbose_name = 'Destination Media'
        verbose_name_plural = 'Destination Media'


class ActivityMedia(BaseModel):
    """
    Media model for Activity - multiple media files per activity
    """
    main_display = models.BooleanField(default=False)
    activity = models.ForeignKey(Activity, on_delete=models.CASCADE, related_name='media')
    media = models.FileField(upload_to=activity_upload_path, help_text="Media file is required")
    
    def __str__(self):
        return f"Media for {self.activity.title}"

    def clean(self):
        # Check if maximum media count is reached - only for existing activities
        if not self.pk and self.activity and self.activity.pk and self.activity.media.count() >= Constants.ACTIVITY_MAX_MEDIA_COUNT:
            raise ValidationError(f"Maximum of {Constants.ACTIVITY_MAX_MEDIA_COUNT} media files allowed per activity")
        
        # Validate file extension
        if self.media: 
            ext = self.media.name.split('.')[-1].lower()
            if ext not in Constants.ACTIVITY_MEDIA_EXTENSIONS:
                raise ValidationError(f"Invalid file extension. Allowed: {', '.join(Constants.ACTIVITY_MEDIA_EXTENSIONS)}")
        
        # Validate main_display - only one per activity
        if self.main_display and self.activity:
            existing_main = ActivityMedia.objects.filter(
                activity=self.activity,
                main_display=True
            )
            
            # Exclude current instance if editing
            if self.pk:
                existing_main = existing_main.exclude(pk=self.pk)
            
            if existing_main.exists():
                raise ValidationError("Only one media file per activity can be set as main display")
        
        super().clean()

    class Meta:
        verbose_name = 'Activity Media'
        verbose_name_plural = 'Activity Media'



class PackageUploader(BaseModel):
    """
    Model to handle JSON/document file uploads for creating packages
    """
    partner = models.ForeignKey(Partner, on_delete=models.CASCADE, related_name='package_uploads')
    file = models.FileField(
        upload_to=package_upload_path,
        validators=[FileExtensionValidator(allowed_extensions=Constants.PACKAGE_FILE_EXTENSIONS)]
    )
    file_type = models.CharField(max_length=255, choices=PackageTypeChoices.choices)
    
    def __str__(self):
        return f"Package Upload {self.id}"
    
    class Meta:
        verbose_name = 'Package Uploader'
        verbose_name_plural = 'Package Uploaders'


"""
M2M Relationships b/w Package, Category, Activity:
Package -> Category
Package -> Activity
"""

class PackageCategory(BaseModel):
    """
    Through model for Package-Category M2M relationship
    """
    package = models.ForeignKey('Package', on_delete=models.CASCADE, related_name='package_categories')
    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='package_categories')
    
    def save(self, *args, **kwargs):
        """Override save to handle soft delete conflicts"""
        if not self.pk:  # Only for new instances
            # Check for existing soft-deleted records with same package-category pair
            existing_soft_deleted = PackageCategory.global_objects.filter(
                package=self.package,
                category=self.category,
                deleted_at__isnull=False
            )
            
            # Hard delete any existing soft-deleted records
            if existing_soft_deleted.exists():
                existing_soft_deleted._raw_delete(existing_soft_deleted.db)
        
        super().save(*args, **kwargs)
    
    def delete(self, using=None, keep_parents=False, **kwargs):
        """Override delete to use hard_delete instead of soft delete for M2M relationships"""
        return self.hard_delete()
    
    class Meta:
        unique_together = ('package', 'category')
        verbose_name = 'Package Category'
        verbose_name_plural = 'Package Categories'
        
    def __str__(self):
        return f"{self.package.title} - {self.category.title}"


class PackageActivity(BaseModel):
    """
    Through model for Package-Activity M2M relationship
    """
    package = models.ForeignKey('Package', on_delete=models.CASCADE, related_name='package_activities')
    activity = models.ForeignKey(Activity, on_delete=models.CASCADE, related_name='package_activities')
    
    def save(self, *args, **kwargs):
        """Override save to handle soft delete conflicts"""
        if not self.pk:  # Only for new instances
            # Check for existing soft-deleted records with same package-activity pair
            existing_soft_deleted = PackageActivity.global_objects.filter(
                package=self.package,
                activity=self.activity,
                deleted_at__isnull=False
            )
            
            # Hard delete any existing soft-deleted records
            if existing_soft_deleted.exists():
                existing_soft_deleted._raw_delete(existing_soft_deleted.db)
        
        super().save(*args, **kwargs)
    
    def delete(self, using=None, keep_parents=False):
        """Override delete to use hard_delete instead of soft delete for M2M relationships"""
        return self.hard_delete()
    
    class Meta:
        unique_together = ('package', 'activity')
        verbose_name = 'Package Activity'
        verbose_name_plural = 'Package Activities'
        
    def __str__(self):
        return f"{self.package.title} - {self.activity.title}"


"""
M2M Relationships b/w Destination, Category, Activity:
Destination -> Category
Destination -> Activity

Category -> Activity
Category -> Destination

Activity -> Destination
Activity -> Category
"""

class DestinationCategory(BaseModel):
    """
    Through model for Destination-Category M2M relationship
    """
    destination = models.ForeignKey(Destination, on_delete=models.CASCADE, related_name='categories')
    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='destinations')
    
    def save(self, *args, **kwargs):
        """Override save to handle soft delete conflicts"""
        if not self.pk:  # Only for new instances
            # Check for existing soft-deleted records with same destination-category pair
            existing_soft_deleted = DestinationCategory.global_objects.filter(
                destination=self.destination,
                category=self.category,
                deleted_at__isnull=False
            )
            
            # Hard delete any existing soft-deleted records
            if existing_soft_deleted.exists():
                existing_soft_deleted._raw_delete(existing_soft_deleted.db)
        
        super().save(*args, **kwargs)
    
    def delete(self, using=None, keep_parents=False):
        """Override delete to use hard_delete instead of soft delete for M2M relationships"""
        return self.hard_delete()
    
    class Meta:
        unique_together = ('destination', 'category')
        verbose_name = 'Destination Category'
        verbose_name_plural = 'Destination Categories'
        
    def __str__(self):
        return f"{self.destination.title} - {self.category.title}"


class DestinationActivity(BaseModel):
    """
    Through model for Destination-Activity M2M relationship
    """
    destination = models.ForeignKey(Destination, on_delete=models.CASCADE, related_name='activities')
    activity = models.ForeignKey(Activity, on_delete=models.CASCADE, related_name='destinations')
    
    def save(self, *args, **kwargs):
        """Override save to handle soft delete conflicts"""
        if not self.pk:  # Only for new instances
            # Check for existing soft-deleted records with same destination-activity pair
            existing_soft_deleted = DestinationActivity.global_objects.filter(
                destination=self.destination,
                activity=self.activity,
                deleted_at__isnull=False
            )
            
            # Hard delete any existing soft-deleted records
            if existing_soft_deleted.exists():
                existing_soft_deleted._raw_delete(existing_soft_deleted.db)
        
        super().save(*args, **kwargs)
    
    def delete(self, using=None, keep_parents=False):
        """Override delete to use hard_delete instead of soft delete for M2M relationships"""
        return self.hard_delete()
    
    class Meta:
        unique_together = ('destination', 'activity')
        verbose_name = 'Destination Activity'
        verbose_name_plural = 'Destination Activities'
        
    def __str__(self):
        return f"{self.destination.title} - {self.activity.title}"


# class CategoryActivity(BaseModel):
#     """
#     Through model for Category-Activity M2M relationship
#     """
#     category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='activities')
#     activity = models.ForeignKey(Activity, on_delete=models.CASCADE, related_name='categories')
    
#     class Meta:
#         unique_together = ('category', 'activity')
#         verbose_name = 'Category Activity'
#         verbose_name_plural = 'Category Activities'
        
#     def __str__(self):
#         return f"{self.category.title} - {self.activity.title}"



class Establishment(BaseModel):
    name = models.TextField()
    address = models.TextField(blank=True, null=True)
    phone = models.CharField(max_length=255, blank=True, null=True)
    website = models.TextField(blank=True, null=True)
    rating = models.DecimalField(max_digits=3, decimal_places=2, blank=True, null=True)
    review_count = models.PositiveIntegerField(blank=True, null=True)
    image_urls = ArrayField(models.CharField(max_length=500), blank=True, default=list)
    description = models.TextField(blank=True, null=True)
    amenities = ArrayField(models.CharField(max_length=500), blank=True, default=list)

    class Meta:
        abstract = True

    def __str__(self):
        return self.name


class PackageHotel(Establishment):
    class Meta:
        verbose_name = "Hotel"
        verbose_name_plural = "Hotels"


class PackageRestaurant(Establishment):
    class Meta:
        verbose_name = "Restaurant"
        verbose_name_plural = "Restaurants"


class Package(BaseModel):
    """
    Main Package model with all relationships
    """
    partner = models.ForeignKey(Partner, on_delete=models.CASCADE, related_name='packages')
    package_uploaded = models.ForeignKey(PackageUploader, on_delete=models.CASCADE, related_name='packages', null=True, blank=True)

    title = models.CharField(max_length=255)
    package_no = models.CharField(max_length=255)
    
    # M2M Relationships with through tables
    categories = models.ManyToManyField(Category, through=PackageCategory, related_name='packages')
    activities = models.ManyToManyField(Activity, through=PackageActivity, related_name='packages')
    
    # Single destination (M2O)
    destination = models.ForeignKey(Destination, on_delete=models.CASCADE, related_name='packages')
    owner = models.CharField(max_length=255)
    type = models.CharField(max_length=255, choices=PackageTypeChoices.choices)
    duration = models.CharField(max_length=255)
    duration_in_nights = models.PositiveIntegerField()
    duration_in_days = models.PositiveIntegerField()

    currency = models.CharField(max_length=255)
    price = models.DecimalField(max_digits=10, decimal_places=2)
    price_per_person = models.CharField(max_length=255)
    visa_type = ArrayField(models.CharField(max_length=255))

    # Description
    about_this_tour = models.TextField()
    # highlights = ArrayField(models.CharField())
    # inclusions = ArrayField(models.CharField())
    exclusions = ArrayField(models.CharField())

    # Package data
    itinerary = RichTextField()
    hotels = ArrayField(models.CharField(max_length=255), default=list)
    package_hotels = models.ManyToManyField(PackageHotel, through='PackageHotelRelation', related_name='packages', blank=True)
    popular_activities = ArrayField(models.CharField(max_length=255), default=list)
    # addons = ArrayField(models.CharField(max_length=255), default=list, verbose_name="Add Ons")

    is_published = models.BooleanField(default=False)
    important_notes = ArrayField(models.CharField(max_length=255), default=list, blank=True)

    best_time_to_visit_months = ArrayField(models.CharField(max_length=255), null=True, blank=True)
    best_time_to_visit = models.TextField(null=True, blank=True)
    package_personas = ArrayField(
        models.CharField(max_length=255, choices=PersonaChoices.choices),
        default=list,
        blank=True,
        help_text="Auto-populated personas from custom activities in itinerary"
    )
    rating = models.DecimalField(max_digits=2, decimal_places=1, null=True, blank=True)
    rating_description = models.TextField(null=True, blank=True, help_text="Brief description about the package rating (auto-generated if empty)")
    currency_conversion_rate = models.TextField(null=True, blank=True)
    destination_safety = models.TextField(null=True, blank=True)
    popular_restaurants = ArrayField(models.CharField(max_length=255), default=list, blank=True)
    package_restaurants = models.ManyToManyField(PackageRestaurant, through='PackageRestaurantRelation', related_name='packages', blank=True)
    what_to_shop = models.TextField(null=True, blank=True)
    what_to_pack = RichTextField(null=True, blank=True)
    cultural_info = models.TextField(null=True, blank=True)
    
    explore_order = models.PositiveIntegerField(default=0)

    def __str__(self):
        return f"{self.title} ({self.package_no})"
    
    class Meta:
        verbose_name = 'Package'
        verbose_name_plural = 'Packages'


class CustomPackage(Package):
    class Meta:
        proxy = True
    
    def calculate_itinerary_price(self):
        """Calculate total price per person based on itinerary activities and hotels"""
        if not self.pk:
            return 0  # Can't calculate for unsaved packages
        
        total_price = 0
        
        # Get all itinerary items for this package
        itinerary_items = self.itineraries.all()
        
        for item in itinerary_items:
            if item.type == 'Activity' and item.activity:
                # Use adult_price if available, otherwise fall back to price
                activity_price = item.activity.adult_price or item.activity.price or 0
                total_price += activity_price
            elif item.type == 'Hotel' and item.hotel:
                # Use base_price from hotel
                hotel_price = item.hotel.base_price or 0
                total_price += hotel_price
        
        return total_price
    
    def get_calculated_price_display(self):
        """Get formatted display of calculated price"""
        calculated_price = self.calculate_itinerary_price()
        if calculated_price > 0:
            return f"₹{calculated_price:,.2f}"
        return "₹0.00 (No itinerary items)"
    
    def save(self, *args, **kwargs):
        """Override save to handle auto-generation of package data"""
        # Always force type to Custom Admin for CustomPackage
        self.type = PackageTypeChoices.CUSTOM_ADMIN.value
        
        # Set default values for excluded fields
        if not self.currency:
            self.currency = 'INR'  # Default currency
        
        # Auto-calculate price from itinerary (after first save for new objects)
        if self.pk:  # Only for existing objects that have itinerary data
            calculated_price = self.calculate_itinerary_price()
            if calculated_price > 0:
                self.price = calculated_price
                self.price_per_person = str(calculated_price)
        elif not self.price:
            # For new objects, set default price if not provided
            self.price = self.price_per_person if self.price_per_person else 0
            
        # Auto-calculate hidden duration fields based on duration_in_nights
        if self.duration_in_nights:
            self.duration_in_days = self.duration_in_nights + 1
            self.duration = f"{self.duration_in_nights}N & {self.duration_in_days}D"
        
        # Auto-populate best_time_to_visit from destination
        if self.destination and hasattr(self.destination, 'best_time_to_visit') and self.destination.best_time_to_visit:
            self.best_time_to_visit_months = self.destination.best_time_to_visit
            # Also populate the TextField with human-readable format
            self.best_time_to_visit = self.destination.get_best_months_display()
        
        # Auto-assign partner from destination if not set
        if not self.partner and self.destination and hasattr(self.destination, 'partner_id') and self.destination.partner_id:
            from accounts.models import Partner
            try:
                partner = Partner.objects.get(id=self.destination.partner_id, is_active=True)
                self.partner = partner
            except Partner.DoesNotExist:
                # Handle case where destination's partner doesn't exist or is inactive
                pass

        # Set default values for new objects
        if not self.pk:  # Only for new objects
            if self.is_active is None:
                self.is_active = True
            if self.is_published is None:
                self.is_published = True
            if not self.owner:
                self.owner = 'ZUUMM'
            # Ensure array fields are initialized as empty lists for new objects
            if self.package_personas is None:
                self.package_personas = []
            if not self.visa_type:
                self.visa_type = []
            if not self.exclusions:
                self.exclusions = []
            if not self.important_notes:
                self.important_notes = []
            if not self.popular_restaurants:
                self.popular_restaurants = []
                

        
        # Call parent save first
        super().save(*args, **kwargs)
        
        # Handle M2M relationships after save (since the instance needs to exist)
        self._post_save_processing()
    
    def _post_save_processing(self):
        """Handle post-save operations that require the instance to exist"""
        # Auto-populate package categories from destination categories
        if self.destination:
            try:
                destination_categories = self.destination.categories.all()
                if destination_categories:
                    # Clear existing package categories and add destination categories
                    self.categories.clear()
                    for dest_cat in destination_categories:
                        PackageCategory.objects.get_or_create(
                            package=self,
                            category=dest_cat.category
                        )
            except AttributeError:
                # Handle cases where relationships might not be fully established
                pass
        
        # Auto-populate personas from custom activities in itinerary
        self._update_package_personas()
    
    def _update_package_personas(self):
        """Update package personas based on custom activities in itinerary"""
        if self.pk:
            # Get all custom activities used in itinerary
            activity_personas = []
            for itinerary in self.itineraries.filter(activity__isnull=False):
                if itinerary.activity and itinerary.activity.persona:
                    activity_personas.extend(itinerary.activity.persona)
            
            # Remove duplicates and save to database
            unique_personas = list(set(activity_personas))
            
            # Ensure package_personas is a list (handle None case)
            current_personas = self.package_personas if self.package_personas is not None else []
            
            if unique_personas != current_personas:
                # Use update to avoid recursive save calls
                CustomPackage.objects.filter(pk=self.pk).update(package_personas=unique_personas)
    
    def get_package_best_time_display(self):
        """Get best time to visit from destination"""
        if self.destination:
            return self.destination.get_best_months_display()
        return "Not specified"
    
    def get_package_persona_display(self):
        """Get personas from stored package_personas field"""
        if self.package_personas and len(self.package_personas) > 0:
            from packages.choices import PersonaChoices
            persona_names = []
            for persona in self.package_personas:
                try:
                    persona_choice = PersonaChoices(persona)
                    persona_names.append(persona_choice.label)
                except ValueError:
                    persona_names.append(persona)  # Fallback for unknown values
            return ", ".join(persona_names)
        return "No personas identified"
    
    def get_package_category_display(self):
        """Get package categories"""
        if self.pk:
            categories = self.categories.all()
            if categories:
                return ", ".join([cat.title for cat in categories])
        return "No categories assigned"


class PackageHotelRelation(BaseModel):
    package = models.ForeignKey(Package, on_delete=models.CASCADE)
    hotel = models.ForeignKey(PackageHotel, on_delete=models.CASCADE)

    class Meta:
        unique_together = ('package', 'hotel')
        verbose_name = 'Package Hotel Relation'
        verbose_name_plural = 'Package Hotel Relations'


class PackageRestaurantRelation(BaseModel):
    package = models.ForeignKey(Package, on_delete=models.CASCADE)
    restaurant = models.ForeignKey(PackageRestaurant, on_delete=models.CASCADE)

    class Meta:
        unique_together = ('package', 'restaurant')
        verbose_name = 'Package Restaurant Relation'
        verbose_name_plural = 'Package Restaurant Relations'


class PackageHighlight(BaseModel):
    """
    Model for package highlights with icon mapping
    """
    package = models.ForeignKey(Package, on_delete=models.CASCADE, related_name='highlights')
    value = models.TextField(help_text="Highlight description")
    icon_class = models.CharField(
        max_length=50, 
        help_text="Icon class for this highlight"
    )
    
    def __str__(self):
        return f"{self.package.title} - {self.value[:50]}"
    
    class Meta:
        verbose_name = 'Package Highlight'
        verbose_name_plural = 'Package Highlights'
        ordering = ['created_at']


class PackageInclusion(BaseModel):
    """
    Model for package inclusions with icon mapping
    """
    package = models.ForeignKey(Package, on_delete=models.CASCADE, related_name='inclusions')
    value = models.TextField(help_text="Inclusion description")
    icon_class = models.CharField(
        max_length=50,
        help_text="Icon class for this inclusion"
    )
    
    def __str__(self):
        return f"{self.package.title} - {self.value[:50]}"
    
    class Meta:
        verbose_name = 'Package Inclusion'
        verbose_name_plural = 'Package Inclusions'
        ordering = ['created_at']


class PackageAddon(BaseModel):
    """
    Model for package addons with icon mapping
    """
    package = models.ForeignKey(Package, on_delete=models.CASCADE, related_name='addons')
    value = models.TextField(help_text="Addon description")
    icon_class = models.CharField(
        max_length=50,
        help_text="Icon class for this addon"
    )
    
    def __str__(self):
        return f"{self.package.title} - {self.value[:50]}"
    
    class Meta:
        verbose_name = 'Package Addon'
        verbose_name_plural = 'Package Addons'
        ordering = ['created_at']


class PackageMedia(BaseModel):
    """
    Model to store media files (images/videos) for a package
    """
    package = models.ForeignKey(Package, on_delete=models.CASCADE, related_name='media')
    title = models.CharField(max_length=255, null=True, blank=True)
    file_type = models.CharField(max_length=255, choices=PackageMediaTypes.choices)
    file = models.FileField(
        upload_to=package_media_upload_path,
        validators=[FileExtensionValidator(allowed_extensions=Constants.PACKAGE_MEDIA_EXTENSIONS)],
        max_length=500  # Increased max_length to accommodate long filenames
    )

    def __str__(self):
        return f"Media for {self.package.title}"
    
    def clean(self):
        # Check if maximum media count is reached - only for existing packages
        if not self.pk and self.package and self.package.pk and self.package.media.count() >= Constants.PACKAGE_MAX_MEDIA_COUNT:
            raise ValidationError(f"Maximum of {Constants.PACKAGE_MAX_MEDIA_COUNT} media files allowed per package")

        # Validate file extension if not already validated by FileExtensionValidator
        if self.file:
            ext = self.file.name.split('.')[-1].lower()
            if ext not in Constants.PACKAGE_MEDIA_EXTENSIONS:
                raise ValidationError(f"Invalid file extension. Allowed: {', '.join(Constants.PACKAGE_MEDIA_EXTENSIONS)}")

        super().clean()

    class Meta:
        verbose_name = 'Package Media'
        verbose_name_plural = 'Package Media'


class DestinationFaq(BaseModel):
    """
    Model to store FAQ for a destination
    """
    destination = models.ForeignKey(Destination, on_delete=models.CASCADE, related_name='faqs')
    partner = models.ForeignKey(Partner, on_delete=models.CASCADE, related_name='destination_faqs')
    question = models.TextField()
    answer = models.TextField()
    is_published = models.BooleanField(default=False)
    priority = models.PositiveIntegerField(default=0)

    class Meta:
        verbose_name = 'Destination FAQ'
        verbose_name_plural = 'Destination FAQs'
        ordering = ['-priority', '-created_at']


class CustomActivityCategory(BaseModel):
    """
    Category model for Custom Activities from GetYourGuide Platform
    """
    name = models.CharField(max_length=255, null=True, blank=True)

    def __str__(self):
        return self.name or 'Unknown Category'
    
    class Meta:
        verbose_name = 'Custom Activity Category'
        verbose_name_plural = 'Custom Activity Categories'


class CustomActivityLocation(BaseModel):
    """
    Location model for Custom Activities from GetYourGuide Platform
    """
    location_coordinates = gis_models.PointField(srid=4326, null=True, blank=True)
    country = models.CharField(max_length=255, null=True, blank=True)
    city = models.CharField(max_length=255, null=True, blank=True)
    google_place_id = models.CharField(max_length=255, null=True, blank=True)

    def __str__(self):
        return f"{self.city}, {self.country}" if self.city and self.country else 'Unknown Location'
    
    class Meta:
        verbose_name = 'Custom Activity Location'
        verbose_name_plural = 'Custom Activity Locations'


class CustomActivity(BaseModel):
    """
    Custom Activity model for storing data from GetYourGuide Platform
    """
    destination = models.ForeignKey(Destination, on_delete=models.CASCADE, related_name='custom_activities', null=True, blank=True)
    tour_id = models.CharField(max_length=255, null=True, blank=True)
    title = models.TextField(null=True, blank=True)
    abstract = models.TextField(null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    activity_type = models.CharField(max_length=255, null=True, blank=True)
    additional_information = models.TextField(null=True, blank=True)
    items_to_bring = models.TextField(null=True, blank=True)
    not_allowed = models.TextField(null=True, blank=True)
    not_suitable_for = models.TextField(null=True, blank=True)
    bestseller = models.BooleanField(default=False, null=True, blank=True)
    certified = models.BooleanField(default=False, null=True, blank=True)
    has_pick_up = models.BooleanField(default=False, null=True, blank=True)
    overall_rating = models.DecimalField(max_digits=3, decimal_places=2, null=True, blank=True)
    number_of_ratings = models.PositiveIntegerField(null=True, blank=True)
    highlights = models.TextField(null=True, blank=True)
    inclusions = models.TextField(null=True, blank=True)
    exclusions = models.TextField(null=True, blank=True)
    coordinates = gis_models.PointField(srid=4326, null=True, blank=True)
    price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    location_id = models.CharField(max_length=255, null=True, blank=True)
    opening_hours = models.TextField(null=True, blank=True)
    cancellation_policy_text = models.TextField(null=True, blank=True)
    cancellation_policy = models.JSONField(default=dict, null=True, blank=True)
    persona = ArrayField(
        models.CharField(max_length=50, choices=PersonaChoices.choices),
        default=list,
        blank=True,
        help_text="Select one or more personas this activity is suitable for"
    )
    durations = models.JSONField(default=list, null=True, blank=True)
    
    # M2M relationships with through tables
    categories = models.ManyToManyField(CustomActivityCategory, through='CustomActivityCategoryRelation', related_name='activities')
    locations = models.ManyToManyField(CustomActivityLocation, through='CustomActivityLocationRelation', related_name='activities')
    # M2M relationship with system categories
    system_categories = models.ManyToManyField(Category, through='CustomActivitySystemCategoryRelation', related_name='custom_activities', blank=True)

    addons = ArrayField(models.CharField(max_length=255), default=list, blank=True)
    adult_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    child_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    infant_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)

    preferred_start_time = models.TimeField(null=True, blank=True)
    preferred_end_time = models.TimeField(null=True, blank=True)

    def __str__(self):
        return self.title or f'Custom Activity {self.tour_id or self.id}'
    
    class Meta:
        verbose_name = 'Custom Activity'
        verbose_name_plural = 'Custom Activities'
        indexes = [
            models.Index(
                fields=['destination', 'is_active', '-overall_rating'],
                name='ca_dest_active_rating'
            ),
            # For queries without persona filter
            models.Index(
                fields=['destination', 'is_active'],
                name='cust_act_dest_active_idx'
            ),
            # GIN index for persona array overlap operations
            GinIndex(
                fields=['persona'],
                name='cust_act_persona_gin_idx'
            ),
        ]


class CustomActivityMedia(BaseModel):
    """
    Media model for Custom Activity - multiple media files per activity
    """
    custom_activity = models.ForeignKey(CustomActivity, on_delete=models.CASCADE, related_name='media')
    media = models.FileField(upload_to=custom_activity_media_upload_path, help_text="Media file is required")
    title = models.CharField(max_length=255, null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    meta_information = models.JSONField(default=dict, null=True, blank=True)
    
    def __str__(self):
        return f"Media for {self.custom_activity.title or 'Custom Activity'}"

    # def clean(self):
    #     # Check if maximum media count is reached - only for existing activities
    #     if not self.pk and self.custom_activity and self.custom_activity.pk and self.custom_activity.media.count() >= Constants.ACTIVITY_MAX_MEDIA_COUNT:
    #         raise ValidationError(f"Maximum of {Constants.ACTIVITY_MAX_MEDIA_COUNT} media files allowed per activity")
    #     
    #     # Validate file extension
    #     if self.media: 
    #         ext = self.media.name.split('.')[-1].lower()
    #         if ext not in Constants.ACTIVITY_MEDIA_EXTENSIONS:
    #             raise ValidationError(f"Invalid file extension. Allowed: {', '.join(Constants.ACTIVITY_MEDIA_EXTENSIONS)}")
    #     
    #     super().clean()

    class Meta:
        verbose_name = 'Custom Activity Media'
        verbose_name_plural = 'Custom Activity Media'


class CustomActivityCategoryRelation(BaseModel):
    """
    Through model for CustomActivity-CustomActivityCategory M2M relationship
    """
    activity = models.ForeignKey(CustomActivity, on_delete=models.CASCADE, related_name='activity_categories')
    category = models.ForeignKey(CustomActivityCategory, on_delete=models.CASCADE, related_name='category_activities')
    
    def save(self, *args, **kwargs):
        """Override save to handle soft delete conflicts"""
        if not self.pk:  # Only for new instances
            # Check for existing soft-deleted records with same activity-category pair
            existing_soft_deleted = CustomActivityCategoryRelation.global_objects.filter(
                activity=self.activity,
                category=self.category,
                deleted_at__isnull=False
            )
            
            # Hard delete any existing soft-deleted records
            if existing_soft_deleted.exists():
                existing_soft_deleted._raw_delete(existing_soft_deleted.db)
        
        super().save(*args, **kwargs)
    
    def delete(self, using=None, keep_parents=False):
        """Override delete to use hard_delete instead of soft delete for M2M relationships"""
        return self.hard_delete()
    
    class Meta:
        unique_together = ('activity', 'category')
        verbose_name = 'Custom Activity Category Relation'
        verbose_name_plural = 'Custom Activity Category Relations'
        
    def __str__(self):
        return f"{self.activity.title or 'Activity'} - {self.category.name or 'Category'}"


class CustomActivityLocationRelation(BaseModel):
    """
    Through model for CustomActivity-CustomActivityLocation M2M relationship
    """
    activity = models.ForeignKey(CustomActivity, on_delete=models.CASCADE, related_name='activity_locations')
    location = models.ForeignKey(CustomActivityLocation, on_delete=models.CASCADE, related_name='location_activities')
    
    def save(self, *args, **kwargs):
        """Override save to handle soft delete conflicts"""
        if not self.pk:  # Only for new instances
            # Check for existing soft-deleted records with same activity-location pair
            existing_soft_deleted = CustomActivityLocationRelation.global_objects.filter(
                activity=self.activity,
                location=self.location,
                deleted_at__isnull=False
            )
            
            # Hard delete any existing soft-deleted records
            if existing_soft_deleted.exists():
                existing_soft_deleted._raw_delete(existing_soft_deleted.db)
        
        super().save(*args, **kwargs)
    
    def delete(self, using=None, keep_parents=False):
        """Override delete to use hard_delete instead of soft delete for M2M relationships"""
        return self.hard_delete()
    
    class Meta:
        unique_together = ('activity', 'location')
        verbose_name = 'Custom Activity Location Relation'
        verbose_name_plural = 'Custom Activity Location Relations'
        
    def __str__(self):
        return f"{self.activity.title or 'Activity'} - {self.location}"


class CustomActivitySystemCategoryRelation(BaseModel):
    """
    Through model for CustomActivity-SystemCategory M2M relationship
    """
    activity = models.ForeignKey(CustomActivity, on_delete=models.CASCADE, related_name='system_category_relations')
    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='custom_activity_relations')
    
    def save(self, *args, **kwargs):
        """Override save to handle soft delete conflicts"""
        if not self.pk:  # Only for new instances
            # Check for existing soft-deleted records with same activity-category pair
            existing_soft_deleted = CustomActivitySystemCategoryRelation.global_objects.filter(
                activity=self.activity,
                category=self.category,
                deleted_at__isnull=False
            )
            
            # Hard delete any existing soft-deleted records
            if existing_soft_deleted.exists():
                existing_soft_deleted._raw_delete(existing_soft_deleted.db)
        
        super().save(*args, **kwargs)
    
    def delete(self, using=None, keep_parents=False):
        """Override delete to use hard_delete instead of soft delete for M2M relationships"""
        return self.hard_delete()
    
    class Meta:
        unique_together = ('activity', 'category')
        verbose_name = 'Custom Activity System Category Relation'
        verbose_name_plural = 'Custom Activity System Category Relations'
        
    def __str__(self):
        return f"{self.activity.title or 'Activity'} - {self.category.title}"

def delete_custom_activities_data():
    """
    Delete all custom activities data from the database
    """

    custom_activity_category_relations = CustomActivityCategoryRelation.global_objects.all()
    for relation in custom_activity_category_relations:
        relation.hard_delete()
    
    custom_activity_location_relations = CustomActivityLocationRelation.global_objects.all()
    for relation in custom_activity_location_relations:
        relation.hard_delete()
    
    custom_activity_system_category_relations = CustomActivitySystemCategoryRelation.global_objects.all()
    for relation in custom_activity_system_category_relations:
        relation.hard_delete()

    custom_activity_categories = CustomActivityCategory.global_objects.all()
    for custom_activity_category in custom_activity_categories:
        custom_activity_category.hard_delete()

    custom_activity_locations = CustomActivityLocation.global_objects.all()
    for custom_activity_location in custom_activity_locations:
        custom_activity_location.hard_delete()

    custom_activities = CustomActivity.global_objects.all()
    for custom_activity in custom_activities:
        custom_activity.hard_delete()
    
    print("All custom activities data deleted successfully")


class TripjackHotels(BaseModel):
    """
    TripJack Hotels model for storing hotel data from TripJack API
    Based on the TripJack Hotel Schema with comprehensive hotel information
    """
    # Primary Key - TripJack hotel unique identifier
    hotel_id = models.CharField(
        max_length=100,
        unique=True,
        help_text="Primary Key (uid) from TripJack API"
    )

    # Basic Hotel Information
    name = models.CharField(
        max_length=255,
        help_text="Hotel name from API"
    )
    description = models.TextField(
        blank=True,
        null=True,
        help_text="Hotel description (des) from API"
    )
    star_rating = models.PositiveIntegerField(
        blank=True,
        null=True,
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        help_text="Star rating (rt) from API"
    )

    # Pricing Information
    base_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="The 'pc' value from the first element in the 'pops' array"
    )

    # Check-in/Check-out Times
    check_in_time = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        help_text="Parsed from policy notes, if available"
    )
    check_out_time = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        help_text="Parsed from policy notes, if available"
    )

    # Location Information
    latitude = models.DecimalField(
        max_digits=10,
        decimal_places=7,
        blank=True,
        null=True,
        help_text="Latitude (gl.lt) from API"
    )
    longitude = models.DecimalField(
        max_digits=10,
        decimal_places=7,
        blank=True,
        null=True,
        help_text="Longitude (gl.ln) from API"
    )

    # Address Information
    address_line = models.TextField(
        blank=True,
        null=True,
        help_text="Address (ad.adr) from API"
    )
    postal_code = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        help_text="Postal code (ad.postalCode) from API"
    )
    city_name = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        help_text="City name (ad.city.name) from API"
    )
    country_name = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        help_text="Country name (ad.country.name) from API"
    )

    # Property Information
    property_type = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        help_text="Property type (pt) from API"
    )

    # JSON Fields for complex data
    images = models.JSONField(
        default=list,
        blank=True,
        help_text="Stores an array of full_url values from the img object"
    )
    amenities = models.JSONField(
        default=list,
        blank=True,
        help_text="Consolidated, unique list of amenities from all room options (fcs)"
    )
    meta_json = models.JSONField(
        default=dict,
        blank=True,
        help_text="The complete, raw JSON response from hotelDetail-search"
    )

    # M2M relationship with Destinations
    destinations = models.ManyToManyField(
        Destination,
        through='TripjackHotelDestinationMapping',
        related_name='tripjack_hotels',
        help_text="Destinations this hotel is associated with"
    )

    class Meta:
        verbose_name = 'TripJack Hotel'
        verbose_name_plural = 'TripJack Hotels'
        ordering = ['-created_at']
        indexes = [
            # For lookups by hotel_id
            models.Index(
                fields=['hotel_id'],
                name='tj_hotels_hotel_id_idx'
            ),
            # For rating-based filtering
            models.Index(
                fields=['star_rating', 'is_active'],
                name='tj_hotels_rating_active_idx'
            ),
            # For location-based queries
            models.Index(
                fields=['city_name', 'country_name'],
                name='tj_hotels_location_idx'
            ),
        ]

    def __str__(self):
        return f"{self.name} ({self.hotel_id})"

    def clean(self):
        """Validate the model's data before saving."""
        super().clean()
        if self.star_rating is not None and not (1 <= self.star_rating <= 5):
            raise ValidationError({'star_rating': 'Star rating must be between 1 and 5.'})

    def save(self, *args, **kwargs):
        """Override save to ensure data consistency and validation."""
        self.full_clean()  # Call clean method before saving
        # Ensure images and amenities are lists
        if not isinstance(self.images, list):
            self.images = []
        if not isinstance(self.amenities, list):
            self.amenities = []
        if not isinstance(self.meta_json, dict):
            self.meta_json = {}

        super().save(*args, **kwargs)

    @property
    def full_address(self):
        """Get formatted full address"""
        address_parts = []
        if self.address_line:
            address_parts.append(self.address_line)
        if self.city_name:
            address_parts.append(self.city_name)
        if self.postal_code:
            address_parts.append(self.postal_code)
        if self.country_name:
            address_parts.append(self.country_name)
        return ", ".join(address_parts) if address_parts else "Address not available"


class TripjackHotelDestinationMapping(BaseModel):
    """
    Through model for TripjackHotels-Destination M2M relationship
    """
    hotel = models.ForeignKey(TripjackHotels, on_delete=models.CASCADE)
    destination = models.ForeignKey(Destination, on_delete=models.CASCADE)

    class Meta:
        unique_together = ('hotel', 'destination')
        verbose_name = 'TripJack Hotel Destination Mapping'
        verbose_name_plural = 'TripJack Hotel Destination Mappings'
        indexes = [
            # This index is critical for speeding up the lookup of hotels by destination.
            models.Index(fields=['destination', 'hotel'], name='tj_hotel_dest_map_idx'),
        ]

    def __str__(self):
        return f"{self.hotel.name} - {self.destination.title}"


class TripjackCities(models.Model):
    """
    TripJack Cities model for storing city data from TripJack API
    """
    cityid = models.IntegerField(
        primary_key=True,
        help_text="City ID from TripJack API"
    )
    cityname = models.TextField(
        help_text="City name from TripJack API"
    )
    countryname = models.TextField(
        help_text="Country name from TripJack API"
    )
    type = models.TextField(
        help_text="Type of the city from TripJack API"
    )

    class Meta:
        db_table = 'tripjack_cities'
        verbose_name = 'TripJack City'
        verbose_name_plural = 'TripJack Cities'

    def __str__(self):
        return f"{self.cityname}, {self.countryname}"


class CityDestinationMapping(models.Model):
    """
    Mapping between TripJack cities and package destinations
    """
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('processed', 'Processed'),
        ('failed', 'Failed'),
    ]

    city = models.OneToOneField(
        TripjackCities,
        on_delete=models.CASCADE,
        primary_key=True,
        related_name='destination_mapping'
    )
    destination = models.ForeignKey(
        Destination,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='city_mappings'
    )
    status = models.CharField(
        max_length=50,
        choices=STATUS_CHOICES,
        default='pending',
        help_text="Processing status of the mapping"
    )
    processed_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Timestamp when the mapping was processed"
    )
    ai_model_version = models.TextField(
        null=True,
        blank=True,
        help_text="AI model version used for mapping"
    )

    class Meta:
        db_table = 'city_destination_mapping'
        verbose_name = 'City Destination Mapping'
        verbose_name_plural = 'City Destination Mappings'
        indexes = [
            models.Index(
                fields=['destination'],
                name='idx_city_dest_mapping_dest_id'
            ),
            models.Index(
                fields=['status'],
                name='idx_city_dest_mapping_status'
            ),
        ]

    def __str__(self):
        destination_name = self.destination.title if self.destination else "No destination"
        return f"{self.city.cityname} -> {destination_name} ({self.status})"


class CustomPackageItinerary(BaseModel):
    package = models.ForeignKey(
        CustomPackage,
        on_delete=models.CASCADE,
        related_name='itineraries'
    )
    day_number = models.PositiveIntegerField()
    type = models.CharField(max_length=255, choices=ItineraryDayItemType.choices)
    activity = models.ForeignKey(
        CustomActivity,
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )
    hotel = models.ForeignKey(
        TripjackHotels,
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )

    class Meta:
        verbose_name = 'Custom Package Itinerary'
        verbose_name_plural = 'Custom Package Itineraries'
        ordering = ['package', 'day_number']
        indexes = [
            models.Index(fields=['package', 'day_number'], name='cpi_package_day_idx'),
        ]
        constraints = [
            models.CheckConstraint(
                check=models.Q(
                    type=ItineraryDayItemType.ACTIVITY, activity__isnull=False
                ) | models.Q(type__in=[
                    ItineraryDayItemType.HOTEL,
                ]),
                name='activity_not_null_if_type_is_activity'
            ),
            models.CheckConstraint(
                check=models.Q(
                    type=ItineraryDayItemType.HOTEL, hotel__isnull=False
                ) | models.Q(type__in=[
                    ItineraryDayItemType.ACTIVITY,
                ]),
                name='hotel_not_null_if_type_is_hotel'
            )
        ]

    def __str__(self):
        return f"{self.package.title} - Day {self.day_number} - {self.type}"
