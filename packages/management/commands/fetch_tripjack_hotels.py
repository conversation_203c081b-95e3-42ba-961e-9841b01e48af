import asyncio
import logging
from django.core.management.base import BaseCommand
from django.conf import settings
from tripjack_hotel_processing.api_client import get_search_id, get_hotel_ids
from tripjack_hotel_processing.hotel_processor import process_hotel
from tripjack_hotel_processing.db_utils import get_unprocessed_cities, mark_city_as_completed, mark_city_as_pending

# Configure logger
logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = "Fetches hotel data from the TripJack API and saves it to the database."

    def handle(self, *args, **options):
        self.stdout.write("Starting TripJack hotel processing script.")
        
        # Configure logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

        try:
            asyncio.run(self.async_handle())
            self.stdout.write(self.style.SUCCESS("TripJack hotel processing script finished successfully."))
        except Exception as e:
            logger.error(f"The hotel processing script failed: {e}", exc_info=True)
            self.stderr.write(self.style.ERROR("The script failed. Check the logs for details."))

    async def async_handle(self):
        """
        Asynchronous handler that will pause and retry if a rate-limit error is detected.
        """
        while True:
            logger.info("Starting a new processing cycle.")
            # The queue will hold the cities to be processed.
            queue = asyncio.Queue(maxsize=settings.MAX_CONCURRENT_CITIES * 2)

            # Event to signal a pause across all tasks for the current cycle.
            pause_event = asyncio.Event()

            # Create a task that will complete only when the pause_event is set.
            # This allows us to use it in asyncio.wait().
            pause_waiter = asyncio.create_task(pause_event.wait())

            # Create and start the producer and worker tasks
            producer_task = asyncio.create_task(self.producer(queue, pause_event))
            worker_tasks = [
                asyncio.create_task(self.worker(f"worker-{i}", queue, pause_event))
                for i in range(settings.MAX_CONCURRENT_CITIES)
            ]

            all_tasks = [producer_task] + worker_tasks
            done, pending = await asyncio.wait(
                [pause_waiter] + all_tasks,
                return_when=asyncio.FIRST_COMPLETED
            )

            # If the pause_waiter is done, it means the pause_event was set.
            if pause_waiter in done:
                logger.critical("Rate-limit error detected. Pausing for 10 minutes before retrying.")

                # Cancel all pending producer/worker tasks
                for task in pending:
                    task.cancel()

                # Wait for all tasks to acknowledge cancellation
                await asyncio.gather(*pending, return_exceptions=True)

                logger.info("All tasks for the current cycle have been cancelled. Entering sleep period.")
                await asyncio.sleep(600)  # Sleep for 10 minutes

                # The loop will now restart
                continue

            # If we are here, it means the producer and workers finished normally.
            # We must wait for the queue to be fully drained before concluding.
            await queue.join()

            # Check if the pause event was set during the final queue processing.
            if pause_event.is_set():
                logger.warning("Pause event was set during final queue processing. Restarting cycle.")
                # Cancel any remaining tasks just in case
                for task in all_tasks:
                    if not task.done():
                        task.cancel()
                await asyncio.gather(*all_tasks, return_exceptions=True)
                continue

            # If the event was not set, it means we've processed all cities.
            logger.info("All cities have been processed successfully.")

            # Gracefully cancel the now-idle tasks and the pause_waiter.
            pause_waiter.cancel()
            for task in all_tasks:
                task.cancel()
            await asyncio.gather(pause_waiter, *all_tasks, return_exceptions=True)

            logger.info("All tasks are cancelled.")
            break # Exit the main while loop

    async def producer(self, queue: asyncio.Queue, pause_event: asyncio.Event):
        """
        Fetches cities from the database and puts them into the queue.
        Stops if the pause_event is set.
        """
        logger.info("Producer started.")
        while not pause_event.is_set():
            # Fetch a batch of cities.
            cities_to_process = await get_unprocessed_cities(batch_size=settings.MAX_CONCURRENT_CITIES)
            if not cities_to_process:
                logger.info("Producer found no more cities to process. Finishing producer task.")
                break

            logger.info(f"Producer is adding {len(cities_to_process)} cities to the queue.")
            for city_info in cities_to_process:
                if pause_event.is_set():
                    logger.warning("Pause event set, producer stopping.")
                    break
                await queue.put(city_info)

        logger.info("Producer finished.")

    async def worker(self, name: str, queue: asyncio.Queue, pause_event: asyncio.Event):
        """
        A worker task that continuously takes a city from the queue and processes it.
        Stops if the pause_event is set.
        """
        logger.info(f"Worker '{name}' started.")
        while not pause_event.is_set():
            try:
                # Get a city from the queue. This will wait if the queue is empty.
                city_info = await queue.get()

                # If pause was triggered while waiting, don't process this city.
                if pause_event.is_set():
                    logger.warning(f"Worker '{name}' stopping, pause event received.")
                    queue.task_done()
                    break

                logger.info(f"Worker '{name}' is processing city_id: {city_info['city_id']}.")
                await self.process_city(city_info, pause_event)

                # Notify the queue that the task is done.
                queue.task_done()

                if not pause_event.is_set():
                    logger.info(f"Worker '{name}' finished processing city_id: {city_info['city_id']}.")

            except asyncio.CancelledError:
                logger.info(f"Worker '{name}' was cancelled.")
                break
            except Exception as e:
                # Log any unexpected errors in the worker itself.
                logger.error(f"An error occurred in worker '{name}': {e}", exc_info=True)
                # Still need to mark the task as done to not block queue.join()
                queue.task_done()

    async def process_city(self, city_info: dict, pause_event: asyncio.Event):
        """
        Fetches all hotels for a single city and processes them.
        """
        city_id = str(city_info["city_id"])
        destination_id = city_info["destination_id"]

        logger.info(f"Starting processing for city_id: {city_id}")

        try:
            # These dates can be adjusted as needed
            checkin_date = "2025-09-04"
            checkout_date = "2025-09-05"

            search_id = await get_search_id(city_id, checkin_date, checkout_date, pause_event)

            # If pause is triggered during get_search_id, halt execution for this city.
            if pause_event.is_set():
                logger.warning(f"Pause triggered. Halting processing for city {city_id}.")
                return

            if not search_id:
                raise ValueError(f"Could not get searchId for city_id: {city_id}. Halting execution for this city.")

            hotel_ids = await get_hotel_ids(search_id, city_id)
            if not hotel_ids:
                logger.warning(f"No hotels found for city_id: {city_id} with searchId: {search_id}")
                await mark_city_as_completed(int(city_id)) # Mark as completed even if no hotels are found
                return

            logger.info(f"Processing {len(hotel_ids)} hotels for city_id: {city_id}")

            tasks = []
            for hotel_id in hotel_ids:
                if pause_event.is_set():
                    logger.warning(f"Pause triggered in hotel processing loop for city {city_id}. Halting further hotel processing.")
                    break
                tasks.append(process_hotel(hotel_id, destination_id, city_id, pause_event))
                if len(tasks) >= settings.MAX_CONCURRENT_HOTELS:
                    await asyncio.gather(*tasks)
                    tasks = []

            if tasks:
                await asyncio.gather(*tasks)

            # If the script is pausing, don't mark the city as completed.
            if not pause_event.is_set():
                await mark_city_as_completed(int(city_id))
                logger.info(f"Finished processing for city_id: {city_id}")

        except Exception as e:
            logger.error(f"An error occurred while processing city {city_id}: {e}", exc_info=True)
            await mark_city_as_pending(int(city_id))
