import json
import os
import requests
import uuid
import mimetypes
from django.core.management.base import BaseCommand
from django.core.files.base import ContentFile
from django.core.files.storage import default_storage
from serpapi import GoogleSearch
from packages.models import (
    Package,
    PackageHotel,
    PackageRestaurant,
)
from packages.utils.establishment_utils import get_place_details, download_image, clean_queries
from django.db.utils import DataError
import logging
from django.conf import settings
from django.db.models import Q

logger = logging.getLogger(__name__)
api_key = settings.SERP_API_KEY

if not api_key:
    raise ValueError("Please set the SERP_API_KEY in your Django settings.")

def get_images_from_link(data_id: str):
    """
    Performs a secondary API call specifically to fetch images for a place.
    """
    if not data_id:
        return []

    logger.info(f"--- Performing secondary search for images using data_id: {data_id} ---")

    image_params = {
        "engine": "google_maps_photos",
        "data_id": data_id,
        "api_key": api_key,
    }

    try:
        search = GoogleSearch(image_params)
        image_results = search.get_dict()

        if "photos" in image_results:
            return [photo.get("image") for photo in image_results["photos"]]
        else:
            return []
    except Exception as e:
        logger.error(f"An error occurred during the image fetch: {e}")
        return []


class Command(BaseCommand):
    help = 'Fetches and saves hotel and restaurant data for existing packages using SerpApi.'

    def add_arguments(self, parser):
        parser.add_argument(
            '--mode',
            type=str,
            default='all',
            choices=['all', 'unprocessed'],
            help='Specify the processing mode. "all" processes all packages, "unprocessed" processes only those with unprocessed establishments.',
        )

    def handle(self, *args, **options):
        mode = options['mode']
        logger.info(f"Running in mode: {mode}")

        # Determine which packages to process based on the mode
        if mode == 'unprocessed':
            logger.info("Filtering for packages with unprocessed establishments.")
            packages_to_process = Package.objects.filter(
                Q(hotels__isnull=False, package_hotels__isnull=True) |
                Q(popular_restaurants__isnull=False, package_restaurants__isnull=True)
            ).distinct()
            if not packages_to_process.exists():
                logger.info("No packages with unprocessed establishments found.")
                return
            logger.info(f"Found {packages_to_process.count()} packages to process.")
        else:
            logger.info("Processing all packages.")
            packages_to_process = Package.objects.all()

        # Step 1: Create and associate establishments for the selected packages
        logger.info('Step 1: Creating and associating establishments...')
        for package in packages_to_process:
            logger.info(f'Processing package: {package.title}')

            # Process hotels
            if hasattr(package, 'hotels') and package.hotels:
                for hotel_entry in package.hotels:
                    hotel_name = clean_queries(hotel_entry)
                    hotel, created = PackageHotel.objects.get_or_create(name=hotel_name)
                    package.package_hotels.add(hotel)
                    if created:
                        logger.info(f"Associated new hotel: {hotel_name}")
                    else:
                        logger.info(f"Associated existing hotel: {hotel_name}")

            # Process restaurants
            if hasattr(package, 'popular_restaurants') and package.popular_restaurants:
                for restaurant_entry in package.popular_restaurants:
                    restaurant_name = clean_queries(restaurant_entry)
                    restaurant, created = PackageRestaurant.objects.get_or_create(name=restaurant_name)
                    package.package_restaurants.add(restaurant)
                    if created:
                        logger.info(f"Associated new restaurant: {restaurant_name}")
                    else:
                        logger.info(f"Associated existing restaurant: {restaurant_name}")

        # Step 2: Populate details for establishments that are missing them
        logger.info('\nStep 2: Populating missing establishment details...')

        # We only need to query for establishments related to the packages we are processing
        package_ids = packages_to_process.values_list('id', flat=True)

        hotels_query = PackageHotel.objects.filter(
            is_active=True,
            address__isnull=True,
            packages__id__in=package_ids
        ).distinct()

        restaurants_query = PackageRestaurant.objects.filter(
            is_active=True,
            address__isnull=True,
            packages__id__in=package_ids
        ).distinct()

        establishments_to_process = list(hotels_query) + list(restaurants_query)

        if not establishments_to_process:
            logger.info('No establishments need detail population for the selected packages. All are up-to-date.')
            return

        logger.info(f"Found {len(establishments_to_process)} establishments to process details for.")

        for establishment in establishments_to_process:
            model_name = "Hotel" if isinstance(establishment, PackageHotel) else "Restaurant"
            logger.info(f"Processing {model_name}: {establishment.name}")

            # Find a package associated with this establishment to get the location
            package_relation = establishment.packages.first()
            if not package_relation:
                logger.warning(f"Skipping: No associated package found for {establishment.name} to determine location.")
                continue

            destination_title = package_relation.destination.title if package_relation.destination else ""
            if not destination_title:
                logger.warning(f"Skipping: Associated package for {establishment.name} has no destination.")
                continue

            try:
                details = get_place_details(establishment.name, destination_title)
                if details:
                    establishment.address = details.get('address')
                    establishment.phone = details.get('phone')
                    establishment.website = details.get('website')
                    establishment.rating = details.get('rating')
                    establishment.review_count = details.get('review_count')
                    establishment.description = details.get('description')
                    establishment.amenities = details.get('amenities')

                    # Handle single image
                    image_paths = []
                    image_urls_from_api = details.get('image_urls', [])
                    if image_urls_from_api:
                        first_image_url = image_urls_from_api[0]
                        sub_folder = 'hotels' if model_name == 'Hotel' else 'restaurants'
                        image_content = download_image(first_image_url)
                        if image_content:
                            destination_path = f"package/media/{sub_folder}/{establishment.id}/{image_content.name}"
                            stored_path = default_storage.save(destination_path, image_content)
                            image_paths.append(stored_path)

                    if image_paths:
                        establishment.image_urls = image_paths

                    establishment.save()
                    logger.info(f"Successfully populated details for {establishment.name}.")
                else:
                    logger.warning(f'Could not retrieve details for: {establishment.name}')

            except DataError as e:
                logger.error(f"Database error for '{establishment.name}'. Name might be too long. Error: {e}")
            except Exception as e:
                logger.error(f"An unexpected error occurred for '{establishment.name}': {e}")

        logger.info('\nFinished populating establishment data.')