# Generated by Django 4.2 on 2025-06-16 08:42

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("accounts", "0010_alter_user_user_type"),
        ("packages", "0012_alter_activity_title_alter_activitymedia_media_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="activitymedia",
            name="main_display",
            field=models.BooleanField(default=False),
        ),
        migrations.CreateModel(
            name="DestinationFaq",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("restored_at", models.DateTimeField(blank=True, null=True)),
                ("transaction_id", models.UUIDField(blank=True, null=True)),
                ("external_id", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.<PERSON>oleanField(default=True)),
                ("question", models.TextField()),
                ("answer", models.TextField()),
                ("is_published", models.BooleanField(default=False)),
                ("priority", models.PositiveIntegerField(default=0)),
                (
                    "destination",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="faqs",
                        to="packages.destination",
                    ),
                ),
                (
                    "partner",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="destination_faqs",
                        to="accounts.partner",
                    ),
                ),
            ],
            options={
                "verbose_name": "Destination FAQ",
                "verbose_name_plural": "Destination FAQs",
                "ordering": ["-priority", "-created_at"],
            },
        ),
    ]
