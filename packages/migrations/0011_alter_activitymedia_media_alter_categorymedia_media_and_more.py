# Generated by Django 4.2 on 2025-06-12 10:16

import base.storage_utils
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("packages", "0010_alter_activity_description_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="activitymedia",
            name="media",
            field=models.FileField(
                default="none",
                help_text="Media file is required*",
                upload_to=base.storage_utils.activity_upload_path,
            ),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="categorymedia",
            name="media",
            field=models.FileField(
                default="none",
                help_text="Media file is required*",
                upload_to=base.storage_utils.category_upload_path,
            ),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="destinationmedia",
            name="media",
            field=models.FileField(
                default="none",
                help_text="Media file is required*",
                upload_to=base.storage_utils.destination_upload_path,
            ),
            preserve_default=False,
        ),
    ]
