# Generated by Django 4.2 on 2025-07-10 10:21

import django.contrib.gis.db.models.fields
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("packages", "0018_package_rating_description_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="CustomActivityCategory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("restored_at", models.DateTimeField(blank=True, null=True)),
                ("transaction_id", models.UUIDField(blank=True, null=True)),
                ("external_id", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                ("name", models.CharField(blank=True, max_length=255, null=True)),
            ],
            options={
                "verbose_name": "Custom Activity Category",
                "verbose_name_plural": "Custom Activity Categories",
            },
        ),
        migrations.CreateModel(
            name="CustomActivityLocation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("restored_at", models.DateTimeField(blank=True, null=True)),
                ("transaction_id", models.UUIDField(blank=True, null=True)),
                ("external_id", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "location_coordinates",
                    django.contrib.gis.db.models.fields.PointField(
                        blank=True, null=True, srid=4326
                    ),
                ),
                ("country", models.CharField(blank=True, max_length=255, null=True)),
                ("city", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "google_place_id",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
            ],
            options={
                "verbose_name": "Custom Activity Location",
                "verbose_name_plural": "Custom Activity Locations",
            },
        ),
        migrations.CreateModel(
            name="CustomActivity",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("restored_at", models.DateTimeField(blank=True, null=True)),
                ("transaction_id", models.UUIDField(blank=True, null=True)),
                ("external_id", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                ("tour_id", models.CharField(blank=True, max_length=255, null=True)),
                ("title", models.TextField(blank=True, null=True)),
                ("abstract", models.TextField(blank=True, null=True)),
                ("description", models.TextField(blank=True, null=True)),
                (
                    "activity_type",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("additional_information", models.TextField(blank=True, null=True)),
                ("items_to_bring", models.TextField(blank=True, null=True)),
                ("not_allowed", models.TextField(blank=True, null=True)),
                ("not_suitable_for", models.TextField(blank=True, null=True)),
                (
                    "bestseller",
                    models.BooleanField(blank=True, default=False, null=True),
                ),
                (
                    "certified",
                    models.BooleanField(blank=True, default=False, null=True),
                ),
                (
                    "has_pick_up",
                    models.BooleanField(blank=True, default=False, null=True),
                ),
                (
                    "overall_rating",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=3, null=True
                    ),
                ),
                (
                    "number_of_ratings",
                    models.PositiveIntegerField(blank=True, null=True),
                ),
                ("highlights", models.TextField(blank=True, null=True)),
                ("inclusions", models.TextField(blank=True, null=True)),
                ("exclusions", models.TextField(blank=True, null=True)),
                ("pictures", models.JSONField(blank=True, default=list, null=True)),
                (
                    "coordinates",
                    django.contrib.gis.db.models.fields.PointField(
                        blank=True, null=True, srid=4326
                    ),
                ),
                (
                    "price",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=10, null=True
                    ),
                ),
                (
                    "location_id",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("opening_hours", models.TextField(blank=True, null=True)),
                ("cancellation_policy_text", models.TextField(blank=True, null=True)),
                (
                    "cancellation_policy",
                    models.JSONField(blank=True, default=dict, null=True),
                ),
                (
                    "destination",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="custom_activities",
                        to="packages.destination",
                    ),
                ),
            ],
            options={
                "verbose_name": "Custom Activity",
                "verbose_name_plural": "Custom Activities",
            },
        ),
    ]
