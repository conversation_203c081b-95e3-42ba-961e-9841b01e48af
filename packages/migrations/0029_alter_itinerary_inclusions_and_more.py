# Generated by Django 4.2 on 2025-07-24 09:18

from django.db import migrations, models
import django_better_admin_arrayfield.models.fields


class Migration(migrations.Migration):

    dependencies = [
        ('packages', '0028_replace_inclusions_jsonfield_with_arrayfield'),
    ]

    operations = [
        migrations.AlterField(
            model_name='itinerary',
            name='inclusions',
            field=django_better_admin_arrayfield.models.fields.ArrayField(base_field=models.CharField(max_length=255), blank=True, null=True, size=None),
        ),
        migrations.AlterField(
            model_name='itinerarydayitem',
            name='inclusions',
            field=django_better_admin_arrayfield.models.fields.ArrayField(base_field=models.Char<PERSON>ield(max_length=255), blank=True, null=True, size=None),
        ),
        migrations.AlterField(
            model_name='packagehotel',
            name='website',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='packagerestaurant',
            name='website',
            field=models.TextField(blank=True, null=True),
        ),
    ]
