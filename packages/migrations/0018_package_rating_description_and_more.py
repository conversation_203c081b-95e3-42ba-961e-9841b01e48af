# Generated by Django 4.2 on 2025-06-23 10:10

import ckeditor.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("packages", "0017_alter_packagemedia_file_alter_packageuploader_file"),
    ]

    operations = [
        migrations.AddField(
            model_name="package",
            name="rating_description",
            field=models.TextField(
                blank=True,
                help_text="Brief description about the package rating (auto-generated if empty)",
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="package",
            name="what_to_pack",
            field=ckeditor.fields.RichTextField(blank=True, null=True),
        ),
    ]
