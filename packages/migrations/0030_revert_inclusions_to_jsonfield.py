# Generated by Django 4.2 on 2025-07-24 09:22

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("packages", "0029_alter_itinerary_inclusions_and_more"),
    ]

    operations = [
        # Remove the ArrayField inclusions fields
        migrations.RemoveField(
            model_name="itinerary",
            name="inclusions",
        ),
        migrations.RemoveField(
            model_name="itinerarydayitem",
            name="inclusions",
        ),
        # Add the JSONField inclusions fields back
        migrations.AddField(
            model_name="itinerary",
            name="inclusions",
            field=models.JSONField(blank=True, default=list, null=True),
        ),
        migrations.AddField(
            model_name="itinerarydayitem",
            name="inclusions",
            field=models.JSONField(blank=True, default=list, null=True),
        ),
    ]
