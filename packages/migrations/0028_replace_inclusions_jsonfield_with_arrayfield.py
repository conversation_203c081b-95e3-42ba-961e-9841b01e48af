# Generated by Django 4.2 on 2025-07-24 06:06

from django.db import migrations, models
import django_better_admin_arrayfield.models.fields


class Migration(migrations.Migration):

    dependencies = [
        ("packages", "0027_itinerary_itineraryactivity_custompackage_and_more"),
    ]

    operations = [
        # Remove the old JSONField inclusions fields
        migrations.RemoveField(
            model_name="itinerary",
            name="inclusions",
        ),
        migrations.RemoveField(
            model_name="itinerarydayitem",
            name="inclusions",
        ),
        # Add the new ArrayField inclusions fields
        migrations.AddField(
            model_name="itinerary",
            name="inclusions",
            field=django_better_admin_arrayfield.models.fields.ArrayField(
                base_field=models.CharField(max_length=255),
                blank=True,
                default=list,
                size=None,
            ),
        ),
        migrations.AddField(
            model_name="itinerarydayitem",
            name="inclusions",
            field=django_better_admin_arrayfield.models.fields.ArrayField(
                base_field=models.Char<PERSON><PERSON>(max_length=255),
                blank=True,
                default=list,
                size=None,
            ),
        ),
    ]
