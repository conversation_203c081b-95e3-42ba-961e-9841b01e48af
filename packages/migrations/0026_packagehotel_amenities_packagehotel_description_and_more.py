# Generated by Django 4.2 on 2025-07-22 12:00

from django.db import migrations, models
import django_better_admin_arrayfield.models.fields


class Migration(migrations.Migration):

    dependencies = [
        ('packages', '0025_packagehotel_packagerestaurant_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='packagehotel',
            name='amenities',
            field=django_better_admin_arrayfield.models.fields.ArrayField(base_field=models.CharField(max_length=500), blank=True, default=list, size=None),
        ),
        migrations.AddField(
            model_name='packagehotel',
            name='description',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='packagerestaurant',
            name='amenities',
            field=django_better_admin_arrayfield.models.fields.ArrayField(base_field=models.CharField(max_length=500), blank=True, default=list, size=None),
        ),
        migrations.AddField(
            model_name='packagerestaurant',
            name='description',
            field=models.TextField(blank=True, null=True),
        ),
    ]
