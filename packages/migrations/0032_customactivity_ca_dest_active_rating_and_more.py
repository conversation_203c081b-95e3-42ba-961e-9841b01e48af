# Generated by Django 4.2 on 2025-07-28 16:32

import django.contrib.postgres.indexes
from django.db import migrations, models
import django.db.models.functions.text


class Migration(migrations.Migration):

    dependencies = [
        ('packages', '0031_auto_20250728_1631'),
    ]

    operations = [
        migrations.AddIndex(
            model_name='customactivity',
            index=models.Index(fields=['destination', 'is_active', '-overall_rating'], name='ca_dest_active_rating'),
        ),
        migrations.AddIndex(
            model_name='customactivity',
            index=models.Index(fields=['destination', 'is_active'], name='cust_act_dest_active_idx'),
        ),
        migrations.AddIndex(
            model_name='customactivity',
            index=django.contrib.postgres.indexes.GinIndex(fields=['persona'], name='cust_act_persona_gin_idx'),
        ),
        migrations.AddIndex(
            model_name='destination',
            index=django.contrib.postgres.indexes.GistIndex(django.contrib.postgres.indexes.OpClass(django.db.models.functions.text.Lower('title'), name='gist_trgm_ops'), name='destination_title_gist_idx'),
        ),
    ]
