# Generated by Django 4.2 on 2025-06-10 08:58

from django.db import migrations, models
import django_better_admin_arrayfield.models.fields


class Migration(migrations.Migration):

    dependencies = [
        ("packages", "0006_remove_package_optional_activities"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="package",
            name="addons",
            field=django_better_admin_arrayfield.models.fields.ArrayField(
                base_field=models.CharField(max_length=255), default=list, size=None
            ),
        ),
        migrations.AlterField(
            model_name="package",
            name="cultural_info",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="package",
            name="destination_safety",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="package",
            name="itinerary",
            field=models.J<PERSON>NField(default=list),
        ),
        migrations.AlterField(
            model_name="package",
            name="rating",
            field=models.DecimalField(
                blank=True, decimal_places=1, max_digits=2, null=True
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="package",
            name="what_to_pack",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="package",
            name="what_to_shop",
            field=models.TextField(blank=True, null=True),
        ),
    ]
