# Generated by Django 4.2 on 2025-07-22 05:42

from django.db import migrations, models
import django.db.models.deletion
import django_better_admin_arrayfield.models.fields
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('packages', '0024_customactivity_preferred_end_time_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='PackageHotel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('restored_at', models.DateTimeField(blank=True, null=True)),
                ('transaction_id', models.UUIDField(blank=True, null=True)),
                ('external_id', models.UUIDField(default=uuid.uuid4, editable=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.Bo<PERSON>anField(default=True)),
                ('name', models.TextField()),
                ('address', models.TextField(blank=True, null=True)),
                ('phone', models.CharField(blank=True, max_length=255, null=True)),
                ('website', models.URLField(blank=True, max_length=500, null=True)),
                ('rating', models.DecimalField(blank=True, decimal_places=2, max_digits=3, null=True)),
                ('review_count', models.PositiveIntegerField(blank=True, null=True)),
                ('image_urls', django_better_admin_arrayfield.models.fields.ArrayField(base_field=models.CharField(max_length=500), blank=True, default=list, size=None)),
            ],
            options={
                'verbose_name': 'Hotel',
                'verbose_name_plural': 'Hotels',
            },
        ),
        migrations.CreateModel(
            name='PackageRestaurant',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('restored_at', models.DateTimeField(blank=True, null=True)),
                ('transaction_id', models.UUIDField(blank=True, null=True)),
                ('external_id', models.UUIDField(default=uuid.uuid4, editable=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('name', models.TextField()),
                ('address', models.TextField(blank=True, null=True)),
                ('phone', models.CharField(blank=True, max_length=255, null=True)),
                ('website', models.URLField(blank=True, max_length=500, null=True)),
                ('rating', models.DecimalField(blank=True, decimal_places=2, max_digits=3, null=True)),
                ('review_count', models.PositiveIntegerField(blank=True, null=True)),
                ('image_urls', django_better_admin_arrayfield.models.fields.ArrayField(base_field=models.CharField(max_length=500), blank=True, default=list, size=None)),
            ],
            options={
                'verbose_name': 'Restaurant',
                'verbose_name_plural': 'Restaurants',
            },
        ),
        migrations.CreateModel(
            name='PackageRestaurantRelation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('restored_at', models.DateTimeField(blank=True, null=True)),
                ('transaction_id', models.UUIDField(blank=True, null=True)),
                ('external_id', models.UUIDField(default=uuid.uuid4, editable=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('package', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='packages.package')),
                ('restaurant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='packages.packagerestaurant')),
            ],
            options={
                'verbose_name': 'Package Restaurant Relation',
                'verbose_name_plural': 'Package Restaurant Relations',
                'unique_together': {('package', 'restaurant')},
            },
        ),
        migrations.CreateModel(
            name='PackageHotelRelation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('restored_at', models.DateTimeField(blank=True, null=True)),
                ('transaction_id', models.UUIDField(blank=True, null=True)),
                ('external_id', models.UUIDField(default=uuid.uuid4, editable=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('hotel', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='packages.packagehotel')),
                ('package', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='packages.package')),
            ],
            options={
                'verbose_name': 'Package Hotel Relation',
                'verbose_name_plural': 'Package Hotel Relations',
                'unique_together': {('package', 'hotel')},
            },
        ),
        migrations.AddField(
            model_name='package',
            name='package_hotels',
            field=models.ManyToManyField(blank=True, related_name='packages', through='packages.PackageHotelRelation', to='packages.packagehotel'),
        ),
        migrations.AddField(
            model_name='package',
            name='package_restaurants',
            field=models.ManyToManyField(blank=True, related_name='packages', through='packages.PackageRestaurantRelation', to='packages.packagerestaurant'),
        ),
    ]
