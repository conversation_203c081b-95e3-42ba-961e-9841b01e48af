# Generated by Django 4.2 on 2025-06-06 07:18

from django.db import migrations, models
import django.db.models.deletion
import django_better_admin_arrayfield.models.fields


class Migration(migrations.Migration):

    dependencies = [
        ("packages", "0003_activity_is_featured_activity_partner_and_more"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="package",
            name="currency_symbol",
        ),
        migrations.AddField(
            model_name="package",
            name="best_time_to_visit_months",
            field=django_better_admin_arrayfield.models.fields.ArrayField(
                base_field=models.CharField(max_length=255),
                blank=True,
                null=True,
                size=None,
            ),
        ),
        migrations.AddField(
            model_name="package",
            name="optional",
            field=django_better_admin_arrayfield.models.fields.ArrayField(
                base_field=models.CharField(max_length=255),
                blank=True,
                default=list,
                size=None,
            ),
        ),
        migrations.AddField(
            model_name="package",
            name="owner",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name="package",
            name="price",
            field=models.DecimalField(decimal_places=2, default=1, max_digits=10),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="package",
            name="currency_conversion_rate",
            field=models.TextField(),
        ),
        migrations.AlterField(
            model_name="package",
            name="package_uploaded",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="packages",
                to="packages.packageuploader",
            ),
        ),
        migrations.AlterField(
            model_name="package",
            name="price_per_person",
            field=models.CharField(max_length=255),
        ),
    ]
