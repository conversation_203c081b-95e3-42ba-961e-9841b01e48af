# Generated by Django 4.2 on 2025-07-16 10:36

from django.db import migrations, models
import django.db.models.deletion
import django_better_admin_arrayfield.models.fields
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("packages", "0021_remove_customactivity_pictures_customactivitymedia"),
    ]

    operations = [
        migrations.AddField(
            model_name="customactivity",
            name="persona",
            field=django_better_admin_arrayfield.models.fields.ArrayField(
                base_field=models.CharField(
                    choices=[
                        ("Couple", "Couple"),
                        ("Solo", "Solo"),
                        ("Family", "Family"),
                        ("Friends", "Friends"),
                    ],
                    max_length=50,
                ),
                blank=True,
                default=list,
                help_text="Select one or more personas this activity is suitable for",
                size=None,
            ),
        ),
        migrations.CreateModel(
            name="CustomActivitySystemCategoryRelation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("restored_at", models.DateTimeField(blank=True, null=True)),
                ("transaction_id", models.UUIDField(blank=True, null=True)),
                ("external_id", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "activity",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="system_category_relations",
                        to="packages.customactivity",
                    ),
                ),
                (
                    "category",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="custom_activity_relations",
                        to="packages.category",
                    ),
                ),
            ],
            options={
                "verbose_name": "Custom Activity System Category Relation",
                "verbose_name_plural": "Custom Activity System Category Relations",
                "unique_together": {("activity", "category")},
            },
        ),
        migrations.AddField(
            model_name="customactivity",
            name="system_categories",
            field=models.ManyToManyField(
                blank=True,
                related_name="custom_activities",
                through="packages.CustomActivitySystemCategoryRelation",
                to="packages.category",
            ),
        ),
    ]
