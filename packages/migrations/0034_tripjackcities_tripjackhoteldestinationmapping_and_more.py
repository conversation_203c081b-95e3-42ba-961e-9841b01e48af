# Generated by Django 4.2 on 2025-08-14 12:51

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('packages', '0033_customactivity_durations'),
    ]

    operations = [
        migrations.CreateModel(
            name='TripjackCities',
            fields=[
                ('cityid', models.IntegerField(help_text='City ID from TripJack API', primary_key=True, serialize=False)),
                ('cityname', models.TextField(help_text='City name from TripJack API')),
                ('countryname', models.TextField(help_text='Country name from TripJack API')),
                ('type', models.TextField(help_text='Type of the city from TripJack API')),
            ],
            options={
                'verbose_name': 'TripJack City',
                'verbose_name_plural': 'TripJack Cities',
                'db_table': 'tripjack_cities',
            },
        ),
        migrations.CreateModel(
            name='TripjackHotelDestinationMapping',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('restored_at', models.DateTimeField(blank=True, null=True)),
                ('transaction_id', models.UUIDField(blank=True, null=True)),
                ('external_id', models.UUIDField(default=uuid.uuid4, editable=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('destination', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='packages.destination')),
            ],
            options={
                'verbose_name': 'TripJack Hotel Destination Mapping',
                'verbose_name_plural': 'TripJack Hotel Destination Mappings',
            },
        ),
        migrations.CreateModel(
            name='CityDestinationMapping',
            fields=[
                ('city', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, primary_key=True, related_name='destination_mapping', serialize=False, to='packages.tripjackcities')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('processed', 'Processed'), ('failed', 'Failed')], default='pending', help_text='Processing status of the mapping', max_length=50)),
                ('processed_at', models.DateTimeField(blank=True, help_text='Timestamp when the mapping was processed', null=True)),
                ('ai_model_version', models.TextField(blank=True, help_text='AI model version used for mapping', null=True)),
            ],
            options={
                'verbose_name': 'City Destination Mapping',
                'verbose_name_plural': 'City Destination Mappings',
                'db_table': 'city_destination_mapping',
            },
        ),
        migrations.CreateModel(
            name='TripjackHotels',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('restored_at', models.DateTimeField(blank=True, null=True)),
                ('transaction_id', models.UUIDField(blank=True, null=True)),
                ('external_id', models.UUIDField(default=uuid.uuid4, editable=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('hotel_id', models.CharField(help_text='Primary Key (uid) from TripJack API', max_length=100, unique=True)),
                ('name', models.CharField(help_text='Hotel name from API', max_length=255)),
                ('description', models.TextField(blank=True, help_text='Hotel description (des) from API', null=True)),
                ('star_rating', models.PositiveIntegerField(blank=True, help_text='Star rating (rt) from API', null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('base_price', models.DecimalField(blank=True, decimal_places=2, help_text="The 'pc' value from the first element in the 'pops' array", max_digits=10, null=True)),
                ('check_in_time', models.CharField(blank=True, help_text='Parsed from policy notes, if available', max_length=20, null=True)),
                ('check_out_time', models.CharField(blank=True, help_text='Parsed from policy notes, if available', max_length=20, null=True)),
                ('latitude', models.DecimalField(blank=True, decimal_places=7, help_text='Latitude (gl.lt) from API', max_digits=10, null=True)),
                ('longitude', models.DecimalField(blank=True, decimal_places=7, help_text='Longitude (gl.ln) from API', max_digits=10, null=True)),
                ('address_line', models.TextField(blank=True, help_text='Address (ad.adr) from API', null=True)),
                ('postal_code', models.CharField(blank=True, help_text='Postal code (ad.postalCode) from API', max_length=20, null=True)),
                ('city_name', models.CharField(blank=True, help_text='City name (ad.city.name) from API', max_length=100, null=True)),
                ('country_name', models.CharField(blank=True, help_text='Country name (ad.country.name) from API', max_length=100, null=True)),
                ('property_type', models.CharField(blank=True, help_text='Property type (pt) from API', max_length=100, null=True)),
                ('images', models.JSONField(blank=True, default=list, help_text='Stores an array of full_url values from the img object')),
                ('amenities', models.JSONField(blank=True, default=list, help_text='Consolidated, unique list of amenities from all room options (fcs)')),
                ('meta_json', models.JSONField(blank=True, default=dict, help_text='The complete, raw JSON response from hotelDetail-search')),
                ('destinations', models.ManyToManyField(help_text='Destinations this hotel is associated with', related_name='tripjack_hotels', through='packages.TripjackHotelDestinationMapping', to='packages.destination')),
            ],
            options={
                'verbose_name': 'TripJack Hotel',
                'verbose_name_plural': 'TripJack Hotels',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddField(
            model_name='tripjackhoteldestinationmapping',
            name='hotel',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='packages.tripjackhotels'),
        ),
        migrations.AddIndex(
            model_name='tripjackhotels',
            index=models.Index(fields=['hotel_id'], name='tj_hotels_hotel_id_idx'),
        ),
        migrations.AddIndex(
            model_name='tripjackhotels',
            index=models.Index(fields=['star_rating', 'is_active'], name='tj_hotels_rating_active_idx'),
        ),
        migrations.AddIndex(
            model_name='tripjackhotels',
            index=models.Index(fields=['city_name', 'country_name'], name='tj_hotels_location_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='tripjackhoteldestinationmapping',
            unique_together={('hotel', 'destination')},
        ),
        migrations.AddField(
            model_name='citydestinationmapping',
            name='destination',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='city_mappings', to='packages.destination'),
        ),
        migrations.AddIndex(
            model_name='citydestinationmapping',
            index=models.Index(fields=['destination'], name='idx_city_dest_mapping_dest_id'),
        ),
        migrations.AddIndex(
            model_name='citydestinationmapping',
            index=models.Index(fields=['status'], name='idx_city_dest_mapping_status'),
        ),
    ]
