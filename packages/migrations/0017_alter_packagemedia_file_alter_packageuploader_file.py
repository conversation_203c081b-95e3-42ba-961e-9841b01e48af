# Generated by Django 4.2 on 2025-06-23 06:52

import base.storage_utils
import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("packages", "0016_alter_package_itinerary"),
    ]

    operations = [
        migrations.AlterField(
            model_name="packagemedia",
            name="file",
            field=models.FileField(
                upload_to=base.storage_utils.package_media_upload_path,
                validators=[
                    django.core.validators.FileExtensionValidator(
                        allowed_extensions=[
                            "jpg",
                            "jpeg",
                            "png",
                            "webp",
                            "mp4",
                            "mov",
                            "JPG",
                            "JPEG",
                            "PNG",
                            "WEBP",
                            "MP4",
                            "MOV",
                        ]
                    )
                ],
            ),
        ),
        migrations.AlterField(
            model_name="packageuploader",
            name="file",
            field=models.FileField(
                upload_to=base.storage_utils.package_upload_path,
                validators=[
                    django.core.validators.FileExtensionValidator(
                        allowed_extensions=[
                            "json",
                            "docx",
                            "doc",
                            "JSO<PERSON>",
                            "DOCX",
                            "DOC",
                        ]
                    )
                ],
            ),
        ),
    ]
