# Generated by Django 4.2 on 2025-07-23 12:11

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("accounts", "0024_alter_partner_logo"),
        ("dynamic_packages", "0003_alter_city_options_alter_country_options_and_more"),
        ("packages", "0026_packagehotel_amenities_packagehotel_description_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="Itinerary",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("restored_at", models.DateTimeField(blank=True, null=True)),
                ("transaction_id", models.UUIDField(blank=True, null=True)),
                ("external_id", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.Boolean<PERSON>ield(default=True)),
                ("day_number", models.PositiveIntegerField()),
                ("date", models.DateField()),
                ("day_title", models.CharField(max_length=255)),
                ("description", models.TextField()),
                ("order", models.PositiveIntegerField()),
                ("inclusions", models.JSONField(blank=True, default=list, null=True)),
                (
                    "meta_information",
                    models.JSONField(blank=True, default=dict, null=True),
                ),
            ],
            options={
                "verbose_name": "Itinerary",
                "verbose_name_plural": "Itineraries",
                "ordering": ["package", "day_number"],
            },
        ),
        migrations.CreateModel(
            name="ItineraryActivity",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("restored_at", models.DateTimeField(blank=True, null=True)),
                ("transaction_id", models.UUIDField(blank=True, null=True)),
                ("external_id", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "start_time",
                    models.TimeField(
                        blank=True, help_text="Start time for activity", null=True
                    ),
                ),
                (
                    "end_time",
                    models.TimeField(
                        blank=True, help_text="End time for activity", null=True
                    ),
                ),
                (
                    "meta_information",
                    models.JSONField(blank=True, default=dict, null=True),
                ),
                (
                    "activity",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="packages.customactivity",
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="CustomPackage",
            fields=[],
            options={
                "proxy": True,
                "indexes": [],
                "constraints": [],
            },
            bases=("packages.package",),
        ),
        migrations.AlterField(
            model_name="package",
            name="type",
            field=models.CharField(
                choices=[
                    ("Fixed", "Fixed"),
                    ("Custom Admin", "Custom Admin"),
                    ("Custom AI", "Custom Ai"),
                ],
                max_length=255,
            ),
        ),
        migrations.AlterField(
            model_name="packageuploader",
            name="file_type",
            field=models.CharField(
                choices=[
                    ("Fixed", "Fixed"),
                    ("Custom Admin", "Custom Admin"),
                    ("Custom AI", "Custom Ai"),
                ],
                max_length=255,
            ),
        ),
        migrations.CreateModel(
            name="ItineraryHotel",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("restored_at", models.DateTimeField(blank=True, null=True)),
                ("transaction_id", models.UUIDField(blank=True, null=True)),
                ("external_id", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "check_in_time",
                    models.TimeField(
                        blank=True, help_text="Check-in time for hotel", null=True
                    ),
                ),
                (
                    "check_out_time",
                    models.TimeField(
                        blank=True, help_text="Check-out time from hotel", null=True
                    ),
                ),
                (
                    "meta_information",
                    models.JSONField(blank=True, default=dict, null=True),
                ),
                (
                    "hotel",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="dynamic_packages.hotel",
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="ItineraryDayItem",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("restored_at", models.DateTimeField(blank=True, null=True)),
                ("transaction_id", models.UUIDField(blank=True, null=True)),
                ("external_id", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("Activity", "Activity"),
                            ("Hotel", "Hotel"),
                            ("Flight", "Flight"),
                            ("Stay", "Stay"),
                            ("Meal", "Meal"),
                            ("Transportation", "Transportation"),
                            ("Other", "Other"),
                        ],
                        max_length=255,
                    ),
                ),
                ("title", models.CharField(blank=True, max_length=255, null=True)),
                ("description", models.TextField()),
                ("order", models.PositiveIntegerField()),
                ("duration", models.CharField(blank=True, max_length=255, null=True)),
                ("inclusions", models.JSONField(blank=True, default=list, null=True)),
                (
                    "meta_information",
                    models.JSONField(blank=True, default=dict, null=True),
                ),
                (
                    "activity",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="packages.itineraryactivity",
                    ),
                ),
                (
                    "hotel",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="packages.itineraryhotel",
                    ),
                ),
                (
                    "itinerary",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="day_items",
                        to="packages.itinerary",
                    ),
                ),
                (
                    "partner",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="itinerary_day_items",
                        to="accounts.partner",
                    ),
                ),
            ],
            options={
                "verbose_name": "Itinerary Day Item",
                "verbose_name_plural": "Itinerary Day Items",
            },
        ),
        migrations.AddField(
            model_name="itinerary",
            name="package",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="itineraries",
                to="packages.package",
            ),
        ),
        migrations.AddField(
            model_name="itinerary",
            name="partner",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="itineraries",
                to="accounts.partner",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="itinerary",
            unique_together={("package", "day_number")},
        ),
    ]
