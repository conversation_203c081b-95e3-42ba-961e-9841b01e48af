# Generated by Django 4.2 on 2025-06-04 07:06

import base.storage_utils
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django_better_admin_arrayfield.models.fields
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("accounts", "0008_rename_usage_preference_partner_preference_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="Activity",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("restored_at", models.DateTimeField(blank=True, null=True)),
                ("transaction_id", models.UUIDField(blank=True, null=True)),
                ("external_id", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                ("title", models.Char<PERSON>ield(max_length=100, unique=True)),
                ("description", models.TextField(blank=True, null=True)),
            ],
            options={
                "verbose_name": "Activity",
                "verbose_name_plural": "Activities",
            },
        ),
        migrations.CreateModel(
            name="Category",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("restored_at", models.DateTimeField(blank=True, null=True)),
                ("transaction_id", models.UUIDField(blank=True, null=True)),
                ("external_id", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                ("title", models.CharField(max_length=100, unique=True)),
                ("description", models.TextField(blank=True, null=True)),
            ],
            options={
                "verbose_name": "Category",
                "verbose_name_plural": "Categories",
            },
        ),
        migrations.CreateModel(
            name="Destination",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("restored_at", models.DateTimeField(blank=True, null=True)),
                ("transaction_id", models.UUIDField(blank=True, null=True)),
                ("external_id", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                ("title", models.CharField(max_length=100, unique=True)),
                ("description", models.TextField(blank=True, null=True)),
                ("is_international", models.BooleanField(default=False)),
            ],
            options={
                "verbose_name": "Destination",
                "verbose_name_plural": "Destinations",
            },
        ),
        migrations.CreateModel(
            name="Package",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("restored_at", models.DateTimeField(blank=True, null=True)),
                ("transaction_id", models.UUIDField(blank=True, null=True)),
                ("external_id", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                ("title", models.CharField(max_length=255)),
                ("package_no", models.CharField(max_length=255)),
                (
                    "type",
                    models.CharField(
                        choices=[("Fixed", "Fixed"), ("Variable", "Variable")],
                        max_length=255,
                    ),
                ),
                ("duration", models.CharField(max_length=255)),
                ("duration_in_nights", models.PositiveIntegerField()),
                ("duration_in_days", models.PositiveIntegerField()),
                ("currency", models.CharField(max_length=255)),
                (
                    "currency_symbol",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "price_per_person",
                    models.DecimalField(decimal_places=2, max_digits=10),
                ),
                (
                    "visa_type",
                    django_better_admin_arrayfield.models.fields.ArrayField(
                        base_field=models.CharField(max_length=255), size=None
                    ),
                ),
                ("best_time_to_visit", models.TextField()),
                ("rating_stars", models.DecimalField(decimal_places=1, max_digits=2)),
                (
                    "currency_conversion_rate",
                    models.DecimalField(decimal_places=2, max_digits=10),
                ),
                ("destination_safety", models.TextField()),
                ("about_this_tour", models.TextField()),
                (
                    "highlights",
                    django_better_admin_arrayfield.models.fields.ArrayField(
                        base_field=models.CharField(), size=None
                    ),
                ),
                (
                    "inclusions",
                    django_better_admin_arrayfield.models.fields.ArrayField(
                        base_field=models.CharField(), size=None
                    ),
                ),
                (
                    "exclusions",
                    django_better_admin_arrayfield.models.fields.ArrayField(
                        base_field=models.CharField(), size=None
                    ),
                ),
                ("itinerary", models.JSONField(blank=True, default=list, null=True)),
                (
                    "hotels",
                    django_better_admin_arrayfield.models.fields.ArrayField(
                        base_field=models.CharField(max_length=255),
                        blank=True,
                        default=list,
                        size=None,
                    ),
                ),
                (
                    "popular_restaurants",
                    django_better_admin_arrayfield.models.fields.ArrayField(
                        base_field=models.CharField(max_length=255),
                        blank=True,
                        default=list,
                        size=None,
                    ),
                ),
                (
                    "popular_activities",
                    django_better_admin_arrayfield.models.fields.ArrayField(
                        base_field=models.CharField(max_length=255),
                        blank=True,
                        default=list,
                        size=None,
                    ),
                ),
                ("cultural_info", models.TextField()),
                ("is_published", models.BooleanField(default=False)),
                ("what_to_shop", models.TextField()),
                ("what_to_pack", models.TextField()),
                (
                    "important_notes",
                    django_better_admin_arrayfield.models.fields.ArrayField(
                        base_field=models.CharField(max_length=255),
                        blank=True,
                        default=list,
                        size=None,
                    ),
                ),
                (
                    "optional_activities",
                    django_better_admin_arrayfield.models.fields.ArrayField(
                        base_field=models.CharField(max_length=255),
                        blank=True,
                        default=list,
                        size=None,
                    ),
                ),
            ],
            options={
                "verbose_name": "Package",
                "verbose_name_plural": "Packages",
            },
        ),
        migrations.CreateModel(
            name="PackageUploader",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("restored_at", models.DateTimeField(blank=True, null=True)),
                ("transaction_id", models.UUIDField(blank=True, null=True)),
                ("external_id", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "file",
                    models.FileField(
                        upload_to=base.storage_utils.package_upload_path,
                        validators=[
                            django.core.validators.FileExtensionValidator(
                                allowed_extensions=["json", "docx", "doc"]
                            )
                        ],
                    ),
                ),
                (
                    "file_type",
                    models.CharField(
                        choices=[("Fixed", "Fixed"), ("Variable", "Variable")],
                        max_length=255,
                    ),
                ),
                (
                    "partner",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="package_uploads",
                        to="accounts.partner",
                    ),
                ),
            ],
            options={
                "verbose_name": "Package Uploader",
                "verbose_name_plural": "Package Uploaders",
            },
        ),
        migrations.CreateModel(
            name="PackageMedia",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("restored_at", models.DateTimeField(blank=True, null=True)),
                ("transaction_id", models.UUIDField(blank=True, null=True)),
                ("external_id", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                ("title", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "file_type",
                    models.CharField(
                        choices=[("image", "Image"), ("video", "Video")], max_length=255
                    ),
                ),
                (
                    "file",
                    models.FileField(
                        upload_to=base.storage_utils.package_media_upload_path,
                        validators=[
                            django.core.validators.FileExtensionValidator(
                                allowed_extensions=[
                                    "jpg",
                                    "jpeg",
                                    "png",
                                    "webp",
                                    "mp4",
                                    "mov",
                                ]
                            )
                        ],
                    ),
                ),
                (
                    "package",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="media",
                        to="packages.package",
                    ),
                ),
            ],
            options={
                "verbose_name": "Package Media",
                "verbose_name_plural": "Package Media",
            },
        ),
        migrations.CreateModel(
            name="PackageCategory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("restored_at", models.DateTimeField(blank=True, null=True)),
                ("transaction_id", models.UUIDField(blank=True, null=True)),
                ("external_id", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "category",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="package_categories",
                        to="packages.category",
                    ),
                ),
                (
                    "package",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="package_categories",
                        to="packages.package",
                    ),
                ),
            ],
            options={
                "verbose_name": "Package Category",
                "verbose_name_plural": "Package Categories",
                "unique_together": {("package", "category")},
            },
        ),
        migrations.CreateModel(
            name="PackageActivity",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("restored_at", models.DateTimeField(blank=True, null=True)),
                ("transaction_id", models.UUIDField(blank=True, null=True)),
                ("external_id", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "activity",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="package_activities",
                        to="packages.activity",
                    ),
                ),
                (
                    "package",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="package_activities",
                        to="packages.package",
                    ),
                ),
            ],
            options={
                "verbose_name": "Package Activity",
                "verbose_name_plural": "Package Activities",
                "unique_together": {("package", "activity")},
            },
        ),
        migrations.AddField(
            model_name="package",
            name="activities",
            field=models.ManyToManyField(
                related_name="packages",
                through="packages.PackageActivity",
                to="packages.activity",
            ),
        ),
        migrations.AddField(
            model_name="package",
            name="categories",
            field=models.ManyToManyField(
                related_name="packages",
                through="packages.PackageCategory",
                to="packages.category",
            ),
        ),
        migrations.AddField(
            model_name="package",
            name="destination",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="packages",
                to="packages.destination",
            ),
        ),
        migrations.AddField(
            model_name="package",
            name="package_uploaded",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="packages",
                to="packages.packageuploader",
            ),
        ),
        migrations.AddField(
            model_name="package",
            name="partner",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="packages",
                to="accounts.partner",
            ),
        ),
        migrations.CreateModel(
            name="DestinationMedia",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("restored_at", models.DateTimeField(blank=True, null=True)),
                ("transaction_id", models.UUIDField(blank=True, null=True)),
                ("external_id", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "media",
                    models.FileField(
                        blank=True,
                        null=True,
                        upload_to=base.storage_utils.destination_upload_path,
                    ),
                ),
                (
                    "destination",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="media",
                        to="packages.destination",
                    ),
                ),
            ],
            options={
                "verbose_name": "Destination Media",
                "verbose_name_plural": "Destination Media",
            },
        ),
        migrations.CreateModel(
            name="CategoryMedia",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("restored_at", models.DateTimeField(blank=True, null=True)),
                ("transaction_id", models.UUIDField(blank=True, null=True)),
                ("external_id", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "media",
                    models.FileField(
                        blank=True,
                        null=True,
                        upload_to=base.storage_utils.category_upload_path,
                    ),
                ),
                (
                    "category",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="media",
                        to="packages.category",
                    ),
                ),
            ],
            options={
                "verbose_name": "Category Media",
                "verbose_name_plural": "Category Media",
            },
        ),
        migrations.CreateModel(
            name="ActivityMedia",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("restored_at", models.DateTimeField(blank=True, null=True)),
                ("transaction_id", models.UUIDField(blank=True, null=True)),
                ("external_id", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "media",
                    models.FileField(
                        blank=True,
                        null=True,
                        upload_to=base.storage_utils.activity_upload_path,
                    ),
                ),
                (
                    "activity",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="media",
                        to="packages.activity",
                    ),
                ),
            ],
            options={
                "verbose_name": "Activity Media",
                "verbose_name_plural": "Activity Media",
            },
        ),
        migrations.CreateModel(
            name="DestinationCategory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("restored_at", models.DateTimeField(blank=True, null=True)),
                ("transaction_id", models.UUIDField(blank=True, null=True)),
                ("external_id", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "category",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="destinations",
                        to="packages.category",
                    ),
                ),
                (
                    "destination",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="categories",
                        to="packages.destination",
                    ),
                ),
            ],
            options={
                "verbose_name": "Destination Category",
                "verbose_name_plural": "Destination Categories",
                "unique_together": {("destination", "category")},
            },
        ),
        migrations.CreateModel(
            name="DestinationActivity",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("restored_at", models.DateTimeField(blank=True, null=True)),
                ("transaction_id", models.UUIDField(blank=True, null=True)),
                ("external_id", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "activity",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="destinations",
                        to="packages.activity",
                    ),
                ),
                (
                    "destination",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="activities",
                        to="packages.destination",
                    ),
                ),
            ],
            options={
                "verbose_name": "Destination Activity",
                "verbose_name_plural": "Destination Activities",
                "unique_together": {("destination", "activity")},
            },
        ),
    ]
