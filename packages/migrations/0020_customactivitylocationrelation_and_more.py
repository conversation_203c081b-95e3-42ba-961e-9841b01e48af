# Generated by Django 4.2 on 2025-07-10 15:59

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("packages", "0019_customactivitycategory_customactivitylocation_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="CustomActivityLocationRelation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("restored_at", models.DateTimeField(blank=True, null=True)),
                ("transaction_id", models.UUIDField(blank=True, null=True)),
                ("external_id", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "activity",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="activity_locations",
                        to="packages.customactivity",
                    ),
                ),
                (
                    "location",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="location_activities",
                        to="packages.customactivitylocation",
                    ),
                ),
            ],
            options={
                "verbose_name": "Custom Activity Location Relation",
                "verbose_name_plural": "Custom Activity Location Relations",
                "unique_together": {("activity", "location")},
            },
        ),
        migrations.CreateModel(
            name="CustomActivityCategoryRelation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("restored_at", models.DateTimeField(blank=True, null=True)),
                ("transaction_id", models.UUIDField(blank=True, null=True)),
                ("external_id", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "activity",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="activity_categories",
                        to="packages.customactivity",
                    ),
                ),
                (
                    "category",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="category_activities",
                        to="packages.customactivitycategory",
                    ),
                ),
            ],
            options={
                "verbose_name": "Custom Activity Category Relation",
                "verbose_name_plural": "Custom Activity Category Relations",
                "unique_together": {("activity", "category")},
            },
        ),
        migrations.AddField(
            model_name="customactivity",
            name="categories",
            field=models.ManyToManyField(
                related_name="activities",
                through="packages.CustomActivityCategoryRelation",
                to="packages.customactivitycategory",
            ),
        ),
        migrations.AddField(
            model_name="customactivity",
            name="locations",
            field=models.ManyToManyField(
                related_name="activities",
                through="packages.CustomActivityLocationRelation",
                to="packages.customactivitylocation",
            ),
        ),
    ]
