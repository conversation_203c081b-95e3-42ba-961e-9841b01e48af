# Generated by Django 4.2 on 2025-08-18 11:32

from django.db import migrations, models
import django.db.models.deletion
import django_better_admin_arrayfield.models.fields
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('packages', '0034_tripjackcities_tripjackhoteldestinationmapping_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='CustomPackageItinerary',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('restored_at', models.DateTimeField(blank=True, null=True)),
                ('transaction_id', models.UUIDField(blank=True, null=True)),
                ('external_id', models.UUIDField(default=uuid.uuid4, editable=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('day_number', models.PositiveIntegerField()),
                ('type', models.CharField(choices=[('Activity', 'Activity'), ('Hotel', 'Hotel')], max_length=255)),
                ('activity', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='packages.customactivity')),
                ('hotel', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='packages.tripjackhotels')),
                ('package', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='itineraries', to='packages.custompackage')),
            ],
            options={
                'verbose_name': 'Custom Package Itinerary',
                'verbose_name_plural': 'Custom Package Itineraries',
                'ordering': ['package', 'day_number'],
            },
        ),
        migrations.RemoveField(
            model_name='itineraryactivity',
            name='activity',
        ),
        migrations.RemoveField(
            model_name='itinerarydayitem',
            name='activity',
        ),
        migrations.RemoveField(
            model_name='itinerarydayitem',
            name='hotel',
        ),
        migrations.RemoveField(
            model_name='itinerarydayitem',
            name='itinerary',
        ),
        migrations.RemoveField(
            model_name='itinerarydayitem',
            name='partner',
        ),
        migrations.RemoveField(
            model_name='itineraryhotel',
            name='hotel',
        ),
        migrations.AddField(
            model_name='package',
            name='package_personas',
            field=django_better_admin_arrayfield.models.fields.ArrayField(base_field=models.CharField(choices=[('Couple', 'Couple'), ('Solo', 'Solo'), ('Family', 'Family'), ('Friends', 'Friends')], max_length=255), blank=True, default=list, help_text='Auto-populated personas from custom activities in itinerary', size=None),
        ),
        migrations.AlterField(
            model_name='package',
            name='type',
            field=models.CharField(choices=[('Fixed', 'Fixed'), ('Customized', 'Custom Admin'), ('Customized AI', 'Custom Ai')], max_length=255),
        ),
        migrations.AlterField(
            model_name='packageuploader',
            name='file_type',
            field=models.CharField(choices=[('Fixed', 'Fixed'), ('Customized', 'Custom Admin'), ('Customized AI', 'Custom Ai')], max_length=255),
        ),
        migrations.DeleteModel(
            name='Itinerary',
        ),
        migrations.DeleteModel(
            name='ItineraryActivity',
        ),
        migrations.DeleteModel(
            name='ItineraryDayItem',
        ),
        migrations.DeleteModel(
            name='ItineraryHotel',
        ),
        migrations.AddIndex(
            model_name='custompackageitinerary',
            index=models.Index(fields=['package', 'day_number'], name='cpi_package_day_idx'),
        ),
        migrations.AddConstraint(
            model_name='custompackageitinerary',
            constraint=models.CheckConstraint(check=models.Q(models.Q(('activity__isnull', False), ('type', 'Activity')), ('type__in', ['Hotel']), _connector='OR'), name='activity_not_null_if_type_is_activity'),
        ),
        migrations.AddConstraint(
            model_name='custompackageitinerary',
            constraint=models.CheckConstraint(check=models.Q(models.Q(('hotel__isnull', False), ('type', 'Hotel')), ('type__in', ['Activity']), _connector='OR'), name='hotel_not_null_if_type_is_hotel'),
        ),
    ]
