# Generated by Django 4.2 on 2025-06-05 11:49

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("accounts", "0008_rename_usage_preference_partner_preference_and_more"),
        ("packages", "0002_destination_best_time_to_visit"),
    ]

    operations = [
        migrations.AddField(
            model_name="activity",
            name="is_featured",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="activity",
            name="partner",
            field=models.ForeignKey(
                default=1,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="activities",
                to="accounts.partner",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="category",
            name="partner",
            field=models.ForeignKey(
                default=1,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="categories",
                to="accounts.partner",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="destination",
            name="is_trending",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="destination",
            name="partner",
            field=models.ForeignKey(
                default=1,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="destinations",
                to="accounts.partner",
            ),
            preserve_default=False,
        ),
    ]
