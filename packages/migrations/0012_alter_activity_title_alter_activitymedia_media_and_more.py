# Generated by Django 4.2 on 2025-06-12 13:03

import base.storage_utils
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("accounts", "0010_alter_user_user_type"),
        (
            "packages",
            "0011_alter_activitymedia_media_alter_categorymedia_media_and_more",
        ),
    ]

    operations = [
        migrations.AlterField(
            model_name="activity",
            name="title",
            field=models.CharField(max_length=100),
        ),
        migrations.AlterField(
            model_name="activitymedia",
            name="media",
            field=models.FileField(
                help_text="Media file is required",
                upload_to=base.storage_utils.activity_upload_path,
            ),
        ),
        migrations.AlterField(
            model_name="category",
            name="title",
            field=models.Char<PERSON>ield(max_length=100),
        ),
        migrations.AlterField(
            model_name="categorymedia",
            name="media",
            field=models.FileField(
                help_text="Media file is required",
                upload_to=base.storage_utils.category_upload_path,
            ),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name="destination",
            name="title",
            field=models.Char<PERSON>ield(max_length=100),
        ),
        migrations.AlterField(
            model_name="destinationmedia",
            name="media",
            field=models.FileField(
                help_text="Media file is required",
                upload_to=base.storage_utils.destination_upload_path,
            ),
        ),
        migrations.AlterUniqueTogether(
            name="activity",
            unique_together={("partner", "title")},
        ),
        migrations.AlterUniqueTogether(
            name="category",
            unique_together={("partner", "title")},
        ),
        migrations.AlterUniqueTogether(
            name="destination",
            unique_together={("partner", "title")},
        ),
    ]
