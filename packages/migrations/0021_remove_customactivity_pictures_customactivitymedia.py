# Generated by Django 4.2 on 2025-07-14 05:10

import base.storage_utils
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("packages", "0020_customactivitylocationrelation_and_more"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="customactivity",
            name="pictures",
        ),
        migrations.CreateModel(
            name="CustomActivityMedia",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("restored_at", models.DateTimeField(blank=True, null=True)),
                ("transaction_id", models.UUIDField(blank=True, null=True)),
                ("external_id", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "media",
                    models.FileField(
                        help_text="Media file is required",
                        upload_to=base.storage_utils.custom_activity_media_upload_path,
                    ),
                ),
                ("title", models.CharField(blank=True, max_length=255, null=True)),
                ("description", models.TextField(blank=True, null=True)),
                (
                    "meta_information",
                    models.JSONField(blank=True, default=dict, null=True),
                ),
                (
                    "custom_activity",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="media",
                        to="packages.customactivity",
                    ),
                ),
            ],
            options={
                "verbose_name": "Custom Activity Media",
                "verbose_name_plural": "Custom Activity Media",
            },
        ),
    ]
