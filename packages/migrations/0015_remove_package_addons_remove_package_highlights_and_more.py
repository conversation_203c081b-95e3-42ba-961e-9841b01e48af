# Generated by Django 4.2 on 2025-06-17 10:43

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("packages", "0014_merge_20250616_0943"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="package",
            name="addons",
        ),
        migrations.RemoveField(
            model_name="package",
            name="highlights",
        ),
        migrations.RemoveField(
            model_name="package",
            name="inclusions",
        ),
        migrations.CreateModel(
            name="PackageInclusion",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("restored_at", models.DateTimeField(blank=True, null=True)),
                ("transaction_id", models.UUIDField(blank=True, null=True)),
                ("external_id", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                ("value", models.TextField(help_text="Inclusion description")),
                (
                    "icon_class",
                    models.CharField(
                        help_text="Icon class for this inclusion", max_length=50
                    ),
                ),
                (
                    "package",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="inclusions",
                        to="packages.package",
                    ),
                ),
            ],
            options={
                "verbose_name": "Package Inclusion",
                "verbose_name_plural": "Package Inclusions",
                "ordering": ["created_at"],
            },
        ),
        migrations.CreateModel(
            name="PackageHighlight",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("restored_at", models.DateTimeField(blank=True, null=True)),
                ("transaction_id", models.UUIDField(blank=True, null=True)),
                ("external_id", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                ("value", models.TextField(help_text="Highlight description")),
                (
                    "icon_class",
                    models.CharField(
                        help_text="Icon class for this highlight", max_length=50
                    ),
                ),
                (
                    "package",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="highlights",
                        to="packages.package",
                    ),
                ),
            ],
            options={
                "verbose_name": "Package Highlight",
                "verbose_name_plural": "Package Highlights",
                "ordering": ["created_at"],
            },
        ),
        migrations.CreateModel(
            name="PackageAddon",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("restored_at", models.DateTimeField(blank=True, null=True)),
                ("transaction_id", models.UUIDField(blank=True, null=True)),
                ("external_id", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                ("value", models.TextField(help_text="Addon description")),
                (
                    "icon_class",
                    models.CharField(
                        help_text="Icon class for this addon", max_length=50
                    ),
                ),
                (
                    "package",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="addons",
                        to="packages.package",
                    ),
                ),
            ],
            options={
                "verbose_name": "Package Addon",
                "verbose_name_plural": "Package Addons",
                "ordering": ["created_at"],
            },
        ),
    ]
