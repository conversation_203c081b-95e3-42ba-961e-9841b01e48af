# Generated by Django 4.2 on 2025-06-09 07:19

from django.db import migrations, models
import django_better_admin_arrayfield.models.fields


class Migration(migrations.Migration):

    dependencies = [
        ("packages", "0004_remove_package_currency_symbol_and_more"),
    ]

    operations = [
        migrations.RenameField(
            model_name="package",
            old_name="optional",
            new_name="addons",
        ),
        migrations.RenameField(
            model_name="package",
            old_name="rating_stars",
            new_name="rating",
        ),
        migrations.AlterField(
            model_name="package",
            name="best_time_to_visit",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="package",
            name="currency_conversion_rate",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="package",
            name="hotels",
            field=django_better_admin_arrayfield.models.fields.ArrayField(
                base_field=models.Char<PERSON>ield(max_length=255), default=list, size=None
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="package",
            name="owner",
            field=models.CharField(default="ZUUMM", max_length=255),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="package",
            name="popular_activities",
            field=django_better_admin_arrayfield.models.fields.ArrayField(
                base_field=models.CharField(max_length=255), default=list, size=None
            ),
        ),
    ]
