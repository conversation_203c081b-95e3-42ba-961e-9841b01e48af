from django import forms
from django.core.exceptions import ValidationError
from django.db import transaction
from django.utils.safestring import mark_safe
import json
import re
import logging

logger = logging.getLogger(__name__)

from base.utils import get_file_and_extension
from base.static import Constants
from packages.models import (
    Package, PackageUploader, PackageCategory, PackageActivity, 
    Destination, Category, Activity, CustomActivity, DestinationFaq,
    ActivityMedia, PackageHighlight, PackageInclusion, PackageAddon,
    CustomPackage, TripjackHotels, CustomPackageItinerary
)
from packages.choices import PackageTypeChoices, PersonaChoices, MonthChoices, ItineraryDayItemType
from packages.utils.package_format_helpers import (
    get_sample_package_format, 
    get_sample_doc_format,
)
from packages.services.package_creation_service import PackageCreationService

from django_better_admin_arrayfield.forms.fields import DynamicArrayField
from django_better_admin_arrayfield.forms.widgets import DynamicArrayWidget
from ckeditor.widgets import CKEditorWidget
from django.contrib.admin.widgets import ForeignKeyRawIdWidget
from django.utils.html import format_html
from .widgets import ImageManagementWidget, InfiniteScrollSelectWidget
from django.core.files.storage import default_storage
from base.storage_utils import tripjack_hotel_media_upload_path

logger = logging.getLogger(__name__)


class FixedTypeWidget(forms.TextInput):
    """Custom widget that always shows 'Fixed' for the type field"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.attrs.update({
            'readonly': True,
            'style': 'background-color: #f5f5f5;',
            'value': 'Fixed'
        })
    
    def render(self, name, value, attrs=None, renderer=None):
        # Always render with 'Fixed' value regardless of what's passed
        from packages.choices import PackageTypeChoices
        return super().render(name, PackageTypeChoices.FIXED.value, attrs, renderer)
    
    def value_from_datadict(self, data, files, name):
        # Always return 'Fixed' regardless of form data
        from packages.choices import PackageTypeChoices
        return PackageTypeChoices.FIXED.value


class CustomAdminTypeWidget(forms.TextInput):
    """Custom widget that always shows 'Custom Admin' as plain text"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.attrs.update({
            'readonly': True,
            'style': 'background-color: #f5f5f5; border: 1px solid #ccc; padding: 6px 12px;',
            'value': PackageTypeChoices.CUSTOM_ADMIN.value
        })
    
    def render(self, name, value, attrs=None, renderer=None):
        # Always render with 'Custom Admin' value regardless of what's passed
        return super().render(name, PackageTypeChoices.CUSTOM_ADMIN.value, attrs, renderer)
    
    def value_from_datadict(self, data, files, name):
        # Always return 'Custom Admin' regardless of form data
        return PackageTypeChoices.CUSTOM_ADMIN.value


class ClearableDynamicArrayField(DynamicArrayField):
    """
    Custom DynamicArrayField that properly handles empty submissions.
    When no data is submitted (user cleared all items), returns empty array
    instead of falling back to initial value.
    """
    
    def clean(self, value):
        """Override clean to handle empty submissions properly"""
        # If no value is submitted (None or empty list), return empty list
        if value is None or (isinstance(value, list) and len(value) == 0):
            return []
        
        # If we get [''] (single empty string), also return empty list
        if isinstance(value, list) and len(value) == 1 and value[0] == '':
            return []
        
        # Otherwise use the parent clean method
        cleaned = super().clean(value)
        
        # Filter out any empty strings from the cleaned data
        if isinstance(cleaned, list):
            cleaned = [item.strip() for item in cleaned if item and item.strip()]
        
        return cleaned


class DynamicModelChoiceField(forms.ModelChoiceField):
    """
    A ModelChoiceField that dynamically populates its queryset based on the submitted value.
    This is useful for infinite scroll widgets where the full queryset is too large to load.
    """
    
    def __init__(self, model_class=None, *args, **kwargs):
        self.model_class = model_class
        super().__init__(*args, **kwargs)
    
    def to_python(self, value):
        """Convert the submitted value to a model instance"""
        if value in self.empty_values:
            return None
        
        try:
            value = int(value)
            # Try to get the object from the database
            if self.model_class:
                obj = self.model_class.objects.filter(pk=value, is_active=True).first()
                if obj:
                    # Dynamically update the queryset to include this object
                    self.queryset = self.model_class.objects.filter(pk=value)
                    return obj
        except (ValueError, TypeError, self.model_class.DoesNotExist):
            pass
            
        raise forms.ValidationError(
            self.error_messages['invalid_choice'],
            code='invalid_choice',
            params={'value': value},
        )
    
    def validate(self, value):
        """Validate that the selected value exists and is active"""
        if value is None and not self.required:
            return
        
        if value is not None:
            # Additional validation - ensure the object is still active
            if hasattr(value, 'is_active') and not value.is_active:
                raise forms.ValidationError(
                    f"The selected {self.model_class._meta.verbose_name.lower()} is no longer available.",
                    code='invalid_choice'
                )
        
        return super().validate(value)


class PackageAdminForm(forms.ModelForm):
    """Comprehensive Package form with AI-oriented validation"""

    # Virtual array fields for the admin interface
    highlights = ClearableDynamicArrayField(
        forms.CharField(max_length=500),
        widget=DynamicArrayWidget(),
        required=True,
        help_text='Package highlights (one per line)'
    )
    
    inclusions = ClearableDynamicArrayField(
        forms.CharField(max_length=500),
        widget=DynamicArrayWidget(),
        required=True,
        help_text='What is included in the package (one per line)'
    )
    
    addons = ClearableDynamicArrayField(
        forms.CharField(max_length=500),
        widget=DynamicArrayWidget(),
        required=False,
        help_text='Add On services like Flight, Visa, Insurance, Upgrades (one per line)'
    )

    visa_type = ClearableDynamicArrayField(
        forms.CharField(max_length=255),
        widget=DynamicArrayWidget(),
        required=False,
        help_text='Types of visa available (one per line)'
    )

    class Meta:
        model = Package
        exclude = ['currency', 'price', 'package_uploaded', 'partner', 'duration_in_nights', 'duration_in_days', 'best_time_to_visit_months']
        widgets = {
            'itinerary': CKEditorWidget(config_name='default'),
            'what_to_pack': CKEditorWidget(config_name='default'),
            'type': forms.TextInput(attrs={'readonly': 'readonly'}),  # Make type readonly
        }

    def __init__(self, *args, **kwargs):
        # Extract request from kwargs if passed by admin
        self.request = kwargs.pop('request', None)
        super().__init__(*args, **kwargs)
        
        # Set type field to FIXED and make it readonly
        from packages.choices import PackageTypeChoices
        if 'type' in self.fields:  # Safety check to ensure field exists
            # Replace the field with a simple CharField that shows Fixed using custom widget
            self.fields['type'] = forms.CharField(
                initial=PackageTypeChoices.FIXED.value,
                widget=FixedTypeWidget(),
                help_text='Package type is set to Fixed for structured packages',
                required=False  # Make it not required since it's readonly
            )
            
            # For new instances, ensure the instance type is set
            if not self.instance.pk:
                self.instance.type = PackageTypeChoices.FIXED.value
        
        # Initialize virtual fields with existing data
        if self.instance and self.instance.pk:
            self.fields['highlights'].initial = [h.value for h in self.instance.highlights.all()]
            self.fields['inclusions'].initial = [i.value for i in self.instance.inclusions.all()]
            self.fields['addons'].initial = [a.value for a in self.instance.addons.all()]
        
        # Add comprehensive help texts
        self._add_help_texts()
    
    def _add_help_texts(self):
        """Add comprehensive help texts for all fields"""
        help_texts = {
            'title': 'Enter a descriptive title for the package',
            'package_no': 'Unique package number/code',
            'destination': 'Select the destination for this package',
            'type': 'Package type is automatically set to Fixed',
            'explore_order': 'Order for displaying packages in API results (lower numbers appear first)',
            'duration': 'Format: "4N & 5D" (4 Nights & 5 Days)',
            'price_per_person': 'Enter price with currency symbol or code (e.g., ₹39,999, INR 39,999, $500, €450) - AI will extract currency and numeric value',
            'currency_conversion_rate': 'Conversion rate to INR (if applicable)',
            'visa_type': 'Types of visa available (one per line)',
            'best_time_to_visit': 'Best months/seasons to visit (AI will extract months automatically)',
            'destination_safety': 'Safety information for travelers',
            'rating': 'Package rating (0.0 to 5.0)',
            'rating_description': 'Brief description about the package rating (optional - AI will auto-generate if empty)',
            'about_this_tour': 'Detailed description of the tour',
            'highlights': 'Key highlights of the package (one per line)',
            'inclusions': 'What is included in the package (one per line)',
            'exclusions': 'What is not included (one per line)',
            'itinerary': 'Day-wise itinerary in HTML format.',
            'hotels': 'Recommended hotels (one per line)',
            'popular_restaurants': 'Popular restaurants (one per line)',
            'popular_activities': 'Popular activities (one per line)',
            'addons': 'Add On services like Flight, Visa, Insurance, Upgrades (one per line)',
            'cultural_info': 'Cultural information and tips',
            'what_to_shop': 'Shopping recommendations',
            'what_to_pack': 'Packing list in HTML format with categories and items (AI will auto-generate if empty)',
            'important_notes': 'Important notes for travelers (one per line)',
            'owner': 'Package owner/operator name',
        }
        
        for field_name, help_text in help_texts.items():
            if field_name in self.fields:
                self.fields[field_name].help_text = help_text

    @transaction.atomic
    def clean(self):
        """Validate and process package data with AI enhancement"""
        cleaned_data = super().clean()
        
        # Force type to be FIXED for PackageAdmin
        cleaned_data['type'] = PackageTypeChoices.FIXED.value
        
        # Convert virtual array fields to regular arrays for PackageCreationService
        if 'highlights' in cleaned_data and cleaned_data['highlights']:
            # Filter out empty strings and None values
            cleaned_data['highlights'] = [h.strip() for h in cleaned_data['highlights'] if h and h.strip()]
        else:
            cleaned_data['highlights'] = []
            
        if 'inclusions' in cleaned_data and cleaned_data['inclusions']:
            # Filter out empty strings and None values
            cleaned_data['inclusions'] = [i.strip() for i in cleaned_data['inclusions'] if i and i.strip()]
        else:
            cleaned_data['inclusions'] = []
            
        if 'addons' in cleaned_data and cleaned_data['addons']:
            # Filter out empty strings and None values
            cleaned_data['addons'] = [a.strip() for a in cleaned_data['addons'] if a and a.strip()]
        else:
            cleaned_data['addons'] = []
        
        # EXPLICIT VALIDATION for required array fields
        print(f"[DEBUG] Validating highlights: {cleaned_data.get('highlights')} (length: {len(cleaned_data.get('highlights', []))})")
        if not cleaned_data.get('highlights') or len(cleaned_data['highlights']) == 0:
            print(f"[DEBUG] Adding error for empty highlights")
            self.add_error('highlights', 'At least one highlight is required.')
        
        print(f"[DEBUG] Validating inclusions: {cleaned_data.get('inclusions')} (length: {len(cleaned_data.get('inclusions', []))})")
        if not cleaned_data.get('inclusions') or len(cleaned_data['inclusions']) == 0:
            print(f"[DEBUG] Adding error for empty inclusions")
            self.add_error('inclusions', 'At least one inclusion is required.')
        
        print(f"[DEBUG] Form errors after validation: {self.errors}")
        print(f"[DEBUG] Form has errors: {bool(self.errors)}")
        
        # Store processed package for later use in save()
        self._processed_package = None
        self._is_update = False
        
        # Only validate absolutely critical fields that AI cannot guess
        critical_fields = ['title', 'package_no', 'destination']
        for field in critical_fields:
            if not cleaned_data.get(field):
                self.add_error(field, f"{field.replace('_', ' ').title()} is required")
        
        # If basic validation failed, don't proceed with AI processing
        if self.errors:
            return cleaned_data
        
        print("DEBUG: Starting PackageAdminForm clean() with AI processing")
        
        # Create a savepoint to ensure rollback if anything fails
        savepoint_id = transaction.savepoint()
        
        try:
            # Get partner information
            partner = None
            if hasattr(self, 'request') and self.request:
                from packages.admin import get_user_effective_partner
                partner = get_user_effective_partner(self.request)
                print(f"DEBUG: Partner from request: {partner}")
            
            if not partner:
                raise forms.ValidationError("Partner information is required but not available")
            
            # Determine if this is an update or new package
            is_update = bool(self.instance and self.instance.pk)
            self._is_update = is_update
            
            print(f"DEBUG: Processing {'UPDATE' if is_update else 'NEW'} package")
            print(f"DEBUG: Highlights: {len(cleaned_data.get('highlights', []))} items")
            print(f"DEBUG: Inclusions: {len(cleaned_data.get('inclusions', []))} items")
            print(f"DEBUG: Addons: {len(cleaned_data.get('addons', []))} items")
            
            # Initialize PackageCreationService
            creation_service = PackageCreationService(
                partner=partner,
                source_type="manual",
                updating_package=is_update,
                skip_m2m_processing=True  # Skip M2M processing for admin - let inlines handle it
            )
            print(f"DEBUG: PackageCreationService initialized for {'update' if is_update else 'creation'}")
            
            # Convert form data to raw JSON
            print(f"DEBUG: Converting form data to raw JSON, cleaned_data keys: {list(cleaned_data.keys())}")
            creation_service.convert_form_data_to_raw_data(cleaned_data)
            print(f"DEBUG: Form data conversion completed")
            
            if is_update:
                # For updates, process and validate but don't create new package
                print(f"DEBUG: Starting package processing for update")
                creation_service.process_package()
                print(f"DEBUG: Package processing for update completed")
                
                # Store the service for use in save() method
                self._creation_service = creation_service
                
            else:
                # For new packages, create the package during validation
                print(f"DEBUG: Starting package creation")
                created_package = creation_service.process_package()
                print(f"DEBUG: Package creation completed, created_package ID: {created_package.id if created_package else 'None'}")
                
                if not created_package:
                    raise forms.ValidationError("Package processing failed - no package was created.")
                
                # Store processed data for save() method
                self._processed_package = created_package
                print(f"DEBUG: Stored processed package {created_package.id} for save() method")
            
            # Commit the savepoint if everything succeeded
            transaction.savepoint_commit(savepoint_id)
            print("DEBUG: PackageAdminForm clean() completed successfully, savepoint committed")
            
        except forms.ValidationError as ve:
            # Rollback the savepoint on validation errors
            transaction.savepoint_rollback(savepoint_id)
            print(f"DEBUG: ValidationError in clean(), rolling back: {str(ve)}")
            
            # Just re-raise the original error without cleaning
            raise ve
            
        except Exception as e:
            # Rollback the savepoint on unexpected errors
            transaction.savepoint_rollback(savepoint_id)
            print(f"DEBUG: Unexpected error in clean(), rolling back: {str(e)}")
            raise forms.ValidationError(f"Package processing failed: {str(e)}. Please check your data and try again.")
        
        return cleaned_data

    @transaction.atomic
    def save(self, commit=True):
        print(f"[DEBUG] PackageAdminForm.save() called with commit={commit}")
        logger.info(f"PackageAdminForm.save() called with commit={commit}")
        
        # Get partner using the correct method
        partner = None
        if hasattr(self, 'request') and self.request:
            from packages.admin import get_user_effective_partner
            partner = get_user_effective_partner(self.request)
            partner_name = getattr(partner, 'entity_name', 'Unknown') if partner else 'None'
            print(f"[DEBUG] Partner from request: {partner_name}")
        
        # Check if we have a processed package from clean()
        if hasattr(self, '_processed_package') and self._processed_package:
            print(f"[DEBUG] Using processed package from clean(): ID {self._processed_package.id}")
            instance = self._processed_package
            
            # Update the instance with any additional form data that might have changed
            # (though this shouldn't happen in normal flow since clean() processes everything)
            instance.is_published = self.cleaned_data.get('is_published', instance.is_published)
            instance.is_active = self.cleaned_data.get('is_active', instance.is_active)
            instance.explore_order = self.cleaned_data.get('explore_order', instance.explore_order)
            
            if commit:
                instance.save()
                print(f"[DEBUG] Processed package saved to database")
            
            return instance
        
        # Fallback: if no processed package (shouldn't happen), create manually
        print(f"[DEBUG] No processed package found, creating manually (fallback)")
        
        # Get the package instance using super().save(commit=False) 
        instance = super().save(commit=False)
        print(f"[DEBUG] Form save(commit=False) completed, instance.pk: {instance.pk}")
        
        if instance.pk:
            # UPDATE existing package
            print(f"[DEBUG] Applying update to existing package ID: {instance.pk}")
            
            # Always commit the instance first to make sure it's saved
            if commit:
                instance.save()
                print(f"[DEBUG] Package instance saved to database")
            
            # Apply AI processing to update related objects
            print(f"[DEBUG] Starting AI processing for package update")
            try:
                # Ensure we have a partner for package creation service
                if not partner:
                    print(f"[ERROR] No partner available for package update")
                    raise ValidationError("Cannot update package: No partner information available")
                
                # Create service for updating package
                service = PackageCreationService(
                    partner=partner,
                    source_type="manual",
                    updating_package=True,
                    skip_m2m_processing=True  # Skip M2M processing for admin - let inlines handle it
                )
                
                print(f"[DEBUG] Converting form data to raw data for AI processing")
                # Convert cleaned form data to the expected format
                service.convert_form_data_to_raw_data(self.cleaned_data)
                
                print(f"[DEBUG] Starting package processing for update")
                # Process the package data (validation + AI enhancement)
                validated_data = service.process_package()
                
                print(f"[DEBUG] Package processing for update completed")
                # Apply the validated data to update the package
                service.update_package(instance)
                
                print(f"[DEBUG] Package update completed successfully")
                
                # Refresh virtual fields after successful update
                self._refresh_virtual_fields_after_update()
                
            except Exception as e:
                print(f"[ERROR] Package update failed: {str(e)}")
                logger.error(f"Package update failed: {str(e)}")
                raise ValidationError(f"Failed to update package: {str(e)}")
        else:
            # CREATE new package - this is the problematic case
            print(f"[ERROR] Attempting to create new package without processed data")
            raise ValidationError("Cannot create package: Missing processed data from validation. Please ensure all required fields are filled correctly.")
        
        print(f"[DEBUG] PackageAdminForm.save() completed successfully")
        return instance

    def _refresh_virtual_fields_after_update(self):
        """Force refresh virtual fields with current database state to clear frontend cache"""
        print(f"[DEBUG] _refresh_virtual_fields_after_update called")
        
        # Force reload current data from database
        fresh_highlights = list(self.instance.highlights.values_list('value', flat=True))
        fresh_inclusions = list(self.instance.inclusions.values_list('value', flat=True)) 
        fresh_addons = list(self.instance.addons.values_list('value', flat=True))
        
        print(f"[DEBUG] Fresh database state - highlights: {len(fresh_highlights)}, inclusions: {len(fresh_inclusions)}, addons: {len(fresh_addons)}")
        
        # Update form fields to reflect current database state
        self.fields['highlights'].initial = fresh_highlights
        self.fields['inclusions'].initial = fresh_inclusions  
        self.fields['addons'].initial = fresh_addons
        
        # DON'T update cleaned_data - this was causing the caching issue
        # The cleaned_data should reflect what the user actually submitted, not the database state
        # self.cleaned_data['highlights'] = fresh_highlights
        # self.cleaned_data['inclusions'] = fresh_inclusions
        # self.cleaned_data['addons'] = fresh_addons
        
        print(f"[DEBUG] Virtual fields refreshed with fresh database data (cleaned_data preserved)")

    def is_valid(self):
        """Override is_valid to add comprehensive logging"""
        print(f"[DEBUG] PackageAdminForm.is_valid() called")
        result = super().is_valid()
        print(f"[DEBUG] PackageAdminForm.is_valid() result: {result}")
        if not result:
            print(f"[DEBUG] PackageAdminForm errors: {self.errors}")
            print(f"[DEBUG] PackageAdminForm non_field_errors: {self.non_field_errors()}")
        return result
    
    def full_clean(self):
        """Override full_clean to add comprehensive logging"""
        print(f"[DEBUG] PackageAdminForm.full_clean() called")
        try:
            result = super().full_clean()
            print(f"[DEBUG] PackageAdminForm.full_clean() completed successfully")
            return result
        except Exception as e:
            print(f"[DEBUG] PackageAdminForm.full_clean() exception: {type(e).__name__}: {e}")
            raise

    def clean_type(self):
        """Ensure type is always FIXED"""
        from packages.choices import PackageTypeChoices
        return PackageTypeChoices.FIXED.value


class PackageUploaderForm(forms.ModelForm):
    """Form for uploading package files (JSON/DOC/DOCX) with unified processing service"""
    
    class Meta:
        model = PackageUploader
        fields = ('file',)  # Remove file_type from fields - it will be set internally
    
    def __init__(self, *args, **kwargs):
        self.file = None
        # Extract request from kwargs if passed by admin
        self.request = kwargs.pop('request', None)
        super().__init__(*args, **kwargs)
        
        # Only add help text if field is available and not readonly
        if 'file' in self.fields and not self.fields['file'].widget.attrs.get('readonly'):
            # Add help text with sample formats
            self.fields['file'].help_text = mark_safe(f"""
            <div style="margin-top: 10px;">
                <details>
                    <summary style="cursor: pointer; color: #0066cc; font-weight: bold;">📄 JSON Sample Format</summary>
                    <pre style="background: #f8f9fa; padding: 15px; border-radius: 5px; font-size: 12px; overflow-x: auto; margin-top: 10px;">{get_sample_package_format()}</pre>
                </details>
                <br>
                <details>
                    <summary style="cursor: pointer; color: #0066cc; font-weight: bold;">📄 DOC Sample Format</summary>
                    <pre style="background: #f8f9fa; padding: 15px; border-radius: 5px; font-size: 12px; overflow-x: auto; margin-top: 10px; white-space: pre-wrap;">{get_sample_doc_format()}</pre>
                </details>
            </div>
            """)

    @transaction.atomic
    def clean(self):
        """Validate and process uploaded files"""
        cleaned_data = super().clean()
        file = cleaned_data.get('file')
        
        if not file:
            raise forms.ValidationError("Please select a file to upload")
        
        print(f"DEBUG: Processing file: {file.name}")
        
        # Determine file type based on extension (this is the actual file extension, not package type)
        filename = file.name.lower()
        
        try:
            if filename.endswith('.json'):
                file_extension = 'json'
                print(f"DEBUG: Processing JSON file")
                
                # Validate JSON format
                try:
                    file.seek(0)
                    content = file.read()
                    
                    # Handle bytes vs string
                    if isinstance(content, bytes):
                        content = content.decode('utf-8')
                    
                    # Try to parse JSON to ensure it's valid
                    json_data = json.loads(content)
                    
                    # Check required fields for JSON
                    required_fields = ['title', 'package_no', 'destination', 'owner', 'price_per_person', 'duration']
                    missing_fields = []
                    
                    for field in required_fields:
                        value = json_data.get(field)
                        if not value or (isinstance(value, str) and not value.strip()) or (isinstance(value, list) and len(value) == 0):
                            missing_fields.append(field.replace('_', ' ').title())
                    
                    # Special handling for addons/add_ons - accept either one
                    addons_value = json_data.get('addons') or json_data.get('add_ons')
                    if not addons_value or (isinstance(addons_value, str) and not addons_value.strip()) or (isinstance(addons_value, list) and len(addons_value) == 0):
                        missing_fields.append('Add Ons')
                    
                    if missing_fields:
                        raise forms.ValidationError(f"Required fields are missing: {', '.join(missing_fields)}")
                    
                    print(f"DEBUG: JSON validation successful")
                    
                except json.JSONDecodeError as e:
                    print(f"DEBUG: JSON validation failed: {str(e)}")
                    raise forms.ValidationError(f"Invalid JSON format: {str(e)}")
                except Exception as e:
                    print(f"DEBUG: File processing error: {str(e)}")
                    raise forms.ValidationError(f"Error processing file: {str(e)}")
                    
            elif filename.endswith(('.doc', '.docx')):
                file_extension = 'doc' if filename.endswith('.doc') else 'docx'
                print(f"DEBUG: Processing {file_extension.upper()} file")
                
                # Basic validation - just check that file can be read
                file.seek(0)
                content = file.read()
                if len(content) == 0:
                    raise forms.ValidationError("The uploaded document appears to be empty")
                print(f"DEBUG: Document validation successful")
                
            else:
                raise forms.ValidationError("Please upload a JSON, DOC, or DOCX file")
            
            # Reset file pointer for later processing
            file.seek(0)
            
            # Create uploader instance first (but don't save to DB until processing succeeds)
            if hasattr(self, 'request') and self.request:
                # Get partner for processing
                from packages.admin import get_user_effective_partner
                from packages.choices import PackageTypeChoices
                partner = get_user_effective_partner(self.request)
                
                if not partner:
                    raise forms.ValidationError("Partner information is required but not available")

                # Create uploader instance (but don't save to DB yet)
                # Always set file_type to FIXED for PackageUploaderAdmin (this is package type, not file extension)
                uploader_instance = PackageUploader(
                    file=file,
                    file_type=PackageTypeChoices.FIXED.value,  # Always set to FIXED (this is for package type in DB)
                    partner=partner
                )
                
                # Store for later use in save() method
                self._package_uploader_instance = uploader_instance
                print(f"DEBUG: Created uploader instance for {file_extension.upper()} file")
            
                # Now process file and create package - this will only happen if validation passes
                # Note: removing @transaction.atomic from _create_package to let this transaction control it
                creation_service = PackageCreationService(
                    partner=partner,
                    source_type="file_upload",
                    file=file,
                    file_type=file_extension  # Use actual file extension (docx, json) for processing
                )
                
                file.seek(0)

                # Read file content based on type
                if file_extension.lower() == 'json':
                    try:
                        file_content = json.loads(file.read().decode('utf-8'))
                        creation_service.raw_json_data = file_content
                        print(f"DEBUG: JSON content loaded in clean(): {len(file_content) if file_content else 0} keys")
                    except json.JSONDecodeError as e:
                        raise forms.ValidationError(f"Invalid JSON file: {str(e)}")
                    except UnicodeDecodeError as e:
                        raise forms.ValidationError(f"File encoding error: {str(e)}")
                else:
                    # For DOC/DOCX, store binary content
                    creation_service.raw_json_data = file.read()
                    print(f"DEBUG: Binary content loaded in clean() for {file_extension.upper()}: {len(creation_service.raw_json_data)} bytes")
                
                # Process and create package within this transaction
                print(f"DEBUG: Starting package processing in clean() for {file_extension.upper()} file")
                created_package = creation_service.process_package()
                print(f"DEBUG: Package processing completed in clean(): {created_package.id if created_package else 'None'}")
                
                if not created_package:
                    raise forms.ValidationError("Package processing failed - no package was created.")
                
                # Store processed data for save() method - only set if everything succeeded
                self._processed_package = created_package
                print(f"DEBUG: Stored processed package {created_package.id} for save() method")
                
                # Reset file pointer for later use
                file.seek(0)
                
            print("DEBUG: clean() completed successfully")
                
        except forms.ValidationError as ve:
            print(f"DEBUG: ValidationError in clean(): {str(ve)}")
            
            # Even if package creation failed, we should still create the uploader instance
            # so that the upload attempt is recorded and user can see what went wrong
            if not hasattr(self, '_package_uploader_instance'):
                print(f"DEBUG: Creating uploader instance even though package creation failed")
                file = cleaned_data.get('file')
                
                if file and hasattr(self, 'request') and self.request:
                    try:
                        from packages.admin import get_user_effective_partner
                        from packages.choices import PackageTypeChoices
                        partner = get_user_effective_partner(self.request)
                        
                        uploader_instance = PackageUploader(
                            file=file,
                            file_type=PackageTypeChoices.FIXED.value,  # Always FIXED for package type
                            partner=partner
                        )
                        self._package_uploader_instance = uploader_instance
                        print(f"DEBUG: Created uploader instance for failed upload attempt")
                    except Exception as e:
                        print(f"DEBUG: Failed to create uploader instance for failed upload: {str(e)}")
            
            # Store the error for display but don't prevent form from saving
            self._validation_error = str(ve)
            print(f"DEBUG: Stored validation error for later display: {self._validation_error}")
            
            # Add error to the form's error dict for display
            if 'file' not in self.errors:
                self.add_error('file', str(ve))
            
            # Return cleaned data without raising - let the form save so uploader instance is created
            print("DEBUG: Returning cleaned data despite validation error to allow uploader creation")
            return cleaned_data
        
        except Exception as e:
            print(f"DEBUG: Unexpected error in clean(): {str(e)}")
            raise forms.ValidationError(f"File processing failed: {str(e)}. Please check your file format and try again.")
        
        return cleaned_data

    @transaction.atomic
    def save(self, commit=True):
        """Save uploader instance and connect to already-created package - SIMPLIFIED"""
        print("DEBUG: PackageUploaderForm.save() called")
        
        # Get the instance - this should have file from form data
        instance = super().save(commit=False)
        
        # Always set file_type to FIXED (this is the package type, not file extension)
        from packages.choices import PackageTypeChoices
        instance.file_type = PackageTypeChoices.FIXED.value
        print(f"DEBUG: Set file_type to FIXED: {instance.file_type}")
        
        # Set partner - always required
        if hasattr(self, 'request') and self.request:
            from packages.admin import get_user_effective_partner
            instance.partner = get_user_effective_partner(self.request)
            print(f"DEBUG: Set partner: {instance.partner}")
        
        # Store the processed package for later linking
        if hasattr(self, '_processed_package') and self._processed_package:
            instance._processed_package_for_linking = self._processed_package
            print(f"DEBUG: Stored processed package {self._processed_package.id} on instance for later linking")
        
        # ALWAYS save the uploader instance first to get an ID
        if commit:
            instance.save()
            print(f"DEBUG: Saved PackageUploader instance with ID: {instance.pk}")
            
            # Handle linking immediately if we have a processed package
            self._link_package_to_uploader(instance)
        else:
            print(f"DEBUG: Commit=False, storing instance for later save")
            # Store the linking function for later execution
            original_save = instance.save
            
            def enhanced_save(*args, **kwargs):
                print(f"DEBUG: Enhanced save called on uploader instance")
                result = original_save(*args, **kwargs)
                print(f"DEBUG: PackageUploader saved with ID: {instance.pk}")
                # Perform linking after save
                self._link_package_to_uploader(instance)
                return result
            
            instance.save = enhanced_save
        
        print(f"DEBUG: PackageUploaderForm.save() completed successfully with instance ID: {instance.pk if instance.pk else 'not saved yet'}")
        return instance
    
    def _link_package_to_uploader(self, uploader_instance):
        """Link the processed package to the uploader instance"""
        if hasattr(uploader_instance, '_processed_package_for_linking'):
            package = uploader_instance._processed_package_for_linking
            try:
                print(f"DEBUG: Attempting to link package {package.id} to uploader {uploader_instance.id}")
                
                # Update the package's package_uploaded field
                package.package_uploaded = uploader_instance
                package.save(update_fields=['package_uploaded'])
                
                print(f"DEBUG: ✅ SUCCESS - Connected package {package.id} to uploader {uploader_instance.id}")
                
                # Verify the link was saved
                package.refresh_from_db()
                if package.package_uploaded and package.package_uploaded.id == uploader_instance.id:
                    print(f"DEBUG: ✅ VERIFIED - Package-Uploader link saved correctly in database")
                else:
                    print(f"DEBUG: ❌ ERROR - Package-Uploader link not saved correctly")
                    
                # Clean up the temporary attribute
                delattr(uploader_instance, '_processed_package_for_linking')
                
            except Exception as e:
                print(f"DEBUG: ❌ ERROR connecting package to uploader: {str(e)}")
                # Don't raise here since the uploader is already saved
                # Just log the error
        else:
            print(f"DEBUG: No processed package to connect - uploader saved standalone")

    def _check_file_changed(self):
        """Check if the file field has actually changed"""
        if not self.instance.pk:
            return True  # New instance, file is definitely "changed"
        
        # For existing instances, check if file field was updated
        if 'file' in self.changed_data:
            return True
        
        return False


# Rest of the existing forms remain the same...
class MonthCheckboxSelectMultiple(forms.CheckboxSelectMultiple):
    """Custom widget for month selection with checkboxes"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.choices = MonthChoices.choices


class CategoryAdminForm(forms.ModelForm):
    """Custom form for Category admin"""
    
    class Meta:
        model = Category
        exclude = ['partner']  # Hide partner field from admin form
    
    def __init__(self, *args, **kwargs):
        # Extract request from kwargs if passed by admin
        self.request = kwargs.pop('request', None)
        super().__init__(*args, **kwargs)
        
        # Customize field appearance
        self.fields['title'].help_text = "Category name (will be converted to lowercase)"
        self.fields['description'].help_text = "Brief description of this category"

    def clean_title(self):
        """Clean and validate title - unique per partner"""
        title = self.cleaned_data.get('title', '').strip().lower()
        
        if not title:
            raise ValidationError("Category title is required.")
        
        # Get partner context
        if hasattr(self, 'request') and self.request:
            from packages.admin import get_user_effective_partner
            partner = get_user_effective_partner(self.request)
        elif self.instance.pk and self.instance.partner:
            partner = self.instance.partner
        else:
            # Fallback - shouldn't happen in normal admin usage
            partner = None
        
        if partner:
            # Check for duplicate categories within the same partner
            existing_filter = {'title': title, 'partner': partner}
            if self.instance.pk:
                # Editing existing category
                existing = Category.objects.filter(**existing_filter).exclude(pk=self.instance.pk)
            else:
                # Creating new category
                existing = Category.objects.filter(**existing_filter)
            
            if existing.exists():
                raise ValidationError(f"Category '{title}' already exists for your organization.")
        
        return title

    def clean(self):
        """Validate that media will be present after saving"""
        cleaned_data = super().clean()
        
        # For existing categories, check if they currently have media
        # We can't check for new media being uploaded here since we don't have access to formsets
        # That validation will be handled at the admin level through custom validation
        if self.instance.pk and not self.instance.media.exists():
            # This will only trigger for existing categories that currently have no media
            # If media is being uploaded, the formset will handle it properly
            pass  # We'll handle this with JavaScript or admin-level validation
        
        return cleaned_data


class DestinationAdminForm(forms.ModelForm):
    """Custom form for Destination admin with month checkboxes"""
    
    best_time_to_visit = forms.MultipleChoiceField(
        choices=MonthChoices.choices,
        widget=MonthCheckboxSelectMultiple,
        required=False,
        help_text="Select the months when it's best to visit this destination"
    )
    
    class Meta:
        model = Destination
        exclude = ['partner']  # Hide partner field from admin form
    
    def __init__(self, *args, **kwargs):
        # Extract request from kwargs if passed by admin
        self.request = kwargs.pop('request', None)
        super().__init__(*args, **kwargs)
        
        # Customize field appearance
        self.fields['title'].help_text = "Destination name (will be converted to lowercase)"
        self.fields['description'].help_text = "Brief description of this destination"

    def clean_title(self):
        """Clean and validate title - unique per partner"""
        title = self.cleaned_data.get('title', '').strip().lower()
        
        if not title:
            raise ValidationError("Destination title is required.")
        
        # Get partner context
        if hasattr(self, 'request') and self.request:
            from packages.admin import get_user_effective_partner
            partner = get_user_effective_partner(self.request)
        elif self.instance.pk and self.instance.partner:
            partner = self.instance.partner
        else:
            # Fallback - shouldn't happen in normal admin usage
            partner = None
        
        if partner:
            # Check for duplicate destinations within the same partner
            existing_filter = {'title': title, 'partner': partner}
            if self.instance.pk:
                # Editing existing destination
                existing = Destination.objects.filter(**existing_filter).exclude(pk=self.instance.pk)
            else:
                # Creating new destination
                existing = Destination.objects.filter(**existing_filter)
            
            if existing.exists():
                raise ValidationError(f"Destination '{title}' already exists for your organization.")
        
        return title

    def clean_best_time_to_visit(self):
        """Convert selected months to list of integers"""
        months = self.cleaned_data.get('best_time_to_visit', [])
        if months:
            try:
                return [int(month) for month in months]
            except (ValueError, TypeError):
                raise ValidationError("Invalid month selection.")
        return []

    def clean(self):
        """Validate that media will be present after saving"""
        cleaned_data = super().clean()
        
        # For existing destinations, check if they currently have media
        # We can't check for new media being uploaded here since we don't have access to formsets
        # That validation will be handled at the admin level through custom validation
        if self.instance.pk and not self.instance.media.exists():
            # This will only trigger for existing destinations that currently have no media
            # If media is being uploaded, the formset will handle it properly
            pass  # We'll handle this with JavaScript or admin-level validation
        
        return cleaned_data

    def save(self, commit=True):
        """Save with proper best_time_to_visit formatting"""
        instance = super().save(commit=False)
        instance.best_time_to_visit = self.cleaned_data.get('best_time_to_visit', [])
        if commit:
            instance.save()
        return instance


class ActivityAdminForm(forms.ModelForm):
    """Custom form for Activity admin"""
    
    class Meta:
        model = Activity
        exclude = ['partner']  # Hide partner field from admin form
    
    def __init__(self, *args, **kwargs):
        # Extract request from kwargs if passed by admin
        self.request = kwargs.pop('request', None)
        super().__init__(*args, **kwargs)
        
        # Customize field appearance
        self.fields['title'].help_text = "Activity name (will be converted to lowercase)"
        self.fields['description'].help_text = "Brief description of this activity"

    def clean_title(self):
        """Clean and validate title - unique per partner"""
        title = self.cleaned_data.get('title', '').strip().lower()
        
        if not title:
            raise ValidationError("Activity title is required.")
        
        # Get partner context
        if hasattr(self, 'request') and self.request:
            from packages.admin import get_user_effective_partner
            partner = get_user_effective_partner(self.request)
        elif self.instance.pk and self.instance.partner:
            partner = self.instance.partner
        else:
            # Fallback - shouldn't happen in normal admin usage
            partner = None
        
        if partner:
            # Check for duplicate activities within the same partner
            existing_filter = {'title': title, 'partner': partner}
            if self.instance.pk:
                # Editing existing activity
                existing = Activity.objects.filter(**existing_filter).exclude(pk=self.instance.pk)
            else:
                # Creating new activity
                existing = Activity.objects.filter(**existing_filter)
            
            if existing.exists():
                raise ValidationError(f"Activity '{title}' already exists for your organization.")
        
        return title

    def clean(self):
        """Validate that media will be present after saving"""
        cleaned_data = super().clean()
        
        # For existing activities, check if they currently have media
        # We can't check for new media being uploaded here since we don't have access to formsets
        # That validation will be handled at the admin level through custom validation
        if self.instance.pk and not self.instance.media.exists():
            # This will only trigger for existing activities that currently have no media
            # If media is being uploaded, the formset will handle it properly
            pass  # We'll handle this with JavaScript or admin-level validation
        
        return cleaned_data


class DestinationFaqAdminForm(forms.ModelForm):
    """Custom form for DestinationFaq admin"""
    
    class Meta:
        model = DestinationFaq
        exclude = ['partner']  # Hide partner field from admin form
    
    def __init__(self, *args, **kwargs):
        # Extract request from kwargs if passed by admin
        self.request = kwargs.pop('request', None)
        super().__init__(*args, **kwargs)
        
        # Set help text
        self.fields['destination'].help_text = 'Select the destination for this FAQ'
        self.fields['question'].help_text = 'Enter the frequently asked question'
        self.fields['answer'].help_text = 'Provide a comprehensive answer to the question'
        self.fields['is_published'].help_text = 'Check to make this FAQ visible to users'
        self.fields['priority'].help_text = 'Order for displaying FAQs (higher numbers appear first)'
        
        # Filter destinations by partner if we have request context
        if hasattr(self, 'request') and self.request:
            from packages.admin import get_user_effective_partner
            partner = get_user_effective_partner(self.request)
            
            if partner:
                # Only show destinations belonging to the user's partner
                self.fields['destination'].queryset = Destination.objects.filter(
                    partner=partner, 
                    is_active=True
                ).order_by('title')
            else:
                # No partner context, show no destinations
                self.fields['destination'].queryset = Destination.objects.none()


class ActivityMediaAdminForm(forms.ModelForm):
    """Custom form for ActivityMedia admin with main_display validation"""
    
    class Meta:
        model = ActivityMedia
        fields = ['media', 'main_display']
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['media'].help_text = "Upload image file (jpg, jpeg, png, webp)"
        self.fields['main_display'].help_text = "Check to set as main display image (only one allowed per activity)"

    def clean(self):
        """Validate main_display field"""
        cleaned_data = super().clean()
        main_display = cleaned_data.get('main_display', False)
        
        # Only validate if main_display is True and we have an activity
        if main_display and hasattr(self.instance, 'activity') and self.instance.activity:
            from packages.models import ActivityMedia
            
            existing_main = ActivityMedia.objects.filter(
                activity=self.instance.activity,
                main_display=True
            )
            
            # Exclude current instance if editing
            if self.instance.pk:
                existing_main = existing_main.exclude(pk=self.instance.pk)
            
            if existing_main.exists():
                self.add_error('main_display', 'Only one media file per activity can be set as main display')
        
        return cleaned_data


class CustomActivityAdminForm(forms.ModelForm):
    """Custom form for CustomActivity admin to handle persona multiselect field"""
    
    persona = forms.MultipleChoiceField(
        choices=PersonaChoices.choices,
        widget=forms.CheckboxSelectMultiple,
        required=False,
        help_text="Select one or more personas this activity is suitable for"
    )
    
    # Array field for addons with proper widget
    # addons = ClearableDynamicArrayField(
    #     forms.CharField(max_length=500),
    #     widget=DynamicArrayWidget(),
    #     required=False,
    # )
    
    class Meta:
        model = CustomActivity
        fields = '__all__'
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # If this is an existing instance, set initial persona values
        if self.instance and self.instance.pk and self.instance.persona:
            self.fields['persona'].initial = self.instance.persona
    
    def save(self, commit=True):
        instance = super().save(commit=False)
        
        # Save the persona field as a list
        persona_data = self.cleaned_data.get('persona', [])
        instance.persona = list(persona_data) if persona_data else []
        
        if commit:
            instance.save()
        
        return instance

class TripjackHotelsAdminForm(forms.ModelForm):
    """
    Custom form for TripjackHotels admin to handle amenities and image management.
    """
    amenities = forms.CharField(
        widget=forms.Textarea(attrs={'rows': 4}),
        help_text='Enter one amenity per line.',
        required=False
    )
    
    # Add a field for image management that will use our custom widget
    # This field doesn't map directly to a model field, it's for UI.
    # We will use it to manipulate the 'images' JSONField.
    manage_images = forms.CharField(widget=ImageManagementWidget, required=False, label="Manage Images")

    class Meta:
        model = TripjackHotels
        fields = '__all__'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Populate our custom field with the data from the actual model field
        if self.instance and self.instance.pk:
            self.fields['manage_images'].initial = self.instance.images
            if self.instance.amenities:
                self.initial['amenities'] = '\n'.join(self.instance.amenities)
        
        # We'll hide the original 'images' field from the user
        if 'images' in self.fields:
            self.fields['images'].widget = forms.HiddenInput()


    def clean_amenities(self):
        amenities = self.cleaned_data.get('amenities', '')
        if not amenities:
            return []
        return [line.strip() for line in amenities.split('\n') if line.strip()]

    def save(self, commit=True):
        # Get the hotel instance, but don't save it to DB yet
        hotel = super().save(commit=False)

        # --- Handle Image Deletion ---
        # The keys to delete are submitted with the name 'manage_images_delete'
        keys_to_delete = self.data.getlist('manage_images_delete')
        
        # Get the current list of keys
        current_keys = hotel.images or []
        
        # Filter out the deleted keys
        updated_keys = [key for key in current_keys if key not in keys_to_delete]
        
        # Delete from S3
        for key in keys_to_delete:
            try:
                default_storage.delete(key)
                logger.info(f"Deleted image {key} from S3 for hotel {hotel.hotel_id}")
            except Exception as e:
                logger.error(f"Failed to delete {key} from S3: {e}")

        # --- Handle Image Upload ---
        uploaded_files = self.files.getlist('manage_images_upload')
        
        for uploaded_file in uploaded_files:
            # Construct the path using our utility function
            # We need to pass a temporary instance to it.
            temp_media_instance = type('TempMedia', (object,), {'hotel': hotel})()
            file_path = tripjack_hotel_media_upload_path(temp_media_instance, uploaded_file.name)
            
            try:
                # Save the file to S3
                s3_key = default_storage.save(file_path, uploaded_file)
                updated_keys.append(s3_key)
                logger.info(f"Uploaded new image {s3_key} to S3 for hotel {hotel.hotel_id}")
            except Exception as e:
                logger.error(f"Failed to upload new image to S3: {e}")

        # Update the hotel's images field
        hotel.images = updated_keys
        
        if commit:
            hotel.save()
            # m2m relations are saved after the main object
            self.save_m2m() 
            
        return hotel


class CustomPackageAdminForm(forms.ModelForm):
    """Admin form for CustomPackage with special handling"""
    
    # Simple text area approach for array fields
    exclusions = forms.CharField(
        widget=forms.Textarea(attrs={
            'rows': 4,
            'style': 'width:100%; max-width:100%; box-sizing:border-box;',
        }),
        required=False,
        help_text='Enter exclusions one per line',
        label='Exclusions'
    )
    
    important_notes = forms.CharField(
        widget=forms.Textarea(attrs={
            'rows': 4,
            'style': 'width:100%; max-width:100%; box-sizing:border-box;',
        }),
        required=False,
        help_text='Enter important notes one per line',
        label='Important Notes'
    )
    
    visa_type = forms.CharField(
        widget=forms.Textarea(attrs={
            'rows': 3,
            'style': 'width:100%; max-width:100%; box-sizing:border-box;',
        }),
        required=False,
        help_text='Enter visa types one per line',
        label='Visa Types'
    )
    
    # Additional Package fields
    popular_restaurants = forms.CharField(
        widget=forms.Textarea(attrs={
            'rows': 4,
            'style': 'width:100%; max-width:100%; box-sizing:border-box;',
        }),
        required=False,
        help_text='Enter popular restaurants one per line',
        label='Popular Restaurants'
    )
    
    # Related model fields (handled as text areas for simplicity)
    highlights = forms.CharField(
        widget=forms.Textarea(attrs={
            'rows': 4,
            'style': 'width:100%; max-width:100%; box-sizing:border-box;',
        }),
        required=False,
        help_text='Enter package highlights one per line',
        label='Highlights'
    )
    
    inclusions = forms.CharField(
        widget=forms.Textarea(attrs={
            'rows': 4,
            'style': 'width:100%; max-width:100%; box-sizing:border-box;',
        }),
        required=False,
        help_text='Enter package inclusions one per line',
        label='Inclusions'
    )
    
    addons = forms.CharField(
        widget=forms.Textarea(attrs={
            'rows': 4,
            'style': 'width:100%; max-width:100%; box-sizing:border-box;',
        }),
        required=False,
        help_text='Enter package add-ons one per line',
        label='Add-ons'
    )
    
    class Meta:
        model = CustomPackage
        exclude = ['partner', 'currency', 'price', 'price_per_person', 'duration_in_days', 'duration', 'package_personas', 'best_time_to_visit_months', 'hotels', 'popular_activities', 'visa_type', 'exclusions', 'important_notes', 'popular_restaurants']  # Hide auto-populated and array fields from view
        widgets = {
            'about_this_tour': forms.Textarea(attrs={
                'rows': 6,
                'style': 'width:100%; max-width:100%; box-sizing:border-box;',
            }),
            'type': CustomAdminTypeWidget(),
            'destination_safety': forms.Textarea(attrs={
                'rows': 4,
                'style': 'width:100%; max-width:100%; box-sizing:border-box;',
            }),
            'rating_description': forms.Textarea(attrs={
                'rows': 3,
                'style': 'width:100%; max-width:100%; box-sizing:border-box;',
            }),
            'cultural_info': forms.Textarea(attrs={
                'rows': 5,
                'style': 'width:100%; max-width:100%; box-sizing:border-box;',
            }),
            'what_to_shop': forms.Textarea(attrs={
                'rows': 4,
                'style': 'width:100%; max-width:100%; box-sizing:border-box;',
            }),
            'rating': forms.NumberInput(attrs={
                'step': '0.1', 'min': '0', 'max': '5',
                'title': 'Rating must be between 0.0 and 5.0',
                'pattern': '^[0-5](\.[0-9])?$'
            }),
            'explore_order': forms.NumberInput(attrs={'min': '0'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Type field is handled by CustomAdminTypeWidget - no choices needed
            
        # Make ALL fields required except auto-calculated and status fields
        excluded_from_required = [
            'type', 'partner', 'currency', 'price', 'price_per_person', 'duration_in_days',
            'duration', 'package_personas', 'best_time_to_visit_months', 'hotels',
            'popular_activities', 'created_at', 'updated_at', 'is_active', 'is_published'
        ]

        for field_name, field in self.fields.items():
            if field_name not in excluded_from_required:
                field.required = True
                
        # Make type field show as plain text with fixed "Custom Admin" value
        if 'type' in self.fields:
            self.fields['type'].widget = CustomAdminTypeWidget()
            self.fields['type'].initial = PackageTypeChoices.CUSTOM_ADMIN.value
            self.fields['type'].help_text = 'Package type is fixed to Custom Admin for this interface'
        
        # duration_in_days and duration fields are hidden from view but calculated in background
        
        # Set help text for duration_in_nights
        if 'duration_in_nights' in self.fields:
            self.fields['duration_in_nights'].help_text = 'Enter number of nights (days, duration text, and package price will be auto-calculated from itinerary)'
        
        # Initialize text areas with existing array data
        if self.instance and self.instance.pk:
            # Convert arrays to newline-separated text
            if self.instance.exclusions:
                self.fields['exclusions'].initial = '\n'.join(self.instance.exclusions)
            if self.instance.important_notes:
                self.fields['important_notes'].initial = '\n'.join(self.instance.important_notes)
            if self.instance.visa_type:
                self.fields['visa_type'].initial = '\n'.join(self.instance.visa_type)
            if self.instance.popular_restaurants:
                self.fields['popular_restaurants'].initial = '\n'.join(self.instance.popular_restaurants)
            
            # Convert related model data to text areas
            highlights_values = list(self.instance.highlights.values_list('value', flat=True))
            if highlights_values:
                self.fields['highlights'].initial = '\n'.join(highlights_values)
                
            inclusions_values = list(self.instance.inclusions.values_list('value', flat=True))
            if inclusions_values:
                self.fields['inclusions'].initial = '\n'.join(inclusions_values)
                
            addons_values = list(self.instance.addons.values_list('value', flat=True))
            if addons_values:
                self.fields['addons'].initial = '\n'.join(addons_values)
        
        # Set default values for status fields (True by default for new packages)
        if not self.instance.pk:  # New instance
            if 'is_active' in self.fields:
                self.fields['is_active'].initial = True
            if 'is_published' in self.fields:
                self.fields['is_published'].initial = True
            
            # Set default value for owner
            if 'owner' in self.fields:
                self.fields['owner'].initial = 'ZUUMM'
        
        # Add help text for new fields
        if 'rating' in self.fields:
            self.fields['rating'].help_text = 'Package rating from 0.0 to 5.0'
        if 'rating_description' in self.fields:
            self.fields['rating_description'].help_text = 'Brief description about the package rating'
        if 'destination_safety' in self.fields:
            self.fields['destination_safety'].help_text = 'Safety information and tips for travelers'
        if 'cultural_info' in self.fields:
            self.fields['cultural_info'].help_text = 'Cultural information and local customs'
        if 'what_to_shop' in self.fields:
            self.fields['what_to_shop'].help_text = 'Shopping recommendations and local specialties'
        if 'what_to_pack' in self.fields:
            self.fields['what_to_pack'].help_text = 'Packing recommendations for this destination'
        if 'explore_order' in self.fields:
            self.fields['explore_order'].help_text = 'Order for displaying packages in listings (lower numbers appear first)'
                
        # Set help text for itinerary section
        if not self.instance.pk:  # New instance
            self.fields['destination'].help_text = (
                "Select a destination first. The itinerary section will appear after you save the package "
                "where you can add activities and hotels. Package price will be automatically calculated "
                "from the selected activities and hotels."
            )

    def clean_type(self):
        """Ensure type is always set to Custom Admin for CustomPackage"""
        # Always force to Custom Admin regardless of input
        return PackageTypeChoices.CUSTOM_ADMIN.value

    def clean_duration_in_days(self):
        """Auto-calculate from duration_in_nights + 1"""
        duration_in_nights = self.cleaned_data.get('duration_in_nights')
        
        if duration_in_nights is not None:
            # Always calculate as nights + 1
            return duration_in_nights + 1
        
        return None

    def clean_duration(self):
        """Auto-generate duration text as 'XN & YD' format"""
        duration_in_nights = self.cleaned_data.get('duration_in_nights')

        if duration_in_nights is not None:
            duration_in_days = duration_in_nights + 1
            return f"{duration_in_nights}N & {duration_in_days}D"

        return ""

    def clean_rating(self):
        """Validate rating field to ensure it's between 0 and 5 with max 1 decimal place"""
        rating = self.cleaned_data.get('rating')
        
        if rating is not None:
            # Check range
            if rating < 0 or rating > 5:
                raise forms.ValidationError("Rating must be between 0.0 and 5.0")
            
            # Check decimal places (ensure only 1 digit after decimal)
            rating_str = str(rating)
            if '.' in rating_str:
                decimal_part = rating_str.split('.')[1]
                if len(decimal_part) > 1:
                    raise forms.ValidationError("Rating can have at most 1 digit after decimal point (e.g., 4.5)")
        
        return rating
    
    def clean(self):
        """Override clean to handle ArrayField initialization and validate required fields"""
        cleaned_data = super().clean()
        
        # Initialize ArrayFields with empty lists if they're empty strings or None
        array_fields = ['visa_type', 'exclusions', 'important_notes', 'popular_restaurants']
        
        for field_name in array_fields:
            if field_name in cleaned_data:
                value = cleaned_data[field_name]
                # If the field is an empty string or None, set it to empty list
                if not value or value == '':
                    cleaned_data[field_name] = []
        
        # Validate that text area fields (converted to arrays) have content
        text_area_fields = {
            'highlights': 'At least one highlight is required',
            'inclusions': 'At least one inclusion is required',
            'exclusions': 'At least one exclusion is required',
            'important_notes': 'At least one important note is required',
            'visa_type': 'At least one visa type is required',
            'popular_restaurants': 'At least one popular restaurant is required'
        }

        for field_name, error_message in text_area_fields.items():
            if field_name in cleaned_data:
                value = cleaned_data[field_name]
                # Check if it's empty string, None, or empty list
                if not value or (isinstance(value, str) and not value.strip()) or (isinstance(value, list) and len(value) == 0):
                    self.add_error(field_name, error_message)

        return cleaned_data
    


    def save(self, commit=True):
        """Save form and convert text areas to arrays"""
        instance = super().save(commit=False)
        
        # Convert newline-separated text to arrays
        exclusions_text = self.cleaned_data.get('exclusions', '')
        important_notes_text = self.cleaned_data.get('important_notes', '')
        visa_type_text = self.cleaned_data.get('visa_type', '')
        popular_restaurants_text = self.cleaned_data.get('popular_restaurants', '')
        
        # Split by lines and filter out empty strings
        instance.exclusions = [line.strip() for line in exclusions_text.split('\n') if line.strip()]
        instance.important_notes = [line.strip() for line in important_notes_text.split('\n') if line.strip()]
        instance.visa_type = [line.strip() for line in visa_type_text.split('\n') if line.strip()]
        instance.popular_restaurants = [line.strip() for line in popular_restaurants_text.split('\n') if line.strip()]
        
        if commit:
            instance.save()
            
            # Handle related model fields (highlights, inclusions, addons)
            self._save_related_fields(instance)
        
        return instance
    
    def _save_related_fields(self, instance):
        """Save related model fields (highlights, inclusions, addons)"""
        from packages.utils.custom_package_icon_helper import CustomPackageIconHelper
        
        # Get text data from form
        highlights_text = self.cleaned_data.get('highlights', '')
        inclusions_text = self.cleaned_data.get('inclusions', '')
        addons_text = self.cleaned_data.get('addons', '')
        
        # Convert to lists
        highlights_list = [line.strip() for line in highlights_text.split('\n') if line.strip()]
        inclusions_list = [line.strip() for line in inclusions_text.split('\n') if line.strip()]
        addons_list = [line.strip() for line in addons_text.split('\n') if line.strip()]
        
        # Skip if no items to process
        if not any([highlights_list, inclusions_list, addons_list]):
            return
        
        try:
            # Initialize icon helper
            icon_helper = CustomPackageIconHelper()
            
            # Generate icons for all items at once
            logger.info(f"Generating icons for package '{instance.title}' - {len(highlights_list)} highlights, {len(inclusions_list)} inclusions, {len(addons_list)} addons")
            
            icon_data = icon_helper.generate_icons_for_package_data(
                highlights_list, inclusions_list, addons_list
            )
            
            # Clear existing related fields
            instance.highlights.all().delete()
            instance.inclusions.all().delete()
            instance.addons.all().delete()
            
            # Create highlights with generated icons
            for highlight_data in icon_data['highlights']:
                PackageHighlight.objects.create(
                    package=instance,
                    value=highlight_data['value'],
                    icon_class=highlight_data['icon_class']
                )
            
            # Create inclusions with generated icons
            for inclusion_data in icon_data['inclusions']:
                PackageInclusion.objects.create(
                    package=instance,
                    value=inclusion_data['value'],
                    icon_class=inclusion_data['icon_class']
                )
            
            # Create addons with generated icons
            for addon_data in icon_data['addons']:
                PackageAddon.objects.create(
                    package=instance,
                    value=addon_data['value'],
                    icon_class=addon_data['icon_class']
                )
            
            logger.info(f"Successfully saved related fields with AI-generated icons for package '{instance.title}'")
        except Exception as e:
            logger.error(f"Error generating icons for package '{instance.title}': {str(e)}")
            logger.info(f"Falling back to default icons for package '{instance.title}'")
            
            # Fallback to default icons if AI generation fails
            self._save_related_fields_with_default_icons(instance, highlights_list, inclusions_list, addons_list)
    
    def _save_related_fields_with_default_icons(self, instance, highlights_list, inclusions_list, addons_list):
        """Fallback method to save related fields with default icons"""
        
        # Clear existing related fields
        instance.highlights.all().delete()
        instance.inclusions.all().delete() 
        instance.addons.all().delete()
        
        # Create highlights with default icons
        for highlight_text in highlights_list:
            PackageHighlight.objects.create(
                package=instance,
                value=highlight_text,
                icon_class='activity'  # Default fallback icon
            )
        
        # Create inclusions with default icons
        for inclusion_text in inclusions_list:
            PackageInclusion.objects.create(
                package=instance,
                value=inclusion_text,
                icon_class='breakfast'  # Default fallback icon for inclusions
            )
        
        # Create addons with default icons
        for addon_text in addons_list:
            PackageAddon.objects.create(
                package=instance,
                value=addon_text,
                icon_class='activity'  # Default fallback icon
            )
        
        logger.info(f"Saved related fields with default icons for package '{instance.title}'")


class CustomPackageItineraryForm(forms.ModelForm):
    """Form for CustomPackageItinerary with dynamic field filtering"""
    
    class Meta:
        model = CustomPackageItinerary
        fields = '__all__'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Add CSS classes for JavaScript targeting
        self.fields['type'].widget.attrs.update({'class': 'itinerary-type-field'})
        self.fields['activity'].widget.attrs.update({'class': 'itinerary-activity-field'})
        self.fields['hotel'].widget.attrs.update({'class': 'itinerary-hotel-field'})

        # Make basic itinerary fields required
        self.fields['day_number'].required = True
        self.fields['type'].required = True

        # Start with empty querysets for performance - we'll update before validation
        self.fields['activity'].queryset = CustomActivity.objects.none()
        self.fields['hotel'].queryset = TripjackHotels.objects.none()

        # For existing instances, include ALL activities/hotels for the destination
        # This allows the JavaScript to work properly with pre-populated dropdowns
        if self.instance and self.instance.pk and self.instance.package and self.instance.package.destination:
            destination = self.instance.package.destination

            # Include all active activities for this destination
            self.fields['activity'].queryset = CustomActivity.objects.filter(
                destination=destination,
                is_active=True
            ).order_by('title')

            # Include all active hotels for this destination
            self.fields['hotel'].queryset = TripjackHotels.objects.filter(
                destinations=destination,
                is_active=True
            ).order_by('name')

    def full_clean(self):
        """
        Runs on form submission (a POST request), before validation.

        This is necessary because the initial queryset is empty. When the user
        selects an item via the autocomplete widget and saves, we must tell the
        form that the submitted ID is a valid choice. We do this by temporarily
        setting the queryset to include the submitted item just for validation.
        """
        # self.is_bound is True for POST requests with data.
        if self.is_bound:
            # Use self.add_prefix() to correctly get the field name from inline formsets.
            activity_id = self.data.get(self.add_prefix('activity'))
            hotel_id = self.data.get(self.add_prefix('hotel'))

            if activity_id:
                self.fields['activity'].queryset = CustomActivity.objects.filter(pk=activity_id)

            if hotel_id:
                self.fields['hotel'].queryset = TripjackHotels.objects.filter(pk=hotel_id)

        super().full_clean()

    def clean_activity(self):
        """Additional validation for activity selection"""
        activity = self.cleaned_data.get('activity')
        if activity and not activity.is_active:
            raise forms.ValidationError("Selected activity is not active.")
        return activity

    def clean_hotel(self):
        """Additional validation for hotel selection"""
        hotel = self.cleaned_data.get('hotel')
        if hotel and not hotel.is_active:
            raise forms.ValidationError("Selected hotel is not active.")
        return hotel

    def clean(self):
        """Validate that appropriate fields are filled based on type"""
        cleaned_data = super().clean()

        itinerary_type = cleaned_data.get('type')
        activity = cleaned_data.get('activity')
        hotel = cleaned_data.get('hotel')

        if itinerary_type == ItineraryDayItemType.ACTIVITY and not activity:
            raise forms.ValidationError({
                'activity': 'Activity is required when type is "Activity".'
            })

        if itinerary_type == ItineraryDayItemType.HOTEL and not hotel:
            raise forms.ValidationError({
                'hotel': 'Hotel is required when type is "Hotel".'
            })

        # Clear inappropriate fields
        if itinerary_type == ItineraryDayItemType.ACTIVITY:
            cleaned_data['hotel'] = None
        elif itinerary_type == ItineraryDayItemType.HOTEL:
            cleaned_data['activity'] = None

        return cleaned_data
