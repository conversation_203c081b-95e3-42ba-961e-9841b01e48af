from django import forms
from django.core.files.storage import default_storage
from django.urls import reverse
from django.utils.safestring import mark_safe
from django.utils.html import format_html
import json
import logging

logger = logging.getLogger(__name__)


class InfiniteScrollSelectWidget(forms.Widget):
    """
    Custom widget that provides infinite scroll and search functionality
    for large datasets like hotels and custom activities.
    """
    
    def __init__(self, attrs=None, model_class=None, search_field='name', display_field='name', extra_filters=None):
        self.model_class = model_class
        self.search_field = search_field
        self.display_field = display_field
        self.extra_filters = extra_filters or {}
        super().__init__(attrs)
        logger.debug(f"InfiniteScrollSelectWidget initialized for {model_class._meta.label if model_class else 'Unknown'}")
    
    def format_value(self, value):
        """Return selected value ID"""
        if value is None:
            return ''
        return str(value)
    
    def render(self, name, value, attrs=None, renderer=None):
        """Render the widget with custom HTML and JavaScript"""
        logger.info(f"InfiniteScrollSelectWidget.render called for field: {name}, value: {value}")
        
        if attrs is None:
            attrs = {}
        
        # Get widget ID for JavaScript
        widget_id = attrs.get('id', name)
        logger.debug(f"Widget ID: {widget_id}")
        
        # Get initial selected item if value exists
        selected_item = None
        selected_text = f'Select {self.model_class._meta.verbose_name.lower()}...' if self.model_class else 'Select option...'
        
        if value and self.model_class:
            try:
                obj = self.model_class.objects.get(pk=value)
                selected_item = {
                    'id': obj.pk,
                    'text': getattr(obj, self.display_field, str(obj))
                }
                selected_text = selected_item['text']
                logger.info(f"Found selected item: {selected_item}")
            except self.model_class.DoesNotExist:
                logger.warning(f"Selected {self.model_class._meta.label} with ID {value} not found")
        
        # Get model verbose name for better UX
        model_verbose_name = self.model_class._meta.verbose_name.title() if self.model_class else 'Item'
        
        # Create clean HTML structure with proper styling
        html = format_html('''
        <div class="infinite-scroll-select-container" data-widget-id="{widget_id}" style="position: relative; width: 100%;">
            <input type="hidden" id="{widget_id}" name="{name}" value="{value}">
            
            <!-- Main display area -->
            <div class="infinite-scroll-display" style="
                border: 1px solid #ddd; 
                padding: 8px 12px; 
                background: #fff; 
                cursor: pointer; 
                border-radius: 4px;
                min-height: 20px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-size: 13px;
                box-sizing: border-box;
                transition: border-color 0.2s ease;
            " onmouseover="this.style.borderColor='#999'" onmouseout="this.style.borderColor='#ddd'">
                <span class="selected-text" style="color: #333; flex: 1;">{selected_text}</span>
                <span class="dropdown-arrow" style="color: #666; font-size: 12px; margin-left: 8px;">▼</span>
            </div>
            
            <!-- Dropdown menu -->
            <div class="infinite-scroll-dropdown" style="
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background: #fff;
                border: 1px solid #ddd;
                border-top: none;
                border-radius: 0 0 4px 4px;
                max-height: 300px;
                overflow: hidden;
                z-index: 1000;
                box-shadow: 0 2px 8px rgba(0,0,0,0.15);
                display: none;
            ">
                <!-- Search input -->
                <div class="search-header" style="padding: 8px; border-bottom: 1px solid #eee; background: #f9f9f9;">
                    <input type="text" 
                           class="search-input" 
                           placeholder="Search {model_verbose_name}..." 
                           autocomplete="off"
                           style="
                               width: 100%;
                               padding: 6px 8px;
                               border: 1px solid #ddd;
                               border-radius: 3px;
                               font-size: 13px;
                               box-sizing: border-box;
                               outline: none;
                           ">
                </div>
                
                <!-- Loading indicator -->
                <div class="loading-indicator" style="
                    display: none;
                    padding: 15px;
                    text-align: center;
                    color: #666;
                    font-style: italic;
                    background: #f9f9f9;
                ">Loading...</div>
                
                <!-- Options container -->
                <div class="options-container" style="
                    max-height: 200px;
                    overflow-y: auto;
                    background: #fff;
                "></div>
                
                <!-- Load more indicator -->
                <div class="load-more-indicator" style="
                    display: none;
                    padding: 10px;
                    text-align: center;
                    color: #666;
                    font-size: 12px;
                    background: #f5f5f5;
                    cursor: pointer;
                    border-top: 1px solid #eee;
                ">Scroll for more...</div>
            </div>
        </div>
        ''', 
        widget_id=widget_id,
        name=name,
        value=self.format_value(value),
        selected_text=selected_text,
        model_verbose_name=model_verbose_name
        )
        
        # JavaScript configuration
        js_config = {
            'widgetId': widget_id,
            'modelName': self.model_class._meta.label_lower if self.model_class else '',
            'searchField': self.search_field,
            'displayField': self.display_field,
            'ajaxUrl': reverse('infinite_scroll_data'),
            'initialValue': selected_item,
            'extraFilters': self.extra_filters,
            'modelVerboseName': model_verbose_name
        }
        
        logger.debug(f"JavaScript config: {js_config}")
        
        # Enhanced JavaScript with Django admin compatibility
        js = format_html('''
        <script>
        console.log('🟢 INFINITE SCROLL WIDGET SCRIPT FOR: {widget_id}');
        console.log('🟢 Script executing at:', new Date().toISOString());
        console.log('🟢 Document readyState:', document.readyState);
        console.log('🟢 Window loaded:', window.loaded || 'not set');
        
        // Global function to initialize our widget - called by both immediate execution and Django admin
        window.initInfiniteScrollWidget_{widget_id_safe} = function() {{
            console.log('🟢 INIT FUNCTION CALLED FOR:', '{widget_id}');
            console.log('🟢 Document ready state:', document.readyState);
            console.log('🟢 DOM fully loaded:', document.readyState === 'complete');
            
            const config = {config_json};
            console.log('🟢 Initializing widget:', config.widgetId);
            console.log('🟢 Config:', config);
            
            const container = document.querySelector('[data-widget-id="' + config.widgetId + '"]');
            console.log('🟢 Container selector:', '[data-widget-id="' + config.widgetId + '"]');
            console.log('🟢 Container found:', container ? 'YES' : 'NO');
            
            if (!container) {{
                console.log('🔴 Container not found for:', config.widgetId);
                console.log('🔴 Available containers in DOM:');
                document.querySelectorAll('[data-widget-id]').forEach((c, i) => {{
                    console.log(`🔴   Container ${{i}}: ${{c.dataset.widgetId}}`);
                }});
                return false;
            }}
            
            console.log('🟢 Container HTML:', container.outerHTML.substring(0, 200) + '...');
            
            const displayArea = container.querySelector('.infinite-scroll-display');
            const dropdown = container.querySelector('.infinite-scroll-dropdown');
            const arrow = container.querySelector('.dropdown-arrow');
            const hiddenInput = container.querySelector('input[type="hidden"]');
            
            console.log('🟢 Display area found:', displayArea ? 'YES' : 'NO');
            console.log('🟢 Dropdown found:', dropdown ? 'YES' : 'NO');
            console.log('🟢 Arrow found:', arrow ? 'YES' : 'NO');
            console.log('🟢 Hidden input found:', hiddenInput ? 'YES' : 'NO');
            
            if (!displayArea || !dropdown) {{
                console.log('🔴 Elements not found for:', config.widgetId);
                console.log('🔴 Available classes in container:');
                container.querySelectorAll('*').forEach((el, i) => {{
                    if (el.className) console.log(`🔴   Element ${{i}}: ${{el.tagName}} with class "${{el.className}}"`);
                }});
                return false;
            }}
            
            // Prevent multiple initializations
            if (displayArea.dataset.initialized) {{
                console.log('🟡 Widget already initialized:', config.widgetId);
                return true;
            }}
            displayArea.dataset.initialized = 'true';
            console.log('🟢 Marking widget as initialized');
            
            console.log('🟢 Attaching click event to display area');
            
            // Toggle dropdown on click
            displayArea.addEventListener('click', function(e) {{
                console.log('🟠 CLICK EVENT TRIGGERED!');
                console.log('🟠 Event target:', e.target);
                console.log('🟠 Event type:', e.type);
                console.log('🟠 Widget ID:', config.widgetId);
                
                e.preventDefault();
                e.stopPropagation();
                console.log('🟠 Prevented default and stopped propagation');
                
                const isVisible = dropdown.style.display === 'block';
                console.log('🟠 Current dropdown visibility:', isVisible ? 'VISIBLE' : 'HIDDEN');
                console.log('🟠 Dropdown display style:', dropdown.style.display);
                
                if (isVisible) {{
                    dropdown.style.display = 'none';
                    arrow.textContent = '▼';
                    console.log('🔴 Dropdown CLOSED');
                }} else {{
                    console.log('🟢 Opening dropdown...');
                    
                    // Close other dropdowns
                    const otherDropdowns = document.querySelectorAll('.infinite-scroll-dropdown');
                    console.log('🟢 Found', otherDropdowns.length, 'total dropdowns');
                    otherDropdowns.forEach(d => {{
                        if (d !== dropdown) {{
                            d.style.display = 'none';
                            const otherArrow = d.parentElement.querySelector('.dropdown-arrow');
                            if (otherArrow) otherArrow.textContent = '▼';
                            console.log('🟡 Closed other dropdown');
                        }}
                    }});
                    
                    dropdown.style.display = 'block';
                    arrow.textContent = '▲';
                    console.log('🟢 Dropdown OPENED - display set to block');
                    console.log('🟢 Arrow changed to up');
                    
                    // Focus search input
                    const searchInput = dropdown.querySelector('.search-input');
                    console.log('🟢 Search input found:', searchInput ? 'YES' : 'NO');
                    if (searchInput) {{
                        setTimeout(() => {{
                            searchInput.focus();
                            searchInput.select();
                            console.log('🟢 Focused search input');
                        }}, 50);
                    }}
                    
                    // Load initial data if empty
                    const optionsContainer = dropdown.querySelector('.options-container');
                    console.log('🟢 Options container found:', optionsContainer ? 'YES' : 'NO');
                    console.log('🟢 Options container children count:', optionsContainer ? optionsContainer.children.length : 'N/A');
                    console.log('🟢 Options container innerHTML:', optionsContainer ? optionsContainer.innerHTML : 'N/A');
                    
                    // Always load data when dropdown opens (for debugging)
                    console.log('🟢 Force loading initial data...');
                    loadData();
                }}
            }});
            
            console.log('🟢 Click event listener attached');
            
            // Test click programmatically - but only for actual forms, not templates
            if (!config.widgetId.includes('__prefix__')) {{
                console.log('🧪 Testing click event programmatically in 2 seconds...');
                setTimeout(() => {{
                    console.log('🧪 Triggering test click');
                    displayArea.click();
                }}, 2000);
            }} else {{
                console.log('🟡 Skipping test click for template form');
            }}
            
            // Close on outside click
            document.addEventListener('click', function(e) {{
                console.log('🟡 Outside click detected, target:', e.target.tagName, e.target.className);
                if (!container.contains(e.target)) {{
                    dropdown.style.display = 'none';
                    arrow.textContent = '▼';
                    console.log('🟡 Closed dropdown due to outside click');
                }}
            }});
            
            // Data loading function
            function loadData(search = '', page = 1) {{
                console.log('🔵 Loading data with search:', search, 'page:', page);
                const loadingIndicator = dropdown.querySelector('.loading-indicator');
                const optionsContainer = dropdown.querySelector('.options-container');
                
                if (page === 1) {{
                    optionsContainer.innerHTML = '';
                    loadingIndicator.style.display = 'block';
                    console.log('🔵 Showing loading indicator');
                }}
                
                const params = new URLSearchParams({{
                    model: config.modelName,
                    search: search,
                    page: page,
                    search_field: config.searchField,
                    display_field: config.displayField,
                    ...config.extraFilters
                }});
                
                const ajaxUrl = config.ajaxUrl + '?' + params.toString();
                console.log('🔵 AJAX URL:', ajaxUrl);
                console.log('🔵 AJAX Params:', Object.fromEntries(params));
                
                fetch(ajaxUrl, {{
                    method: 'GET',
                    headers: {{
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    }}
                }})
                    .then(response => {{
                        console.log('🔵 AJAX response status:', response.status);
                        console.log('🔵 AJAX response headers:', response.headers);
                        if (!response.ok) {{
                            throw new Error(`HTTP error! status: ${{response.status}}`);
                        }}
                        return response.json();
                    }})
                    .then(data => {{
                        console.log('🔵 AJAX data received:', data);
                        loadingIndicator.style.display = 'none';
                        
                        if (data.error) {{
                            console.error('🔴 Server error:', data.error);
                            optionsContainer.innerHTML = '<div style="padding: 15px; color: #d32f2f; text-align: center;">Error: ' + data.error + '</div>';
                            return;
                        }}
                        
                        if (data.items && data.items.length > 0) {{
                            console.log('🔵 Adding', data.items.length, 'options');
                            
                            // Add "None" option at the top when loading initial data (page 1)
                            if (page === 1) {{
                                console.log('🔵 Adding "None" option at top');
                                const noneOption = document.createElement('div');
                                noneOption.style.cssText = `
                                    padding: 8px 12px;
                                    cursor: pointer;
                                    border-bottom: 1px solid #ddd;
                                    font-size: 13px;
                                    font-style: italic;
                                    color: #666;
                                    background-color: #f9f9f9;
                                    transition: background-color 0.2s ease;
                                `;
                                noneOption.textContent = '— None —';
                                noneOption.dataset.value = '';
                                noneOption.className = 'none-option';
                                
                                // Mark as selected if no current value
                                if (!hiddenInput.value || hiddenInput.value === '') {{
                                    noneOption.style.backgroundColor = '#e6f3ff';
                                    noneOption.style.color = '#0066cc';
                                    noneOption.style.fontStyle = 'normal';
                                    console.log('🔵 Marked none option as selected');
                                }}
                                
                                noneOption.addEventListener('mouseenter', function() {{
                                    if (this.style.backgroundColor !== '#e6f3ff') {{
                                        this.style.backgroundColor = '#f0f8ff';
                                    }}
                                }});
                                
                                noneOption.addEventListener('mouseleave', function() {{
                                    if (this.style.backgroundColor !== '#e6f3ff') {{
                                        this.style.backgroundColor = '#f9f9f9';
                                    }}
                                }});
                                
                                noneOption.addEventListener('click', function() {{
                                    console.log('🔵 None option clicked - clearing selection');
                                    hiddenInput.value = '';
                                    const placeholder = `Select ${{config.modelVerboseName.toLowerCase()}}...`;
                                    displayArea.querySelector('.selected-text').textContent = placeholder;
                                    dropdown.style.display = 'none';
                                    arrow.textContent = '▼';
                                    
                                    // Trigger change event for form validation
                                    const changeEvent = new Event('change', {{ bubbles: true }});
                                    hiddenInput.dispatchEvent(changeEvent);
                                    console.log('🟢 Selection cleared, showing placeholder');
                                }});
                                
                                optionsContainer.appendChild(noneOption);
                                console.log('🔵 Added "None" option');
                            }}
                            
                            data.items.forEach((item, index) => {{
                                const option = document.createElement('div');
                                option.style.cssText = `
                                    padding: 8px 12px;
                                    cursor: pointer;
                                    border-bottom: 1px solid #f0f0f0;
                                    font-size: 13px;
                                    transition: background-color 0.2s ease;
                                `;
                                option.textContent = item.text;
                                option.dataset.value = item.id;
                                
                                // Mark as selected if it matches current value
                                if (hiddenInput.value == item.id) {{
                                    option.style.backgroundColor = '#e6f3ff';
                                    option.style.color = '#0066cc';
                                    console.log('🔵 Marked option as selected:', item.text);
                                }}
                                
                                option.addEventListener('mouseenter', function() {{
                                    if (this.style.backgroundColor !== '#e6f3ff') {{
                                        this.style.backgroundColor = '#f5f5f5';
                                    }}
                                }});
                                
                                option.addEventListener('mouseleave', function() {{
                                    if (this.style.backgroundColor !== '#e6f3ff') {{
                                        this.style.backgroundColor = '';
                                    }}
                                }});
                                
                                option.addEventListener('click', function() {{
                                    console.log('🔵 Option clicked:', item.text);
                                    hiddenInput.value = item.id;
                                    displayArea.querySelector('.selected-text').textContent = item.text;
                                    dropdown.style.display = 'none';
                                    arrow.textContent = '▼';
                                    
                                    // Trigger change event for form validation
                                    const changeEvent = new Event('change', {{ bubbles: true }});
                                    hiddenInput.dispatchEvent(changeEvent);
                                    console.log('🟢 Selected:', item.text);
                                }});
                                
                                optionsContainer.appendChild(option);
                                console.log('🔵 Added option', index + 1, ':', item.text);
                            }});
                        }} else if (page === 1) {{
                            console.log('🟡 No results found');
                            
                            // Still add "None" option even if no results
                            console.log('🔵 Adding "None" option for empty results');
                            const noneOption = document.createElement('div');
                            noneOption.style.cssText = `
                                padding: 8px 12px;
                                cursor: pointer;
                                border-bottom: 1px solid #ddd;
                                font-size: 13px;
                                font-style: italic;
                                color: #666;
                                background-color: #f9f9f9;
                                transition: background-color 0.2s ease;
                            `;
                            noneOption.textContent = '— None —';
                            noneOption.dataset.value = '';
                            noneOption.className = 'none-option';
                            
                            // Mark as selected if no current value
                            if (!hiddenInput.value || hiddenInput.value === '') {{
                                noneOption.style.backgroundColor = '#e6f3ff';
                                noneOption.style.color = '#0066cc';
                                noneOption.style.fontStyle = 'normal';
                            }}
                            
                            noneOption.addEventListener('mouseenter', function() {{
                                if (this.style.backgroundColor !== '#e6f3ff') {{
                                    this.style.backgroundColor = '#f0f8ff';
                                }}
                            }});
                            
                            noneOption.addEventListener('mouseleave', function() {{
                                if (this.style.backgroundColor !== '#e6f3ff') {{
                                    this.style.backgroundColor = '#f9f9f9';
                                }}
                            }});
                            
                            noneOption.addEventListener('click', function() {{
                                console.log('🔵 None option clicked - clearing selection');
                                hiddenInput.value = '';
                                const placeholder = `Select ${{config.modelVerboseName.toLowerCase()}}...`;
                                displayArea.querySelector('.selected-text').textContent = placeholder;
                                dropdown.style.display = 'none';
                                arrow.textContent = '▼';
                                
                                // Trigger change event for form validation
                                const changeEvent = new Event('change', {{ bubbles: true }});
                                hiddenInput.dispatchEvent(changeEvent);
                                console.log('🟢 Selection cleared');
                            }});
                            
                            optionsContainer.appendChild(noneOption);
                            
                            // Add no results message below None option
                            const noResults = document.createElement('div');
                            noResults.style.cssText = 'padding: 15px; color: #666; text-align: center; font-style: italic;';
                            noResults.textContent = 'No results found';
                            optionsContainer.appendChild(noResults);
                        }}
                    }})
                    .catch(error => {{
                        console.error('🔴 AJAX Error:', error);
                        loadingIndicator.style.display = 'none';
                        optionsContainer.innerHTML = '<div style="padding: 15px; color: #d32f2f; text-align: center;">Error loading data: ' + error.message + '</div>';
                    }});
            }}
            
            // Search input handler
            const searchInput = dropdown.querySelector('.search-input');
            if (searchInput) {{
                console.log('🔵 Setting up search input handler');
                let searchTimeout;
                searchInput.addEventListener('input', function() {{
                    console.log('🔵 Search input changed:', this.value);
                    clearTimeout(searchTimeout);
                    searchTimeout = setTimeout(() => loadData(this.value), 300);
                }});
            }}
            
            // Infinite scroll handler
            const optionsContainer = dropdown.querySelector('.options-container');
            if (optionsContainer) {{
                console.log('🔵 Setting up infinite scroll handler');
                let currentPage = 1;
                let isLoading = false;
                let hasNextPage = true;
                
                optionsContainer.addEventListener('scroll', function() {{
                    // Check if scrolled near bottom (within 50px)
                    const scrollTop = this.scrollTop;
                    const scrollHeight = this.scrollHeight;
                    const clientHeight = this.clientHeight;
                    const scrolledToBottom = scrollTop + clientHeight >= scrollHeight - 50;
                    
                    if (scrolledToBottom && !isLoading && hasNextPage) {{
                        console.log('🔵 Reached bottom, loading next page:', currentPage + 1);
                        isLoading = true;
                        
                        const search = searchInput ? searchInput.value : '';
                        const nextPage = currentPage + 1;
                        
                        // Load next page
                        const params = new URLSearchParams({{
                            model: config.modelName,
                            search: search,
                            page: nextPage,
                            search_field: config.searchField,
                            display_field: config.displayField,
                            ...config.extraFilters
                        }});
                        
                        const ajaxUrl = config.ajaxUrl + '?' + params.toString();
                        console.log('🔵 Loading page', nextPage, 'URL:', ajaxUrl);
                        
                        fetch(ajaxUrl, {{
                            method: 'GET',
                            headers: {{
                                'Accept': 'application/json',
                                'X-Requested-With': 'XMLHttpRequest'
                            }}
                        }})
                        .then(response => response.json())
                        .then(data => {{
                            console.log('🔵 Page', nextPage, 'data received:', data.items ? data.items.length : 0, 'items');
                            
                            if (data.items && data.items.length > 0) {{
                                data.items.forEach((item, index) => {{
                                    const option = document.createElement('div');
                                    option.style.cssText = `
                                        padding: 8px 12px;
                                        cursor: pointer;
                                        border-bottom: 1px solid #f0f0f0;
                                        font-size: 13px;
                                        transition: background-color 0.2s ease;
                                    `;
                                    option.textContent = item.text;
                                    option.dataset.value = item.id;
                                    
                                    option.addEventListener('mouseenter', () => option.style.backgroundColor = '#f5f5f5');
                                    option.addEventListener('mouseleave', () => option.style.backgroundColor = '');
                                    
                                    option.addEventListener('click', function() {{
                                        console.log('🔵 Option clicked:', item.text);
                                        hiddenInput.value = item.id;
                                        displayArea.querySelector('.selected-text').textContent = item.text;
                                        dropdown.style.display = 'none';
                                        arrow.textContent = '▼';
                                        console.log('🟢 Selected:', item.text);
                                    }});
                                    
                                    optionsContainer.appendChild(option);
                                }});
                                
                                currentPage = nextPage;
                                hasNextPage = data.has_next || false;
                                console.log('🔵 Page', nextPage, 'loaded. Has next:', hasNextPage);
                            }} else {{
                                hasNextPage = false;
                                console.log('🔵 No more items available');
                            }}
                            
                            isLoading = false;
                        }})
                        .catch(error => {{
                            console.error('🔴 Error loading page', nextPage, ':', error);
                            isLoading = false;
                        }});
                    }}
                }});
                
                // Reset pagination when search changes
                searchInput.addEventListener('input', function() {{
                    currentPage = 1;
                    hasNextPage = true;
                }});
            }}
            
            console.log('🟢 Widget fully initialized:', config.widgetId);
            return true;
        }};
        
        console.log('🟢 Global function defined');
        
        // Multiple initialization strategies for Django admin compatibility
        
        // 1. Immediate execution
        console.log('🟢 Strategy 1: Immediate execution');
        if (document.readyState === 'loading') {{
            console.log('🟡 Document still loading, adding DOMContentLoaded listener');
            document.addEventListener('DOMContentLoaded', function() {{
                console.log('🟢 DOMContentLoaded fired, initializing widget');
                window.initInfiniteScrollWidget_{widget_id_safe}();
            }});
        }} else {{
            console.log('🟢 Document ready, initializing immediately');
            window.initInfiniteScrollWidget_{widget_id_safe}();
        }}
        
        // 2. Delayed execution for Django admin inlines
        console.log('🟢 Strategy 2: Delayed execution');
        setTimeout(() => {{
            console.log('🟡 Delayed init (100ms)');
            window.initInfiniteScrollWidget_{widget_id_safe}();
        }}, 100);
        setTimeout(() => {{
            console.log('🟡 Delayed init (500ms)');
            window.initInfiniteScrollWidget_{widget_id_safe}();
        }}, 500);
        
        // 3. Django admin inline form events (if django.jQuery is available)
        console.log('🟢 Strategy 3: Django admin events');
        if (typeof django !== 'undefined' && django.jQuery) {{
            console.log('🟢 Django jQuery found, setting up formset events');
            django.jQuery(document).on('formset:added', function(event, row) {{
                console.log('🟡 Django formset:added event detected');
                setTimeout(() => {{
                    console.log('🟡 Formset added - initializing widget');
                    window.initInfiniteScrollWidget_{widget_id_safe}();
                }}, 100);
            }});
        }} else {{
            console.log('🟡 Django jQuery not found');
        }}
        
        console.log('🟢 All initialization strategies set up for widget: {widget_id}');
        </script>
        ''', 
        widget_id=widget_id,
        widget_id_safe=widget_id.replace('-', '_').replace('__prefix__', 'prefix'),
        config_json=mark_safe(json.dumps(js_config))
        )
        
        logger.info(f"Rendering complete HTML for widget {widget_id}")
        return mark_safe(html + js)
    
    class Media:
        css = {
            'all': ('admin/css/infinite_scroll_widget.css',)
        }
        js = ('admin/js/infinite_scroll_widget.js',) 


class ImageManagementWidget(forms.Widget):
    def render(self, name, value, attrs=None, renderer=None):
        if not value:
            value = []

        image_previews = ""
        for s3_key in value:
            try:
                url = default_storage.url(s3_key)
                image_previews += f"""
                    <div style="display: block; margin-bottom: 20px; border: 1px solid #ddd; padding: 10px; max-width: 420px;">
                        <a href="{url}" target="_blank" rel="noopener noreferrer">
                            <img src="{url}" style="width: 100%; height: auto; display: block;" />
                        </a>
                        <br/>
                        <div style="margin-top: 5px;">
                            <label for="delete_{s3_key}">Delete:</label>
                            <input type="checkbox" name="{name}_delete" value="{s3_key}" id="delete_{s3_key}">
                        </div>
                    </div>
                """
            except Exception as e:
                logger.error(f"Error generating presigned URL for {s3_key}: {e}")
                image_previews += f"<p>Error loading image: {s3_key}</p>"

        upload_input = f'<br><p><strong>Upload new images:</strong></p><input type="file" name="{name}_upload" multiple>'

        hidden_input = f'<input type="hidden" name="{name}" value=\'{json.dumps(value)}\'>'

        return mark_safe(f"<div>{image_previews}</div>{upload_input}{hidden_input}")
