"""
Signals for the custom packages.
"""
import logging

from django.db.models.signals import post_save, pre_save, m2m_changed
from django.dispatch import receiver
from django.core.files.storage import default_storage

from packages.models import CustomActivity, CustomPackage, Package, PackageHotel, PackageRestaurant, Destination, TripjackHotels
from packages.services.activity_pinecone_service import activity_pinecone_service
from packages.services.hotel_pinecone_service import hotel_pinecone_service
from packages.services.destination_pinecone_service import destination_pinecone_service
from packages.utils.establishment_utils import get_place_details, download_image, clean_queries

logger = logging.getLogger(__name__)


def _populate_establishment_details(instance, package):
    """
    Populates the establishment instance with details from SerpApi.
    Requires the package to determine the location for the search query.
    """
    if not instance.name or not package or not package.destination:
        logger.warning(
            f"Cannot populate details for '{instance.name}'. Missing required package or destination information."
        )
        return

    location = package.destination.title

    logger.info(
        f"Fetching details for {instance.__class__._meta.verbose_name} '{instance.name}' in location '{location}'"
    )
    details = get_place_details(instance.name, location)

    if details:
        logger.info(f"Details found for '{instance.name}'. Populating instance.")
        instance.address = details.get('address')
        instance.phone = details.get('phone')
        instance.website = details.get('website')
        instance.rating = details.get('rating')
        instance.review_count = details.get('review_count')
        instance.description = details.get('description')
        instance.amenities = details.get('amenities')

        image_paths = []
        image_urls_from_api = details.get('image_urls', [])
        if image_urls_from_api:
            logger.info(f"Found {len(image_urls_from_api)} images for '{instance.name}'. Downloading...")
            sub_folder = 'hotels' if isinstance(instance, PackageHotel) else 'restaurants'
            for image_url in image_urls_from_api:
                image_content = download_image(image_url)
                if image_content:
                    destination_path = f"package/media/{sub_folder}/{instance.id}/{image_content.name}"
                    stored_path = default_storage.save(destination_path, image_content)
                    image_paths.append(stored_path)
        
        if image_paths:
            instance.image_urls = image_paths

        post_save.disconnect(establishment_post_save_handler, sender=instance.__class__)
        # Use update_fields to avoid triggering pre_save again
        try:
            # Save the instance with the new details
            instance.save(update_fields=['address', 'phone', 'website', 'rating', 'review_count', 'image_urls', 'description', 'amenities'])
        finally:
            # Reconnect the signal handler to ensure it works for subsequent, unrelated saves
            post_save.connect(establishment_post_save_handler, sender=instance.__class__)

        logger.info(f"Successfully populated and saved details for '{instance.name}'.")
    else:
        logger.warning(
            f"Could not retrieve details for {instance.__class__._meta.verbose_name}: {instance.name}"
        )


@receiver(pre_save, sender=PackageHotel)
@receiver(pre_save, sender=PackageRestaurant)
def establishment_pre_save_handler(sender, instance, **kwargs):
    """
    Handles pre_save signals for PackageHotel and PackageRestaurant.
    If an establishment's name is changed, it deactivates the old record,
    and a new one will be created.
    """
    if not instance.pk:
        return

    try:
        old_instance = sender.objects.get(pk=instance.pk)
    except sender.DoesNotExist:
        return

    if old_instance.name != instance.name:
        logger.info(
            f"Name changed for {sender._meta.verbose_name} {instance.pk}: "
            f"'{old_instance.name}' -> '{instance.name}'. "
            f"Deactivating old record and creating a new one."
        )
        old_instance.is_active = False
        old_instance.save(update_fields=['is_active'])
        
        instance.pk = None
        instance.id = None
        instance.is_active = True


@receiver(post_save, sender=PackageHotel)
@receiver(post_save, sender=PackageRestaurant)
def establishment_post_save_handler(sender, instance, created, **kwargs):
    """
    Handles post_save signals for PackageHotel and PackageRestaurant.
    Triggers detail population if an establishment is activated and associated
    with an active/published package, but details are missing.
    """
    if created:
        return  # Handled by m2m_changed when added to a package

    # For updates, check if an existing establishment was activated and needs details.
    if instance.is_active and not instance.address:
        package = instance.packages.filter(is_active=True, is_published=True).first()
        if package:
            logger.info(
                f"Establishment '{instance.name}' was activated and is missing details. "
                "Fetching now."
            )
            _populate_establishment_details(instance, package)


@receiver(m2m_changed, sender=Package.package_hotels.through)
@receiver(m2m_changed, sender=Package.package_restaurants.through)
@receiver(m2m_changed, sender=CustomPackage.package_hotels.through)
@receiver(m2m_changed, sender=CustomPackage.package_restaurants.through)
def establishments_added_to_package(sender, instance, action, pk_set, **kwargs):
    """
    When an establishment is added to a package, check if we need to fetch details.
    """
    if action == "post_add":
        package = instance
        logger.info(
            f"Establishments with pks {pk_set} added to package '{package.title}'. "
            "Checking if details need to be fetched."
        )

        if not package.is_active or not package.is_published:
            logger.info(
                f"Package '{package.title}' is not active/published. "
                "Skipping detail fetch for added establishments."
            )
            return

        EstablishmentModel = PackageHotel if sender == Package.package_hotels.through else PackageRestaurant

        for pk in pk_set:
            try:
                establishment = EstablishmentModel.objects.get(pk=pk)
                if establishment.is_active and not establishment.address:
                    logger.info(f"Fetching details for added establishment '{establishment.name}'")
                    _populate_establishment_details(establishment, package)
            except EstablishmentModel.DoesNotExist:
                logger.error(f"Could not find establishment with pk {pk} to process.")


@receiver(post_save, sender=CustomActivity)
def upsert_activity_to_pinecone(sender, instance, created, **kwargs):
    """
    Signal handler to upsert a CustomActivity instance to Pinecone after it's saved.
    """
    if instance.destination and instance.destination.partner and instance.destination.partner.external_id:
        namespace = str(instance.destination.partner.external_id)
        if (instance.is_active and instance.deleted_at is None):
            logger.debug(f"Upserting activity {instance.id} to Pinecone with namespace {namespace}")
            activity_pinecone_service.upsert_activity(instance, namespace)
        else:
            logger.debug(f"Deleting activity {instance.id} from Pinecone with namespace {namespace}")
            activity_pinecone_service.delete(str(instance.id), namespace)


@receiver(post_save, sender=TripjackHotels)
def upsert_hotel_to_pinecone(sender, instance, created, **kwargs):
    """
    Signal handler to upsert a Hotel instance to Pinecone after it's saved.
    """
    if instance.is_active and instance.deleted_at is None:
        destinations = instance.destinations.all()
        if not destinations.exists():
            logger.warning(f"TripjackHotel {instance.hotel_id} has no associated destinations. Cannot determine namespace for Pinecone.")
            return
        
        destination = destinations.first()

        # namespace (partner_id) will be same even for multiple destinations
        if destination and destination.partner and destination.partner.external_id:
            namespace = str(destination.partner.external_id)
            logger.debug(f"Upserting hotel {instance.id} to Pinecone with namespace {namespace}")
            hotel_pinecone_service.upsert_hotel(instance, namespace)
        else:
            logger.debug(f"Deleting hotel {instance.id} from Pinecone with namespace {namespace}")
            hotel_pinecone_service.delete(str(instance.id), namespace)


@receiver(post_save, sender=Destination)
def upsert_destination_to_pinecone(sender, instance, created, **kwargs):
    """
    Signal handler to upsert a Destination instance to Pinecone after it's saved.
    """
    if instance.partner and instance.partner.external_id:
        namespace = str(instance.partner.external_id)
        if (instance.is_active and instance.deleted_at is None):
            logger.debug(f"Upserting destination {instance.id} to Pinecone with namespace {namespace}")
            destination_pinecone_service.upsert_destination(instance, namespace)
        else:
            logger.debug(f"Deleting destination {instance.id} from Pinecone with namespace {namespace}")
            destination_pinecone_service.delete(str(instance.id), namespace)


@receiver(post_save, sender=Package)
@receiver(post_save, sender=CustomPackage)
def check_package_establishments_on_save(sender, instance, **kwargs):
    """
    When a package is saved, ensure its string-based hotel/restaurant
    lists are converted into proper M2M relationships. Then, if the package
    is active and published, check its establishments and populate any
    that are missing details.
    """
    package = instance

    # Step 1: Sync ArrayFields with M2M relationships for legacy or bulk-added data.
    logger.info(f"Syncing establishment ArrayFields for package '{package.title}'...")

    # Process hotels (skip for custom packages)
    if hasattr(package, 'hotels') and not isinstance(instance, CustomPackage):
        hotel_names = {clean_queries(h) for h in package.hotels if h}
        hotels_to_set = []
        if hotel_names:
            for name in hotel_names:
                try:
                    hotel, _ = PackageHotel.objects.get_or_create(name=name)
                    hotels_to_set.append(hotel)
                except PackageHotel.MultipleObjectsReturned:
                    logger.warning(f"Duplicate PackageHotel found for name '{name}'. Using the first one.")
                    hotel = PackageHotel.objects.filter(name=name).first()
                    if hotel:
                        hotels_to_set.append(hotel)
        package.package_hotels.set(hotels_to_set)

    # Process restaurants
    if hasattr(package, 'popular_restaurants'):
        restaurant_names = {clean_queries(r) for r in package.popular_restaurants if r}
        restaurants_to_set = []
        if restaurant_names:
            for name in restaurant_names:
                try:
                    restaurant, _ = PackageRestaurant.objects.get_or_create(name=name)
                    restaurants_to_set.append(restaurant)
                except PackageRestaurant.MultipleObjectsReturned:
                    logger.warning(f"Duplicate PackageRestaurant found for name '{name}'. Using the first one.")
                    restaurant = PackageRestaurant.objects.filter(name=name).first()
                    if restaurant:
                        restaurants_to_set.append(restaurant)
        package.package_restaurants.set(restaurants_to_set)

    # Step 2: Populate details if the package is active and published.
    if package.is_active and package.is_published:
        logger.info(
            f"Package '{package.title}' saved and is active/published. "
            "Checking its establishments for missing details."
        )

        establishments = list(package.package_hotels.filter(is_active=True, address__isnull=True)) + \
                       list(package.package_restaurants.filter(is_active=True, address__isnull=True))

        if establishments:
            logger.info(f"Found {len(establishments)} establishments with missing details for package '{package.title}'.")
            for establishment in establishments:
                logger.info(f"Fetching details for '{establishment.name}' in package '{instance.title}'")
                _populate_establishment_details(establishment, instance)
        else:
            logger.info(f"All establishments for package '{package.title}' are up-to-date.")
