from django.urls import path, include
from .views import InfiniteScrollDataView, GetDestinationActivitiesView, GetDestinationHotelsView


urlpatterns = [
    # Package V1 URLs
    path("v1/", include("packages.api.v1.urls")),
    
    # Admin widget AJAX endpoints
    path("infinite-scroll-data/", InfiniteScrollDataView.as_view(), name='infinite_scroll_data'),
    path("get-destination-activities/", GetDestinationActivitiesView.as_view(), name='get_destination_activities'),
    path("get-destination-hotels/", GetDestinationHotelsView.as_view(), name='get_destination_hotels'),
]
