"""
Activity Pinecone Service.

This service is used to manage the activities data in the Pinecone index.
"""
from typing import Dict
from packages.models import CustomActivity
from packages.services.abstract_pinecone_service import AbstractPineconeService


ACTIVITY_PINECONE_INDEX_NAME = "rag-activities-openai-v1-index"


class ActivityPineconeService(AbstractPineconeService[CustomActivity]):
    """
    A specialized Pinecone service for managing activities, inheriting from
    BasePineconeService.
    """

    def __init__(self):
        """Initializes the ActivityPineconeService."""
        super().__init__(
            name="ActivityPineconeService", index_name=ACTIVITY_PINECONE_INDEX_NAME
        )

    def _prepare_text_for_embedding(self, data: CustomActivity) -> str:
        """
        Creates a single, semantically rich string from activity data for embedding.
        """
        # Get category names from M2M relationships
        categories = self._get_activity_categories(data)

        parts = [
            f"Title: {data.title}",
            f"Summary: {data.abstract}",
            f"Description: {data.description}",
            f"Highlights: {data.highlights}",
            f"Type: {data.activity_type}",
            f"Cancellation Policy: {data.cancellation_policy_text}",
            f"Categories: {', '.join(categories) if categories else ''}",
            f"Activity Type: {data.activity_type}",
        ]

        return ". ".join(p for p in parts if p)

    def _extract_metadata(self, data: CustomActivity) -> Dict:
        """
        Extracts structured metadata from the activity data for filtering and display.
        """

        metadata = {
            "id": data.id,
            "title": data.title,
            "tour_id": data.tour_id,
            "city": data.destination.title if data.destination else None,
            "price": float(data.price) if data.price is not None else None,
            "overall_rating": float(data.overall_rating) if data.overall_rating is not None else None,
            "number_of_ratings": int(data.number_of_ratings) if data.number_of_ratings is not None else None,
            "persona": list(data.persona) if data.persona else None,
            "categories": self._get_activity_categories(data),
        }

        return {k: v for k, v in metadata.items() if v is not None}

    def _get_activity_categories(self, data: CustomActivity) -> list:
        """
        Extracts category names from activity-category relationships.
        Handles both custom activity categories and system categories.
        """
        categories = []

        # Get custom activity categories through M2M relationship
        if hasattr(data, 'categories'):
            for activity_category in data.activity_categories.all():
                if activity_category.category and activity_category.category.name:
                    categories.append(activity_category.category.name)

        # Get system categories through M2M relationship
        if hasattr(data, 'system_categories'):
            for system_category_relation in data.system_category_relations.all():
                if system_category_relation.category and system_category_relation.category.title:
                    categories.append(system_category_relation.category.title)

        return list(set(categories))  # Remove duplicates

    def upsert_activity(self, activity: CustomActivity, namespace: str):
        """Upserts a single activity to the Pinecone index."""
        return self.upsert(activity, namespace)


activity_pinecone_service = ActivityPineconeService()
