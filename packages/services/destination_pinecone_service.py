"""
Destination Pinecone Service.

This service is used to manage the destination data in the Pinecone index.
It provides semantic search capabilities for travel destinations.
"""
import calendar
from typing import Dict, List
from packages.models import Destination
from packages.services.abstract_pinecone_service import AbstractPineconeService


DESTINATION_PINECONE_INDEX_NAME = "rag-destinations-openai-v1-index"


class DestinationPineconeService(AbstractPineconeService[Destination]):
    """
    A specialized Pinecone service for managing destinations, inheriting from
    AbstractPineconeService.
    """

    def __init__(self):
        """Initializes the DestinationPineconeService."""
        super().__init__(
            name="DestinationPineconeService", index_name=DESTINATION_PINECONE_INDEX_NAME
        )

    def _prepare_text_for_embedding(self, data: Destination) -> str:
        """
        Creates a single, semantically rich string from destination data for embedding.
        """
        parts = []
        best_time_str = self._format_best_time(data.best_time_to_visit)
        best_time_str = ", ".join(best_time_str) if best_time_str else ""

        if data.title:
            parts.append(f"Title: {data.title}")
        if data.description:
            parts.append(f"Description: {data.description}")
        if best_time_str:
            parts.append(f"Best time to visit: {best_time_str}")
        categories = self._get_destination_categories(data)
        if categories:
            parts.append(f"Categories: {', '.join(categories)}")

        return ". ".join(filter(None, parts))

    def _extract_metadata(self, data: Destination) -> Dict:
        """
        Extracts structured metadata from the destination data for filtering and display.
        """
        metadata = {
            "id": data.id,
            "title": data.title,
            "best_time_to_visit": self._format_best_time(data.best_time_to_visit),
            "categories": self._get_destination_categories(data),
        }

        return {k: v for k, v in metadata.items() if v is not None}

    def _format_best_time(self, best_time) -> str:
        if isinstance(best_time, dict):
            months = best_time.get("months")
            if isinstance(months, list):
                # Convert numbers to month names, ignoring invalid ones
                month_names = [
                    calendar.month_name[m]
                    for m in months
                    if isinstance(m, int) and 1 <= m <= 12
                ]
                return month_names

        elif isinstance(best_time, list):
            # If it's a raw list like [1, 2, 3]
            month_names = [
                calendar.month_name[m]
                for m in best_time
                if isinstance(m, int) and 1 <= m <= 12
            ]
            return month_names

        return best_time

    def _get_destination_categories(self, data: Destination) -> List[str]:
        """
        Extracts category names from destination-category relationships.
        """
        categories = []
        if hasattr(data, 'categories'):
            # Get categories through the M2M relationship
            for dest_category in data.categories.all():
                if dest_category.category and dest_category.category.title:
                    categories.append(dest_category.category.title)
        return categories

    def upsert_destination(self, destination: Destination, namespace: str):
        """Upserts a single destination to the Pinecone index."""
        return self.upsert(destination, namespace)


# Global service instance
destination_pinecone_service = DestinationPineconeService()
