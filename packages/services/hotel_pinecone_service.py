"""
Hotel Pinecone Service.

This service is used to manage the hotel data in the Pinecone index.
It provides semantic search capabilities for hotels from the TripJack platform.
"""
from typing import Dict, List
from packages.models import TripjackHotels
from packages.services.abstract_pinecone_service import AbstractPineconeService


HOTEL_PINECONE_INDEX_NAME = "rag-hotels-openai-v1-index"


class HotelPineconeService(AbstractPineconeService[TripjackHotels]):
    """
    A specialized Pinecone service for managing hotels, inheriting from
    AbstractPineconeService.
    """

    def __init__(self):
        """Initializes the HotelPineconeService."""
        super().__init__(
            name="HotelPineconeService", index_name=HOTEL_PINECONE_INDEX_NAME
        )

    def _prepare_text_for_embedding(self, data: TripjackHotels) -> str:
        """
        Creates a single, semantically rich string from hotel data for embedding.
        """
        parts = []

        # Hotel name - most important for identification
        if data.name:
            parts.append(f"Name: {data.name}")

        # Description - rich content for semantic matching
        if data.description:
            parts.append(f"Description: {data.description}")

        # Facilities/Amenities - important for feature-based search
        amenities_list = list(set(data.amenities)) if data.amenities else []
        if amenities_list:
            parts.append(f"Facilities: {', '.join(amenities_list)}")

        # Address information - crucial for location-based search
        address_str = self._format_address(data)
        if address_str:
            parts.append(f"Address: {address_str}")

        # Rating information - important for quality assessment
        if data.star_rating:
            parts.append(f"Rating: {data.star_rating} stars")

        return ". ".join(filter(None, parts))

    def _extract_metadata(self, data: TripjackHotels) -> Dict:
        """
        Extracts structured metadata from the hotel data for filtering and display.
        """
        metadata = {
            "id": data.hotel_id,
            "name": data.name,
            "rating": data.star_rating,
            "base_price": float(data.base_price) if data.base_price is not None else None,
        }

        # Add address metadata with proper property access
        metadata.update({
            "city": data.city_name,
            "country": data.country_name,
            "postal_code": data.postal_code,
        })

        # Add destination metadata
        destination = data.destinations.first()
        if destination:
            metadata.update({
                "destination": destination.title,
            })

        # Filter out None values
        return {k: v for k, v in metadata.items() if v is not None}

    def _format_address(self, data: TripjackHotels) -> str:
        """
        Formats the hotel address into a readable string.
        """
        address_parts = []
        
        # Add address line if available
        if data.address_line:
            address_parts.append(data.address_line)
            
        # Add city, state, country using properties
        if data.city_name:
            address_parts.append(data.city_name)
        if data.country_name:
            address_parts.append(data.country_name)

        # Add postal code if available
        if data.postal_code:
            address_parts.append(data.postal_code)

        return ", ".join(filter(None, address_parts))

    def upsert_hotel(self, hotel: TripjackHotels, namespace: str):
        """Upserts a single hotel to the Pinecone index."""
        return self.upsert(hotel, namespace)

# Global service instance
hotel_pinecone_service = HotelPineconeService()
