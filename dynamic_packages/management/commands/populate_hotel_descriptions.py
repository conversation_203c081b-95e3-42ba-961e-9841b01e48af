import asyncio
import json
import logging
import os
import sys

from django.core.management.base import BaseCommand
from django.db.models import Q
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import JsonOutputParser
from langchain_core.exceptions import OutputParserException
from langchain_openai import ChatOpenAI

# Assuming 'Hotel' is your Django model
from dynamic_packages.models import Hotel

# --- Logger Setup ---
log_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

# 1. Console Handler
console_handler = logging.StreamHandler(sys.stdout)
console_handler.setFormatter(log_formatter)
logger.addHandler(console_handler)

# 2. File Handler
file_handler = logging.FileHandler("hotel_description_update.log")
file_handler.setFormatter(log_formatter)
logger.addHandler(file_handler)


class Command(BaseCommand):
    help = "Populates hotel descriptions one-by-one using AI and logs output to a file."

    def _get_description_source(self, hotel: Hotel) -> str:
        """
        Extracts and cleans the description source from the hotel's meta_info.
        If the source is a JSON string, it parses and combines the relevant data.
        """
        if isinstance(hotel.meta_info, dict):
            detail_data = hotel.meta_info.get("detail_data", {})
            if isinstance(detail_data, dict):
                hotel_data = detail_data.get("hotel", {})
                if isinstance(hotel_data, dict):
                    raw_description = hotel_data.get("des", "")
                    if not raw_description or not isinstance(raw_description, str):
                        return ""
                    return raw_description

        return ""

    async def _generate_description(self, hotel_name: str, content: str) -> str:
        """
        Generates a short description from the given content using an AI model.
        """
        logger.info(f"Hitting OpenAI for '{hotel_name}'...")

        prompt = ChatPromptTemplate.from_template(
            "Generate a short and appealing description (around 50-60 words) for a hotel based on the following data. "
            "The description should be suitable for a travel website. "
            "Focus on the guest experience, highlighting key features from the rooms, location, and amenities. "
            "Avoid just listing facts. "
            "Content: {content}\n\n"
            "Output the description in a JSON object with a single key: 'description'."
        )

        api_key = os.environ.get("OPENAI_API_KEY")
        if not api_key:
            logger.error("FATAL: OPENAI_API_KEY environment variable not set.")
            return ""

        model = ChatOpenAI(temperature=0.7, model="gpt-4o-mini", api_key=api_key)
        parser = JsonOutputParser()
        chain = prompt | model | parser

        try:
            response = await chain.ainvoke({"content": content})
            description = response.get("description", "")
            if description:
                logger.info(f"Successfully generated description for '{hotel_name}'.")
            else:
                logger.warning(f"AI returned an empty description for '{hotel_name}'.")
            return description
        except OutputParserException as e:
            logger.error(f"Error parsing AI response for '{hotel_name}': {e}")
            return ""
        except Exception as e:
            logger.error(f"An unexpected error occurred while generating description for '{hotel_name}': {e}")
            return ""

    def handle(self, *args, **options):
        asyncio.run(self._async_handle(*args, **options))

    async def _async_handle(self, *args, **options):
        logger.info("Starting sequential script to populate hotel descriptions...")

        hotels_to_check = Hotel.objects.filter(
            Q(description__isnull=True) | Q(description__exact=''),
            meta_info__isnull=False
        ).exclude(meta_info={})

        total_to_check = await hotels_to_check.acount()
        if total_to_check == 0:
            logger.info("No hotels to process. All set!")
            return

        logger.info(f"Found {total_to_check} hotels to process one by one.")

        updated_count = 0
        failed_count = 0
        skipped_count = 0

        async for hotel in hotels_to_check:
            logger.info(f"--- Processing hotel: {hotel.name} (ID: {hotel.id}) ---")
            
            description_content = self._get_description_source(hotel)

            if not description_content:
                logger.info(f"Skipping '{hotel.name}' due to no valid content in meta_info.")
                skipped_count += 1
                continue

            # 1. HIT OPENAI
            new_description = await self._generate_description(hotel.name, description_content)

            # 2. SAVE
            if new_description:
                hotel.description = new_description
                await hotel.asave(update_fields=['description'])
                logger.info(f"SUCCESS: Description for '{hotel.name}' has been saved to the database.")
                updated_count += 1
            else:
                logger.warning(f"FAILURE: Could not generate or save description for '{hotel.name}'.")
                failed_count += 1
            
            logger.info("-" * 25)

        logger.info("--- Script Execution Summary ---")
        logger.info(f"Hotels checked: {total_to_check}")
        logger.info(f"Successfully updated: {updated_count}")
        logger.info(f"Failed to update: {failed_count}")
        logger.info(f"Skipped (no content): {skipped_count}")
        logger.info("--- Finished. Log saved to hotel_description_update.log ---")