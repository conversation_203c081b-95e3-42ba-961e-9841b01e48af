# Generated by Django 4.2 on 2025-08-18 11:32

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('packages', '0035_custompackageitinerary_and_more'),
        ('dynamic_packages', '0004_hotel_hotel_dest_active_rating_idx_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='facility',
            name='hotel',
        ),
        migrations.RemoveField(
            model_name='facility',
            name='room',
        ),
        migrations.RemoveField(
            model_name='hotel',
            name='address',
        ),
        migrations.RemoveField(
            model_name='hotel',
            name='destination',
        ),
        migrations.RemoveField(
            model_name='hoteladdress',
            name='city',
        ),
        migrations.RemoveField(
            model_name='hoteladdress',
            name='country',
        ),
        migrations.RemoveField(
            model_name='hoteladdress',
            name='state',
        ),
        migrations.RemoveField(
            model_name='hotelcontact',
            name='hotel',
        ),
        migrations.Remove<PERSON>ield(
            model_name='hotelmedia',
            name='hotel',
        ),
        migrations.RemoveField(
            model_name='room',
            name='hotel',
        ),
        migrations.RemoveField(
            model_name='roommedia',
            name='room',
        ),
        migrations.DeleteModel(
            name='City',
        ),
        migrations.DeleteModel(
            name='Country',
        ),
        migrations.DeleteModel(
            name='Facility',
        ),
        migrations.DeleteModel(
            name='Hotel',
        ),
        migrations.DeleteModel(
            name='HotelAddress',
        ),
        migrations.DeleteModel(
            name='HotelContact',
        ),
        migrations.DeleteModel(
            name='HotelMedia',
        ),
        migrations.DeleteModel(
            name='Room',
        ),
        migrations.DeleteModel(
            name='RoomMedia',
        ),
        migrations.DeleteModel(
            name='State',
        ),
    ]
