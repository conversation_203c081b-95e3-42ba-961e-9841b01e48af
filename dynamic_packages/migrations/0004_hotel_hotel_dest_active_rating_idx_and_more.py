# Generated by Django 4.2 on 2025-07-28 16:40

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('dynamic_packages', '0003_alter_city_options_alter_country_options_and_more'),
    ]

    operations = [
        migrations.AddIndex(
            model_name='hotel',
            index=models.Index(fields=['destination_id', 'is_active', 'rating'], name='hotel_dest_active_rating_idx'),
        ),
        migrations.AddIndex(
            model_name='hotel',
            index=models.Index(fields=['tripjack_static_id'], name='hotel_tripjack_static_idx'),
        ),
        migrations.AddIndex(
            model_name='hotel',
            index=models.Index(fields=['tripjack_hotel_id'], name='hotel_tripjack_hotel_idx'),
        ),
    ]
