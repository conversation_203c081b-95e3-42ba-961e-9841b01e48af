# Generated by Django 4.2 on 2025-08-24 17:25

import django.core.validators
from django.db import migrations, models
import django_better_admin_arrayfield.models.fields
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('dynamic_packages', '0005_remove_facility_hotel_remove_facility_room_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='AIGeneratedPackage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('restored_at', models.DateTimeField(blank=True, null=True)),
                ('transaction_id', models.UUIDField(blank=True, null=True)),
                ('external_id', models.UUIDField(default=uuid.uuid4, editable=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('destination_name', models.CharField(db_index=True, max_length=255)),
                ('package_title', models.CharField(max_length=255)),
                ('package_no', models.CharField(max_length=255)),
                ('duration', models.CharField(blank=True, max_length=255, null=True)),
                ('about_this_tour', models.TextField(blank=True, null=True)),
                ('best_time_to_visit', models.TextField(blank=True, null=True)),
                ('cultural_info', models.TextField(blank=True, null=True)),
                ('destination_safety', models.TextField(blank=True, null=True)),
                ('exclusions', django_better_admin_arrayfield.models.fields.ArrayField(base_field=models.CharField(max_length=255), blank=True, null=True, size=None)),
                ('important_notes', django_better_admin_arrayfield.models.fields.ArrayField(base_field=models.CharField(max_length=255), blank=True, null=True, size=None)),
                ('what_to_pack', models.TextField(blank=True, null=True)),
                ('what_to_shop', models.TextField(blank=True, null=True)),
                ('rating', models.DecimalField(blank=True, decimal_places=1, max_digits=2, null=True, validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(5.0)])),
                ('rating_description', models.TextField(blank=True, null=True)),
                ('itinerary', models.JSONField(blank=True, null=True)),
                ('media', models.JSONField(blank=True, null=True)),
                ('pricing_estimate', models.JSONField(blank=True, null=True)),
                ('highlights', models.JSONField(blank=True, null=True)),
                ('inclusions', models.JSONField(blank=True, null=True)),
                ('addons', models.JSONField(blank=True, null=True)),
            ],
            options={
                'verbose_name': 'AI Generated Package',
                'verbose_name_plural': 'AI Generated Packages',
                'ordering': ['-created_at'],
            },
        ),
    ]
