# Generated by Django 4.2 on 2025-07-21 10:03

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ("dynamic_packages", "0002_alter_facility_options"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="city",
            options={"verbose_name": "City", "verbose_name_plural": "Cities"},
        ),
        migrations.AlterModelOptions(
            name="country",
            options={"verbose_name": "Country", "verbose_name_plural": "Countries"},
        ),
        migrations.AlterModelOptions(
            name="state",
            options={"verbose_name": "State", "verbose_name_plural": "States"},
        ),
        migrations.RenameField(
            model_name="hotel",
            old_name="tripjack_search_hisid",
            new_name="tripjack_hotel_id",
        ),
        migrations.RenameField(
            model_name="hotel",
            old_name="tripjack_hid",
            new_name="tripjack_static_id",
        ),
    ]
