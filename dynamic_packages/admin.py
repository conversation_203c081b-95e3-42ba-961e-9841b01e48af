from django.contrib import admin
from django.utils.html import format_html
from django.utils.safestring import mark_safe
from django.urls import reverse
from django.core.files.storage import default_storage
import json
import logging

from .models import AIGeneratedPackage

logger = logging.getLogger(__name__)


@admin.register(AIGeneratedPackage)
class AIGeneratedPackageAdmin(admin.ModelAdmin):
    """
    Readonly admin for AI Generated Packages - View Only
    Displays comprehensive package information including media, itinerary, and all JSON fields
    """
    
    list_display = [
        'package_title', 
        'destination_name', 
        'package_no',
        'duration',
        'rating_display',
        'has_media',
        'has_itinerary',
        'created_at'
    ]
    
    list_filter = [
        'created_at',
        ('destination_name', admin.AllValuesFieldListFilter),
    ]
    
    search_fields = [
        'package_title',
        'destination_name', 
        'package_no',
        'about_this_tour'
    ]
    
    readonly_fields = [
        'id',
        'destination_name',
        'package_title', 
        'package_no',
        'duration',
        'about_this_tour',
        'best_time_to_visit',
        'cultural_info',
        'destination_safety',
        'exclusions_display',
        'important_notes_display',
        'what_to_pack',
        'what_to_shop',
        'rating',
        'rating_description',
        'itinerary_display',
        'media_display',
        'pricing_estimate_display',
        'highlights_display',
        'inclusions_display', 
        'addons_display',
        'created_at',
        'updated_at',
        'is_active'
    ]
    
    fieldsets = (
        ('Package Information', {
            'fields': (
                'id',
                'destination_name',
                'package_title',
                'package_no', 
                'duration',
                'rating',
                'rating_description',
                'pricing_estimate_display'
            ),
            'classes': ('wide',),
        }),
        ('Package Description', {
            'fields': (
                'about_this_tour',
                'best_time_to_visit',
                'cultural_info',
                'destination_safety',
                'what_to_pack',
                'what_to_shop'
            ),
            'classes': ('wide',),
        }),
        ('Package Details', {
            'fields': (
                'highlights_display',
                'inclusions_display',
                'exclusions_display',
                'addons_display',
                'important_notes_display'
            ),
            'classes': ('wide',),
        }),
        ('Itinerary', {
            'fields': ('itinerary_display',),
            'classes': ('wide',),
        }),
        ('Media', {
            'fields': ('media_display',),
            'classes': ('wide',),
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at', 'is_active'),
            'classes': ('wide', 'collapse'),
        }),
    )
    
    # Custom display methods
    def rating_display(self, obj):
        """Display rating as simple number"""
        if obj.rating:
            return f"{obj.rating}/5"
        return '-'
    rating_display.short_description = 'Rating'
    rating_display.admin_order_field = 'rating'
    
    def has_media(self, obj):
        """Check if package has media with count"""
        if obj.media and isinstance(obj.media, (list, dict)):
            if isinstance(obj.media, list):
                count = len(obj.media)
                return f"{count} image{'s' if count != 1 else ''}"
            elif isinstance(obj.media, dict):
                return "1 media item"
        return "No media"
    has_media.short_description = 'Media'
    
    def has_itinerary(self, obj):
        """Check if package has itinerary"""
        if obj.itinerary:
            if isinstance(obj.itinerary, dict) and 'days' in obj.itinerary:
                return f"Yes ({len(obj.itinerary['days'])} days)"
            elif isinstance(obj.itinerary, list):
                return f"Yes ({len(obj.itinerary)} days)"
            elif isinstance(obj.itinerary, dict):
                return "Yes"
        return "No"
    has_itinerary.short_description = 'Has Itinerary'
    
    def exclusions_display(self, obj):
        """Display exclusions as formatted list"""
        if obj.exclusions:
            return self._format_array_field(obj.exclusions, 'Exclusions')
        return '-'
    exclusions_display.short_description = 'Exclusions'
    
    def important_notes_display(self, obj):
        """Display important notes as formatted list"""
        if obj.important_notes:
            return self._format_array_field(obj.important_notes, 'Important Notes')
        return '-'
    important_notes_display.short_description = 'Important Notes'
    
    def highlights_display(self, obj):
        """Display highlights as bullet points"""
        if obj.highlights:
            return self._format_json_array_as_bullets(obj.highlights)
        return '-'
    highlights_display.short_description = 'Package Highlights'
    
    def inclusions_display(self, obj):
        """Display inclusions as bullet points"""
        if obj.inclusions:
            return self._format_json_array_as_bullets(obj.inclusions)
        return '-'
    inclusions_display.short_description = 'Package Inclusions'
    
    def addons_display(self, obj):
        """Display addons as bullet points"""
        if obj.addons:
            return self._format_json_array_as_bullets(obj.addons)
        return '-'
    addons_display.short_description = 'Package Add-ons'
    
    def itinerary_display(self, obj):
        """Display itinerary with day-by-day breakdown"""
        if not obj.itinerary:
            return '-'
        
        try:
            if isinstance(obj.itinerary, str):
                itinerary_data = json.loads(obj.itinerary)
            else:
                itinerary_data = obj.itinerary
            
            html_parts = []
            
            # Handle the structure with 'days' key
            if isinstance(itinerary_data, dict) and 'days' in itinerary_data:
                days_data = itinerary_data['days']
            elif isinstance(itinerary_data, list):
                days_data = itinerary_data
            else:
                return self._format_json_field(obj.itinerary, 'Itinerary')
            
            for day in days_data:
                if isinstance(day, dict):
                    day_number = day.get('day_number', '?')
                    html_parts.append(f'<div><strong>Day {day_number}</strong><br>')
                    
                    # Hotel information
                    if 'hotel' in day and isinstance(day['hotel'], dict):
                        hotel = day['hotel']
                        hotel_name = hotel.get('name', 'Hotel')
                        star_rating = hotel.get('star_rating', '')
                        address = hotel.get('address', '')
                        
                        html_parts.append(f'<strong>Hotel:</strong> {hotel_name}')
                        if star_rating:
                            html_parts.append(f' ({star_rating} star)')
                        if address:
                            html_parts.append(f'<br><em>{address}</em>')
                        html_parts.append('<br>')
                    
                    # Activities information
                    if 'activities' in day and isinstance(day['activities'], list):
                        html_parts.append('<strong>Activities:</strong><ul>')
                        for activity in day['activities']:
                            if isinstance(activity, dict):
                                name = activity.get('name', 'Activity')
                                duration = activity.get('duration', '')
                                timing = activity.get('timing', '')
                                description = activity.get('description', '')
                                
                                html_parts.append(f'<li>{name}')
                                if duration:
                                    html_parts.append(f' ({duration})')
                                if timing:
                                    html_parts.append(f' - {timing}')
                                if description:
                                    html_parts.append(f'<br><em>{description}</em>')
                                html_parts.append('</li>')
                        html_parts.append('</ul>')
                    
                    html_parts.append('</div><br>')
            
            return mark_safe(''.join(html_parts))
                
        except (json.JSONDecodeError, TypeError, AttributeError) as e:
            return f'Error displaying itinerary: {str(e)}'
            
    itinerary_display.short_description = 'Package Itinerary'
    
    def media_display(self, obj):
        """Display media with thumbnails like TripJack hotels - readonly view"""
        if not obj.media:
            return '-'
        
        try:
            if isinstance(obj.media, str):
                media_data = json.loads(obj.media)
            else:
                media_data = obj.media
            
            if isinstance(media_data, list):
                html_parts = ['<div style="max-width: 100%;">']
                
                for i, media_item in enumerate(media_data):
                    if isinstance(media_item, dict):
                        # Handle structure with 'file' key (S3 paths)
                        file_path = media_item.get('file', '')
                        title = media_item.get('title', f'Media {i+1}')
                        file_type = media_item.get('file_type', 'unknown')
                        
                        if file_path:
                            try:
                                # Get S3 URL like TripJack hotels do
                                url = default_storage.url(file_path)
                                html_parts.append(f'''
                                    <div style="display: inline-block; margin: 10px; border: 1px solid #ddd; padding: 10px; max-width: 200px; text-align: center;">
                                        <div style="margin-bottom: 5px;">
                                            <strong>{title}</strong><br>
                                            <small>({file_type})</small>
                                        </div>
                                        <a href="{url}" target="_blank" rel="noopener noreferrer">
                                            <img src="{url}" style="max-width: 180px; max-height: 120px; object-fit: cover; display: block; margin: 0 auto;" 
                                                 alt="{title}" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';" />
                                            <div style="display: none; padding: 20px; background: #f5f5f5; text-align: center;">
                                                <small>Preview not available</small><br>
                                                <strong>View File</strong>
                                            </div>
                                        </a>
                                        <div style="margin-top: 5px; font-size: 11px; color: #666;">
                                            Click to view full size
                                        </div>
                                    </div>
                                ''')
                            except Exception as e:
                                logger.error(f"Error generating URL for {file_path}: {e}")
                                html_parts.append(f'''
                                    <div style="display: inline-block; margin: 10px; border: 1px solid #ddd; padding: 10px; max-width: 200px; text-align: center; background: #f9f9f9;">
                                        <strong>{title}</strong><br>
                                        <small>({file_type})</small><br>
                                        <em>Error loading: {file_path}</em>
                                    </div>
                                ''')
                        elif media_item.get('url', ''):  # Fallback to 'url' key
                            url = media_item.get('url')
                            html_parts.append(f'''
                                <div style="display: inline-block; margin: 10px; border: 1px solid #ddd; padding: 10px; max-width: 200px; text-align: center;">
                                    <div style="margin-bottom: 5px;">
                                        <strong>{title}</strong><br>
                                        <small>({file_type})</small>
                                    </div>
                                    <a href="{url}" target="_blank" rel="noopener noreferrer">
                                        <img src="{url}" style="max-width: 180px; max-height: 120px; object-fit: cover; display: block; margin: 0 auto;" 
                                             alt="{title}" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';" />
                                        <div style="display: none; padding: 20px; background: #f5f5f5; text-align: center;">
                                            <small>Preview not available</small><br>
                                            <strong>View File</strong>
                                        </div>
                                    </a>
                                    <div style="margin-top: 5px; font-size: 11px; color: #666;">
                                        Click to view full size
                                    </div>
                                </div>
                            ''')
                        else:
                            html_parts.append(f'''
                                <div style="display: inline-block; margin: 10px; border: 1px solid #ddd; padding: 10px; max-width: 200px; text-align: center; background: #f9f9f9;">
                                    <strong>{title}</strong><br>
                                    <small>({file_type})</small><br>
                                    <em>No file path available</em>
                                </div>
                            ''')
                    elif isinstance(media_item, str):
                        # Handle direct string URLs/paths
                        try:
                            url = default_storage.url(media_item)
                            html_parts.append(f'''
                                <div style="display: inline-block; margin: 10px; border: 1px solid #ddd; padding: 10px; max-width: 200px; text-align: center;">
                                    <div style="margin-bottom: 5px;">
                                        <strong>Media {i+1}</strong>
                                    </div>
                                    <a href="{url}" target="_blank" rel="noopener noreferrer">
                                        <img src="{url}" style="max-width: 180px; max-height: 120px; object-fit: cover; display: block; margin: 0 auto;" 
                                             alt="Media {i+1}" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';" />
                                        <div style="display: none; padding: 20px; background: #f5f5f5; text-align: center;">
                                            <small>Preview not available</small><br>
                                            <strong>View File</strong>
                                        </div>
                                    </a>
                                </div>
                            ''')
                        except Exception as e:
                            html_parts.append(f'<div style="margin: 5px; padding: 5px;">Media {i+1}: {media_item}</div>')
                
                html_parts.append('</div>')
                return mark_safe(''.join(html_parts))
            else:
                return str(media_data)
            
        except (json.JSONDecodeError, TypeError, AttributeError) as e:
            return f'Error displaying media: {str(e)}'
    
    media_display.short_description = 'Package Media'
    
    def pricing_estimate_display(self, obj):
        """Display pricing estimate in a simple way"""
        if not obj.pricing_estimate:
            return '-'
        try:
            if isinstance(obj.pricing_estimate, str):
                data = json.loads(obj.pricing_estimate)
            else:
                data = obj.pricing_estimate
            return str(data)
        except (json.JSONDecodeError, TypeError) as e:
            return f'Error displaying pricing: {str(e)}'
    pricing_estimate_display.short_description = 'Pricing Estimate'
    
    # Helper methods
    def _format_array_field(self, array_data, field_name):
        """Helper to format ArrayField data as simple bullets"""
        if not array_data:
            return '-'
        
        html_parts = ['<ul>']
        for item in array_data[:10]:  # Limit to first 10 items
            html_parts.append(f'<li>{item}</li>')
        
        if len(array_data) > 10:
            html_parts.append(f'<li><em>... and {len(array_data) - 10} more items</em></li>')
        
        html_parts.append('</ul>')
        return mark_safe(''.join(html_parts))
    
    def _format_json_array_as_bullets(self, json_data):
        """Helper to format JSON array with text/icon_class structure as bullets"""
        if not json_data:
            return '-'
        
        try:
            if isinstance(json_data, str):
                data = json.loads(json_data)
            else:
                data = json_data
            
            if isinstance(data, list):
                html_parts = ['<ul>']
                for item in data:
                    if isinstance(item, dict) and 'text' in item:
                        html_parts.append(f'<li>{item["text"]}</li>')
                    else:
                        html_parts.append(f'<li>{str(item)}</li>')
                html_parts.append('</ul>')
                return mark_safe(''.join(html_parts))
            else:
                return str(data)
                
        except (json.JSONDecodeError, TypeError) as e:
            return f'Error displaying data: {str(e)}'
    
    def _format_json_field(self, json_data, field_name):
        """Helper to format JSON field data simply"""
        if not json_data:
            return '-'
        
        try:
            if isinstance(json_data, str):
                data = json.loads(json_data)
            else:
                data = json_data
            
            return str(data)
        except (json.JSONDecodeError, TypeError) as e:
            return f'Error displaying {field_name}: {str(e)}'
    
    # Permission methods - Make completely readonly
    def has_add_permission(self, request):
        """Disable add permission - packages are AI generated only"""
        return False
    
    def has_change_permission(self, request, obj=None):
        """Disable change permission - packages are readonly"""
        return False
    
    def has_delete_permission(self, request, obj=None):
        """Disable delete permission - packages are readonly"""
        return False
    
    def has_view_permission(self, request, obj=None):
        """Allow viewing for authenticated users"""
        return request.user.is_authenticated and request.user.is_active
    
    def has_module_permission(self, request):
        """Show in admin if user has view permission"""
        return request.user.is_authenticated and request.user.is_active
    
    # Customize admin interface
    def get_actions(self, request):
        """Remove all actions since this is readonly"""
        actions = super().get_actions(request)
        # Remove delete action
        if 'delete_selected' in actions:
            del actions['delete_selected']
        return actions