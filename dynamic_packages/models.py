from django.db import models
from django.core.validators import MinV<PERSON>ueValidator, MaxValueValidator
from django_better_admin_arrayfield.models.fields import <PERSON>rray<PERSON>ield

from base.models import BaseModel


class AIGeneratedPackage(BaseModel):
    """
    Model for storing dynamically generated travel packages for destinations
    not present in the internal database.
    """

    destination_name = models.CharField(max_length=255, db_index=True)
    package_title = models.CharField(max_length=255)
    package_no = models.CharField(max_length=255)
    duration = models.CharField(max_length=255, null=True, blank=True)
    about_this_tour = models.TextField(null=True, blank=True)
    best_time_to_visit = models.TextField(null=True, blank=True)
    cultural_info = models.TextField(null=True, blank=True)
    destination_safety = models.TextField(null=True, blank=True)
    exclusions = ArrayField(
        models.CharField(max_length=255),
        null=True,
        blank=True
    )
    important_notes = ArrayField(
        models.CharField(max_length=255),
        null=True,
        blank=True
    )
    what_to_pack = models.TextField(null=True, blank=True)
    what_to_shop = models.TextField(null=True, blank=True)
    rating = models.DecimalField(
        max_digits=2,
        decimal_places=1,
        null=True,
        blank=True,
        validators=[MinValueValidator(0.0), MaxValueValidator(5.0)]
    )
    rating_description = models.TextField(null=True, blank=True)
    itinerary = models.JSONField(null=True, blank=True)
    media = models.JSONField(null=True, blank=True)
    pricing_estimate = models.JSONField(null=True, blank=True)
    highlights = models.JSONField(null=True, blank=True)
    inclusions = models.JSONField(null=True, blank=True)
    addons = models.JSONField(null=True, blank=True)

    class Meta:
        verbose_name = 'AI Generated Package'
        verbose_name_plural = 'AI Generated Packages'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.package_title} - {self.destination_name}"