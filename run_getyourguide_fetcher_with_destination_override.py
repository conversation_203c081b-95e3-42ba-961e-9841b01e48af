#!/usr/bin/env python3
"""
GetYourGuide Data Fetcher with Destination Override - Custom Script
Fetch activities from one destination but assign them to a different destination by ID

Usage:
    python run_getyourguide_fetcher_with_destination_override.py --destination dubaishimmer 4
    
This will:
1. Check if destination with ID=4 exists
2. Fetch activities from GetYourGuide for 'dubaishimmer'
3. Save activities to CustomActivity table with destination FK pointing to ID=4
"""

import os
import sys
import argparse
import django
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'zuumm.settings')
django.setup()

# Import after Django setup
from base.getyourguide_fetcher.main import GetYourGuideDataFetcher
from base.getyourguide_fetcher.config import GetYourGuideConfig
from packages.models import Destination


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description='GetYourGuide Data Fetcher with Destination Override - Fetch activities from one destination but assign to another',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_getyourguide_fetcher_with_destination_override.py --destination dubaishimmer 4
    # Fetch activities for 'dubaishimmer' but assign to destination ID=4
        """
    )
    
    parser.add_argument(
        '--destination',
        type=str,
        required=True,
        help='Search destination name for GetYourGuide API (e.g., "dubaishimmer")'
    )
    
    parser.add_argument(
        'destination_id',
        type=int,
        help='Target destination ID to assign the fetched activities to'
    )
    
    parser.add_argument(
        '--test',
        action='store_true',
        help='Run in test mode (process limited activities)'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose logging output'
    )
    
    return parser.parse_args()


def print_banner():
    """Print script banner"""
    print("=" * 80)
    print("🎯 GetYourGuide Data Fetcher with Destination Override")
    print("   Fetch activities from one destination, assign to another")
    print("=" * 80)


def validate_destination_id(destination_id):
    """
    Validate that the destination ID exists in database
    
    Args:
        destination_id (int): Destination ID to validate
        
    Returns:
        Destination or None: Destination object if exists, None otherwise
    """
    try:
        destination = Destination.objects.get(id=destination_id, is_active=True)
        print(f"✓ Found target destination: ID={destination.id}, Title='{destination.title}'")
        return destination
    except Destination.DoesNotExist:
        print(f"❌ Destination with ID={destination_id} not found or inactive")
        return None


class CustomDestinationFetcher(GetYourGuideDataFetcher):
    """
    Custom fetcher that inherits from main fetcher but overrides destination assignment
    """
    
    def __init__(self, target_destination):
        """
        Initialize with target destination override
        
        Args:
            target_destination: Destination object to assign activities to
        """
        super().__init__()
        self.target_destination = target_destination
    
    def process_destination_with_override(self, search_destination_name):
        """
        Process a destination by search name but assign to target destination
        
        Args:
            search_destination_name (str): Destination name to search for in GetYourGuide
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            print(f"\n🔍 Searching GetYourGuide for activities in: '{search_destination_name}'")
            print(f"🎯 Will assign activities to destination: ID={self.target_destination.id}, Title='{self.target_destination.title}'")
            
            # Validate prerequisites
            if not self._validate_prerequisites():
                self.logger.error("Prerequisites validation failed")
                return False
            
            # Fetch activities from GetYourGuide API using search destination name
            activities_data = self.api_client.fetch_activities_for_destination(search_destination_name)
            
            if activities_data is None:
                print(f"❌ No activities found for '{search_destination_name}' in GetYourGuide API")
                return False
            
            if not activities_data:
                print(f"⚠️  Empty response for '{search_destination_name}'")
                return False
            
            print(f"✓ Found {len(activities_data)} activities for '{search_destination_name}'")
            
            # Process the activities data (but don't assign destination yet)
            processed_activities = self.data_processor.process_activities_data(
                activities_data, search_destination_name
            )
            
            if not processed_activities:
                print(f"⚠️  No activities could be processed for '{search_destination_name}'")
                return False
            
            print(f"✓ Processed {len(processed_activities)} activities")
            
            # Create activities in database with target destination override
            print(f"💾 Saving activities to database with destination override...")
            stats = self.database_manager.create_custom_activities(
                processed_activities, 
                self.target_destination  # Use target destination instead of search destination
            )
            
            # Extract counts from stats dictionary
            created_count = stats.get('created', 0)
            failed_count = stats.get('failed', 0)
            duplicate_count = stats.get('duplicates', 0)
            
            if created_count > 0:
                print(f"✅ Successfully created {created_count} activities for {self.target_destination.title}")
            else:
                print(f"ℹ️ No new activities created. {duplicate_count} duplicates found, {failed_count} failed")
            
            print(f"📊 Summary: {created_count} created, {duplicate_count} duplicates, {failed_count} failed")
            
            return True
            
        except Exception as e:
            self.logger.log_exception(e, f"processing destination with override: {search_destination_name}")
            print(f"❌ Error processing destination: {str(e)}")
            return False


def main():
    """Main execution function"""
    try:
        # Parse arguments
        args = parse_arguments()
        
        # Print banner
        print_banner()
        print(f"Search Destination: '{args.destination}'")
        print(f"Target Destination ID: {args.destination_id}")
        print()
        
        # Validate target destination exists
        target_destination = validate_destination_id(args.destination_id)
        if not target_destination:
            return 1
        
        # Override test mode if specified
        if args.test:
            GetYourGuideConfig.TEST_MODE = True
            print("✓ Test mode enabled")
        
        # Create custom fetcher instance with destination override
        fetcher = CustomDestinationFetcher(target_destination)
        
        # Process the destination with override
        success = fetcher.process_destination_with_override(args.destination)
        
        # Print final status
        if success:
            print(f"\n🎉 SUCCESS: Activities fetched and assigned successfully!")
            print(f"   Search Query: '{args.destination}'")
            print(f"   Assigned to: ID={target_destination.id}, Title='{target_destination.title}'")
            return 0
        else:
            print(f"\n❌ FAILED: Could not process activities for '{args.destination}'")
            return 1
        
    except KeyboardInterrupt:
        print("\n\n⚠️  Process interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")
        return 1
    finally:
        # Cleanup
        try:
            if 'fetcher' in locals():
                fetcher.cleanup()
        except:
            pass


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 