version: "3.8"

services:
  django-server:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: django_server
    command: >
      bash -c "
        python manage.py migrate &&
        gunicorn zuumm.wsgi:application --workers 4 --threads 5 --bind 0.0.0.0:8000 --timeout 30 --log-file - --access-logfile -
      "
    volumes:
      - .:/code
      - ~/.aws:/root/.aws
    ports:
      - 8000:8000
    restart: always
