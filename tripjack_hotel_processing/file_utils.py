import asyncio
import json
import os
from pathlib import Path
from aiofiles import open as aio_open

async def save_response_to_json(directory_path: str, filename: str, data: dict):
    """
    Asynchronously saves a dictionary to a JSON file, creating directories if they don't exist.
    """
    try:
        # Create the directory path if it doesn't exist
        Path(directory_path).mkdir(parents=True, exist_ok=True)

        filepath = os.path.join(directory_path, filename)

        # Asynchronously write the data to the file
        async with aio_open(filepath, 'w') as f:
            await f.write(json.dumps(data, indent=4))

    except Exception as e:
        # Log the error, but don't let it crash the main process
        print(f"Error saving response to {filepath}: {e}")

