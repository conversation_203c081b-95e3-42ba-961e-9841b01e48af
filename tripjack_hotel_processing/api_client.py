import httpx
import logging
import asyncio
from django.conf import settings
from .file_utils import save_response_to_json

logger = logging.getLogger(__name__)

# Construct endpoints from the base API URL
BASE_HMS_URL = f"{settings.TRIPJACK_API_URL}/hms/v1"
HOTEL_SEARCH_QUERY_LIST_ENDPOINT = f"{BASE_HMS_URL}/hotel-searchquery-list"
HOTEL_SEARCH_ENDPOINT = f"{BASE_HMS_URL}/hotel-search"
HOTEL_DETAIL_SEARCH_ENDPOINT = f"{BASE_HMS_URL}/hotelDetail-search"

async def get_search_id(city_id: str, checkin_date: str, checkout_date: str, pause_event: asyncio.Event):
    """
    Get searchId from TripJack for a given city.
    """
    headers = {
        "apikey": settings.TRIPJACK_API_KEY,
        "Content-Type": "application/json"
    }
    payload = {
        "searchQuery": {
            "checkinDate": checkin_date,
            "checkoutDate": checkout_date,
            "roomInfo": [{"numberOfAdults": 1, "numberOfChild": 0, "childAge": []}],
            "searchCriteria": {"city": city_id, "nationality": "106", "currency": "INR"},
            "searchPreferences": {"ratings": [3, 4, 5], "fsc": False}
        },
        "sync": False
    }
    try:
        async with httpx.AsyncClient(timeout=300.0) as client:
            logger.info(f"Requesting searchId for city: {city_id}")
            response = await client.post(HOTEL_SEARCH_QUERY_LIST_ENDPOINT, headers=headers, json=payload)
            response.raise_for_status()
            data = response.json()

            # Save the response
            log_dir = f"logs/{city_id}"
            await save_response_to_json(log_dir, "get_search_id.json", data)

            # Check for the specific rate limit error signature from TripJack
            status = data.get("status", {})
            errors = data.get("errors", [])
            if status.get("success") is False and any(err.get("errCode") == "408" for err in errors):
                logger.critical(
                    f"Access Denied/Rate Limit error from TripJack API for city {city_id}. "
                    "This is likely due to too many concurrent requests. "
                    "Triggering script shutdown. Try reducing MAX_CONCURRENT_CITIES in settings."
                )
                pause_event.set()
                return None

            search_ids = data.get("searchIds")
            if search_ids and isinstance(search_ids, list) and len(search_ids) > 0:
                search_id = search_ids[0]
                logger.info(f"Successfully received searchId: {search_id} for city: {city_id}")
                return search_id
            else:
                logger.warning(f"API response for city {city_id} did not contain a valid searchId. Response: {data}")
                return None

    except httpx.HTTPStatusError as e:
        logger.error(f"HTTP error while getting searchId for city {city_id}: {e.response.status_code} - {e.response.text}")
    except Exception as e:
        logger.error(f"An unexpected error occurred while getting searchId for city {city_id}: {e}")
    return None

async def get_hotel_ids(search_id: str, city_id: str):
    """
    Get a list of hotel IDs for a given searchId.
    """
    headers = {"apikey": settings.TRIPJACK_API_KEY, "Content-Type": "application/json"}
    payload = {"searchId": search_id}
    response = None
    max_retries = 10  # To prevent infinite loops

    for attempt in range(max_retries):
        try:
            async with httpx.AsyncClient(timeout=300.0) as client:
                logger.info(f"Requesting hotel list for searchId: {search_id} (Attempt {attempt + 1})")
                response = await client.post(HOTEL_SEARCH_ENDPOINT, headers=headers, json=payload)
                response.raise_for_status()
                data = response.json()

                retry_seconds = data.get("retryInSecond")
                if retry_seconds and isinstance(retry_seconds, (int, float)) and retry_seconds > 0:
                    logger.info(f"API requested a retry for searchId {search_id} in {retry_seconds} seconds.")
                    await asyncio.sleep(retry_seconds)
                    continue  # Continue to the next attempt

                log_dir = f"logs/{city_id}"
                await save_response_to_json(log_dir, "get_hotel_ids.json", data)

                # If no retry is needed, process the response
                hotels = data.get("searchResult", {}).get("his", [])
                logger.info(f"Found {len(hotels)} hotels for searchId: {search_id}")
                return hotels

        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error while getting hotel list for searchId {search_id}: {e.response.status_code} - {e.response.text}")
            break  # Exit loop on HTTP error
        except Exception as e:
            logger.error(f"An unexpected error occurred while getting hotel list for searchId {search_id}: {e}", exc_info=True)
            if response:
                logger.error(f"Full response (raw): {response.text}")
            break # Exit loop on other errors

    logger.warning(f"Max retries reached or an error occurred for searchId {search_id}. Returning empty list.")
    return []

async def get_hotel_details(hotel_id: str, city_id: str, pause_event: asyncio.Event):
    """
    Get detailed information for a specific hotel.
    """
    headers = {"apikey": settings.TRIPJACK_API_KEY, "Content-Type": "application/json"}
    payload = {"id": hotel_id}
    try:
        async with httpx.AsyncClient(timeout=60.0) as client:
            logger.info(f"Requesting details for hotel: {hotel_id}")
            response = await client.post(HOTEL_DETAIL_SEARCH_ENDPOINT, headers=headers, json=payload)
            response.raise_for_status()
            data = response.json()

            # Check for the specific rate limit error signature from TripJack
            status = data.get("status", {})
            errors = data.get("errors", [])
            if status.get("success") is False and any(err.get("errCode") == "408" for err in errors):
                logger.critical(
                    f"Access Denied/Rate Limit error from TripJack API for hotel {hotel_id}. "
                    "This is likely due to too many concurrent requests. "
                    "Triggering script shutdown. Try reducing MAX_CONCURRENT_HOTELS in settings."
                )
                pause_event.set()
                return None

            # Save the response
            raw_hotel_id = data.get('hotel', {}).get('id', hotel_id).split('-')[-1]
            log_dir = f"logs/{city_id}/{raw_hotel_id}"
            await save_response_to_json(log_dir, "get_hotel_details.json", data)

            logger.info(f"Successfully received details for hotel: {hotel_id}")
            return data
    except httpx.HTTPStatusError as e:
        logger.error(f"HTTP error while getting details for hotel {hotel_id}: {e.response.status_code} - {e.response.text}")
    except Exception as e:
        logger.error(f"An unexpected error occurred while getting details for hotel {hotel_id}: {e}")
    return None
