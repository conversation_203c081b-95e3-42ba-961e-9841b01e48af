import asyncio
import httpx
import boto3
import logging
import uuid
from botocore.exceptions import NoCredentialsError
from django.conf import settings

logger = logging.getLogger(__name__)

# Initialize S3 client
s3_client = boto3.client(
    "s3",
    aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
    aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
    region_name=settings.AWS_S3_REGION_NAME,
)

async def upload_image_in_background(image_url: str, s3_key: str):
    """
    Downloads an image and uploads it to S3 in the background.
    This is a fire-and-forget task.
    """
    try:
        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.get(image_url)
            response.raise_for_status()
            image_data = response.content

        await asyncio.to_thread(
            s3_client.put_object,
            Bucket=settings.AWS_STORAGE_BUCKET_NAME,
            Key=s3_key,
            Body=image_data,
            ContentType=response.headers.get("Content-Type", "image/jpeg"),
        )
        logger.info(f"Successfully uploaded {s3_key} to S3.")
    except NoCredentialsError:
        logger.error("S3 credentials not found. Cannot upload to S3.")
    except httpx.HTTPStatusError as e:
        logger.error(f"HTTP error downloading image {image_url}: {e.response.status_code}")
    except Exception as e:
        logger.error(f"Background upload failed for {s3_key}: {e}")


async def process_images(hotel_id: str, image_urls: list, destination_id: int):
    """
    Schedules background uploads of hotel images to S3 and returns the S3 keys immediately.
    """
    s3_keys = []
    s3_dir = f"tripjack_hotels/{destination_id}/{hotel_id}"

    for image_url in image_urls:
        random_filename = f"{uuid.uuid4()}.jpg"
        s3_key = f"{s3_dir}/{random_filename}"
        s3_keys.append(s3_key)
        asyncio.create_task(upload_image_in_background(image_url, s3_key))
        logger.info(f"Scheduled background upload for {s3_key} from {image_url}")

    return s3_keys
