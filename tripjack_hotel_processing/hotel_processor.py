import asyncio
import logging
from .api_client import get_hotel_details
from .db_utils import create_hotel_entry

logger = logging.getLogger(__name__)

async def process_hotel(hotel_id: str, destination_id: int, city_id: str, pause_event: asyncio.Event):
    """
    Fetches details for a single hotel and saves it to the database.
    """
    try:
        hotel_id_val = hotel_id.get('id')
        logger.info(f"Starting processing for hotel_id: {hotel_id_val}")
        hotel_details = await get_hotel_details(hotel_id_val, city_id, pause_event)
        if hotel_details:
            await create_hotel_entry(hotel_details, destination_id, pause_event)
            logger.info(f"Successfully processed hotel_id: {hotel_id_val}")
        else:
            logger.warning(f"Could not retrieve details for hotel_id: {hotel_id_val}. Skipping.")
    except Exception as e:
        logger.error(f"An unexpected error occurred while processing hotel {hotel_id_val}: {e}", exc_info=True)
