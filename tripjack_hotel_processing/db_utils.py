import asyncio
from decimal import Decimal, InvalidOperation
import os
import django
import logging
from json.decoder import JSONDecodeError
from django.db import IntegrityError, transaction
from django.utils import timezone
from asgiref.sync import sync_to_async

# Set up Django environment
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "zuumm.settings")
django.setup()

from packages.models import TripjackHotelDestinationMapping, TripjackHotels, Destination, CityDestinationMapping
from .image_processor import process_images

# Configure logger
logger = logging.getLogger(__name__)

def _extract_images_from_response(hotel_data: dict) -> list[str]:
    """
    A robust helper to extract image URLs, handling multiple formats.
    It prioritizes 'XXL' images if available.
    """
    img_list = hotel_data.get('img', [])
    if not img_list:
        return []

    xxl_images = []
    other_images = []

    for img_obj in img_list:
        url = None
        # Case 1: Clean 'url' key exists
        if 'url' in img_obj:
            url = img_obj['url']
        # Case 2: Fallback to malformed 'tns' key
        elif 'tns' in img_obj:
            try:
                # The malformed URL looks like: "https://.../some_pathhttps://.../actual_image.jpeg"
                url = "https://" + img_obj['tns'].split("hotelshttps://")[1]
            except (IndexError, TypeError):
                logger.warning(f"Could not parse malformed tns image URL: {img_obj.get('tns')}")
                continue

        if url:
            if img_obj.get('sz') == 'XXL':
                xxl_images.append(url)
            else:
                other_images.append(url)

    # Prioritize XXL images, then fill with others, up to the desired limit
    final_images = xxl_images + other_images
    return final_images[:5]


async def create_hotel_entry(hotel_data_wrapper: dict, destination_id: int, pause_event: asyncio.Event):
    """
    Creates or updates a TripjackHotels entry.
    This is the final, robust version that handles multiple API response schemas.
    """
    # The actual hotel data is nested inside the 'hotel' key
    hotel_data = hotel_data_wrapper.get('hotel', {})

    if not hotel_data or not hotel_data.get('id'):
        # Check for the specific rate limit error signature from TripJack
        status = hotel_data_wrapper.get("status", {})
        errors = hotel_data_wrapper.get("errors", [])
        if status.get("success") is False and any(err.get("errCode") == "408" for err in errors):
            logger.critical(
                "Access Denied/Rate Limit error from TripJack API during hotel detail processing. "
                "This is likely due to too many concurrent requests. "
                "Triggering script shutdown. Try reducing MAX_CONCURRENT_HOTELS in settings."
            )
            pause_event.set()
            return None

        logger.error(f"Attempted to process an empty or invalid hotel_data object. Full response: {hotel_data_wrapper}")
        return None

    raw_hotel_id = hotel_data.get('id')
    try:
        # Extract the numeric part of the hotel ID
        hotel_id = raw_hotel_id.split('-')[-1]
    except (AttributeError, IndexError):
        logger.error(f"Could not parse hotel_id from raw value: {raw_hotel_id}")
        return None
    
    # --- Consolidated Amenity Extraction ---
    amenities = hotel_data.get('fl')
    if not amenities:
        unique_amenities = set()
        for option in hotel_data.get('ops', []):
            for room_info in option.get('ris', []):
                for facility in room_info.get('fcs', []):
                    if facility:
                        unique_amenities.add(facility)
        amenities = list(unique_amenities)

    # --- Unified Base Price Extraction & Cleaning ---
    base_price_raw = None
    base_price = None
    try:
        base_price_raw = hotel_data.get('ops', [{}])[0].get('ris', [{}])[0].get('tp')
        if base_price_raw is not None:
            # Clean and round the decimal value to 2 places
            base_price = round(Decimal(str(base_price_raw)), 2)
    except (IndexError, KeyError):
        logger.warning(f"Could not find a base price ('tp') for hotel_id: {hotel_id}")
    except (InvalidOperation, TypeError):
        logger.warning(f"Invalid base_price value '{base_price_raw}' for hotel_id: {hotel_id}. Setting to null.")
        base_price = None

    # --- Latitude/Longitude Extraction & Cleaning ---
    latitude_raw = hotel_data.get('gl', {}).get('lt')
    longitude_raw = hotel_data.get('gl', {}).get('ln')
    latitude, longitude = None, None

    try:
        if latitude_raw is not None and str(latitude_raw).strip():
            # Clean and round the decimal value to 7 places
            latitude = round(Decimal(str(latitude_raw)), 7)
    except (InvalidOperation, TypeError):
        logger.warning(f"Could not parse latitude '{latitude_raw}' for hotel_id: {hotel_id}. Setting to null.")
        latitude = None

    try:
        if longitude_raw is not None and str(longitude_raw).strip():
            # Clean and round the decimal value to 7 places
            longitude = round(Decimal(str(longitude_raw)), 7)
    except (InvalidOperation, TypeError):
        logger.warning(f"Could not parse longitude '{longitude_raw}' for hotel_id: {hotel_id}. Setting to null.")
        longitude = None

    try:
        hotel, created = await TripjackHotels.objects.aupdate_or_create(
            hotel_id=hotel_id,
            defaults={
                'name': hotel_data.get('name'),
                'description': hotel_data.get('des'),
                'star_rating': hotel_data.get('rt'),
                'base_price': base_price,
                'check_in_time': hotel_data.get('checkInTime', {}).get('beginTime'),
                'check_out_time': hotel_data.get('checkOutTime', {}).get('beginTime'),
                'latitude': latitude,
                'longitude': longitude,
                'address_line': hotel_data.get('ad', {}).get('adr'),
                'postal_code': hotel_data.get('ad', {}).get('postalCode'),
                'city_name': hotel_data.get('ad', {}).get('city', {}).get('name'),
                'country_name': hotel_data.get('ad', {}).get('country', {}).get('name'),
                'property_type': hotel_data.get('pt'),
                'amenities': amenities,
                'meta_json': hotel_data_wrapper,
            }
        )
    except Exception as e:
        logger.error(f"Database error while creating/updating hotel {hotel_id}: {e}", exc_info=True)
        return None

    # --- Process and Save Images (Only for new hotels) ---
    if created:
        logger.info(f"Created new hotel: {hotel.name} ({hotel_id}).")
        image_urls = _extract_images_from_response(hotel_data)
        if image_urls:
            s3_paths = await process_images(hotel_id, image_urls, destination_id)
            hotel.images = s3_paths
            await hotel.asave(update_fields=['images'])
            logger.info(f"Scheduled background image uploads for hotel {hotel_id}.")
    else:
        logger.info(f"Updated existing hotel: {hotel.name} ({hotel_id}).")

    # --- Map the hotel to the destination ---
    try:
        destination = await Destination.objects.aget(id=destination_id)
        _, mapping_created = await TripjackHotelDestinationMapping.objects.aget_or_create(
            hotel=hotel,
            destination=destination
        )
        if mapping_created:
            logger.info(f"Created new mapping for hotel '{hotel.name}' to destination '{destination.title}'")

    except Destination.DoesNotExist:
        logger.error(f"Destination with id={destination_id} does not exist. Cannot create mapping for hotel {hotel_id}.")
    except Exception as e:
        logger.error(f"An unexpected error occurred during destination mapping for hotel {hotel_id}: {e}", exc_info=True)

    return hotel


def _get_unprocessed_cities_sync(batch_size):
    """
    Synchronous helper to fetch and lock cities.
    """
    with transaction.atomic():
        cities_to_process = list(CityDestinationMapping.objects.select_for_update(
            skip_locked=True
        ).filter(status='pending').filter(destination__isnull=False)[:batch_size])

        if not cities_to_process:
            return []

        city_ids = [city.city_id for city in cities_to_process]
        CityDestinationMapping.objects.filter(city_id__in=city_ids).update(status='processing')

        logger.info(f"Locked and marked {len(cities_to_process)} cities as 'processing'.")
        return [{"city_id": city.city_id, "destination_id": city.destination_id} for city in cities_to_process]


async def get_unprocessed_cities(batch_size=10):
    """
    Retrieves a batch of unprocessed cities from the city_destination_mapping table,
    locking them to prevent concurrent processing.
    """
    logger.info("Fetching unprocessed cities.")
    try:
        return await sync_to_async(_get_unprocessed_cities_sync, thread_sensitive=True)(batch_size)
    except Exception as e:
        logger.error(f"An error occurred while fetching unprocessed cities: {e}", exc_info=True)
        return []


async def mark_city_as_completed(city_id: int):
    """
    Marks a city as 'completed' in the database.
    """
    try:
        await CityDestinationMapping.objects.filter(city_id=city_id).aupdate(
            status='completed',
            processed_at=timezone.now()
        )
        logger.info(f"Successfully marked city {city_id} as completed.")
    except Exception as e:
        logger.error(f"Error marking city {city_id} as completed: {e}", exc_info=True)


async def mark_city_as_pending(city_id: int):
    """
    Marks a city as 'pending' in the database, so it can be retried.
    """
    try:
        await CityDestinationMapping.objects.filter(city_id=city_id).aupdate(status='pending')
        logger.info(f"Successfully marked city {city_id} as pending for retry.")
    except Exception as e:
        logger.error(f"Error marking city {city_id} as pending: {e}", exc_info=True)
