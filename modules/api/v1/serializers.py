from rest_framework import serializers
from modules.models import TermAndCondition, FrequentlyAskedQuestion, PrivacyPolicy, Subscriber


class TermAndConditionSerializer(serializers.ModelSerializer):
    """
    Serializer for Terms and Conditions
    """
    class Meta:
        model = TermAndCondition
        fields = ['id', 'content', 'created_at', 'updated_at']


class PrivacyPolicySerializer(serializers.ModelSerializer):
    """
    Serializer for Privacy Policy
    """
    class Meta:
        model = PrivacyPolicy
        fields = ['id', 'content', 'created_at', 'updated_at']


class FrequentlyAskedQuestionSerializer(serializers.ModelSerializer):
    """
    Serializer for Frequently Asked Questions
    """
    class Meta:
        model = FrequentlyAskedQuestion
        fields = ['id', 'question', 'answer', 'keywords', 'priority', 'created_at', 'updated_at']


class SubscriberSerializer(serializers.ModelSerializer):
    """
    Serializer for Newsletter Subscribers
    """
    email = serializers.EmailField(required=True)
    class Meta:
        model = Subscriber
        fields = ['email']
    
    def validate_email(self, value):
        """
        Validate email format and check for existing subscriptions
        """
        
        # Check if email already exists
        if Subscriber.objects.filter(email__iexact=value.lower()).exists():
            raise serializers.ValidationError("You have already subscribed.")
        
        return value.lower()
    
    def create(self, validated_data):
        """
        Create new subscriber
        """
        return Subscriber.objects.create(**validated_data)
