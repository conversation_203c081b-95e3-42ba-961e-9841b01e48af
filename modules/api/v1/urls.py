from django.urls import path, include
from modules.api.v1 import views

urlpatterns = [
    # Frequently Asked Questions for Zuumm
    path(
        "frequently-asked-questions/", 
        views.FrequentlyAskedQuestionsView.as_view(),
        name="frequently-asked-questions"
    ),

    # Get Terms and Conditions for Zuumm
    path(
        "terms-and-conditions/", 
        views.TermsAndConditionsView.as_view(),
        name="terms-and-conditions"
    ),

    # Get Privacy Policy for Zuumm
    path(
        "privacy-policy/", 
        views.PrivacyPolicyView.as_view(),
        name="privacy-policy"
    ),
    
    # Newsletter subscription
    path(
        "newsletter/", 
        views.NewsletterSubscriptionViewSet.as_view(
            {
                'post': 'create'
            }
        ),
        name="newsletter-subscription"
    ),
    
]
