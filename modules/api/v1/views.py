from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import AllowAny
from rest_framework.generics import ListAPIView
from rest_framework.viewsets import ModelViewSet

from modules.models import TermAndCondition, FrequentlyAskedQuestion, PrivacyPolicy, Subscriber
from modules.api.v1.serializers import TermAndConditionSerializer, FrequentlyAskedQuestionSerializer, PrivacyPolicySerializer, SubscriberSerializer
from accounts.models import Partner
from accounts.choices import PartnerTypeChoices


class TermsAndConditionsView(APIView):
    """
    API view for getting terms and conditions
    """
    permission_classes = [AllowAny]

    def get(self, request):
        """
        Get the latest terms and conditions
        """
        try:
            # Get the latest terms and conditions
            terms = TermAndCondition.objects.latest('created_at')
            serializer = TermAndConditionSerializer(terms)
            
            return Response({
                'success': True,
                'message': 'Terms and conditions retrieved successfully',
                'data': serializer.data
            }, status=status.HTTP_200_OK)
            
        except TermAndCondition.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Terms and conditions not found',
                'data': None
            }, status=status.HTTP_404_NOT_FOUND)
        
        except Exception as e:
            return Response({
                'success': False,
                'message': 'An error occurred while retrieving terms and conditions',
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class PrivacyPolicyView(APIView):
    """
    API view for getting privacy policy
    """
    permission_classes = [AllowAny]

    def get(self, request):
        """
        Get the latest privacy policy
        """
        try:
            # Get the latest privacy policy
            policy = PrivacyPolicy.objects.latest('created_at')
            serializer = PrivacyPolicySerializer(policy)
            
            return Response({
                'success': True,
                'message': 'Privacy policy retrieved successfully',
                'data': serializer.data
            }, status=status.HTTP_200_OK)
            
        except PrivacyPolicy.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Privacy policy not found',
                'data': None
            }, status=status.HTTP_404_NOT_FOUND)
        
        except Exception as e:
            return Response({
                'success': False,
                'message': 'An error occurred while retrieving privacy policy',
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class FrequentlyAskedQuestionsView(ListAPIView):
    """
    API view for getting frequently asked questions based on partner domain
    """
    serializer_class = FrequentlyAskedQuestionSerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        """
        Get FAQs for the partner resolved by middleware
        Returns empty queryset if no partner domain resolved
        """
        # Get partner from request.domain (set by PartnerMiddleware)
        partner = getattr(self.request, 'domain', None)
        
        if not partner:
            # Return empty queryset if no domain resolved (don't expose internal info)
            return FrequentlyAskedQuestion.objects.none()
        
        # Return FAQs for the resolved partner, ordered by priority and creation date
        return FrequentlyAskedQuestion.objects.filter(
            partner=partner, 
            is_active=True
        ).order_by('priority', 'created_at')


class NewsletterSubscriptionViewSet(ModelViewSet):
    """
    ViewSet for newsletter subscription
    Provides automatic CRUD operations with custom create response
    """
    queryset = Subscriber.objects.all()
    serializer_class = SubscriberSerializer
    permission_classes = [AllowAny]
    
    def create(self, request, *args, **kwargs):
        """
        Override create method to provide custom success message
        """
        response = super().create(request, *args, **kwargs)
        return Response({
            'success': True,
            'message': 'Thank you! You\'re now subscribed.',
        }, status=status.HTTP_201_CREATED)
