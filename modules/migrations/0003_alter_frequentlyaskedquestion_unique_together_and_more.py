# Generated by Django 4.2 on 2025-05-30 06:14

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        (
            "accounts",
            "0004_partner_remove_tenant_address_delete_brandadminuser_and_more",
        ),
        ("modules", "0002_termandcondition"),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name="frequentlyaskedquestion",
            unique_together=set(),
        ),
        migrations.AddField(
            model_name="frequentlyaskedquestion",
            name="partner",
            field=models.ForeignKey(
                default=1,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="frequently_asked_questions",
                to="accounts.partner",
            ),
            preserve_default=False,
        ),
        migrations.AlterUniqueTogether(
            name="frequentlyaskedquestion",
            unique_together={("partner", "question"), ("partner", "priority")},
        ),
        migrations.RemoveField(
            model_name="frequentlyaskedquestion",
            name="tenant",
        ),
    ]
