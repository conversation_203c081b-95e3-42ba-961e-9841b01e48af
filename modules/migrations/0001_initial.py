# Generated by Django 4.2 on 2025-05-22 08:43

from django.db import migrations, models
import django.db.models.deletion
import django_better_admin_arrayfield.models.fields
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("accounts", "0002_alter_brandadminuser_options_alter_tenant_options_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="FrequentlyAskedQuestion",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("restored_at", models.DateTimeField(blank=True, null=True)),
                ("transaction_id", models.UUIDField(blank=True, null=True)),
                ("external_id", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.<PERSON><PERSON>anField(default=True)),
                ("question", models.TextField(max_length=2000)),
                ("answer", models.TextField(max_length=2000)),
                (
                    "keywords",
                    django_better_admin_arrayfield.models.fields.ArrayField(
                        base_field=models.CharField(max_length=200),
                        default=list,
                        size=None,
                    ),
                ),
                ("priority", models.PositiveIntegerField(default=1)),
                (
                    "tenant",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="frequently_asked_questions",
                        to="accounts.tenant",
                    ),
                ),
            ],
            options={
                "verbose_name": "Frequently Asked Question",
                "verbose_name_plural": "Frequently Asked Questions",
                "ordering": ["priority", "created_at"],
                "unique_together": {("tenant", "priority"), ("tenant", "question")},
            },
        ),
    ]
