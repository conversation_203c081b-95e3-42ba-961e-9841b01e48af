# Generated by Django 4.2 on 2025-06-19 10:15

import django.core.validators
from django.db import migrations, models
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("modules", "0005_privacypolicy"),
    ]

    operations = [
        migrations.CreateModel(
            name="Subscriber",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("restored_at", models.DateTimeField(blank=True, null=True)),
                ("transaction_id", models.UUIDField(blank=True, null=True)),
                ("external_id", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.<PERSON><PERSON>an<PERSON>ield(default=True)),
                (
                    "email",
                    models.<PERSON><PERSON><PERSON>ield(
                        help_text="Subscriber's email address",
                        max_length=255,
                        unique=True,
                        validators=[django.core.validators.EmailValidator()],
                    ),
                ),
            ],
            options={
                "verbose_name": "Newsletter Subscriber",
                "verbose_name_plural": "Newsletter Subscribers",
                "ordering": ["-created_at"],
            },
        ),
    ]
