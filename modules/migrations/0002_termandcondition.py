# Generated by Django 4.2 on 2025-05-29 07:13

import ckeditor.fields
from django.db import migrations, models
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("modules", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="TermAndCondition",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("restored_at", models.DateTimeField(blank=True, null=True)),
                ("transaction_id", models.UUIDField(blank=True, null=True)),
                ("external_id", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.<PERSON><PERSON>an<PERSON>ield(default=True)),
                ("content", ckeditor.fields.RichTextField()),
            ],
            options={
                "verbose_name": "Terms and Condition",
                "verbose_name_plural": "Terms and Conditions",
            },
        ),
    ]
