from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django_better_admin_arrayfield.admin.mixins import DynamicArrayMixin
from import_export.admin import ExportActionMixin
from import_export.formats.base_formats import XLSX, CSV

from modules.models import FrequentlyAskedQuestion, TermAndCondition, PrivacyPolicy, Subscriber
from modules.forms import FrequentlyAskedQuestionForm
from accounts.choices import UserTypeChoices, PartnerTypeChoices
from accounts.models import Partner
from accounts.admin import CustomExportActionMixin
from base.admin_filters import ActiveStatusFilter


class OneObjectAdmin(admin.ModelAdmin):
    def has_delete_permission(self, request, obj=None):
        return None

    def get_actions(self, request):
        actions = super().get_actions(request)
        if 'delete_selected' in actions:
            del actions['delete_selected']
        return actions

    def has_add_permission(self, request):
        if self.model.objects.exists():
            return False
        return super().has_add_permission(request)

    def can_actually_save(self, request, obj=None):
        """
        Determine if the user can actually save changes to this object.
        This is different from has_change_permission which might return True
        but all fields are disabled/readonly.
        """
        if not self.has_change_permission(request, obj):
            return False
            
        # Check if there are any editable fields
        form = self.get_form(request, obj)()
        readonly_fields = self.get_readonly_fields(request, obj)
        
        # Count non-disabled and non-readonly fields
        editable_fields = 0
        for field_name, field in form.base_fields.items():
            if (not getattr(field, 'disabled', False) and 
                field_name not in readonly_fields):
                editable_fields += 1
                
        return editable_fields > 0

    def change_view(self, request, object_id, form_url='', extra_context=None):
        """Override change_view to add can_actually_save to context"""
        if extra_context is None:
            extra_context = {}
            
        obj = self.get_object(request, object_id)
        extra_context['can_actually_save'] = self.can_actually_save(request, obj)
        
        return super().change_view(request, object_id, form_url, extra_context)


@admin.register(Subscriber)
class SubscriberAdmin(CustomExportActionMixin, admin.ModelAdmin):
    list_display = ('email', 'formatted_created_at')
    search_fields = ('email',)
    readonly_fields = ('created_at', 'updated_at')
    ordering = ('-created_at',)
    fieldsets = (
        (None, {'fields': ('email', 'formatted_created_at')}),
    )
    
    def formatted_created_at(self, obj):
        """Format the created_at field for better display"""
        return obj.created_at.strftime('%Y-%m-%d %I:%M %p') if obj.created_at else '-'
    formatted_created_at.short_description = 'Date Subscribed'
    formatted_created_at.admin_order_field = 'created_at'

    def get_exclude(self, request, obj=None):
        # Exclude softdelete model fields
        return ['transaction_id', 'restored_at', 'deleted_at', 'external_id', 'is_active']
    
    def delete_model(self, request, obj):
        """Use hard_delete instead of soft delete"""
        obj.hard_delete()
    
    def delete_queryset(self, request, queryset):
        """Hard delete multiple objects"""
        for obj in queryset:
            obj.hard_delete()
    
    def has_module_permission(self, request):
        """Allow SuperAdmins to see this module"""
        return request.user.is_active and request.user.is_superuser
    
    def has_view_permission(self, request, obj=None):
        """Permission to view subscribers"""
        return request.user.is_active and request.user.is_superuser
    
    def has_change_permission(self, request, obj=None):
        """Permission to edit subscribers"""
        return False
    
    def has_delete_permission(self, request, obj=None):
        """Permission to delete subscribers"""
        return False
    
    def has_add_permission(self, request):
        """Permission to add subscribers"""
        return False


@admin.register(FrequentlyAskedQuestion)
class FrequentlyAskedQuestionAdmin(admin.ModelAdmin, DynamicArrayMixin):
    form = FrequentlyAskedQuestionForm
    list_display = ('question', 'priority', 'is_active', 'created_at')
    list_filter = ('priority', ActiveStatusFilter)
    search_fields = ('question', 'answer', 'keywords')
    readonly_fields = ('created_at', 'updated_at')
    
    def get_form(self, request, obj=None, **kwargs):
        """Inject request into form for validation"""
        form = super().get_form(request, obj, **kwargs)
        form.request = request
        return form
    
    def delete_model(self, request, obj):
        """Use hard_delete instead of soft delete"""
        # Use the hard_delete method from django-softdelete
        obj.hard_delete()
    
    def delete_queryset(self, request, queryset):
        """Hard delete multiple objects"""
        # Use hard_delete for bulk deletions
        for obj in queryset:
            obj.hard_delete()
    
    def get_fieldsets(self, request, obj=None):
        if obj:  # Editing an existing object
            return [
                (None, {'fields': ('question', 'answer', 'is_active', 'keywords', 'priority', 'created_at', 'updated_at')}),
            ]
        else:  # Creating a new object
            return [
                (None, {'fields': ('question', 'answer', 'is_active', 'keywords', 'priority')}),
            ]
    
    def get_exclude(self, request, obj=None):
        # Exclude softdelete model fields and partner (which we set programmatically)
        return ['transaction_id', 'restored_at', 'deleted_at', 'external_id', 'partner']
    
    def get_queryset(self, request):
        """Filter FAQs based on user role"""
        qs = super().get_queryset(request)
        
        # SuperAdmin with partner_type ZUUMM sees only ZUUMM partner FAQs
        if request.user.is_superuser and request.user.user_type == UserTypeChoices.SUPER_ADMIN.value:
            return qs.filter(partner__partner_type=PartnerTypeChoices.ZUUMM.value)

        # PartnerAdmin sees only their partner's FAQs
        elif request.user.user_type == UserTypeChoices.PARTNER_ADMIN.value:
            return qs.filter(partner=request.user.partner)
        return qs
    
    def save_model(self, request, obj, form, change):
        """Set partner automatically based on user role"""
        if not change:  # Only set partner when creating new FAQ
            if (request.user.is_superuser 
                and request.user.user_type == UserTypeChoices.SUPER_ADMIN.value
            ) or request.user.user_type == UserTypeChoices.PARTNER_ADMIN.value:
                # For SuperAdmin, use ZUUMM partner or For PartnerAdmin, use their own partner
                # zuumm_partner = Partner.objects.filter(partner_type=PartnerTypeChoices.ZUUMM.value).first()
                obj.partner = request.user.partner

        super().save_model(request, obj, form, change)
    
    def has_module_permission(self, request):
        """Allow SuperAdmins and PartnerAdmins to see this module"""
        return request.user.is_active and (
            request.user.is_superuser or 
            request.user.user_type == UserTypeChoices.PARTNER_ADMIN.value
        )
    
    def has_view_permission(self, request, obj=None):
        """Permission to view FAQs"""
        if not (request.user.is_active and (
            request.user.is_superuser or 
            request.user.user_type == UserTypeChoices.PARTNER_ADMIN.value
        )):
            return False
            
        # PartnerAdmins can only view their own partner's FAQs
        if obj and request.user.user_type == UserTypeChoices.PARTNER_ADMIN.value:
            return obj.partner == request.user.partner
            
        return True
    
    def has_change_permission(self, request, obj=None):
        """Permission to edit FAQs"""
        if not (request.user.is_active and (
            request.user.is_superuser or 
            request.user.user_type == UserTypeChoices.PARTNER_ADMIN.value
        )):
            return False
            
        # PartnerAdmins can only edit their own partner's FAQs
        if obj and request.user.user_type == UserTypeChoices.PARTNER_ADMIN.value:
            return obj.partner == request.user.partner
            
        return True
    
    def has_delete_permission(self, request, obj=None):
        """Permission to delete FAQs"""
        if not (request.user.is_active and (
            request.user.is_superuser or 
            request.user.user_type == UserTypeChoices.PARTNER_ADMIN.value
        )):
            return False
            
        # PartnerAdmins can only delete their own partner's FAQs
        if obj and request.user.user_type == UserTypeChoices.PARTNER_ADMIN.value:
            return obj.partner == request.user.partner
            
        return True
    
    def has_add_permission(self, request):
        """Permission to add FAQs"""
        return request.user.is_active and (
            request.user.is_superuser or 
            request.user.user_type == UserTypeChoices.PARTNER_ADMIN.value
        )

    def can_actually_save(self, request, obj=None):
        """
        Determine if the user can actually save changes to this object.
        This is different from has_change_permission which might return True
        but all fields are disabled/readonly.
        """
        if not self.has_change_permission(request, obj):
            return False
            
        # Check if there are any editable fields
        form = self.get_form(request, obj)()
        readonly_fields = self.get_readonly_fields(request, obj)
        
        # Count non-disabled and non-readonly fields
        editable_fields = 0
        for field_name, field in form.base_fields.items():
            if (not getattr(field, 'disabled', False) and 
                field_name not in readonly_fields):
                editable_fields += 1
                
        return editable_fields > 0

    def change_view(self, request, object_id, form_url='', extra_context=None):
        """Override change_view to add can_actually_save to context"""
        if extra_context is None:
            extra_context = {}
            
        obj = self.get_object(request, object_id)
        extra_context['can_actually_save'] = self.can_actually_save(request, obj)
        
        return super().change_view(request, object_id, form_url, extra_context)


@admin.register(TermAndCondition)
class TermAndConditionAdmin(OneObjectAdmin):
    list_display = ('updated_at',)
    fields = ('content',)
    
    def get_fieldsets(self, request, obj=None):
        return [
            (None, {'fields': ('content',)}),
        ]

    def delete_model(self, request, obj):
        """Use hard_delete instead of soft delete"""
        # Use the hard_delete method from django-softdelete
        obj.hard_delete()


@admin.register(PrivacyPolicy)
class PrivacyPolicyAdmin(OneObjectAdmin):
    list_display = ('updated_at',)
    fields = ('content',)
    
    def get_fieldsets(self, request, obj=None):
        return [
            (None, {'fields': ('content',)}),
        ]

    def delete_model(self, request, obj):
        """Use hard_delete instead of soft delete"""
        # Use the hard_delete method from django-softdelete
        obj.hard_delete()
