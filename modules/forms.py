from django import forms
from django.core.exceptions import ValidationError
from modules.models import FrequentlyAskedQuestion


class FrequentlyAskedQuestionForm(forms.ModelForm):
    class Meta:
        model = FrequentlyAskedQuestion
        fields = '__all__'
    
    def clean(self):
        cleaned_data = super().clean()
        value = cleaned_data.get('keywords')
        question = cleaned_data.get('question')
        priority = cleaned_data.get('priority')
        is_active = cleaned_data.get('is_active', True)  # Default to True if not provided

        # Check if value is empty
        if not value:
            self.add_error('keywords', 'At least one keyword must be provided.')
        # Ensure unique values (case insensitive)
        else:
            unique_values = []
            seen = set()
            duplicates = []
            
            for item in value:
                item_stripped = item.strip()
                item_lower = item_stripped.lower()
                
                if item_lower in seen:
                    duplicates.append(item_stripped)
                else:
                    seen.add(item_lower)
                    unique_values.append(item_stripped)
            
            if duplicates:
                self.add_error(
                    'keywords', f'Duplicate values found (case insensitive): {", ".join(duplicates)}'
                )

        # Get partner based on user from request
        request = getattr(self, 'request', None)
        if not request or not hasattr(request, 'user') or not hasattr(request.user, 'partner'):
            return cleaned_data
        
        partner = request.user.partner
        
        # 1. Validate unique (partner, question) constraint
        if question:
            existing_questions = FrequentlyAskedQuestion.objects.filter(
                partner=partner, 
                question=question
            )
            
            # Exclude current instance if we're editing
            if self.instance and self.instance.pk:
                existing_questions = existing_questions.exclude(pk=self.instance.pk)
                
            if existing_questions.exists():
                existing_faq = existing_questions.first()
                if existing_faq.is_active and is_active:
                    # Both are active - true duplicate
                    self.add_error('question', 
                        'A FAQ with this question already exists for your partner. '
                        'Please use a different question.'
                    )
                elif existing_faq.is_active and not is_active:
                    # Existing one is active, new one is inactive - OK
                    pass
                elif not existing_faq.is_active and is_active:
                    # Existing one is inactive, new one is active - warn and suggest
                    self.add_error('question', 
                        'An inactive FAQ with this question already exists. '
                        'Please activate that FAQ instead of creating a duplicate.'
                    )
                # Both inactive - probably OK but warn
                elif not existing_faq.is_active and not is_active:
                    self.add_error('question', 
                        'An inactive FAQ with this question already exists. '
                        'Consider reusing that one instead of creating a duplicate.'
                    )
        
        # 2. Validate unique (partner, priority) constraint
        if priority:
            existing_priorities = FrequentlyAskedQuestion.objects.filter(
                partner=partner, 
                priority=priority
            )
            
            # Exclude current instance if we're editing
            if self.instance and self.instance.pk:
                existing_priorities = existing_priorities.exclude(pk=self.instance.pk)
                
            if existing_priorities.exists():
                existing_faq = existing_priorities.first()
                if existing_faq.is_active and is_active:
                    # Both are active - true duplicate priority
                    self.add_error('priority', 
                        f'An active FAQ with priority {priority} already exists for your partner. '
                        f'Please choose a different priority.'
                    )
                elif existing_faq.is_active and not is_active:
                    # Existing one is active, new one is inactive - OK
                    pass
                elif not existing_faq.is_active and is_active:
                    # Existing one is inactive, new one is active
                    self.add_error('priority', 
                        f'An inactive FAQ with priority {priority} already exists. '
                        f'Please activate that FAQ or choose a different priority.'
                    )
                # Both inactive - probably OK but warn
                elif not existing_faq.is_active and not is_active:
                    self.add_error('priority', 
                        f'An inactive FAQ with priority {priority} already exists. '
                        f'Consider using a different priority to avoid confusion.'
                )
        
        return cleaned_data
