from django.db import models
from base.models import BaseModel
from accounts.models import Partner
from django_better_admin_arrayfield.models.fields import <PERSON><PERSON>y<PERSON>ield
from django.core.validators import EmailValidator
from ckeditor.fields import RichTextField



class FrequentlyAskedQuestion(BaseModel):
    partner = models.ForeignKey(Partner, on_delete=models.CASCADE, related_name='frequently_asked_questions')
    question = models.TextField(max_length=2000)
    answer = models.TextField(max_length=2000)
    keywords = ArrayField(
        models.CharField(max_length=200),
        default=list
    )
    priority = models.PositiveIntegerField(default=1)

    def __str__(self):
        return self.question

    class Meta:
        ordering = ['priority', 'created_at']
        unique_together = (
            ('partner', 'question'), 
            ('partner', 'priority'),
        )
        verbose_name = 'Web Frequently Asked Question'
        verbose_name_plural = 'Web Frequently Asked Questions'


class TermAndCondition(BaseModel):
    content = RichTextField()

    class Meta:
        verbose_name = "Web Terms and Condition"
        verbose_name_plural = "Web Terms and Conditions"


class PrivacyPolicy(BaseModel):
    content = RichTextField()

    class Meta:
        verbose_name = "Web Privacy Policy"
        verbose_name_plural = "Web Privacy Policies"


class Subscriber(BaseModel):
    """
    Model to store newsletter subscribers
    """
    email = models.EmailField(
        max_length=255,
        unique=True,
        validators=[EmailValidator()],
        help_text="Subscriber's email address"
    )
    
    def __str__(self):
        return self.email
    
    class Meta:
        ordering = ['-created_at']
        verbose_name = 'Newsletter Subscriber'
        verbose_name_plural = 'Newsletter Subscribers'
