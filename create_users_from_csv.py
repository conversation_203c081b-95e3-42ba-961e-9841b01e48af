#!/usr/bin/env python3
"""
CSV User Creator - Bulk User Creation Script
Creates users from a CSV file with brand ambassador data

Usage:
    python create_users_from_csv.py
    
Expected CSV Format:
    Company Name, First Name, Last Name, Contact No, Email Address
    
The script will:
- Ignore Company Name column
- Concatenate First Name + Last Name for full_name
- Use Contact No as phone_number with +91 country code
- Use Email Address as email
- Set user_type to ZUUMM_USER
- Set partner to ZUUMM partner
- Set is_email_verified to True
"""

import os
import sys
import csv
import django
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'zuumm.settings')
django.setup()

# Import after Django setup
from django.db import transaction
from django.contrib.auth import get_user_model
from accounts.models import Partner
from accounts.choices import UserTypeChoices, PartnerTypeChoices

User = get_user_model()

def get_zuumm_partner():
    """Get the ZUUMM partner instance"""
    try:
        partner = Partner.objects.filter(partner_type=PartnerTypeChoices.ZUUMM.value).first()
        if not partner:
            raise ValueError("ZUUMM Partner not found. Please ensure a Partner with partner_type='ZUUMM' exists.")
        print(f"Using ZUUMM Partner: {partner.entity_name} (ID: {partner.id})")
        return partner
    except Exception as e:
        print(f"Error getting ZUUMM Partner: {e}")
        raise

def read_csv_file(csv_path):
    """Read and parse the CSV file"""
    users_data = []
    
    try:
        with open(csv_path, 'r', encoding='utf-8') as file:
            # Try to detect if it has headers
            sample = file.read(1024)
            file.seek(0)
            
            # Check if first row contains headers (non-numeric contact field)
            sniffer = csv.Sniffer()
            has_header = sniffer.has_header(sample)
            
            reader = csv.reader(file)
            
            # Skip header row if detected
            if has_header:
                next(reader)
                print("Header row detected and skipped")
            
            for row_num, row in enumerate(reader, start=2 if has_header else 1):
                # Extract data from CSV columns
                company_name = row[0].strip() if len(row) > 0 and row[0] else ""  # Ignored
                first_name = row[1].strip() if len(row) > 1 and row[1] else ""
                last_name = row[2].strip() if len(row) > 2 and row[2] else ""
                contact_no = row[3].strip() if len(row) > 3 and row[3] else ""
                email = row[4].strip() if len(row) > 4 and row[4] else ""
                
                # Skip rows with no first name (completely empty rows)
                if not first_name:
                    print(f"Warning: Row {row_num} has no first name, skipping")
                    continue
                
                # Create full name - if no last name, just use first name
                if last_name:
                    full_name = f"{first_name} {last_name}".strip()
                else:
                    full_name = first_name.strip()
                
                # Normalize email to lowercase if present
                email_normalized = email.lower() if email else None
                
                users_data.append({
                    'full_name': full_name,
                    'email': email_normalized,
                    'phone_number': contact_no,  # Can be empty
                    'row_num': row_num
                })
    
    except FileNotFoundError:
        print(f"Error: CSV file '{csv_path}' not found")
        return []
    except Exception as e:
        print(f"Error reading CSV file: {e}")
        return []
    
    print(f"Successfully parsed {len(users_data)} user records from CSV")
    return users_data

def create_users_bulk(users_data, zuumm_partner):
    """Create users in bulk with transaction safety"""
    created_count = 0
    skipped_count = 0
    error_count = 0
    
    with transaction.atomic():
        i = 0
        for user_data in users_data:
            if i == 0:
                print("-" * 80)
                print("Skipping first row")
                print(f"Row {user_data['row_num']}: {user_data}")
                print("-" * 80)
                i += 1
                continue
            try:
                # For duplicate checking, we need either email or phone number
                # If both are empty, we'll still create the user
                existing_user = None
                
                if user_data['email']:
                    # Check by email if email exists
                    existing_user = User.objects.filter(
                        email=user_data['email'], 
                        partner=zuumm_partner,
                        user_type=UserTypeChoices.ZUUMM_USER.value
                    ).first()
                elif user_data['phone_number']:
                    # Check by phone number if no email but phone exists
                    existing_user = User.objects.filter(
                        phone_number=user_data['phone_number'],
                        partner=zuumm_partner,
                        user_type=UserTypeChoices.ZUUMM_USER.value
                    ).first()
                
                if existing_user:
                    identifier = user_data['email'] or user_data['phone_number'] or user_data['full_name']
                    print(f"Row {user_data['row_num']}: User {identifier} already exists, skipping")
                    skipped_count += 1
                    continue
                
                # Prepare user creation data
                create_data = {
                    'full_name': user_data['full_name'],
                    'partner': zuumm_partner,
                    'user_type': UserTypeChoices.ZUUMM_USER.value,
                    'is_email_verified': True,
                }
                
                # Add email only if present (avoid empty string)
                if user_data['email']:
                    create_data['email'] = user_data['email'].lower()
                else:
                    create_data['email'] = None

                # Add phone number only if present (avoid empty string)
                if user_data['phone_number']:
                    create_data['phone_number_country_code'] = '+91'
                    create_data['phone_number'] = user_data['phone_number']
                
                # Create new user
                user = User.objects.create_user(**create_data)
                
                identifier = user_data['email'] or user_data['phone_number'] or user_data['full_name']
                print(f"Row {user_data['row_num']}: Created user {identifier} - {user.full_name}")
                created_count += 1
                
            except Exception as e:
                identifier = user_data.get('email') or user_data.get('phone_number') or user_data.get('full_name', 'unknown')
                print(f"Row {user_data['row_num']}: Error creating user {identifier}: {e}")
                error_count += 1
    
    return created_count, skipped_count, error_count

def main():
    """Main execution function"""
    print("=" * 80)
    print("CSV USER CREATOR SCRIPT STARTED")
    print("=" * 80)
    
    # Define CSV file path - Original brand ambassadors list
    csv_file_path = "/home/<USER>/Downloads/Brand-ambassadorslist_Final_25jun2025.csv"
    
    # Check if CSV file exists
    if not os.path.exists(csv_file_path):
        print(f"Error: CSV file '{csv_file_path}' not found")
        print("Please ensure the CSV file exists at the specified path")
        return
    
    try:
        # Get ZUUMM partner
        zuumm_partner = get_zuumm_partner()
        
        # Read CSV data
        print(f"Reading CSV file: {csv_file_path}")
        users_data = read_csv_file(csv_file_path)
        
        if not users_data:
            print("No valid user data found in CSV file")
            return
        
        # Create users
        print(f"\nCreating {len(users_data)} users...")
        created_count, skipped_count, error_count = create_users_bulk(users_data, zuumm_partner)
        
        # Print summary
        print("\n" + "=" * 80)
        print("USER CREATION SUMMARY")
        print("=" * 80)
        print(f"Total records processed: {len(users_data)}")
        print(f"Users created: {created_count}")
        print(f"Users skipped (already exist): {skipped_count}")
        print(f"Errors encountered: {error_count}")
        print("=" * 80)
        
        if created_count > 0:
            print(f"✅ Successfully created {created_count} new users!")
        if skipped_count > 0:
            print(f"ℹ️  Skipped {skipped_count} existing users")
        if error_count > 0:
            print(f"❌ {error_count} errors occurred during processing")
            
    except Exception as e:
        print(f"Script execution failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 