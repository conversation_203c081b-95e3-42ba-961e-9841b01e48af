#!/usr/bin/env python3
"""
Update Durations for Custom Activities - Custom Script
Fetch activities from GetYourGuide API and update existing CustomActivity records with duration data

Usage:
    python update_durations_for_activities.py --destination "turkey" 51
    
This will:
1. Check if destination with ID=51 exists
2. Fetch activities from GetYourGuide for 'turkey'
3. Match activities by tour_id with existing CustomActivity records
4. Update the durations field for matched activities
"""

import os
import sys
import argparse
import django
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'zuumm.settings')
django.setup()

# Import after Django setup
from base.getyourguide_fetcher.api_client import GetYourGuideAPIClient
from base.getyourguide_fetcher.logger import GetYourGuideLogger
from base.getyourguide_fetcher.config import GetYourGuideConfig
from packages.models import Destination, CustomActivity


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description='Update Durations for Custom Activities - Fetch durations from GetYourGuide API and update existing records',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python update_durations_for_activities.py --destination "turkey" 51
    # Fetch activities for 'turkey' and update durations for destination ID=51
        """
    )
    
    parser.add_argument(
        '--destination',
        type=str,
        required=True,
        help='Search destination name for GetYourGuide API (e.g., "turkey")'
    )
    
    parser.add_argument(
        'destination_id',
        type=int,
        help='Target destination ID to update activities for'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose logging output'
    )
    
    return parser.parse_args()


def print_banner():
    """Print script banner"""
    print("=" * 80)
    print("⏱️  Update Durations for Custom Activities")
    print("   Fetch durations from GetYourGuide API and update existing records")
    print("=" * 80)


def validate_destination_id(destination_id):
    """
    Validate that the destination ID exists in database
    
    Args:
        destination_id (int): Destination ID to validate
        
    Returns:
        Destination or None: Destination object if exists, None otherwise
    """
    try:
        destination = Destination.objects.get(id=destination_id, is_active=True)
        print(f"✓ Found target destination: ID={destination.id}, Title='{destination.title}'")
        return destination
    except Destination.DoesNotExist:
        print(f"❌ Destination with ID={destination_id} not found or inactive")
        return None


class DurationUpdater:
    """
    Class to handle fetching durations from GetYourGuide API and updating existing CustomActivity records
    """
    
    def __init__(self, target_destination):
        """
        Initialize with target destination
        
        Args:
            target_destination: Destination object to update activities for
        """
        self.target_destination = target_destination
        self.logger = GetYourGuideLogger()
        self.api_client = GetYourGuideAPIClient(self.logger)
        
        # Statistics
        self.stats = {
            'total_api_activities': 0,
            'matched_activities': 0,
            'updated_activities': 0,
            'no_duration_data': 0,
            'api_errors': 0
        }
    
    def update_durations_for_destination(self, search_destination_name):
        """
        Update durations for activities by fetching from GetYourGuide API
        
        Args:
            search_destination_name (str): Destination name to search for in GetYourGuide
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            print(f"\n🔍 Searching GetYourGuide for activities in: '{search_destination_name}'")
            print(f"🎯 Will update durations for activities in destination: ID={self.target_destination.id}, Title='{self.target_destination.title}'")
            
            # Get existing CustomActivity records for this destination
            existing_activities = CustomActivity.objects.filter(
                destination=self.target_destination,
                tour_id__isnull=False
            ).exclude(tour_id='')
            
            print(f"📊 Found {existing_activities.count()} existing activities with tour_id in destination")
            
            if existing_activities.count() == 0:
                print("⚠️  No existing activities found with tour_id. Nothing to update.")
                return False
            
            # Fetch activities from GetYourGuide API
            activities_data = self.api_client.fetch_activities_for_destination(search_destination_name)
            
            if activities_data is None:
                print(f"❌ No activities found for '{search_destination_name}' in GetYourGuide API")
                return False
            
            if not activities_data:
                print(f"⚠️  Empty response for '{search_destination_name}'")
                return False
            
            self.stats['total_api_activities'] = len(activities_data)
            print(f"✓ Found {len(activities_data)} activities from GetYourGuide API")
            
            # Create a mapping of tour_id to duration data
            duration_mapping = self._extract_duration_mapping(activities_data)
            
            if not duration_mapping:
                print("⚠️  No duration data found in API response")
                return False
            
            print(f"✓ Extracted duration data for {len(duration_mapping)} activities")
            
            # Update existing activities with duration data
            updated_count = self._update_existing_activities(existing_activities, duration_mapping)
            
            # Print summary
            print(f"\n📊 Update Summary:")
            print(f"   Total API Activities: {self.stats['total_api_activities']}")
            print(f"   Activities with Duration Data: {len(duration_mapping)}")
            print(f"   Matched Activities: {self.stats['matched_activities']}")
            print(f"   Successfully Updated: {self.stats['updated_activities']}")
            print(f"   No Duration Data: {self.stats['no_duration_data']}")
            
            return updated_count > 0
            
        except Exception as e:
            self.logger.log_exception(e, f"updating durations for destination: {search_destination_name}")
            print(f"❌ Error updating durations: {str(e)}")
            return False
    
    def _extract_duration_mapping(self, activities_data):
        """
        Extract duration data from API response and create tour_id -> duration mapping
        
        Args:
            activities_data (list): List of activity data from API
            
        Returns:
            dict: Mapping of tour_id to duration data
        """
        duration_mapping = {}
        
        for activity in activities_data:
            tour_id = activity.get('tour_id')
            durations = activity.get('durations', [])
            
            if tour_id and durations:
                duration_mapping[str(tour_id)] = durations
            elif tour_id:
                # Activity has tour_id but no durations
                self.stats['no_duration_data'] += 1
        
        return duration_mapping
    
    def _update_existing_activities(self, existing_activities, duration_mapping):
        """
        Update existing CustomActivity records with duration data
        
        Args:
            existing_activities: QuerySet of existing CustomActivity records
            duration_mapping (dict): Mapping of tour_id to duration data
            
        Returns:
            int: Number of successfully updated activities
        """
        updated_count = 0
        
        for activity in existing_activities:
            try:
                tour_id = str(activity.tour_id)
                
                if tour_id in duration_mapping:
                    self.stats['matched_activities'] += 1
                    
                    # Get duration data
                    duration_data = duration_mapping[tour_id]
                    
                    # Update the activity
                    activity.durations = duration_data
                    activity.save(update_fields=['durations', 'updated_at'])
                    
                    updated_count += 1
                    self.stats['updated_activities'] += 1
                    
                    print(f"✓ Updated durations for: {activity.title[:50]}... (ID: {activity.id})")
                    
                    if len(duration_data) > 0:
                        print(f"  → Added {len(duration_data)} duration entries")
                
            except Exception as e:
                print(f"❌ Error updating activity {activity.id}: {str(e)}")
                continue
        
        return updated_count
    
    def cleanup(self):
        """Clean up resources"""
        try:
            if hasattr(self, 'api_client') and self.api_client:
                self.api_client.close()
        except Exception as e:
            if hasattr(self, 'logger'):
                self.logger.log_exception(e, "cleaning up resources")


def main():
    """Main execution function"""
    try:
        # Parse arguments
        args = parse_arguments()
        
        # Print banner
        print_banner()
        print(f"Search Destination: '{args.destination}'")
        print(f"Target Destination ID: {args.destination_id}")
        print()
        
        # Validate target destination exists
        target_destination = validate_destination_id(args.destination_id)
        if not target_destination:
            return 1
        
        # Create duration updater instance
        updater = DurationUpdater(target_destination)
        
        # Update durations for the destination
        success = updater.update_durations_for_destination(args.destination)
        
        # Print final status
        if success:
            print(f"\n🎉 SUCCESS: Durations updated successfully!")
            print(f"   Search Query: '{args.destination}'")
            print(f"   Updated for: ID={target_destination.id}, Title='{target_destination.title}'")
            return 0
        else:
            print(f"\n❌ FAILED: Could not update durations for '{args.destination}'")
            return 1
        
    except KeyboardInterrupt:
        print("\n\n⚠️  Process interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")
        return 1
    finally:
        # Cleanup
        try:
            if 'updater' in locals():
                updater.cleanup()
        except:
            pass


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 