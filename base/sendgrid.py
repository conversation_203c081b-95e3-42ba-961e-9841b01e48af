"""Mailer service for sendgrid"""
import logging
import os
from smtplib import SMTPException

from django.conf import settings
from django.core.mail import EmailMessage

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "settings")


# def test_sendgrid_api_directly(to_email, subject="Test Email", content="This is a test email"):
#     """
#     Test SendGrid API directly using their REST API.
#     Returns (success, response_text)
#     """
#     url = "https://api.sendgrid.com/v3/mail/send"
#     headers = {
#         "Authorization": f"Bearer {settings.EMAIL_HOST_PASSWORD}",
#         "Content-Type": "application/json"
#     }
#     data = {
#         "personalizations": [
#             {
#                 "to": [{"email": to_email}]
#             }
#         ],
#         "from": {"email": settings.DEFAULT_FROM_EMAIL},
#         "subject": subject,
#         "content": [
#             {
#                 "type": "text/plain",
#                 "value": content
#             }
#         ]
#     }
    
#     try:
#         response = requests.post(url, json=data, headers=headers)
#         if response.status_code == 202:
#             return True, "Em<PERSON> sent successfully"
#         return False, f"Failed to send email. Status code: {response.status_code}, Response: {response.text}"
#     except Exception as e:
#         return False, f"Error sending email: {str(e)}"


class Mailer:
    """Mailer class"""

    def __init__(self, subject, body, to_email, attachments=None):
        """init method for Mailer class"""
        self.subject = subject
        self.body = body
        self.to_email = to_email
        self.attachments = attachments or []

    @staticmethod
    def get_email(to_email):
        """Get email"""
        if isinstance(to_email, (list, tuple)):
            return list(set(to_email))
        return [to_email]

    def send(self):
        """Send mail"""
        try:
            if not self.to_email:
                print("Receiver Email required.")
                return

            mail = EmailMessage(
                subject=self.subject,
                body=self.body,
                from_email=settings.DEFAULT_FROM_EMAIL,
                to=self.get_email(self.to_email),
            )
            for attachment in self.attachments:
                mail.attach_file(attachment)

            if "</html>" in self.body:
                mail.content_subtype = "html"
            mail.send(fail_silently=False)

        except SMTPException as error:
            logging.debug("error in sending mail: %s", error)
