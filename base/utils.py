from base.static import Constants
from packages.choices import MonthChoices

def get_file_and_extension(file: str):
    """file & extension extraction"""
    file_data = file.rsplit(".")
    return file_data[0], file_data[-1]


def get_file_meta_data(file_type, user=None):
    """
    Get file metadata based on file type and user type
    """
    file_path = None
    allowed_extensions = None
    size_limit = None

    if file_type == 'partner_logo':
        file_path = "partner/logo/"
        allowed_extensions = Constants.PARTNER_LOGO_EXTENSIONS
        size_limit = Constants.FILE_SIZE_FIVE_MB

    
    return file_path, allowed_extensions, size_limit, None


def destination_best_time_to_visit_display(destination):
    if not destination.best_time_to_visit:
        return "Not specified"
    
    # Sort the months
    months = sorted(destination.best_time_to_visit)
    if not months:
        return "Not specified"
    
    # Group consecutive months into ranges
    ranges = []
    start = months[0]
    end = months[0]
    
    for i in range(1, len(months)):
        if months[i] == end + 1:
            # Consecutive month
            end = months[i]
        else:
            # Gap found, close current range and start new one
            ranges.append((start, end))
            start = months[i]
            end = months[i]
    
    # Add the last range
    ranges.append((start, end))
    
    # Convert ranges to display format
    range_strings = []
    for start_month, end_month in ranges:
        try:
            start_name = MonthChoices(str(start_month)).label
            if start_month == end_month:
                # Single month
                range_strings.append(start_name)
            else:
                # Range of months
                end_name = MonthChoices(str(end_month)).label
                range_strings.append(f"{start_name} to {end_name}")
        except ValueError:
            continue
    
    return " & ".join(range_strings) if range_strings else "Not specified"


def package_best_time_to_visit_display(package):
    """Get best time to visit in formatted ranges like Jan-Feb / Jun-Aug / Nov-Dec for packages"""
    if not package.best_time_to_visit_months:
        return None
    
    # Month mapping for short names
    month_map = {
        'January': 'Jan', 'February': 'Feb', 'March': 'Mar', 'April': 'Apr',
        'May': 'May', 'June': 'Jun', 'July': 'Jul', 'August': 'Aug',
        'September': 'Sep', 'October': 'Oct', 'November': 'Nov', 'December': 'Dec'
    }
    
    # Convert month names to numbers for sorting
    month_to_num = {
        'January': 1, 'February': 2, 'March': 3, 'April': 4,
        'May': 5, 'June': 6, 'July': 7, 'August': 8,
        'September': 9, 'October': 10, 'November': 11, 'December': 12
    }
    
    # Get valid months and convert to numbers
    month_numbers = []
    for month_name in package.best_time_to_visit_months:
        if month_name in month_to_num:
            month_numbers.append((month_to_num[month_name], month_name))
    
    if not month_numbers:
        return None
    
    # Sort by month number
    month_numbers.sort()
    
    # Group consecutive months into ranges
    ranges = []
    current_range = [month_numbers[0]]
    
    for i in range(1, len(month_numbers)):
        prev_num, prev_name = month_numbers[i-1]
        curr_num, curr_name = month_numbers[i]
        
        # Check if consecutive (with wrap-around for Dec->Jan)
        if (curr_num == prev_num + 1) or (prev_num == 12 and curr_num == 1):
            current_range.append(month_numbers[i])
        else:
            # Start new range
            ranges.append(current_range)
            current_range = [month_numbers[i]]
    
    # Add the last range
    ranges.append(current_range)
    
    # Format ranges
    formatted_ranges = []
    for range_months in ranges:
        if len(range_months) == 1:
            # Single month
            month_name = range_months[0][1]
            formatted_ranges.append(month_map[month_name])
        else:
            # Range of months
            start_month = range_months[0][1]
            end_month = range_months[-1][1]
            formatted_ranges.append(f"{month_map[start_month]}-{month_map[end_month]}")
    
    return " / ".join(formatted_ranges)
