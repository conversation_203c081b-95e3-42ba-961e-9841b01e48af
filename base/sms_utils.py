from django.conf import settings
from typing import List, Dict, Any, Optional
import logging
import requests
from accounts.choices import OTPSourceChoices
import json

logger = logging.getLogger(__name__)


# class SMSTemplateConstant:
#     """
#     Constants for SMS templates
#     """

#     # Signing-in ZUUMM: 68771c84d6fc0516625dd092 is the template ID
#     # OTP is your OTP for signing in to ZUUMM. Please do not share it with anyone.
#     # PARTNER_REGISTRATION_OTP = "68771c84d6fc0516625dd092"
#     PARTNER_REGISTRATION_OTP = "6880736dd6fc053b3a20f2b2"

#     # Signing-up ZUUMM: 68771d3ad6fc051729269c43 is the template ID
#     # OTP is your OTP for signing up to ZUUMM. Please do not share it with anyone.
#     LOGIN_SIGNUP_OTP = '68771d3ad6fc051729269c43'


class SMSTemplateConstant:
    """
    Constants for SMS templates
    """
    # OTP is your OTP for signing in to ZUUMM. Please do not share it with anyone.
    # LOGIN_OTP = '6880736dd6fc053b3a20f2b2'
    LOGIN_OTP = '68771d3ad6fc051729269c43'

    # OTP is your OTP for signing up to ZUUMM. Please do not share it with anyone.
    PARTNER_REGISTRATION_OTP = '68771d3ad6fc051729269c43'
    # SIGNUP_OTP = '68771d3ad6fc051729269c43'
    SIGNUP_OTP = '6880736dd6fc053b3a20f2b2'



class SMSContentManager:
    """
    Manages SMS content templates for different types of SMS
    """
    @staticmethod
    def get_sms_content(sms_type, context, nested_content_type=None):
        """
        Get SMS content based on SMS type and context
        """
        otp = None
        message = None
        template_id = None

        if sms_type == 'partner_registration_otp':
            otp = context.get('otp', '')
            message = f"Dear Partner, your OTP for phone number verification during registration on Zuumm is {otp}. Do not share it with anyone."
            template_id = SMSTemplateConstant.PARTNER_REGISTRATION_OTP

        elif sms_type == OTPSourceChoices.LOGIN_SIGNUP.value:
            otp = context.get('otp', '')
            message = f"Dear User, your OTP for phone number verification during login/signup on Zuumm is {otp}. Do not share it with anyone."
            template_id = SMSTemplateConstant.SIGNUP_OTP

            if nested_content_type:
                if nested_content_type == 'LOGIN_OTP':
                    template_id = SMSTemplateConstant.LOGIN_OTP
                elif nested_content_type == 'SIGNUP_OTP':
                    template_id = SMSTemplateConstant.SIGNUP_OTP

        return otp, message, template_id


class MSG91SMSProvider:
    """
    MSG91 SMS provider implementation (Indian SMS service)
    """
    def __init__(self):
        self.api_key = getattr(settings, 'MSG91_API_KEY', None)
        # self.template_id = getattr(settings, 'MSG91_TEMPLATE_ID', None)
        self.base_url = 'https://api.msg91.com/api/v5/flow/'
        
        if not self.api_key:
            logger.warning("MSG91 API key not configured")

    def send_sms(self, phone_number, otp=None, message=None, template_id=None):
        """
        Send SMS using MSG91 API
        """
        try:
            headers = {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'authkey': self.api_key
            }
            
            # Remove '+' from phone number if present
            phone_number = phone_number.replace('+', '')
            
            payload = {
                'template_id': template_id,
                'short_url': '0',
                'recipients': [
                    {
                        'mobiles': phone_number,
                        'VAR1': otp
                    }
                ]
            }
            
            response = requests.post(
                f"{self.base_url}",
                headers=headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                logger.info(f"SMS sent successfully via MSG91: {result}")
                return True, result.get('request_id', 'success')
            else:
                logger.error(f"MSG91 API error: {response.status_code} - {response.text}")
                return False, f"API error: {response.status_code}"
                
        except Exception as e:
            logger.error(f"Failed to send SMS via MSG91: {str(e)}")
            return False, str(e)


def send_sms_task(phone_number, sms_type, context={}, nested_content_type=None):
    """
    Generic function to send SMS
    
    Args:
        phone_number: Recipient phone number (with country code)
        message: SMS content (optional if sms_type and context provided)
        sms_type: Type of SMS for template generation
        context: Context data for template generation
        nested_content_type: Nested content type for template generation
    Returns:
        tuple: (success: bool, result: str)
    """
    try:
        otp, message, template_id = SMSContentManager.get_sms_content(sms_type, context, nested_content_type)
        if not message:
            logger.error("No SMS message provided")
            return False, "No message content"

        if not template_id:
            logger.error("No template ID provided")
            return False, "No template ID"
        
        # Validate phone number format
        if not phone_number or len(phone_number.replace('+', '').replace(' ', '').replace('-', '')) < 10:
            logger.error(f"Invalid phone number format: {phone_number}")
            return False, "Invalid phone number format"
        
        # Get SMS provider and send
        provider = MSG91SMSProvider()
        success, result = provider.send_sms(phone_number, otp, message, template_id)
        
        if success:
            logger.info(f"SMS sent successfully to {phone_number}")
        else:
            logger.error(f"Failed to send SMS to {phone_number}: {result}")
        
        return success, result
        
    except Exception as e:
        logger.error(f"SMS sending failed: {str(e)}")
        return False, str(e)
