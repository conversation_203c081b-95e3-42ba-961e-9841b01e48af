from rest_framework.pagination import PageNumberPagination
from rest_framework.response import Response


class GenericPagination(PageNumberPagination):
    """
    Generic pagination class that can be used across all APIs
    Provides consistent pagination response format with 50 records per page default
    Can be customized for different APIs by overriding data_key and message
    """
    page_size = 50
    page_size_query_param = 'page_size'
    max_page_size = 100
    
    # These can be overridden in subclasses for custom messaging
    data_key = 'data'  # Key name for the actual data in response
    success_message = 'Data retrieved successfully'
    
    def get_paginated_response(self, data):
        """
        Generic paginated response format that can be used across all APIs
        """
        # Get the actual page size used
        actual_page_size = self.get_page_size(self.request) if hasattr(self, 'request') else len(data)
        
        return Response({
            'status': 'success',
            'message': self.success_message,
            'data': {
                'pagination': {
                    'count': self.page.paginator.count,
                    'current_page': self.page.number,
                    'total_pages': self.page.paginator.num_pages,
                    'page_size': actual_page_size,
                    'next': self.get_next_link(),
                    'previous': self.get_previous_link(),
                    'has_next': self.page.has_next(),
                    'has_previous': self.page.has_previous(),
                },
                self.data_key: data
            }
        })


class BlogPostPagination(GenericPagination):
    """
    Custom pagination class for blog posts
    Inherits from GenericPagination with blog-specific messaging
    """
    data_key = 'blogs'
    success_message = 'Blog posts retrieved successfully'


class BlogPodcastPagination(GenericPagination):
    """
    Custom pagination class for blog podcasts
    Inherits from GenericPagination with podcast-specific messaging
    """
    data_key = 'blogs'
    success_message = 'Blog podcasts retrieved successfully'
