from django.conf import settings
from django.http import Http404
from accounts.models import Partner
from accounts.choices import UserTypeChoices, PartnerTypeChoices
from django.contrib.auth import logout
from django.contrib import messages
import re
import logging

logger = logging.getLogger(__name__)

class PartnerMiddleware:
    """
    Middleware to extract partner domain from request and validate partner existence.
    Now also supports API domain detection via 'domain' header for REST framework APIs.
    """
    def __init__(self, get_response):
        self.get_response = get_response
        # Compile regex patterns for different environments
        self.domain_patterns = {
            'dev': r'^(?P<partner_domain>[^.]+)\.dev-api\.zuumm\.ai$',
            'uat': r'^(?P<partner_domain>[^.]+)\.uat-api\.zuumm\.ai$',
            'prod': r'^(?P<partner_domain>[^.]+)\.app\.zuumm\.ai$'
        }
        # Map hostnames to environments
        self.env_map = {
            'dev-api.zuumm.ai': 'dev',
            'uat-api.zuumm.ai': 'uat',
            'app.zuumm.ai': 'prod',
            '127.0.0.1:8000': 'local'
        }

    def _extract_partner_domain(self, request):
        """
        Extract partner domain from request host
        Returns tuple of (partner_domain, environment)
        """
        host = request.get_host().lower()
        
        # First check if it's a base domain (superadmin login)
        if host in self.env_map:
            return None, self.env_map[host]
            
        # Try to match partner subdomain pattern
        for env, pattern in self.domain_patterns.items():
            match = re.match(pattern, host)
            if match:
                return match.group('partner_domain'), env
                
        # If no pattern matches, it's an invalid domain
        return None, None

    def _extract_partner_from_header(self, request):
        """
        Extract partner domain from 'domain' header for API requests.
        Returns partner_domain or None.
        """
        # Get domain from header (case-insensitive)
        domain_header = request.META.get('HTTP_DOMAIN', '').strip()
        
        if not domain_header:
            return None
            
        # Return the domain value from header
        return domain_header

    def _get_partner(self, partner_domain):
        """
        Get partner by domain
        Returns partner object or None
        """
        try:
            return Partner.objects.get(subdomain=partner_domain)
        except Partner.DoesNotExist:
            return None

    def _get_zuumm_partner(self):
        """
        Get the ZUUMM partner (fallback for API requests without domain header)
        Returns partner object or None
        """
        try:
            return Partner.objects.filter(partner_type=PartnerTypeChoices.ZUUMM.value).first()
        except Exception as e:
            logger.error(f"Error fetching ZUUMM partner: {e}")
            return None

    def _is_api_request(self, request):
        """
        Check if the request is an API request
        """
        # Check if it's a REST framework API request
        return (
            request.path.startswith('/api/') or
            request.content_type == 'application/json' or
            'application/json' in request.META.get('HTTP_ACCEPT', '')
        )

    def __call__(self, request):
        # Extract partner domain and environment from host
        partner_domain, environment = self._extract_partner_domain(request)
        
        # Store environment in request
        request.environment = environment
        
        # Initialize request.domain as None
        request.domain = None
        
        # Check if this is an API request and handle domain header
        if self._is_api_request(request):
            # For API requests, check for domain header
            header_domain = self._extract_partner_from_header(request)
            
            if header_domain:
                # Domain header is present, try to get partner by subdomain
                api_partner = self._get_partner(header_domain)
                if api_partner:
                    # Valid partner found
                    request.domain = api_partner
                    logger.debug(f"API request with valid domain header: {header_domain} -> Partner: {api_partner.entity_name}")
                else:
                    # Invalid subdomain in header
                    request.domain = None
                    logger.warning(f"API request with invalid domain header: {header_domain}")
            else:
                # No domain header, default to ZUUMM partner
                zuumm_partner = self._get_zuumm_partner()
                request.domain = zuumm_partner
                logger.debug(f"API request without domain header, defaulting to ZUUMM partner")
        else:
            # For non-API requests (admin panel, web), use host-based logic
            # Set request.domain same as request.partner for consistency
            if partner_domain:
                partner = self._get_partner(partner_domain)
                request.domain = partner
            else:
                request.domain = None
    
        # Handle user authentication and partner validation (existing logic)
        if request.user.is_authenticated and not request.user.is_active:
            # Handle inactive users (both partner admins and superadmins)
            if request.user.user_type == UserTypeChoices.SUPER_ADMIN.value:
                logger.warning(
                    f"Inactive superadmin access attempt: "
                    f"by user {request.user.email} - logging out"
                )
                
                # Logout the user
                logout(request)
                
                # Add a message if we're handling an admin request
                if request.path.startswith('/admin/'):
                    messages.error(
                        request, 
                        "Your superadmin account has been deactivated. Please contact support or the primary superadmin."
                    )

        # If partner domain exists, validate partner (existing logic for admin panel)
        if partner_domain:
            partner = self._get_partner(partner_domain)
            if not partner:
                # Log invalid partner access attempt
                logger.warning(
                    f"Invalid partner access attempt: {partner_domain} "
                    f"from {request.META.get('REMOTE_ADDR')}"
                )
                # Don't raise 404 here, let the view handle the error
                request.partner = None
            else:
                request.partner = partner
                request.partner_domain = partner_domain
                
                # Check if partner is inactive and user is authenticated
                if not partner.is_active and request.user.is_authenticated:
                    # Only log out partner admins of this specific partner
                    if (hasattr(request.user, 'partner') and 
                        request.user.partner == partner and 
                        request.user.user_type == UserTypeChoices.PARTNER_ADMIN.value):  # PA = PartnerAdmin
                        
                        logger.warning(
                            f"Inactive partner access attempt: {partner_domain} "
                            f"by user {request.user.email} - logging out"
                        )
                        
                        # Logout the user
                        logout(request)
                        
                        # Add a message if we're handling an admin request
                        if request.path.startswith('/admin/'):
                            messages.error(
                                request, 
                                "Your partner has been deactivated by Zuumm. Please contact support."
                            )
            
                # Check if user is authenticated but inactive
                elif request.user.is_authenticated and not request.user.is_active:
                    # Only log out partner admins of this specific partner
                    if (hasattr(request.user, 'partner') and 
                        request.user.partner == partner and 
                        request.user.user_type == UserTypeChoices.PARTNER_ADMIN.value):
                        
                        logger.warning(
                            f"Inactive partner admin access attempt: {partner_domain} "
                            f"by user {request.user.email} - logging out"
                        )
                        
                        # Logout the user
                        logout(request)
                        
                        # Add a message if we're handling an admin request
                        if request.path.startswith('/admin/'):
                            messages.error(
                                request, 
                                "Your partner admin account has been deactivated. Please contact support."
                            )
                        
                        # Let the response continue, user will see login page with message
        else:
            request.partner = None
            request.partner_domain = None

        response = self.get_response(request)
        return response 