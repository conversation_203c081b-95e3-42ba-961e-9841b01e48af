"""
Main GetYourGuide Data Fetcher Script
Orchestrates the entire process of fetching and storing GetYourGuide activity data
"""

import time
from datetime import datetime
from .config import GetYourGuideConfig
from .logger import GetYourGuideLogger
from .api_client import GetYourGuideAPIClient
from .data_processor import GetYourGuideDataProcessor
from .database_manager import GetYourGuideDatabaseManager


class GetYourGuideDataFetcher:
    """
    Main class that orchestrates the entire GetYourGuide data fetching process
    Coordinates API calls, data processing, and database operations
    """
    
    def __init__(self):
        # Initialize components
        self.logger = GetYourGuideLogger()
        self.api_client = GetYourGuideAPIClient(self.logger)
        self.data_processor = GetYourGuideDataProcessor(self.logger)
        self.database_manager = GetYourGuideDatabaseManager(self.logger)
        
        # Initialize statistics
        self.stats = {
            'total_destinations': 0,
            'successful_destinations': 0,
            'failed_destinations': 0,
            'total_activities_found': 0,
            'successful_activities': 0,
            'failed_activities': 0,
            'empty_responses': 0,
            'destinations_not_found': 0,
            'duplicates_skipped': 0,
            'start_time': None,
            'end_time': None,
            'execution_time': 0
        }
    
    def cleanup(self):
        """Clean up resources"""
        try:
            if hasattr(self, 'database_manager') and self.database_manager:
                self.database_manager.close()
            if hasattr(self, 'api_client') and self.api_client:
                self.api_client.close()
        except Exception as e:
            if hasattr(self, 'logger'):
                self.logger.log_exception(e, "cleaning up resources")
    
    def run(self):
        """
        Main execution method
        Runs the complete data fetching process
        """
        try:
            # Log script start
            self.stats['start_time'] = datetime.now()
            self.logger.log_script_start()
            
            # Validate prerequisites
            if not self._validate_prerequisites():
                self.logger.error("Prerequisites validation failed. Exiting.")
                return False
            
            # Get all destinations
            destinations = self.database_manager.get_all_destinations()
            if not destinations:
                self.logger.error("No destinations found in database. Exiting.")
                return False
            
            self.stats['total_destinations'] = len(destinations)
            self.logger.info(f"Found {len(destinations)} destinations to process")
            
            # Process each destination
            for index, destination in enumerate(destinations, 1):
                try:
                    success = self._process_destination(destination, index, len(destinations))
                    
                    if success:
                        self.stats['successful_destinations'] += 1
                    else:
                        self.stats['failed_destinations'] += 1
                
                except Exception as e:
                    self.stats['failed_destinations'] += 1
                    self.logger.log_exception(e, f"processing destination: {destination.title}")
                    continue
            
            # Log completion
            self.stats['end_time'] = datetime.now()
            self.stats['execution_time'] = (self.stats['end_time'] - self.stats['start_time']).total_seconds()
            
            # Log final statistics
            self._log_final_statistics()
            
            return True
            
        except KeyboardInterrupt:
            self.logger.warning("Script interrupted by user")
            return False
        except Exception as e:
            self.logger.log_exception(e, "main execution")
            return False
        finally:
            # Always cleanup resources
            self.cleanup()
    
    def _validate_prerequisites(self):
        """Validate all prerequisites before starting"""
        self.logger.info("Validating prerequisites...")
        
        try:
            # Validate API client configuration
            if not self.api_client.validate_configuration():
                self.logger.error("API client configuration validation failed")
                return False
            
            # Test API connection
            if not self.api_client.test_api_connection():
                self.logger.error("API connection test failed")
                return False
            
            # Validate database connection
            if not self.database_manager.validate_database_connection():
                self.logger.error("Database connection validation failed")
                return False
            
            self.logger.info("All prerequisites validated successfully")
            return True
            
        except Exception as e:
            self.logger.log_exception(e, "validating prerequisites")
            return False
    
    def _process_destination(self, destination, index, total):
        """
        Process a single destination
        
        Args:
            destination: Destination model instance
            index: Current destination index
            total: Total number of destinations
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Log start of destination processing
            self.logger.log_destination_processing_start(destination.title, index, total)
            
            # Fetch activities from GetYourGuide API
            activities_data = self.api_client.fetch_activities_for_destination(destination.title)
            
            if activities_data is None:
                # Handle case where destination not found or API error
                self.stats['destinations_not_found'] += 1
                self.logger.log_destination_processing_end(destination.title, 0, success=False)
                return False
            
            if not activities_data:
                # Handle empty response
                self.stats['empty_responses'] += 1
                self.logger.log_destination_processing_end(destination.title, 0, success=False)
                return False
            
            # Update statistics
            self.stats['total_activities_found'] += len(activities_data)
            
            # Process the activities data
            processed_activities = self.data_processor.process_activities_data(
                activities_data, destination.title
            )
            
            if not processed_activities:
                self.logger.warning(f"No activities could be processed for {destination.title}")
                self.logger.log_destination_processing_end(destination.title, 0, success=False)
                return False
            
            # Create activities in database
            creation_stats = self.database_manager.create_custom_activities(
                processed_activities, destination
            )
            
            # Update global statistics
            self.stats['successful_activities'] += creation_stats.get('created', 0)
            self.stats['failed_activities'] += creation_stats.get('failed', 0)
            self.stats['duplicates_skipped'] += creation_stats.get('duplicates', 0)
            
            # Log successful completion
            activities_created = creation_stats.get('created', 0)
            self.logger.log_destination_processing_end(destination.title, activities_created, success=True)
            
            return activities_created > 0
            
        except Exception as e:
            self.logger.log_exception(e, f"processing destination: {destination.title}")
            self.logger.log_destination_processing_end(destination.title, 0, success=False)
            return False
    
    def _log_final_statistics(self):
        """Log comprehensive final statistics"""
        # Log main completion
        self.logger.log_script_end(self.stats)
        
        # Log performance metrics
        if self.stats['execution_time'] > 0:
            self.logger.log_performance_metrics(
                self.stats['execution_time'],
                self.stats['successful_destinations'],
                self.stats['successful_activities']
            )
        
        # Log detailed summary
        self.logger.log_summary_stats(self.stats)
        
        # Get and log database statistics
        db_stats = self.database_manager.get_activity_statistics()
        if db_stats:
            self.logger.info("Current Database Statistics:")
            for key, value in db_stats.items():
                self.logger.info(f"  {key}: {value}")
    
    def process_single_destination(self, destination_name):
        """
        Process a single destination by name (for testing)
        
        Args:
            destination_name (str): Name of the destination to process
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.logger.info(f"Processing single destination: {destination_name}")
            
            # Find destination in database
            destination = self.database_manager.find_destination_by_name(destination_name)
            
            if not destination:
                self.logger.error(f"Destination not found in database: {destination_name}")
                return False
            
            # Validate prerequisites
            if not self._validate_prerequisites():
                self.logger.error("Prerequisites validation failed")
                return False
            
            # Process the destination
            success = self._process_destination(destination, 1, 1)
            
            if success:
                self.logger.info(f"Successfully processed destination: {destination_name}")
            else:
                self.logger.error(f"Failed to process destination: {destination_name}")
            
            return success
            
        except Exception as e:
            self.logger.log_exception(e, f"processing single destination: {destination_name}")
            return False
    
    def get_current_statistics(self):
        """Get current execution statistics"""
        return self.stats.copy()
    
    def clean_existing_data(self):
        """Clean existing custom activity data"""
        try:
            self.logger.info("Cleaning existing custom activity data...")
            
            # Remove duplicates
            removed_count = self.database_manager.clean_duplicate_activities()
            self.logger.info(f"Cleanup completed. Removed {removed_count} duplicate activities")
            
            return True
            
        except Exception as e:
            self.logger.log_exception(e, "cleaning existing data")
            return False


def main():
    """
    Main entry point for the script
    Can be called directly or imported
    """
    try:
        # Create and run the fetcher
        fetcher = GetYourGuideDataFetcher()
        
        # Optional: Clean existing data first
        # fetcher.clean_existing_data()
        
        # Run the main process
        success = fetcher.run()
        
        if success:
            print("\n" + "="*60)
            print("GETYOURGUIDE DATA FETCHER COMPLETED SUCCESSFULLY")
            print("="*60)
            return 0
        else:
            print("\n" + "="*60)
            print("GETYOURGUIDE DATA FETCHER COMPLETED WITH ERRORS")
            print("="*60)
            return 1
    
    except Exception as e:
        print(f"\nFATAL ERROR: {str(e)}")
        return 1


if __name__ == "__main__":
    import sys
    exit_code = main()
    sys.exit(exit_code) 