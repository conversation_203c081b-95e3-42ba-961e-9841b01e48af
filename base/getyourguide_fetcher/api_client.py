"""
API Client for GetYourGuide Data Fetcher
Handles all API requests to GetYourGuide with proper error handling and rate limiting
"""

import requests
import time
import json
from urllib.parse import urlencode
from .config import GetYourGuideConfig
from .logger import GetYourGuideLogger


class GetYourGuideAPIClient:
    """
    API client for making requests to GetYourGuide Partner API
    Handles rate limiting, retries, and error management
    """
    
    def __init__(self, logger=None):
        self.logger = logger or GetYourGuideLogger()
        self.base_url = GetYourGuideConfig.GYG_BASE_URL
        self.headers = GetYourGuideConfig.GYG_HEADERS.copy()
        self.default_params = GetYourGuideConfig.GYG_DEFAULT_PARAMS.copy()
        
        # Rate limiting and retry settings
        self.request_delay = GetYourGuideConfig.REQUEST_DELAY
        self.max_retries = GetYourGuideConfig.MAX_RETRIES
        self.timeout = GetYourGuideConfig.REQUEST_TIMEOUT
        
        # Track last request time for rate limiting
        self._last_request_time = 0
        
        # Create a persistent session for better performance
        self.session = requests.Session()
        self.session.headers.update(self.headers)
    
    def close(self):
        """Close the requests session"""
        try:
            if hasattr(self, 'session') and self.session:
                self.session.close()
        except Exception as e:
            if hasattr(self, 'logger'):
                self.logger.log_exception(e, "closing API client session")
    
    def fetch_activities_for_destination(self, destination_name):
        """
        Fetch activities from GetYourGuide API for a specific destination
        
        Args:
            destination_name (str): Name of the destination to search for
            
        Returns:
            dict: API response data with activities or None if failed
        """
        try:
            # Prepare request parameters
            params = self._prepare_request_params(destination_name)
            
            # Build full URL for logging
            full_url = f"{self.base_url}?{urlencode(params, doseq=True)}"
            
            # Log API request
            self.logger.log_api_request(destination_name, full_url, params)
            
            # Make API request with retries
            response_data = self._make_request_with_retries(params, destination_name)
            
            if response_data is None:
                self.logger.log_empty_response(destination_name, full_url)
                return None
            
            # Extract activities from response
            activities = response_data.get('tours', [])
            
            # Log API response
            self.logger.log_api_response(destination_name, 200, len(activities))
            
            if not activities:
                self.logger.log_empty_response(destination_name, full_url)
                return None
            
            self.logger.info(f"Successfully fetched {len(activities)} activities for {destination_name}")
            return activities
            
        except Exception as e:
            self.logger.log_exception(e, f"fetching activities for {destination_name}")
            return None
    
    def _prepare_request_params(self, destination_name):
        """Prepare request parameters for API call"""
        params = self.default_params.copy()
        params['q'] = destination_name
        
        # Handle date parameter properly for URL encoding
        if 'date[]' in params:
            # Convert date array to proper format for requests
            dates = params.pop('date[]')
            for i, date in enumerate(dates):
                params[f'date[{i}]'] = date
        
        return params
    
    def _make_request_with_retries(self, params, destination_name):
        """Make API request with retry logic"""
        last_exception = None
        
        for attempt in range(1, self.max_retries + 1):
            try:
                # Apply rate limiting
                self._apply_rate_limiting()
                
                # Make the actual request
                response = self.session.get(
                    self.base_url,
                    params=params,
                    timeout=self.timeout
                )
                
                # Update last request time
                self._last_request_time = time.time()
                
                # Check response status
                if response.status_code == 200:
                    try:
                        response_data = response.json()
                        return response_data
                    except json.JSONDecodeError as e:
                        raise Exception(f"Invalid JSON response: {str(e)}")
                
                elif response.status_code == 404:
                    # Destination not found in GetYourGuide
                    self.logger.log_no_destination_found(destination_name)
                    return None
                
                elif response.status_code == 429:
                    # Rate limit exceeded - wait longer
                    wait_time = self.request_delay * attempt * 2
                    self.logger.warning(f"Rate limit exceeded. Waiting {wait_time} seconds...")
                    time.sleep(wait_time)
                    continue
                
                else:
                    raise Exception(f"HTTP {response.status_code}: {response.text}")
                
            except requests.exceptions.Timeout:
                last_exception = Exception(f"Request timeout after {self.timeout} seconds")
                
            except requests.exceptions.ConnectionError:
                last_exception = Exception("Connection error - network or server issue")
                
            except requests.exceptions.RequestException as e:
                last_exception = Exception(f"Request error: {str(e)}")
                
            except Exception as e:
                last_exception = e
            
            # Log retry attempt if not the last attempt
            if attempt < self.max_retries:
                self.logger.log_retry_attempt(attempt, self.max_retries, str(last_exception))
                
                # Exponential backoff
                wait_time = self.request_delay * (2 ** attempt)
                time.sleep(wait_time)
            else:
                # Final attempt failed
                self.logger.error(f"All {self.max_retries} attempts failed for {destination_name}")
                if last_exception:
                    self.logger.log_exception(last_exception, f"final API request for {destination_name}")
        
        return None
    
    def _apply_rate_limiting(self):
        """Apply rate limiting to prevent overwhelming the API"""
        current_time = time.time()
        time_since_last_request = current_time - self._last_request_time
        
        if time_since_last_request < self.request_delay:
            sleep_time = self.request_delay - time_since_last_request
            self.logger.log_rate_limiting(sleep_time)
            time.sleep(sleep_time)
    
    def test_api_connection(self):
        """Test API connection with a simple request"""
        try:
            self.logger.info("Testing GetYourGuide API connection...")
            
            # Make a simple test request for a popular destination
            test_params = self.default_params.copy()
            test_params['q'] = 'dubai'
            test_params['limit'] = 1  # Just get one result for testing
            
            response = self.session.get(
                self.base_url,
                params=test_params,
                timeout=10
            )
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    tours_count = len(data.get('tours', []))
                    self.logger.info(f"API connection test successful. Found {tours_count} tours for Dubai.")
                    return True
                except json.JSONDecodeError:
                    self.logger.error("API connection test failed - invalid JSON response")
                    return False
            else:
                self.logger.error(f"API connection test failed - HTTP {response.status_code}")
                return False
                
        except Exception as e:
            self.logger.log_exception(e, "testing API connection")
            return False
    
    def get_api_status(self):
        """Get current API status and configuration"""
        status = {
            'base_url': self.base_url,
            'headers_configured': bool(self.headers.get('authorization')),
            'rate_limit_delay': self.request_delay,
            'max_retries': self.max_retries,
            'timeout': self.timeout,
            'default_params': self.default_params
        }
        
        self.logger.debug(f"API Status: {status}")
        return status
    
    def validate_configuration(self):
        """Validate API client configuration"""
        issues = []
        
        # Check authorization token
        if not self.headers.get('authorization'):
            issues.append("Missing authorization token")
        
        # Check base URL
        if not self.base_url:
            issues.append("Missing base URL")
        
        # Check rate limiting settings
        if self.request_delay <= 0:
            issues.append("Invalid rate limit delay")
        
        if self.max_retries <= 0:
            issues.append("Invalid max retries setting")
        
        if self.timeout <= 0:
            issues.append("Invalid timeout setting")
        
        if issues:
            for issue in issues:
                self.logger.error(f"Configuration issue: {issue}")
            return False
        
        self.logger.info("API client configuration is valid")
        return True 