"""
Logger utility for GetYourGuide Data Fetcher Script
Provides comprehensive logging functionality with file and console output
"""

import logging
import sys
from datetime import datetime
from pathlib import Path
from .config import GetYourGuideConfig


class GetYourGuideLogger:
    """
    Custom logger class for GetYourGuide data fetcher script
    Provides both file and console logging with different levels
    """
    
    def __init__(self, log_file_path=None, log_level=None):
        self.log_file_path = log_file_path or GetYourGuideConfig.MAIN_LOG_FILE
        self.log_level = log_level or GetYourGuideConfig.LOG_LEVEL
        self.logger = None
        self._setup_logger()
        
        # Setup separate loggers for different error types
        self._setup_error_loggers()
    
    def _setup_logger(self):
        """Setup main logger with file and console handlers"""
        # Create logger
        self.logger = logging.getLogger('GetYourGuideDataFetcher')
        self.logger.setLevel(getattr(logging, self.log_level))
        
        # Remove existing handlers to avoid duplication
        self.logger.handlers.clear()
        
        # Create formatter
        formatter = logging.Formatter(GetYourGuideConfig.LOG_FORMAT)
        
        # Create file handler
        file_handler = logging.FileHandler(self.log_file_path)
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        
        # Create console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        
        # Add handlers to logger
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
    
    def _setup_error_loggers(self):
        """Setup separate loggers for different error types"""
        # Simple file formatter for error logs
        simple_formatter = logging.Formatter('%(asctime)s - %(message)s')
        
        # Failed activities logger
        self.failed_activities_logger = logging.getLogger('FailedActivities')
        self.failed_activities_logger.setLevel(logging.INFO)
        failed_handler = logging.FileHandler(GetYourGuideConfig.FAILED_ACTIVITIES_LOG)
        failed_handler.setFormatter(simple_formatter)
        self.failed_activities_logger.addHandler(failed_handler)
        
        # No destination logger
        self.no_destination_logger = logging.getLogger('NoDestination')
        self.no_destination_logger.setLevel(logging.INFO)
        no_dest_handler = logging.FileHandler(GetYourGuideConfig.NO_DESTINATION_LOG)
        no_dest_handler.setFormatter(simple_formatter)
        self.no_destination_logger.addHandler(no_dest_handler)
        
        # Empty response logger
        self.empty_response_logger = logging.getLogger('EmptyResponse')
        self.empty_response_logger.setLevel(logging.INFO)
        empty_handler = logging.FileHandler(GetYourGuideConfig.EMPTY_RESPONSE_LOG)
        empty_handler.setFormatter(simple_formatter)
        self.empty_response_logger.addHandler(empty_handler)
    
    def info(self, message):
        """Log info message"""
        self.logger.info(message)
    
    def debug(self, message):
        """Log debug message"""
        self.logger.debug(message)
    
    def warning(self, message):
        """Log warning message"""
        self.logger.warning(message)
    
    def error(self, message):
        """Log error message"""
        self.logger.error(message)
    
    def critical(self, message):
        """Log critical message"""
        self.logger.critical(message)
    
    def log_exception(self, exception, context=""):
        """Log exception with context"""
        if context:
            self.logger.error(f"Exception in {context}: {str(exception)}")
        else:
            self.logger.error(f"Exception: {str(exception)}")
        self.logger.debug(f"Exception details: {str(exception)}", exc_info=True)
    
    def log_script_start(self):
        """Log script start with header"""
        self.logger.info("=" * 80)
        self.logger.info("GETYOURGUIDE DATA FETCHER SCRIPT STARTED")
        self.logger.info("=" * 80)
        self.logger.info(f"Start Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        self.logger.info(f"Test Mode: {GetYourGuideConfig.TEST_MODE}")
        if GetYourGuideConfig.TEST_MODE:
            self.logger.info(f"Test Mode Limit: {GetYourGuideConfig.TEST_MODE_DESTINATION_LIMIT} destinations")
    
    def log_script_end(self, stats):
        """Log script completion with statistics"""
        self.logger.info("-" * 60)
        self.logger.info("SCRIPT EXECUTION COMPLETED")
        self.logger.info("-" * 60)
        self.logger.info(f"End Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        self.logger.info(f"Total Destinations Processed: {stats.get('total_destinations', 0)}")
        self.logger.info(f"Successful Destinations: {stats.get('successful_destinations', 0)}")
        self.logger.info(f"Failed Destinations: {stats.get('failed_destinations', 0)}")
        self.logger.info(f"Total Activities Found: {stats.get('total_activities_found', 0)}")
        self.logger.info(f"Successfully Created Activities: {stats.get('successful_activities', 0)}")
        self.logger.info(f"Failed Activities: {stats.get('failed_activities', 0)}")
        self.logger.info(f"Empty Responses: {stats.get('empty_responses', 0)}")
        self.logger.info(f"Destinations Not Found in GYG: {stats.get('destinations_not_found', 0)}")
        self.logger.info("=" * 80)
    
    def log_destination_processing_start(self, destination_name, index, total):
        """Log start of destination processing"""
        self.logger.info("-" * 60)
        self.logger.info(f"Processing Destination [{index}/{total}]: {destination_name}")
        self.logger.info("-" * 60)
    
    def log_destination_processing_end(self, destination_name, activities_count, success=True):
        """Log end of destination processing"""
        status = "SUCCESS" if success else "FAILED"
        self.logger.info(f"Destination Processing {status}: {destination_name}")
        if success:
            self.logger.info(f"Activities Created: {activities_count}")
        self.logger.info("-" * 60)
    
    def log_api_request(self, destination_name, url, params):
        """Log API request details"""
        self.logger.debug(f"API Request for {destination_name}:")
        self.logger.debug(f"URL: {url}")
        self.logger.debug(f"Params: {params}")
    
    def log_api_response(self, destination_name, response_status, activities_count):
        """Log API response details"""
        self.logger.debug(f"API Response for {destination_name}:")
        self.logger.debug(f"Status Code: {response_status}")
        self.logger.debug(f"Activities Found: {activities_count}")
    
    def log_activity_creation(self, activity_title, tour_id, success=True):
        """Log activity creation result"""
        status = "SUCCESS" if success else "FAILED"
        self.logger.debug(f"Activity Creation {status}: {activity_title} (Tour ID: {tour_id})")
    
    def log_failed_activity(self, destination_name, activity_data, error_message):
        """Log failed activity to separate file"""
        error_info = {
            'destination': destination_name,
            'tour_id': activity_data.get('tour_id', 'Unknown'),
            'title': activity_data.get('title', 'Unknown'),
            'error': error_message
        }
        self.failed_activities_logger.info(f"Failed Activity: {error_info}")
        self.logger.warning(f"Activity creation failed for {activity_data.get('title', 'Unknown')}: {error_message}")
    
    def log_no_destination_found(self, destination_name):
        """Log destination not found in GetYourGuide to separate file"""
        self.no_destination_logger.info(f"Destination not found in GetYourGuide API: {destination_name}")
        self.logger.warning(f"Destination not found in GetYourGuide: {destination_name}")
    
    def log_empty_response(self, destination_name, api_url):
        """Log empty API response to separate file"""
        self.empty_response_logger.info(f"Empty response for destination: {destination_name} | URL: {api_url}")
        self.logger.warning(f"Empty API response for destination: {destination_name}")
    
    def log_data_validation_error(self, activity_data, missing_fields):
        """Log data validation error"""
        self.logger.warning(f"Data validation failed for activity {activity_data.get('title', 'Unknown')}")
        self.logger.warning(f"Missing required fields: {missing_fields}")
    
    def log_database_operation(self, operation, model_name, count=1, success=True):
        """Log database operation"""
        status = "SUCCESS" if success else "FAILED"
        self.logger.debug(f"Database {operation} {status}: {model_name} (Count: {count})")
    
    def log_rate_limiting(self, delay_seconds):
        """Log rate limiting delay"""
        self.logger.debug(f"Rate limiting: Waiting {delay_seconds} seconds before next request")
    
    def log_retry_attempt(self, attempt, max_attempts, error):
        """Log retry attempt"""
        self.logger.warning(f"Retry attempt {attempt}/{max_attempts} due to error: {error}")
    
    def log_transaction_start(self):
        """Log start of database transaction"""
        self.logger.debug("Starting database transaction")
    
    def log_transaction_commit(self):
        """Log successful transaction commit"""
        self.logger.debug("Database transaction committed successfully")
    
    def log_transaction_rollback(self, error):
        """Log transaction rollback"""
        self.logger.error(f"Database transaction rolled back due to error: {error}")
    
    def log_field_mapping(self, original_field, mapped_value, field_name):
        """Log field mapping details"""
        self.logger.debug(f"Field mapping - {field_name}: '{original_field}' -> '{mapped_value}'")
    
    def log_coordinate_conversion(self, lat, lng, success=True):
        """Log coordinate conversion"""
        status = "SUCCESS" if success else "FAILED"
        self.logger.debug(f"Coordinate conversion {status}: lat={lat}, lng={lng}")
    
    def log_category_creation(self, category_name, created=True):
        """Log category creation"""
        action = "Created" if created else "Retrieved existing"
        self.logger.debug(f"Custom Activity Category {action}: {category_name}")
    
    def log_location_creation(self, location_info, created=True):
        """Log location creation"""
        action = "Created" if created else "Retrieved existing"
        self.logger.debug(f"Custom Activity Location {action}: {location_info}")
    
    def log_bulk_operation(self, operation, model_name, batch_size, total_count):
        """Log bulk database operation"""
        self.logger.info(f"Bulk {operation} - {model_name}: Processing {batch_size} items (Total: {total_count})")
    
    def log_performance_metrics(self, total_time, destinations_processed, activities_created):
        """Log performance metrics"""
        self.logger.info(f"Performance Metrics:")
        self.logger.info(f"Total Execution Time: {total_time:.2f} seconds")
        self.logger.info(f"Destinations per minute: {(destinations_processed / total_time * 60):.2f}")
        self.logger.info(f"Activities per minute: {(activities_created / total_time * 60):.2f}")
    
    def log_summary_stats(self, stats):
        """Log detailed summary statistics"""
        self.logger.info("Detailed Summary Statistics:")
        for key, value in stats.items():
            self.logger.info(f"  {key}: {value}") 