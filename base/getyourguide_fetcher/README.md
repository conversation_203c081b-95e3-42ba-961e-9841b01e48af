# GetYourGuide Activity Fetcher

## Overview
This script fetches activities data from the GetYourGuide Partner API and stores it in our database with proper M2M relationships. It automatically downloads activity images to S3 storage and creates comprehensive activity records with categories and locations.

## Key Features
- 🔄 **Automated Data Fetching**: Retrieves activities from GetYourGuide API for all destinations
- 🏷️ **Smart Categorization**: Automatically extracts and maps categories from activity types, API categories, and tags
- 📍 **Location Processing**: Creates location records with coordinates and geographic data
- 🖼️ **Image Management**: Downloads and stores activity images in S3 with multiple quality formats
- 🔗 **M2M Relationships**: Properly links activities with categories and locations through database relationships
- 📊 **Comprehensive Logging**: Detailed logs for monitoring, debugging, and error tracking

## Quick Start

### Single Destination (Test Mode)
```bash
# Process only the first destination (recommended for testing)
python manage.py shell -c "
import sys
sys.path.append('base/getyourguide_fetcher')
from main import main
main()
"
```

### All Destinations (Production Mode)
```bash
# Process all destinations in the database
python manage.py shell -c "
import sys
sys.path.append('base/getyourguide_fetcher')
from main import main
main()
"
```

## Configuration

### Test Mode
**Test Mode (Default: ON)**: Processes only the first destination for testing purposes - useful for debugging and initial setup validation.

- **Enable**: Set `TEST_MODE = True` in `config.py`
- **Disable**: Set `TEST_MODE = False` in `config.py`

### Authentication Token
The script uses JWT authentication for GetYourGuide API access. Tokens typically expire every few hours.

**If you see 401 errors in logs:**
1. Get a fresh token from GetYourGuide Partner dashboard
2. Update `GYG_AUTHORIZATION_TOKEN` in `base/getyourguide_fetcher/config.py`
3. Re-run the script

## Log Files & Monitoring

### Main Log Files
- **Primary Log**: `logs/getyourguide_fetcher.log` - Main execution log
- **Failed Activities**: `logs/gyg_failed_activities.log` - Activities that failed processing
- **Missing Destinations**: `logs/gyg_no_destination.log` - Destinations in our DB with no GYG activities
- **Empty Responses**: `logs/gyg_empty_response.log` - API calls that returned no data

### Checking Destinations Without Activities
To find destinations in our database that don't have activities on GetYourGuide:
```bash
# Check the no destination log
tail -f logs/gyg_no_destination.log

# Or search for specific destination
grep "destination_name" logs/gyg_no_destination.log
```

## Troubleshooting

### Common Issues

| Issue | Log Location | Solution |
|-------|-------------|----------|
| **401 Authentication Error** | `getyourguide_fetcher.log` | Update `GYG_AUTHORIZATION_TOKEN` in config.py |
| **No Activities for Destination** | `gyg_no_destination.log` | Check if destination exists on GetYourGuide platform |
| **Image Download Failures** | `getyourguide_fetcher.log` | Usually temporary - images will retry on next run |
| **Database Connection Issues** | `getyourguide_fetcher.log` | Check Django database settings |
| **API Rate Limiting** | `getyourguide_fetcher.log` | Increase `REQUEST_DELAY` in config.py |

### Error Logs Pattern
```bash
# Monitor real-time execution
tail -f logs/getyourguide_fetcher.log

# Check for authentication issues
grep "401\|Unauthorized" logs/getyourguide_fetcher.log

# View destinations with no activities
cat logs/gyg_no_destination.log
```

## Data Flow

1. **Fetch Destinations**: Retrieves all destinations from our database
2. **API Queries**: Makes requests to GetYourGuide API for each destination
3. **Data Processing**: Cleans and validates activity data
4. **Category Extraction**: Maps activity types and tags to categories
5. **Location Processing**: Creates location records with coordinates
6. **Image Download**: Downloads and stores images in S3
7. **Database Storage**: Creates activities with M2M relationships
8. **Logging**: Records success/failure details

## Database Impact

### Created/Updated Models
- `CustomActivity`: Main activity records
- `CustomActivityCategory`: Activity categories
- `CustomActivityLocation`: Geographic locations
- `CustomActivityCategoryRelation`: M2M links (activity ↔ category)
- `CustomActivityLocationRelation`: M2M links (activity ↔ location)

### Admin Interface
- View activities with category and location counts
- Clickable links to filtered related records
- Image galleries and rating displays
- Geographic coordinate visualization

## Configuration Options

### Image Processing
- `DOWNLOAD_IMAGES`: Enable/disable image downloading
- `MAX_IMAGES_PER_ACTIVITY`: Limit images per activity (default: 5)
- `IMAGE_QUALITY_FORMATS`: Multiple quality versions for each image

### Performance Tuning
- `REQUEST_DELAY`: Delay between API calls (default: 1.0 seconds)
- `MAX_RETRIES`: Retry attempts for failed requests (default: 3)
- `BULK_CREATE_BATCH_SIZE`: Database batch size (default: 100)

### Validation Rules
- `MIN_ACTIVITY_FIELDS`: Required fields for valid activities
- `SKIP_DESTINATIONS_WITH_NO_ACTIVITIES`: Skip empty responses
- `SKIP_ACTIVITIES_WITH_MISSING_REQUIRED_FIELDS`: Skip invalid data

## Success Metrics

After successful execution, you should see:
- ✅ Activities created with proper titles and descriptions
- ✅ Categories assigned based on activity types and tags
- ✅ Locations created with coordinates when available
- ✅ Images downloaded and stored in S3
- ✅ Admin interface showing proper counts and relationships

## Next Steps

1. **Monitor Logs**: Check log files for any issues
2. **Verify Data**: Use Django admin to review created activities
3. **Update Token**: Set up monitoring for 401 errors
4. **Schedule Runs**: Consider setting up periodic execution for data updates

---

For technical details and advanced configuration, see the individual module files in this directory. 