"""
Image downloader utility for GetYourGuide fetcher
Downloads images from GetYourGuide URLs and stores them in our S3 storage
"""

import requests
import os
import logging
from urllib.parse import urlparse
from django.core.files.base import ContentFile
from django.core.files.storage import default_storage
from base.storage_utils import get_random_name, get_mime_type_for_extension
from .config import GetYourGuideConfig
import time

logger = logging.getLogger(__name__)


class GetYourGuideImageDownloader:
    """Download and store GetYourGuide images to S3"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.0.0 Safari/537.36'
        })
        
    def _process_getyourguide_url(self, image_url):
        """
        Process GetYourGuide image URLs to handle format_id placeholders
        
        Args:
            image_url (str): Original image URL from GetYourGuide API
            
        Returns:
            list: List of URLs to try in order of preference
        """
        urls_to_try = []
        
        # Check if this is a GetYourGuide CDN URL with format_id placeholder
        if 'cdn.getyourguide.com' in image_url and '[format_id]' in image_url:
            # Based on the actual URL structure from GetYourGuide API:
            # https://cdn.getyourguide.com/img/tour/63085587da5e9.jpeg/[format_id].jpg
            
            # Approach 1: Try verified working dimension formats
            dimension_formats = [
                '128',      # Medium thumbnail (verified working)
                '68',       # Small thumbnail (verified working)
            ]
            
            for format_id in dimension_formats:
                processed_url = image_url.replace('[format_id]', format_id)
                urls_to_try.append(processed_url)
            
            # Approach 2: Try removing the /[format_id].jpg part and use just the base path
            if '/[format_id].' in image_url:
                base_url = image_url.split('/[format_id].')[0]
                # The base URL should be like: https://cdn.getyourguide.com/img/tour/63085587da5e9.jpeg
                urls_to_try.append(base_url)
                
        else:
            # For non-GetYourGuide URLs or URLs without format_id, use as-is
            urls_to_try.append(image_url)
        
        # Remove duplicates while preserving order
        seen = set()
        unique_urls = []
        for url in urls_to_try:
            if url not in seen:
                seen.add(url)
                unique_urls.append(url)
        
        return unique_urls
        
    def download_and_store_image(self, image_url, activity_id=None):
        """
        Download image from URL and store in S3
        
        Args:
            image_url (str): URL of the image to download
            activity_id (str): Optional activity ID for folder organization
            
        Returns:
            dict: {'success': bool, 'url': str, 'error': str}
        """
        try:
            # Validate URL
            if not image_url or not image_url.startswith(('http://', 'https://')):
                return {'success': False, 'url': None, 'error': 'Invalid URL'}
            
            # Process GetYourGuide URLs to handle format_id placeholders
            urls_to_try = self._process_getyourguide_url(image_url)
            
            # Try each URL until one works
            max_retries = 2  # Reduce retries for faster processing
            timeout = GetYourGuideConfig.IMAGE_DOWNLOAD_TIMEOUT
            max_size_bytes = GetYourGuideConfig.MAX_IMAGE_SIZE_MB * 1024 * 1024
            last_error = None
            
            # Limit to first 3 URL variations to avoid too many attempts
            urls_to_try = urls_to_try[:3]
            
            for url_index, processed_url in enumerate(urls_to_try):
                # Parse URL to get file extension
                parsed_url = urlparse(processed_url)
                filename = os.path.basename(parsed_url.path)
                
                # Try to get extension from URL
                if '.' in filename:
                    extension = filename.split('.')[-1].lower()
                else:
                    extension = 'jpg'  # Default to jpg
                
                # Ensure extension is valid
                valid_extensions = ['jpg', 'jpeg', 'png', 'webp', 'gif']
                if extension not in valid_extensions:
                    extension = 'jpg'
                
                # Try to download this URL variation
                for attempt in range(max_retries):
                    try:
                        logger.info(f"Downloading image (URL {url_index + 1}/{len(urls_to_try)}, attempt {attempt + 1}/{max_retries}): {processed_url}")
                        
                        response = self.session.get(
                            processed_url, 
                            timeout=timeout,
                            stream=True
                        )
                        response.raise_for_status()
                        
                        # Check content type
                        content_type = response.headers.get('content-type', '')
                        if not content_type.startswith('image/'):
                            logger.warning(f"URL does not return an image: {processed_url} (content-type: {content_type})")
                            last_error = f'Not an image: {content_type}'
                            break  # Try next URL variation
                        
                        # Check file size using configured limit
                        content_length = response.headers.get('content-length')
                        if content_length and int(content_length) > max_size_bytes:
                            last_error = f'Image too large: {int(content_length)/1024/1024:.1f}MB > {GetYourGuideConfig.MAX_IMAGE_SIZE_MB}MB'
                            break  # Try next URL variation
                        
                        # Read image content
                        image_content = response.content
                        
                        if not image_content:
                            last_error = 'Empty image content'
                            break  # Try next URL variation
                        
                        # Generate unique filename
                        original_filename = f"getyourguide_image.{extension}"
                        unique_filename = get_random_name(original_filename)
                        
                        # Create storage path
                        if activity_id:
                            storage_path = f"custom_activities/{activity_id}/{unique_filename}"
                        else:
                            storage_path = f"custom_activities/images/{unique_filename}"
                        
                        # Create ContentFile and save to storage
                        content_file = ContentFile(image_content)
                        
                        # Save to default storage (S3)
                        saved_path = default_storage.save(storage_path, content_file)
                        
                        # Get the URL of the saved file
                        saved_url = default_storage.url(saved_path)
                        
                        logger.info(f"Successfully downloaded and stored image: {saved_url}")
                        
                        return {
                            'success': True, 
                            'url': saved_url,
                            'path': saved_path,
                            'error': None
                        }
                        
                    except requests.exceptions.RequestException as e:
                        last_error = str(e)
                        if attempt == max_retries - 1:  # Last attempt for this URL
                            logger.warning(f"Attempt {attempt + 1} failed for URL {url_index + 1}, trying next URL: {str(e)}")
                            break
                        else:
                            logger.warning(f"Attempt {attempt + 1} failed, retrying: {str(e)}")
                            time.sleep(GetYourGuideConfig.REQUEST_DELAY)
                            continue
            
            # If we get here, all URL variations failed
            logger.error(f"Failed to download image after trying {len(urls_to_try)} URL variations: {image_url} - {last_error}")
            return {'success': False, 'url': None, 'error': last_error or 'All URL variations failed'}
                        
        except Exception as e:
            logger.error(f"Unexpected error downloading image {image_url}: {str(e)}")
            return {'success': False, 'url': None, 'error': str(e)}
    
    def process_activity_images(self, pictures_data, activity_id=None):
        """
        Process a list of image URLs for an activity
        
        Args:
            pictures_data (list): List of image data from GetYourGuide API
            activity_id (str): Activity ID for organization
            
        Returns:
            list: Updated pictures data with our S3 URLs
        """
        if not pictures_data or not isinstance(pictures_data, list):
            return []
        
        updated_pictures = []
        successful_downloads = 0
        
        logger.info(f"Processing {len(pictures_data)} images for activity {activity_id}")
        
        for i, picture in enumerate(pictures_data):
            try:
                if isinstance(picture, dict):
                    original_url = picture.get('url')
                    if original_url:
                        # Download and store image
                        result = self.download_and_store_image(original_url, activity_id)
                        
                        if result['success']:
                            # Update picture data with our URL
                            updated_picture = picture.copy()
                            updated_picture['original_url'] = original_url
                            updated_picture['url'] = result['url']
                            updated_picture['stored_path'] = result['path']
                            updated_pictures.append(updated_picture)
                            successful_downloads += 1
                            logger.info(f"Successfully processed image {i+1}/{len(pictures_data)} for activity {activity_id}")
                        else:
                            logger.warning(f"Failed to download image {i+1} for activity {activity_id}: {result['error']}")
                            # Keep original data but mark as failed
                            failed_picture = picture.copy()
                            failed_picture['download_failed'] = True
                            failed_picture['error'] = result['error']
                            updated_pictures.append(failed_picture)
                    else:
                        logger.warning(f"No URL found in picture data {i+1} for activity {activity_id}")
                        updated_pictures.append(picture)
                else:
                    logger.warning(f"Invalid picture data format for activity {activity_id}: {picture}")
                    updated_pictures.append(picture)
                    
                # Add delay between downloads to be respectful
                if i < len(pictures_data) - 1:  # Don't delay after last image
                    time.sleep(GetYourGuideConfig.IMAGE_RETRY_DELAY)  # Use configured delay
                    
            except Exception as e:
                logger.error(f"Error processing picture {i+1} for activity {activity_id}: {str(e)}")
                # Include original data even if processing failed
                if isinstance(picture, dict):
                    failed_picture = picture.copy()
                    failed_picture['processing_error'] = str(e)
                    updated_pictures.append(failed_picture)
                else:
                    updated_pictures.append(picture)
        
        logger.info(f"Completed image processing for activity {activity_id}: {successful_downloads}/{len(pictures_data)} successful downloads")
        
        return updated_pictures

    def save_images_to_media_model(self, custom_activity, pictures_data):
        """
        Save processed images to CustomActivityMedia model
        
        Args:
            custom_activity: CustomActivity instance
            pictures_data (list): List of processed image data
            
        Returns:
            int: Number of media objects created
        """
        from packages.models import CustomActivityMedia
        
        if not pictures_data or not isinstance(pictures_data, list):
            return 0
        
        created_count = 0
        
        for i, picture in enumerate(pictures_data):
            try:
                if isinstance(picture, dict) and picture.get('url') and not picture.get('download_failed'):
                    # Create media object
                    media_obj = CustomActivityMedia(
                        custom_activity=custom_activity,
                        title=picture.get('title', ''),
                        description=picture.get('description', ''),
                        meta_information={
                            'original_url': picture.get('original_url'),
                            'stored_path': picture.get('stored_path'),
                            'download_success': True
                        }
                    )
                    
                    # Save the file from S3 URL to the media field
                    # Note: Since the file is already in S3, we just need to reference it
                    if picture.get('stored_path'):
                        media_obj.media.name = picture['stored_path']
                        media_obj.save()
                        created_count += 1
                        logger.info(f"Created CustomActivityMedia {i+1} for activity {custom_activity.tour_id}")
                    
            except Exception as e:
                logger.error(f"Error creating CustomActivityMedia for picture {i+1}: {str(e)}")
                continue
        
        logger.info(f"Created {created_count} CustomActivityMedia objects for activity {custom_activity.tour_id}")
        return created_count
    
    def close(self):
        """Close the requests session"""
        if self.session:
            self.session.close() 