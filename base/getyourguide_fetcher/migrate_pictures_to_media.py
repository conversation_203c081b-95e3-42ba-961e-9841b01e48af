"""
Migration script to convert existing pictures JSON data to CustomActivityMedia objects
Run this script after applying the migration for CustomActivityMedia model
"""

import os
import sys
import django
from django.db import transaction

# Setup Django environment
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.append(project_root)

# Initialize Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from packages.models import CustomActivity, CustomActivityMedia


def migrate_pictures_to_media():
    """
    Convert existing pictures JSON data to CustomActivityMedia objects
    """
    print("Starting migration of pictures data to CustomActivityMedia model...")
    
    # Get all CustomActivity objects that have pictures data
    activities_with_pictures = CustomActivity.objects.exclude(pictures__isnull=True).exclude(pictures=[])
    
    print(f"Found {activities_with_pictures.count()} activities with pictures data")
    
    created_count = 0
    error_count = 0
    
    for activity in activities_with_pictures:
        try:
            with transaction.atomic():
                pictures_data = activity.pictures or []
                
                for i, picture in enumerate(pictures_data):
                    if isinstance(picture, dict) and picture.get('url'):
                        # Create CustomActivityMedia object
                        media_obj = CustomActivityMedia(
                            custom_activity=activity,
                            title=picture.get('title', f'Image {i+1}'),
                            description=picture.get('description', ''),
                            meta_information={
                                'original_url': picture.get('original_url'),
                                'stored_path': picture.get('stored_path'),
                                'download_success': not picture.get('download_failed', False),
                                'error': picture.get('error', ''),
                                'migrated_from_json': True
                            }
                        )
                        
                        # Set the media file path if available
                        if picture.get('stored_path'):
                            media_obj.media.name = picture['stored_path']
                        
                        media_obj.save()
                        created_count += 1
                
                print(f"Migrated {len(pictures_data)} images for activity: {activity.title}")
                
        except Exception as e:
            error_count += 1
            print(f"Error migrating activity {activity.id}: {str(e)}")
            continue
    
    print(f"Migration completed!")
    print(f"Created: {created_count} CustomActivityMedia objects")
    print(f"Errors: {error_count}")
    
    return created_count, error_count


if __name__ == "__main__":
    migrate_pictures_to_media() 