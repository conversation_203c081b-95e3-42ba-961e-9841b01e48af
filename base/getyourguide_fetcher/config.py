"""
Configuration file for GetYourGuide Data Fetcher Script
Contains all constants, settings and configuration for fetching activities from GetYourGuide API
"""

import os
from django.conf import settings
from pathlib import Path


class GetYourGuideConfig:
    """Configuration class for GetYourGuide data fetcher script"""
    
    # =============================================
    # API CONFIGURATION
    # =============================================
    
    # GetYourGuide API Configuration
    GYG_BASE_URL = "https://partner.getyourguide.com/en-us/api/public-partner-api/1/tours"
    # GYG_AUTHORIZATION_TOKEN = "Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IjQ3YWU0OWM0YzlkM2ViODVhNTI1NDA3MmMzMGQyZThlNzY2MWVmZTEiLCJ0eXAiOiJKV1QifQ.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.pT94agdNc4arRV4O7ot65x0ZF5Yp1AuWm2XnREpak8rI5ZGhV0aDuwuftZtLtRXdrj3mbodAKOxRhMf-CiVVHXPqswUVMZ4nktzZzHYys2qMXlaB6LhhsUvlIgOhifrIMh4qxXeChq5Yrc05hgO1SAV9PxIfreWV9WscF_T9eeEx55V0NKvKhRLulw2T_SzFxTgFs_LGjzMQJy1u7uvtBF890BtkqoXf2axTxMPDjBaxpXiWziNRvsTwykx2xjhFw_wffn-5FQSCVkMTBKOzWSjfwvCfI1vI2NjP5JehBdLnWeCTD-_6aRTfqNpe-WD4mbq_3UE8K3n--eLLMlEcTg"
    GYG_AUTHORIZATION_TOKEN = settings.GYG_AUTHORIZATION_TOKEN
    print("--------------------------------")
    print(GYG_AUTHORIZATION_TOKEN)
    print("--------------------------------")
    # Request Headers
    GYG_HEADERS = {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'en-GB,en-US;q=0.9,en;q=0.8',
        'authorization': GYG_AUTHORIZATION_TOKEN,
        'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.0.0 Safari/537.36'
    }
    
    # API Request Parameters
    GYG_DEFAULT_PARAMS = {
        'limit': 60,
        'preformatted': 'full',
        'sortfield': 'popularity',
        'sortdirection': 'DESC',
        'date[]': ['2025-07-10T00:00:00', '2025-07-31T23:59:59']  # Default date range
    }
    
    # Rate Limiting
    REQUEST_DELAY = 1.0  # Seconds between API requests
    MAX_RETRIES = 3      # Maximum retries for failed requests
    REQUEST_TIMEOUT = 30  # Request timeout in seconds
    
    # =============================================
    # LOGGING CONFIGURATION
    # =============================================
    
    # Log files
    PROJECT_ROOT = Path(__file__).parent.parent.parent
    LOG_DIR = PROJECT_ROOT / 'logs'
    LOG_DIR.mkdir(exist_ok=True)
    
    MAIN_LOG_FILE = LOG_DIR / 'getyourguide_fetcher.log'
    FAILED_ACTIVITIES_LOG = LOG_DIR / 'gyg_failed_activities.log'
    NO_DESTINATION_LOG = LOG_DIR / 'gyg_no_destination.log'
    EMPTY_RESPONSE_LOG = LOG_DIR / 'gyg_empty_response.log'
    
    # Log format and level
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    LOG_LEVEL = 'DEBUG'
    
    # =============================================
    # DATA PROCESSING CONFIGURATION
    # =============================================
    
    # Test mode settings
    TEST_MODE = False
    TEST_MODE_DESTINATION_LIMIT = 1  # Process only first 1 destination in test mode
    
    # Data processing settings
    DOWNLOAD_IMAGES = True  # Set to False to skip image downloading
    MAX_IMAGES_PER_ACTIVITY = 5  # Limit number of images to download per activity
    
    # Image processing settings
    IMAGE_QUALITY_FORMATS = [
        'w_1200,h_630,c_fill,f_auto,fl_progressive',  # Large format
        'w_800,h_600,c_fill,f_auto,fl_progressive',   # Medium format
        'w_600,h_400,c_fill,f_auto,fl_progressive',   # Small format
        'w_400,h_300,c_fill,f_auto,fl_progressive',   # Thumbnail format
        'original'  # Original format
    ]
    
    IMAGE_FALLBACK_EXTENSIONS = ['jpg', 'jpeg', 'png', 'webp']
    MAX_IMAGE_SIZE_MB = 10  # Maximum image size in MB
    IMAGE_DOWNLOAD_TIMEOUT = 30  # Timeout for image downloads in seconds
    IMAGE_RETRY_DELAY = 0.5  # Delay between image downloads in seconds
    
    # Data validation settings
    MIN_ACTIVITY_FIELDS = ['tour_id', 'title']  # Minimum required fields from API response
    
    # Custom activity categories mapping
    ACTIVITY_CATEGORY_MAPPING = {
        'Airport lounges': 'Airport Services',
        'Food & drinks': 'Food & Beverage',
        'Entry tickets': 'Attractions',
        'Tours': 'Guided Tours',
        'Activities': 'Adventure Sports',
        'Transportation': 'Transport',
        'Hotels': 'Accommodation',
        'Cruises': 'Water Activities',
        'Shows': 'Entertainment',
        'Museums': 'Cultural',
        'Shopping': 'Shopping',
        'Wellness': 'Wellness & Spa',
        'Theme Parks': 'Theme Parks',
        'Nature': 'Nature & Wildlife',
        'Adventure': 'Adventure Sports',
        'Cultural': 'Cultural',
        'Historical': 'Historical',
        'Religious': 'Religious',
        'Romantic': 'Romantic',
        'Family': 'Family Friendly',
        'Luxury': 'Luxury Experience',
        'Budget': 'Budget Friendly',
        'Group': 'Group Activities',
        'Private': 'Private Tours',
        'Guided': 'Guided Tours',
        'Self-guided': 'Self Guided',
        'Walking': 'Walking Tours',
        'Cycling': 'Cycling Tours',
        'Photography': 'Photography Tours',
        'Foodie': 'Food Tours',
        'Art': 'Art & Culture',
        'Music': 'Music & Dance',
        'Nightlife': 'Nightlife',
        'Sports': 'Sports',
        'Extreme': 'Extreme Sports',
        'Water': 'Water Activities',
        'Air': 'Aerial Activities',
        'Underground': 'Underground Activities',
        'Seasonal': 'Seasonal Activities',
        'Festival': 'Festivals & Events',
        'Workshop': 'Workshops',
        'Class': 'Classes',
        'Other': 'Miscellaneous'
    }
    
    # Field extraction settings
    MAX_DESCRIPTION_LENGTH = 5000
    MAX_TITLE_LENGTH = 500
    MAX_ABSTRACT_LENGTH = 1000
    
    # Geographic settings
    DEFAULT_SRID = 4326  # WGS84 coordinate system
    
    # =============================================
    # DATABASE CONFIGURATION
    # =============================================
    
    # Default field values
    DEFAULT_ACTIVITY_VALUES = {
        'bestseller': False,
        'certified': False,
        'has_pick_up': False,
        'overall_rating': None,
        'number_of_ratings': None,
        'price': None
    }
    
    # Batch processing settings
    BULK_CREATE_BATCH_SIZE = 100
    
    # =============================================
    # ERROR HANDLING CONFIGURATION
    # =============================================
    
    # Error categories for logging
    ERROR_CATEGORIES = {
        'API_ERROR': 'API Request Failed',
        'DESTINATION_NOT_FOUND': 'Destination Not Found',
        'EMPTY_RESPONSE': 'Empty API Response',
        'DATA_VALIDATION_ERROR': 'Data Validation Failed',
        'DATABASE_ERROR': 'Database Operation Failed',
        'PARSING_ERROR': 'Data Parsing Failed'
    }
    
    # Skip rules
    SKIP_DESTINATIONS_WITH_NO_ACTIVITIES = True
    SKIP_ACTIVITIES_WITH_MISSING_REQUIRED_FIELDS = True
    
    # =============================================
    # FIELD MAPPING CONFIGURATION
    # =============================================
    
    # Map GetYourGuide API response fields to our model fields
    GYG_TO_MODEL_FIELD_MAPPING = {
        'tour_id': 'tour_id',
        'title': 'title',
        'abstract': 'abstract',
        'description': 'description',
        'activity_type': 'activity_type',
        'additional_information': 'additional_information',
        'items_to_bring': lambda x: ', '.join(x) if isinstance(x, list) else str(x) if x else None,
        'not_allowed': lambda x: ', '.join(x) if isinstance(x, list) else str(x) if x else None,
        'not_suitable_for': lambda x: ', '.join(x) if isinstance(x, list) else str(x) if x else None,
        'bestseller': 'bestseller',
        'certified': 'certified',
        'has_pick_up': 'has_pick_up',
        'overall_rating': 'overall_rating',
        'number_of_ratings': 'number_of_ratings',
        'highlights': lambda x: ', '.join(x) if isinstance(x, list) else str(x) if x else None,
        'inclusions': 'inclusions',
        'exclusions': 'exclusions',
        'pictures': 'pictures',
        'coordinates': 'coordinates',
        'price': lambda x: x.get('values', {}).get('amount') if isinstance(x, dict) and x.get('values') else None,
        'opening_hours': 'opening_hours',
        'cancellation_policy_text': 'cancellation_policy_text',
        'cancellation_policy': 'cancellation_policy',
        'durations': 'durations'
    } 