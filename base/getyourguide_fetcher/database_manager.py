"""
Database manager for GetYourGuide Data Fetcher Script
Handles all database operations including creating destinations, activities, categories, and locations
"""

import os
import sys
import django
import logging
from django.db import transaction
from django.db.utils import IntegrityError
from django.core.exceptions import ValidationError
from django.contrib.gis.geos import Point

# Setup Django environment
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.append(project_root)

# Initialize Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

# Import models after Django setup
from packages.models import Destination, CustomActivity, CustomActivityCategory, CustomActivityLocation
from .config import GetYourGuideConfig
from .logger import GetYourGuideLogger
from .image_downloader import GetYourGuideImageDownloader


class GetYourGuideDatabaseManager:
    """
    Database manager for handling all database operations
    Manages creation and updates of custom activity models
    """
    
    def __init__(self, logger=None):
        """Initialize database manager with optional logger"""
        self.logger = logger or GetYourGuideLogger()
        self.batch_size = GetYourGuideConfig.BULK_CREATE_BATCH_SIZE
        self.image_downloader = GetYourGuideImageDownloader()
        
    def close(self):
        """Close any open connections"""
        if hasattr(self, 'image_downloader') and self.image_downloader:
            self.image_downloader.close()
    
    def get_all_destinations(self):
        """
        Get all destinations from the database
        
        Returns:
            QuerySet: All destination objects
        """
        try:
            destinations = Destination.objects.all().order_by('title')
            count = destinations.count()
            self.logger.info(f"Retrieved {count} destinations from database")
            
            if GetYourGuideConfig.TEST_MODE:
                limit = GetYourGuideConfig.TEST_MODE_DESTINATION_LIMIT
                destinations = destinations[:limit]
                self.logger.info(f"Test mode: Limited to {limit} destinations")
            
            return destinations
            
        except Exception as e:
            self.logger.log_exception(e, "retrieving destinations from database")
            return Destination.objects.none()
    
    def find_destination_by_name(self, destination_name):
        """
        Find destination by name (case-insensitive)
        
        Args:
            destination_name (str): Name of the destination
            
        Returns:
            Destination: Destination object or None
        """
        try:
            destination = Destination.objects.filter(
                title__icontains=destination_name
            ).first()
            
            if destination:
                self.logger.debug(f"Found destination: {destination.title}")
            else:
                self.logger.debug(f"Destination not found: {destination_name}")
            
            return destination
            
        except Exception as e:
            self.logger.log_exception(e, f"finding destination: {destination_name}")
            return None
    
    def create_custom_activities(self, activities_data, destination):
        """
        Create custom activities in the database
        
        Args:
            activities_data (list): List of processed activity data
            destination (Destination): Destination object
            
        Returns:
            dict: Creation statistics
        """
        if not activities_data:
            self.logger.warning("No activities data to create")
            return {'created': 0, 'failed': 0, 'duplicates': 0}
        
        created_count = 0
        failed_count = 0
        duplicate_count = 0
        
        self.logger.info(f"Creating {len(activities_data)} custom activities for {destination.title}")
        
        try:
            with transaction.atomic():
                self.logger.log_transaction_start()
                
                for activity_data in activities_data:
                    try:
                        # Check if activity already exists
                        if self._activity_exists(activity_data.get('tour_id')):
                            duplicate_count += 1
                            self.logger.debug(f"Activity already exists: {activity_data.get('title', 'Unknown')}")
                            continue
                        
                        # Create the activity
                        activity = self._create_single_activity(activity_data, destination)
                        
                        if activity:
                            created_count += 1
                            self.logger.log_activity_creation(
                                activity_data.get('title', 'Unknown'),
                                activity_data.get('tour_id', 'Unknown'),
                                success=True
                            )
                        else:
                            failed_count += 1
                            self.logger.log_activity_creation(
                                activity_data.get('title', 'Unknown'),
                                activity_data.get('tour_id', 'Unknown'),
                                success=False
                            )
                    
                    except Exception as e:
                        failed_count += 1
                        self.logger.log_failed_activity(destination.title, activity_data, str(e))
                        continue
                
                self.logger.log_transaction_commit()
                
        except Exception as e:
            self.logger.log_transaction_rollback(str(e))
            # Return partial results even if transaction failed
            pass
        
        stats = {
            'created': created_count,
            'failed': failed_count,
            'duplicates': duplicate_count
        }
        
        self.logger.log_database_operation(
            'CREATE', 'CustomActivity', 
            count=created_count, 
            success=(created_count > 0)
        )
        
        return stats
    
    def _create_single_activity(self, activity_data, destination):
        """Create a single custom activity"""
        try:
            # Remove metadata fields
            clean_data = {k: v for k, v in activity_data.items() if not k.startswith('_')}
            
            # Extract pictures data before creating activity
            pictures_data = clean_data.pop('pictures', [])
            
            # Process images if enabled and they exist
            if (GetYourGuideConfig.DOWNLOAD_IMAGES and pictures_data and 
                isinstance(pictures_data, list)):
                
                # Limit number of images if configured
                max_images = GetYourGuideConfig.MAX_IMAGES_PER_ACTIVITY
                pictures_to_process = pictures_data[:max_images] if max_images > 0 else pictures_data
                
                self.logger.info(f"Processing {len(pictures_to_process)} images for activity: {clean_data.get('title', 'Unknown')}")
                updated_pictures = self.image_downloader.process_activity_images(
                    pictures_to_process, 
                    clean_data.get('tour_id')
                )
            else:
                updated_pictures = []
            
            # Create activity instance without pictures field
            activity = CustomActivity(
                destination=destination,
                **clean_data
            )
            
            # Validate the activity
            activity.full_clean()
            
            # Save to database
            activity.save()
            
            # Save images to CustomActivityMedia model
            if updated_pictures:
                self.image_downloader.save_images_to_media_model(activity, updated_pictures)
            
            # Handle related models (categories, locations) if data exists
            self._handle_related_models(activity, activity_data)
            
            return activity
            
        except ValidationError as e:
            self.logger.error(f"Validation error creating activity: {e}")
            return None
        except IntegrityError as e:
            self.logger.error(f"Integrity error creating activity: {e}")
            return None
        except Exception as e:
            self.logger.log_exception(e, "creating single activity")
            return None
    
    def _handle_related_models(self, activity, activity_data):
        """Handle creation of related models (categories, locations) and M2M relationships"""
        try:
            # Import M2M relationship models
            from packages.models import CustomActivityCategoryRelation, CustomActivityLocationRelation
            
            # Handle categories
            categories_data = activity_data.get('_categories', [])
            if categories_data:
                created_categories = self._create_activity_categories(categories_data)
                # Link categories to activity
                for category in created_categories:
                    try:
                        CustomActivityCategoryRelation.objects.get_or_create(
                            activity=activity,
                            category=category
                        )
                    except Exception as e:
                        self.logger.error(f"Failed to link category {category.name} to activity {activity.title}: {e}")
            
            # Handle locations
            locations_data = activity_data.get('_locations', [])
            if locations_data:
                created_locations = self._create_activity_locations(locations_data)
                # Link locations to activity
                for location in created_locations:
                    try:
                        CustomActivityLocationRelation.objects.get_or_create(
                            activity=activity,
                            location=location
                        )
                    except Exception as e:
                        self.logger.error(f"Failed to link location {location} to activity {activity.title}: {e}")
        
        except Exception as e:
            self.logger.log_exception(e, "handling related models")
    
    def _create_activity_categories(self, categories_data):
        """Create custom activity categories and return created/found objects"""
        created_categories = []
        
        for category_data in categories_data:
            try:
                if isinstance(category_data, dict):
                    category_name = category_data.get('name', '')
                else:
                    category_name = str(category_data)
                
                if category_name:
                    category, created = CustomActivityCategory.objects.get_or_create(
                        name=category_name
                    )
                    created_categories.append(category)
                    self.logger.log_category_creation(category_name, created)
            
            except Exception as e:
                self.logger.log_exception(e, f"creating category: {category_name}")
        
        return created_categories
    
    def _create_activity_locations(self, locations_data):
        """Create custom activity locations and return created/found objects"""
        created_locations = []
        
        for location_data in locations_data:
            try:
                if isinstance(location_data, dict) and location_data.get('type') == 'city':
                    coordinates = location_data.get('coordinates', {})
                    google_place = location_data.get('google_place', {})
                    
                    location_info = {
                        'country': location_data.get('country', ''),
                        'city': location_data.get('name', ''),
                        'google_place_id': google_place.get('google_place_id', ''),
                    }
                    
                    # Handle coordinates
                    if coordinates:
                        lat = coordinates.get('lat')
                        lng = coordinates.get('long') or coordinates.get('lng')
                        
                        if lat is not None and lng is not None:
                            try:
                                point = Point(float(lng), float(lat), srid=GetYourGuideConfig.DEFAULT_SRID)
                                location_info['location_coordinates'] = point
                            except (ValueError, TypeError):
                                pass
                    
                    # Create or get location
                    location, created = CustomActivityLocation.objects.get_or_create(
                        city=location_info['city'],
                        country=location_info['country'],
                        defaults=location_info
                    )
                    
                    created_locations.append(location)
                    self.logger.log_location_creation(
                        f"{location_info['city']}, {location_info['country']}", 
                        created
                    )
            
            except Exception as e:
                self.logger.log_exception(e, "creating location")
        
        return created_locations
    
    def _activity_exists(self, tour_id):
        """Check if activity with given tour_id already exists"""
        if not tour_id:
            return False
        
        try:
            return CustomActivity.objects.filter(tour_id=tour_id).exists()
        except Exception as e:
            self.logger.log_exception(e, f"checking if activity exists: {tour_id}")
            return False
    
    def get_activity_statistics(self):
        """Get statistics about custom activities in database"""
        try:
            stats = {
                'total_activities': CustomActivity.objects.count(),
                'total_categories': CustomActivityCategory.objects.count(),
                'total_locations': CustomActivityLocation.objects.count(),
                'activities_by_destination': {}
            }
            
            # Get activities by destination
            destinations = Destination.objects.all()
            for destination in destinations:
                activity_count = CustomActivity.objects.filter(destination=destination).count()
                if activity_count > 0:
                    stats['activities_by_destination'][destination.title] = activity_count
            
            return stats
            
        except Exception as e:
            self.logger.log_exception(e, "getting activity statistics")
            return {}
    
    def clean_duplicate_activities(self):
        """Remove duplicate activities based on tour_id"""
        try:
            self.logger.info("Cleaning duplicate activities...")
            
            # Find duplicates
            from django.db.models import Count
            duplicates = CustomActivity.objects.values('tour_id').annotate(
                count=Count('id')
            ).filter(count__gt=1)
            
            removed_count = 0
            
            for duplicate in duplicates:
                tour_id = duplicate['tour_id']
                if tour_id:
                    # Keep the first one, remove the rest
                    activities = CustomActivity.objects.filter(tour_id=tour_id).order_by('created_at')
                    for activity in activities[1:]:
                        activity.delete()
                        removed_count += 1
            
            self.logger.info(f"Removed {removed_count} duplicate activities")
            return removed_count
            
        except Exception as e:
            self.logger.log_exception(e, "cleaning duplicate activities")
            return 0
    
    def bulk_create_activities(self, activities_data, destination):
        """
        Create multiple custom activities in batches with media handling
        
        Args:
            activities_data (list): List of activity data dictionaries
            destination: Destination model instance
            
        Returns:
            dict: Statistics about the bulk creation operation
        """
        if not activities_data:
            self.logger.warning("No activities data provided for bulk creation")
            return {'created': 0, 'failed': 0, 'skipped': 0}
        
        stats = {
            'created': 0,
            'failed': 0,
            'skipped': 0
        }
        
        self.logger.info(f"Starting bulk creation of {len(activities_data)} activities for {destination.title}")
        
        # Process activities one by one to handle media properly
        for i, activity_data in enumerate(activities_data):
            try:
                # Check if activity already exists
                if self._activity_exists(activity_data.get('tour_id')):
                    stats['skipped'] += 1
                    continue
                
                # Create single activity with media handling
                activity = self._create_single_activity(activity_data, destination)
                if activity:
                    stats['created'] += 1
                    self.logger.info(f"Created activity {i+1}/{len(activities_data)}: {activity.title}")
                else:
                    stats['failed'] += 1
                    
            except Exception as e:
                stats['failed'] += 1
                self.logger.log_failed_activity(destination.title, activity_data, str(e))
                continue
        
        self.logger.info(f"Bulk creation completed. Created: {stats['created']}, Failed: {stats['failed']}, Skipped: {stats['skipped']}")
        
        return stats
    
    def validate_database_connection(self):
        """Validate database connection and required models"""
        try:
            # Test database connection
            from django.db import connection
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
            
            # Test model accessibility
            Destination.objects.count()
            CustomActivity.objects.count()
            CustomActivityCategory.objects.count()
            CustomActivityLocation.objects.count()
            
            self.logger.info("Database connection and models validated successfully")
            return True
            
        except Exception as e:
            self.logger.log_exception(e, "validating database connection")
            return False