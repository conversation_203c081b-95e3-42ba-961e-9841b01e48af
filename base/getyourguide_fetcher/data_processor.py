"""
Data Processor for GetYourGuide Data Fetcher
Handles data transformation from GetYourGuide API response to our model format
"""

import json
from decimal import Decimal, InvalidOperation
from django.contrib.gis.geos import Point
from .config import GetYourGuideConfig
from .logger import GetYourGuideLogger


class GetYourGuideDataProcessor:
    """
    Data processor for transforming GetYourGuide API responses
    Maps API data to our custom activity model format
    """
    
    def __init__(self, logger=None):
        self.logger = logger or GetYourGuideLogger()
        self.field_mapping = GetYourGuideConfig.GYG_TO_MODEL_FIELD_MAPPING
        self.category_mapping = GetYourGuideConfig.ACTIVITY_CATEGORY_MAPPING
        self.default_values = GetYourGuideConfig.DEFAULT_ACTIVITY_VALUES
    
    def process_activities_data(self, activities_data, destination_name):
        """
        Process list of activities from GetYourGuide API response
        
        Args:
            activities_data (list): List of activity data from API
            destination_name (str): Name of the destination
            
        Returns:
            list: List of processed activity data ready for database
        """
        processed_activities = []
        
        if not activities_data:
            self.logger.warning(f"No activities data to process for {destination_name}")
            return processed_activities
        
        self.logger.info(f"Processing {len(activities_data)} activities for {destination_name}")
        
        for i, activity_data in enumerate(activities_data):
            try:
                # Validate required fields
                if not self._validate_activity_data(activity_data):
                    continue
                
                # Process individual activity
                processed_activity = self._process_single_activity(activity_data, destination_name)
                
                if processed_activity:
                    processed_activities.append(processed_activity)
                    self.logger.debug(f"Processed activity {i+1}/{len(activities_data)}: {processed_activity.get('title', 'Unknown')}")
                
            except Exception as e:
                self.logger.log_exception(e, f"processing activity {i+1} for {destination_name}")
                self.logger.log_failed_activity(destination_name, activity_data, str(e))
                continue
        
        self.logger.info(f"Successfully processed {len(processed_activities)} activities for {destination_name}")
        return processed_activities
    
    def _process_single_activity(self, activity_data, destination_name):
        """Process a single activity from API response"""
        try:
            processed_data = {}
            
            # Process basic fields with mapping
            for api_field, model_field in self.field_mapping.items():
                if api_field in activity_data:
                    api_value = activity_data[api_field]
                    
                    if callable(model_field):
                        # Apply transformation function
                        processed_value = model_field(api_value)
                    else:
                        # Direct mapping
                        processed_value = api_value
                    
                    # Store processed value
                    if isinstance(model_field, str):
                        processed_data[model_field] = processed_value
                    else:
                        # For callable mappings, use the original field name
                        processed_data[api_field] = processed_value
            
            # Process special fields that need custom handling
            processed_data.update(self._process_special_fields(activity_data, destination_name))
            
            # Apply default values for missing fields
            processed_data.update(self._apply_default_values(processed_data))
            
            # Validate and clean the processed data
            processed_data = self._validate_and_clean_data(processed_data)
            
            return processed_data
            
        except Exception as e:
            self.logger.log_exception(e, f"processing single activity: {activity_data.get('title', 'Unknown')}")
            return None
    
    def _process_special_fields(self, activity_data, destination_name):
        """Process special fields that need custom handling"""
        special_fields = {}
        
        try:
            # Process coordinates
            coordinates_data = activity_data.get('coordinates', {})
            if coordinates_data:
                lat = coordinates_data.get('lat')
                lng = coordinates_data.get('lng') or coordinates_data.get('long')
                
                if lat is not None and lng is not None:
                    try:
                        point = Point(float(lng), float(lat), srid=GetYourGuideConfig.DEFAULT_SRID)
                        special_fields['coordinates'] = point
                    except (ValueError, TypeError):
                        pass
            
            # Process price
            price_data = activity_data.get('price', {})
            if price_data and isinstance(price_data, dict):
                values = price_data.get('values', {})
                if values and isinstance(values, dict):
                    amount = values.get('amount')
                    if amount is not None:
                        try:
                            special_fields['price'] = Decimal(str(amount))
                        except (InvalidOperation, ValueError):
                            pass
            
            # Extract location_id for backward compatibility
            locations = activity_data.get('locations', [])
            if locations and isinstance(locations, list):
                for location in locations:
                    if isinstance(location, dict) and location.get('type') == 'city':
                        special_fields['location_id'] = str(location.get('id', ''))
                        break
            
            # Extract categories as metadata
            categories = self._extract_categories_from_activity(activity_data)
            if categories:
                special_fields['_categories'] = categories
            
            # Extract locations as metadata
            locations_data = self._extract_locations_from_activity(activity_data)
            if locations_data:
                special_fields['_locations'] = locations_data
            
        except Exception as e:
            self.logger.log_exception(e, f"processing special fields for {destination_name}")
        
        return special_fields
    
    def _format_opening_hours(self, opening_hours_list):
        """Format opening hours list into readable text"""
        try:
            if not opening_hours_list:
                return None
            
            formatted_hours = []
            for hours in opening_hours_list[:7]:  # Limit to 7 days
                opening_time = hours.get('opening_time', '')
                closing_time = hours.get('closing_time', '')
                
                if opening_time and closing_time:
                    # Extract date and time information
                    date_part = opening_time.split('T')[0] if 'T' in opening_time else ''
                    open_time = opening_time.split('T')[1] if 'T' in opening_time else opening_time
                    close_time = closing_time.split('T')[1] if 'T' in closing_time else closing_time
                    
                    formatted_hours.append(f"{date_part}: {open_time} - {close_time}")
            
            return '\n'.join(formatted_hours) if formatted_hours else None
            
        except Exception as e:
            self.logger.log_exception(e, "formatting opening hours")
            return None
    
    def _apply_default_values(self, processed_data):
        """Apply default values for missing fields"""
        for field, default_value in self.default_values.items():
            if field not in processed_data or processed_data[field] is None:
                processed_data[field] = default_value
        
        return processed_data
    
    def _validate_activity_data(self, activity_data):
        """Validate that activity data has required fields"""
        missing_fields = []
        
        for required_field in GetYourGuideConfig.MIN_ACTIVITY_FIELDS:
            if required_field not in activity_data or not activity_data[required_field]:
                missing_fields.append(required_field)
        
        if missing_fields:
            self.logger.log_data_validation_error(activity_data, missing_fields)
            return False
        
        return True
    
    def _validate_and_clean_data(self, processed_data):
        """Validate and clean processed data"""
        try:
            # Truncate text fields to maximum length
            if 'title' in processed_data and processed_data['title']:
                processed_data['title'] = str(processed_data['title'])[:GetYourGuideConfig.MAX_TITLE_LENGTH]
            
            if 'abstract' in processed_data and processed_data['abstract']:
                processed_data['abstract'] = str(processed_data['abstract'])[:GetYourGuideConfig.MAX_ABSTRACT_LENGTH]
            
            if 'description' in processed_data and processed_data['description']:
                processed_data['description'] = str(processed_data['description'])[:GetYourGuideConfig.MAX_DESCRIPTION_LENGTH]
            
            # Ensure boolean fields are properly set
            boolean_fields = ['bestseller', 'certified', 'has_pick_up']
            for field in boolean_fields:
                if field in processed_data:
                    processed_data[field] = bool(processed_data[field])
            
            # Ensure numeric fields are properly typed
            if 'overall_rating' in processed_data and processed_data['overall_rating'] is not None:
                try:
                    processed_data['overall_rating'] = float(processed_data['overall_rating'])
                    # Ensure rating is within reasonable bounds
                    if processed_data['overall_rating'] < 0 or processed_data['overall_rating'] > 5:
                        processed_data['overall_rating'] = None
                except (ValueError, TypeError):
                    processed_data['overall_rating'] = None
            
            if 'number_of_ratings' in processed_data and processed_data['number_of_ratings'] is not None:
                try:
                    processed_data['number_of_ratings'] = int(processed_data['number_of_ratings'])
                    # Ensure ratings count is non-negative
                    if processed_data['number_of_ratings'] < 0:
                        processed_data['number_of_ratings'] = None
                except (ValueError, TypeError):
                    processed_data['number_of_ratings'] = None
            
            return processed_data
            
        except Exception as e:
            self.logger.log_exception(e, "validating and cleaning data")
            return processed_data
    
    def _extract_categories_from_activity(self, activity_data):
        """Extract category information from activity data"""
        categories = []
        
        try:
            # Extract from activity_type field
            activity_type = activity_data.get('activity_type', '')
            if activity_type and activity_type in self.category_mapping:
                mapped_category = self.category_mapping[activity_type]
                categories.append({'name': mapped_category})
            elif activity_type:
                # Use original activity_type as category if no mapping found
                categories.append({'name': activity_type.title()})
            
            # Extract from categories field if it exists
            api_categories = activity_data.get('categories', [])
            if isinstance(api_categories, list):
                for cat in api_categories:
                    if isinstance(cat, dict):
                        cat_name = cat.get('name', cat.get('title', ''))
                        if cat_name and cat_name not in [c['name'] for c in categories]:
                            categories.append({'name': cat_name})
                    elif isinstance(cat, str):
                        if cat not in [c['name'] for c in categories]:
                            categories.append({'name': cat})
            
            # Extract from tags if available
            tags = activity_data.get('tags', [])
            if isinstance(tags, list):
                for tag in tags:
                    if isinstance(tag, dict):
                        tag_name = tag.get('name', tag.get('title', ''))
                        if tag_name and tag_name not in [c['name'] for c in categories]:
                            # Only add tag as category if it looks like a category
                            tag_lower = tag_name.lower()
                            if any(keyword in tag_lower for keyword in ['tour', 'activity', 'ticket', 'experience', 'adventure', 'cultural', 'historical']):
                                categories.append({'name': tag_name})
                    elif isinstance(tag, str):
                        if tag not in [c['name'] for c in categories]:
                            tag_lower = tag.lower()
                            if any(keyword in tag_lower for keyword in ['tour', 'activity', 'ticket', 'experience', 'adventure', 'cultural', 'historical']):
                                categories.append({'name': tag})
            
        except Exception as e:
            self.logger.log_exception(e, "extracting categories from activity")
        
        return categories
    
    def _extract_locations_from_activity(self, activity_data):
        """Extract location information from activity data"""
        locations = []
        
        try:
            # Extract from locations field
            api_locations = activity_data.get('locations', [])
            if isinstance(api_locations, list):
                for location in api_locations:
                    if isinstance(location, dict):
                        # We're interested in city-type locations
                        if location.get('type') == 'city':
                            locations.append(location)
            
            # If no locations found but we have coordinates, try to create a basic location
            if not locations:
                coordinates = activity_data.get('coordinates', {})
                if coordinates:
                    # Create a basic location entry
                    basic_location = {
                        'type': 'city',
                        'name': activity_data.get('city', ''),
                        'country': activity_data.get('country', ''),
                        'coordinates': coordinates
                    }
                    
                    # Only add if we have some location info
                    if basic_location['name'] or basic_location['country']:
                        locations.append(basic_location)
            
        except Exception as e:
            self.logger.log_exception(e, "extracting locations from activity")
        
        return locations
    
    def get_processing_stats(self, original_count, processed_count):
        """Get processing statistics"""
        success_rate = (processed_count / original_count * 100) if original_count > 0 else 0
        failed_count = original_count - processed_count
        
        stats = {
            'original_count': original_count,
            'processed_count': processed_count,
            'failed_count': failed_count,
            'success_rate': round(success_rate, 2)
        }
        
        return stats