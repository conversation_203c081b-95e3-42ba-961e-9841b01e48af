"""
Writer Setup Utility
This script provides functions to create writers with profile pictures for the Zuumm platform.
"""

import os
from django.core.files import File
from accounts.models import Writer


def create_zuumm_writers():
    """
    Create the two default Zuumm writers: <PERSON><PERSON> and <PERSON><PERSON><PERSON>
    """
    import os
    from django.core.files import File
    from accounts.models import Writer

    writers_data = [
        {
            'name': 'Sonal',
            'bio': '<PERSON><PERSON> loves discovering new places and turning smart travel ideas into simple stories. At Zuumm, she blends curiosity with AI tools to help travelers plan with ease and confidence.',
            'profile_picture_path': '/home/<USER>/Downloads/zuumm-sonal.jpeg'
        },
        {
            'name': '<PERSON><PERSON><PERSON>', 
            'bio': '<PERSON><PERSON><PERSON> combines a passion for travel with the power of AI to uncover smarter ways to explore the world. At Zuumm, he turns complex plans into smooth adventures for modern travelers.',
            'profile_picture_path': '/home/<USER>/Downloads/zuumm-ajeet.jpeg'
        }
    ]
    
    created_writers = []
    errors = []
    
    for writer_data in writers_data:
        try:
            # Check if writer already exists
            existing_writer = Writer.objects.filter(name=writer_data['name']).first()
            if existing_writer:
                print(f"Writer '{writer_data['name']}' already exists. ID: {existing_writer.id}")
                created_writers.append(existing_writer)
                continue
            
            # Check if profile picture file exists
            if not os.path.exists(writer_data['profile_picture_path']):
                error_msg = f"Profile picture not found: {writer_data['profile_picture_path']}"
                print(f"ERROR: {error_msg}")
                errors.append(error_msg)
                continue
            
            # Create writer
            writer = Writer.objects.create(
                name=writer_data['name'],
                bio=writer_data['bio'],
                email=None  # Explicitly set to None since it's optional
            )
            
            # Add profile picture
            with open(writer_data['profile_picture_path'], 'rb') as f:
                writer.profile_picture.save(
                    f"{writer_data['name'].lower()}-profile.jpeg",
                    File(f),
                    save=True
                )
            
            print(f"✓ Created writer: {writer.name}")
            print(f"  - ID: {writer.id}")
            print(f"  - Bio: {writer.bio[:50]}...")
            print(f"  - Profile Picture: {writer.profile_picture.url}")
            print("-" * 60)
            
            created_writers.append(writer)
            
        except Exception as e:
            error_msg = f"Error creating writer '{writer_data['name']}': {str(e)}"
            print(f"ERROR: {error_msg}")
            errors.append(error_msg)
    
    # Summary
    print(f"\n=== SUMMARY ===")
    print(f"Successfully created/found: {len(created_writers)} writers")
    print(f"Errors: {len(errors)}")
    
    if errors:
        print("\nErrors encountered:")
        for error in errors:
            print(f"  - {error}")
    
    return created_writers, errors


def create_writer_with_picture(name, bio, profile_picture_path, email=None):
    """
    Create a single writer with profile picture
    
    Args:
        name (str): Writer's name
        bio (str): Writer's biography
        profile_picture_path (str): Path to profile picture file
        email (str, optional): Writer's email address
        
    Returns:
        tuple: (writer_instance, error_message)
    """
    try:
        # Check if writer already exists
        existing_writer = Writer.objects.filter(name=name).first()
        if existing_writer:
            return existing_writer, f"Writer '{name}' already exists"
        
        # Check if profile picture file exists
        if not os.path.exists(profile_picture_path):
            return None, f"Profile picture not found: {profile_picture_path}"
        
        # Create writer
        writer = Writer.objects.create(
            name=name,
            bio=bio,
            email=email
        )
        
        # Add profile picture
        with open(profile_picture_path, 'rb') as f:
            filename = f"{name.lower().replace(' ', '-')}-profile{os.path.splitext(profile_picture_path)[1]}"
            writer.profile_picture.save(
                filename,
                File(f),
                save=True
            )
        
        return writer, None
        
    except Exception as e:
        return None, f"Error creating writer '{name}': {str(e)}"


def list_all_writers():
    """
    List all writers in the database
    """
    writers = Writer.objects.all()
    
    if not writers.exists():
        print("No writers found in the database.")
        return
    
    print("=== ALL WRITERS ===")
    for writer in writers:
        print(f"ID: {writer.id}")
        print(f"Name: {writer.name}")
        print(f"Email: {writer.email or 'Not set'}")
        print(f"Bio: {writer.bio[:100]}{'...' if len(writer.bio) > 100 else ''}")
        print(f"Profile Picture: {writer.profile_picture.url if writer.profile_picture else 'No picture'}")
        print(f"Active: {writer.is_active}")
        print(f"Created: {writer.created_at}")
        print("-" * 80)


def delete_writer(name):
    """
    Delete a writer by name (soft delete)
    
    Args:
        name (str): Writer's name
        
    Returns:
        bool: True if deleted, False if not found
    """
    try:
        writer = Writer.objects.get(name=name)
        writer.delete()  # This will soft delete
        print(f"Writer '{name}' deleted successfully.")
        return True
    except Writer.DoesNotExist:
        print(f"Writer '{name}' not found.")
        return False
    except Exception as e:
        print(f"Error deleting writer '{name}': {str(e)}")
        return False


# Convenience function for quick setup
def setup_default_writers():
    """
    Quick function to set up the default Zuumm writers
    """
    print("Setting up default Zuumm writers...")
    return create_zuumm_writers()


if __name__ == "__main__":
    # This allows the script to be run directly
    print("Zuumm Writer Setup Utility")
    print("Run setup_default_writers() to create Sonal and Ajeet") 