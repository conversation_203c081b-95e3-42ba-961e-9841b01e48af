import boto3
from botocore.client import Config
import logging
from uuid import uuid4
import os
from django.conf import settings
from botocore.exceptions import ClientError
from storages.backends.s3boto3 import S3Boto3Storage
from django.utils import timezone
from django.core.management.utils import get_random_string

logger = logging.getLogger(__name__)


def get_extension(filename):
    return f".{filename.split('.')[-1]}"


def get_random_name(filename):
    return f'{get_random_string(length=8)}-{int(timezone.now().timestamp() * 1000)}{get_extension(filename)}'


def get_mime_type_for_extension(extension):
    """
    Get the proper MIME type for a file extension.
    Handles the case where .jpg files should use image/jpeg MIME type.
    
    Args:
        extension (str): File extension (without dot)
        
    Returns:
        str: Proper MIME type
    """
    # Normalize extension to lowercase
    ext = extension.lower()
    
    # MIME type mapping for image files
    image_mime_types = {
        'jpg': 'image/jpeg',
        'jpeg': 'image/jpeg', 
        'png': 'image/png',
        'webp': 'image/webp',
        'gif': 'image/gif',
        'bmp': 'image/bmp',
        'svg': 'image/svg+xml',
        'tiff': 'image/tiff',
        'tif': 'image/tiff'
    }
    
    # Video MIME types
    video_mime_types = {
        'mp4': 'video/mp4',
        'mov': 'video/quicktime',
        'avi': 'video/x-msvideo',
        'wmv': 'video/x-ms-wmv',
        'flv': 'video/x-flv'
    }
    
    # Document MIME types
    document_mime_types = {
        'pdf': 'application/pdf',
        'doc': 'application/msword',
        'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'txt': 'text/plain',
        'json': 'application/json'
    }
    
    # Check in order: images, videos, documents
    if ext in image_mime_types:
        return image_mime_types[ext]
    elif ext in video_mime_types:
        return video_mime_types[ext]
    elif ext in document_mime_types:
        return document_mime_types[ext]
    else:
        # Default fallback - assume it's an image for legacy compatibility
        return f'image/{ext}'


def get_logo_path(instance, filename):
    """
    Get upload path for partner logos
    
    Args:
        instance: Partner instance
        filename: Original filename
        
    Returns:
        string: Path to store the logo file
    """
    # Get the file extension
    ext = os.path.splitext(filename)[1].lower()
    # Get base name without extension
    base_name = os.path.splitext(os.path.basename(filename))[0]
    # Create a unique filename with UUID
    unique_filename = f"{base_name}-{str(uuid4())}{ext}"
    # Return the path
    return f"partner/logo/{unique_filename}"


def get_writer_profile_picture_path(instance, filename):
    # Get the file extension
    ext = os.path.splitext(filename)[1].lower()
    # Get base name without extension
    base_name = os.path.splitext(os.path.basename(filename))[0]
    # Create a unique filename with UUID
    unique_filename = f"{base_name}-{str(uuid4())}{ext}"
    # Return the path
    return f"writer/profile/{unique_filename}"


def voucher_pdf_upload_path(instance, filename):
    """Generate upload path for voucher PDF files"""
    # Get the file extension
    ext = os.path.splitext(filename)[1].lower()
    # Get base name without extension
    base_name = os.path.splitext(os.path.basename(filename))[0]
    # Create a unique filename with UUID
    unique_filename = f"{base_name}-{str(uuid4())}{ext}"
    # Return the path
    return f"bookings/vouchers/{unique_filename}"



def get_blog_banner_image_path(instance, filename):
    # Get the file extension
    ext = os.path.splitext(filename)[1].lower()
    # Get base name without extension
    base_name = os.path.splitext(os.path.basename(filename))[0]
    # Create a unique filename with UUID
    unique_filename = f"{base_name}-{str(uuid4())}{ext}"
    # Return the path
    return f"blog/banner/{unique_filename}"



def hotel_image_upload_path(instance, filename):
    ext = os.path.splitext(filename)[1].lower()
    # Get base name without extension
    base_name = os.path.splitext(os.path.basename(filename))[0]
    # Create a unique filename with UUID
    unique_filename = f"{base_name}-{str(uuid4())}{ext}"
    # Return the path
    return f"hotels/images/{unique_filename}"


def room_image_upload_path(instance, filename):
    ext = os.path.splitext(filename)[1].lower()
    # Get base name without extension
    base_name = os.path.splitext(os.path.basename(filename))[0]
    # Create a unique filename with UUID
    unique_filename = f"{base_name}-{str(uuid4())}{ext}"
    # Return the path
    return f"hotels/rooms/images/{unique_filename}"


def get_blog_audio_file_path(instance, filename):
    # Get the file extension
    ext = os.path.splitext(filename)[1].lower()
    # Get base name without extension
    base_name = os.path.splitext(os.path.basename(filename))[0]
    # Create a unique filename with UUID
    unique_filename = f"{base_name}-{str(uuid4())}{ext}"
    # Return the path
    return f"blog/audio/{unique_filename}"


def package_upload_path(instance, filename):
    # For PackageUploader: instance.partner.id exists when uploading
    if hasattr(instance, 'partner') and instance.partner and instance.partner.id:
        partner_id = str(instance.partner.id)
    else:
        raise ValueError("PackageUploader must have a partner assigned with valid ID")
    
    new_filename = f"package/{partner_id}/{get_random_name(filename)}"
    return new_filename


def package_media_upload_path(instance, filename):
    # For PackageMedia: instance.package.id should exist
    if hasattr(instance, 'package') and instance.package and instance.package.id:
        package_id = str(instance.package.id)
    else:
        raise ValueError("PackageMedia must have a package assigned with valid ID")
    
    new_filename = f"package/media/{package_id}/{get_random_name(filename)}"
    return new_filename


def category_upload_path(instance, filename):
    # For CategoryMedia: instance.category.id should exist
    if hasattr(instance, 'category') and instance.category and instance.category.id:
        category_id = str(instance.category.id)
    else:
        raise ValueError("CategoryMedia must have a category assigned with valid ID")
    
    new_filename = f"category/{category_id}/{get_random_name(filename)}"
    return new_filename


def destination_upload_path(instance, filename):
    # For DestinationMedia: instance.destination.id should exist
    if hasattr(instance, 'destination') and instance.destination and instance.destination.id:
        destination_id = str(instance.destination.id)
    else:
        raise ValueError("DestinationMedia must have a destination assigned with valid ID")
    
    new_filename = f"destination/{destination_id}/{get_random_name(filename)}"
    return new_filename


def activity_upload_path(instance, filename):
    # For ActivityMedia: instance.activity.id should exist
    if hasattr(instance, 'activity') and instance.activity and instance.activity.id:
        activity_id = str(instance.activity.id)
    else:
        raise ValueError("ActivityMedia must have an activity assigned with valid ID")
    
    new_filename = f"activity/{activity_id}/{get_random_name(filename)}"
    return new_filename


def custom_activity_media_upload_path(instance, filename):
    # For CustomActivityMedia: instance.custom_activity.id should exist
    if hasattr(instance, 'custom_activity') and instance.custom_activity and instance.custom_activity.id:
        activity_id = str(instance.custom_activity.id)
    else:
        raise ValueError("CustomActivityMedia must have a custom_activity assigned with valid ID")
    
    new_filename = f"custom_activities/{activity_id}/{get_random_name(filename)}"
    return new_filename


def tripjack_hotel_media_upload_path(instance, filename):
    """
    Returns the upload path for TripJack hotel media files.
    """
    if hasattr(instance, 'hotel') and instance.hotel and instance.hotel.id:
        hotel_id = str(instance.hotel.id)
    else:
        raise ValueError("TripjackHotelMedia must have a hotel assigned with a valid ID")

    new_filename = f"tripjack_hotels/admin_media/{hotel_id}/{get_random_name(filename)}"
    return new_filename


class CustomS3Boto3Storage(S3Boto3Storage):
    """Custom S3 storage for generating presigned URLs"""
    client = None
    bucket = None

    def __init__(self, is_public=True, **kwargs):
        """Initialize storage with optional is_public parameter"""
        super().__init__(**kwargs)
        self.get_client(is_public)

    @classmethod
    def get_client(cls, is_public):
        """Get S3 client with credentials"""
        if not cls.client:
            cls.client = boto3.client(
                's3',
                aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
                region_name=settings.AWS_S3_REGION_NAME,
            )

        if is_public:
            cls.bucket = settings.AWS_STORAGE_BUCKET_NAME
        else:
            # If not public, use the Private client/bucket from S3Boto3Storage
            cls.bucket = settings.AWS_STORAGE_PRIVATE_BUCKET_NAME

    @classmethod
    def get_random_filename(cls, name, extension):
        """Generate a random filename with UUID"""
        return f"{name}-{str(uuid4())}.{extension}"

    @classmethod
    def generate_presigned_url(cls, file_path, file_name, extension, limit, expiration=3600):
        """
        Generate a presigned URL for direct S3 upload
        
        Args:
            file_path: Path in S3 bucket (e.g., 'user/123/document/')
            file_name: Base name of the file
            extension: File extension (pdf, jpg, etc.)
            limit: Maximum file size in bytes
            expiration: URL expiration time in seconds
            
        Returns:
            tuple: (response data, error)
        """
        try:
            custom_file_name = cls.get_random_filename(file_name, extension)
            object_name = f"{file_path}{custom_file_name}"
            
            # Get proper MIME type for the file extension
            content_type = get_mime_type_for_extension(extension)

            # New Response flow - Generate presigned PUT URL
            url = cls.client.generate_presigned_url(
                'put_object',
                Params={
                    'Bucket': cls.bucket,
                    'Key': object_name,
                    'ContentType': content_type
                },
                ExpiresIn=expiration,
                HttpMethod="PUT"
            )

            response = {
                'url': url,
                'path': f"{file_path}{custom_file_name}"
            }
            
        except ClientError as error:
            logger.error(f"AWS client error: {error}")
            return None, error

        return response, False

    def get_presigned_url(cls, file_path, expiration=3600):
        # Generate a presigned URL that expires in 1 hour (3600 seconds)
        presigned_url = cls.client.generate_presigned_url(
            'get_object',
            Params={
                'Bucket': cls.bucket,
                'Key': f"media/{file_path}"
            },
            ExpiresIn=expiration
        )
        return presigned_url
