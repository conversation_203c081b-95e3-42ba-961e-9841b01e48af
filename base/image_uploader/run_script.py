#!/usr/bin/env python3
"""
Image Upload Script Runner
Sets up Django environment and executes the image upload process

This is the main entry point for running the image upload script.
It handles Django setup and provides a simple interface for execution.
"""

import os
import sys
import django
from pathlib import Path

# Add the project root to Python path
current_dir = Path(__file__).resolve().parent
project_root = current_dir.parent.parent
sys.path.insert(0, str(project_root))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'zuumm.settings')
django.setup()

# Import after Django setup
from .main_script import run_image_upload_script


def main():
    """
    Main function to run the image upload script
    """
    print("=" * 80)
    print("ZUUMM IMAGE UPLOADER")
    print("=" * 80)
    print("This script will upload images to Category, Activity, Destination, and Package models")
    print("from the specified local folder structure.")
    print()
    
    # Get user inputs
    dry_run = False
    
    # Ask for dry run mode
    dry_run_input = input("Run in dry-run mode? (y/N): ").strip().lower()
    if dry_run_input in ['y', 'yes']:
        dry_run = True
        print("DRY RUN MODE ENABLED - No images will actually be uploaded")
    
    print()
    print("Starting image upload process...")
    print("=" * 80)
    
    # Run the script
    try:
        success = run_image_upload_script(dry_run=dry_run)
        
        if success:
            print("\n" + "=" * 80)
            print("IMAGE UPLOAD COMPLETED SUCCESSFULLY!")
            print("Check the log file for detailed results.")
            print("=" * 80)
            return 0
        else:
            print("\n" + "=" * 80)
            print("IMAGE UPLOAD FAILED!")
            print("Check the log file for error details.")
            print("=" * 80)
            return 1
            
    except KeyboardInterrupt:
        print("\n\nScript interrupted by user.")
        return 1
    except Exception as e:
        print(f"\nFatal error: {str(e)}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 