"""
Image Upload Helper for handling Django model media uploads
Manages the actual uploading of images to S3 through Django FileField
"""

import os
from django.core.files import File
from django.core.files.base import ContentFile
from django.db import transaction
from typing import List, Optional, Tuple, Dict
from packages.models import (
    Category, CategoryMedia, 
    Destination, DestinationMedia,
    Activity, ActivityMedia, 
    Package, PackageMedia
)
from accounts.models import Partner
from .config import ImageUploaderConfig
from .logger import ImageUploaderLogger


class ImageUploadHelper:
    """
    Helper class for uploading images to Django models
    Handles all the S3 upload logic through Django FileField
    """
    
    def __init__(self, logger: ImageUploaderLogger):
        self.logger = logger
        self.config = ImageUploaderConfig()
    
    def _read_image_file(self, file_path: str) -> Optional[ContentFile]:
        """
        Read image file and return Django ContentFile
        """
        try:
            with open(file_path, 'rb') as f:
                file_content = f.read()
            
            # Get filename from path
            filename = os.path.basename(file_path)
            
            # Create ContentFile
            content_file = ContentFile(file_content)
            content_file.name = filename
            
            return content_file
            
        except Exception as e:
            self.logger.error(f"Error reading image file {file_path}: {str(e)}")
            return None
    
    def upload_category_images(self, category: Category, image_paths: List[str]) -> Tuple[int, int]:
        """
        Upload images for a category (max 1 image)
        Returns (success_count, error_count)
        """
        success_count = 0
        error_count = 0
        
        # Categories only allow 1 image max
        max_images = self.config.CATEGORY_MAX_IMAGES
        images_to_upload = image_paths[:max_images]
        
        # Check if category already has media
        existing_media_count = category.media.count()
        if existing_media_count >= max_images:
            self.logger.log_upload_skip("Category", category.title, f"Already has {existing_media_count} images")
            return 0, 0
        
        try:
            with transaction.atomic():
                for image_path in images_to_upload:
                    try:
                        # Read image file
                        content_file = self._read_image_file(image_path)
                        if not content_file:
                            error_count += 1
                            continue
                        
                        # Create CategoryMedia instance
                        category_media = CategoryMedia(category=category)
                        category_media.media.save(content_file.name, content_file, save=True)
                        
                        success_count += 1
                        self.logger.debug(f"Uploaded image {content_file.name} for category {category.title}")
                        
                    except Exception as e:
                        self.logger.error(f"Error uploading image {image_path} for category {category.title}: {str(e)}")
                        error_count += 1
                        
        except Exception as e:
            self.logger.error(f"Transaction error uploading images for category {category.title}: {str(e)}")
            error_count += len(images_to_upload)
        
        return success_count, error_count
    
    def upload_destination_images(self, destination: Destination, image_paths: List[str]) -> Tuple[int, int]:
        """
        Upload images for a destination (max 1 image)
        Returns (success_count, error_count)
        """
        success_count = 0
        error_count = 0
        
        # Destinations only allow 1 image max
        max_images = self.config.DESTINATION_MAX_IMAGES
        images_to_upload = image_paths[:max_images]
        
        # Check if destination already has media
        existing_media_count = destination.media.count()
        if existing_media_count >= max_images:
            self.logger.log_upload_skip("Destination", destination.title, f"Already has {existing_media_count} images")
            return 0, 0
        
        try:
            with transaction.atomic():
                for image_path in images_to_upload:
                    try:
                        # Read image file
                        content_file = self._read_image_file(image_path)
                        if not content_file:
                            error_count += 1
                            continue
                        
                        # Create DestinationMedia instance
                        destination_media = DestinationMedia(destination=destination)
                        destination_media.media.save(content_file.name, content_file, save=True)
                        
                        success_count += 1
                        self.logger.debug(f"Uploaded image {content_file.name} for destination {destination.title}")
                        
                    except Exception as e:
                        self.logger.error(f"Error uploading image {image_path} for destination {destination.title}: {str(e)}")
                        error_count += 1
                        
        except Exception as e:
            self.logger.error(f"Transaction error uploading images for destination {destination.title}: {str(e)}")
            error_count += len(images_to_upload)
        
        return success_count, error_count
    
    def upload_activity_images(self, activity: Activity, image_paths: List[str]) -> Tuple[int, int]:
        """
        Upload images for an activity (max 10 images)
        Returns (success_count, error_count)
        """
        success_count = 0
        error_count = 0
        
        # Activities allow up to 10 images
        max_images = self.config.ACTIVITY_MAX_IMAGES
        
        # Check existing media count
        existing_media_count = activity.media.count()
        remaining_slots = max_images - existing_media_count
        
        if remaining_slots <= 0:
            self.logger.log_upload_skip("Activity", activity.title, f"Already has {existing_media_count} images (max: {max_images})")
            return 0, 0
        
        # Limit images to upload based on remaining slots
        images_to_upload = image_paths[:remaining_slots]
        
        try:
            with transaction.atomic():
                for i, image_path in enumerate(images_to_upload):
                    try:
                        # Read image file
                        content_file = self._read_image_file(image_path)
                        if not content_file:
                            error_count += 1
                            continue
                        
                        # Create ActivityMedia instance
                        # Set first image as main_display if no existing main_display exists
                        is_main_display = (i == 0 and not activity.media.filter(main_display=True).exists())
                        
                        activity_media = ActivityMedia(
                            activity=activity,
                            main_display=is_main_display
                        )
                        activity_media.media.save(content_file.name, content_file, save=True)
                        
                        success_count += 1
                        self.logger.debug(f"Uploaded image {content_file.name} for activity {activity.title} (main_display: {is_main_display})")
                        
                    except Exception as e:
                        self.logger.error(f"Error uploading image {image_path} for activity {activity.title}: {str(e)}")
                        error_count += 1
                        
        except Exception as e:
            self.logger.error(f"Transaction error uploading images for activity {activity.title}: {str(e)}")
            error_count += len(images_to_upload)
        
        return success_count, error_count
    
    def upload_package_images(self, package: Package, image_paths: List[str]) -> Tuple[int, int]:
        """
        Upload images for a package (max 20 images)
        Returns (success_count, error_count)
        """
        success_count = 0
        error_count = 0
        
        # Packages allow up to 20 images
        max_images = self.config.PACKAGE_MAX_IMAGES
        
        # Check existing media count
        existing_media_count = package.media.count()
        remaining_slots = max_images - existing_media_count
        
        if remaining_slots <= 0:
            self.logger.log_upload_skip("Package", package.title, f"Already has {existing_media_count} images (max: {max_images})")
            return 0, 0
        
        # Limit images to upload based on remaining slots
        images_to_upload = image_paths[:remaining_slots]
        
        try:
            with transaction.atomic():
                for image_path in images_to_upload:
                    try:
                        # Read image file
                        content_file = self._read_image_file(image_path)
                        if not content_file:
                            error_count += 1
                            continue
                        
                        # Create PackageMedia instance
                        package_media = PackageMedia(
                            package=package,
                            file_type="image"  # Set as image type
                        )
                        package_media.file.save(content_file.name, content_file, save=True)
                        
                        success_count += 1
                        self.logger.debug(f"Uploaded image {content_file.name} for package {package.title}")
                        
                    except Exception as e:
                        self.logger.error(f"Error uploading image {image_path} for package {package.title}: {str(e)}")
                        error_count += 1
                        
        except Exception as e:
            self.logger.error(f"Transaction error uploading images for package {package.title}: {str(e)}")
            error_count += len(images_to_upload)
        
        return success_count, error_count
    
    def get_model_stats(self, model_name: str) -> Dict[str, int]:
        """
        Get statistics for a specific model
        """
        try:
            if model_name.lower() == "category":
                total_count = Category.objects.count()
                with_media_count = Category.objects.filter(media__isnull=False).distinct().count()
            elif model_name.lower() == "destination":
                total_count = Destination.objects.count()
                with_media_count = Destination.objects.filter(media__isnull=False).distinct().count()
            elif model_name.lower() == "activity":
                total_count = Activity.objects.count()
                with_media_count = Activity.objects.filter(media__isnull=False).distinct().count()
            elif model_name.lower() == "package":
                total_count = Package.objects.count()
                with_media_count = Package.objects.filter(media__isnull=False).distinct().count()
            else:
                return {"total": 0, "with_media": 0, "without_media": 0}
            
            return {
                "total": total_count,
                "with_media": with_media_count,
                "without_media": total_count - with_media_count
            }
            
        except Exception as e:
            self.logger.error(f"Error getting stats for {model_name}: {str(e)}")
            return {"total": 0, "with_media": 0, "without_media": 0}
    
    def get_all_model_stats(self) -> Dict[str, Dict[str, int]]:
        """
        Get statistics for all models
        """
        return {
            "category": self.get_model_stats("category"),
            "destination": self.get_model_stats("destination"),
            "activity": self.get_model_stats("activity"),
            "package": self.get_model_stats("package")
        } 