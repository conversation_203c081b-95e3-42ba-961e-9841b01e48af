"""
Logger utility for Image Uploader Script
Provides comprehensive logging functionality with file and console output
"""

import logging
import sys
from datetime import datetime
from pathlib import Path
from .config import ImageUploaderConfig


class ImageUploaderLogger:
    """
    Custom logger class for image upload script
    Provides both file and console logging with different levels
    """
    
    def __init__(self, log_file_path=None, log_level=None):
        self.log_file_path = log_file_path or ImageUploaderConfig.LOG_FILE_PATH
        self.log_level = log_level or ImageUploaderConfig.LOG_LEVEL
        self.logger = None
        self._setup_logger()
    
    def _setup_logger(self):
        """Setup logger with file and console handlers"""
        # Ensure log directory exists
        ImageUploaderConfig.ensure_log_dir()
        
        # Create logger
        self.logger = logging.getLogger('ImageUploader')
        self.logger.setLevel(getattr(logging, self.log_level))
        
        # Remove existing handlers to avoid duplication
        self.logger.handlers.clear()
        
        # Create formatter
        formatter = logging.Formatter(ImageUploaderConfig.LOG_FORMAT)
        
        # Create file handler
        file_handler = logging.FileHandler(self.log_file_path)
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        
        # Create console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        
        # Add handlers to logger
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
    
    def info(self, message):
        """Log info message"""
        self.logger.info(message)
    
    def debug(self, message):
        """Log debug message"""
        self.logger.debug(message)
    
    def warning(self, message):
        """Log warning message"""
        self.logger.warning(message)
    
    def error(self, message):
        """Log error message"""
        self.logger.error(message)
    
    def critical(self, message):
        """Log critical message"""
        self.logger.critical(message)
    
    def log_stats(self, stats_dict):
        """Log statistics in a formatted way"""
        self.info("=== IMAGE UPLOAD STATISTICS ===")
        for key, value in stats_dict.items():
            self.info(f"{key}: {value}")
        self.info("=================================")
    
    def log_section_start(self, section_name):
        """Log the start of a major section"""
        separator = "=" * 50
        self.info(f"\n{separator}")
        self.info(f"STARTING: {section_name}")
        self.info(f"{separator}")
    
    def log_section_end(self, section_name):
        """Log the end of a major section"""
        separator = "=" * 50
        self.info(f"{separator}")
        self.info(f"COMPLETED: {section_name}")
        self.info(f"{separator}\n")
    
    def log_model_summary(self, model_name, total_processed, success_count, skip_count, error_count):
        """Log summary for a specific model"""
        self.info(f"\n--- {model_name} SUMMARY ---")
        self.info(f"Total {model_name}s processed: {total_processed}")
        self.info(f"Successfully uploaded images: {success_count}")
        self.info(f"Skipped (no folder/images): {skip_count}")
        self.info(f"Errors encountered: {error_count}")
        self.info(f"--- END {model_name} SUMMARY ---\n")
    
    def log_upload_attempt(self, model_name, item_title, folder_name, image_count):
        """Log an upload attempt"""
        self.info(f"[{model_name}] Processing '{item_title}' -> folder '{folder_name}' ({image_count} images)")
    
    def log_upload_success(self, model_name, item_title, uploaded_count):
        """Log successful upload"""
        self.info(f"[{model_name}] ✓ Successfully uploaded {uploaded_count} images for '{item_title}'")
    
    def log_upload_skip(self, model_name, item_title, reason):
        """Log skipped upload"""
        self.warning(f"[{model_name}] ⚠ Skipped '{item_title}': {reason}")
    
    def log_upload_error(self, model_name, item_title, error):
        """Log upload error"""
        self.error(f"[{model_name}] ✗ Error uploading images for '{item_title}': {error}")
    
    def log_folder_match(self, item_title, matched_folder, similarity_score=None):
        """Log folder matching result"""
        if similarity_score:
            self.debug(f"Matched '{item_title}' to folder '{matched_folder}' (similarity: {similarity_score:.2f})")
        else:
            self.debug(f"Exact match: '{item_title}' -> '{matched_folder}'")
    
    def log_no_folder_match(self, item_title, available_folders):
        """Log when no folder match is found"""
        self.debug(f"No folder match found for '{item_title}'. Available folders: {available_folders[:5]}...")
    
    def log_processing_start(self, total_items, model_name):
        """Log the start of processing for a model"""
        self.info(f"Starting to process {total_items} {model_name}s...")
    
    def log_processing_progress(self, current, total, model_name):
        """Log processing progress"""
        percentage = (current / total) * 100
        self.info(f"Progress: {current}/{total} {model_name}s processed ({percentage:.1f}%)")
    
    def close(self):
        """Close all handlers"""
        for handler in self.logger.handlers:
            handler.close()
            self.logger.removeHandler(handler) 