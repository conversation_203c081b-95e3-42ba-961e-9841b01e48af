"""
Main Image Upload Script
Orchestrates the entire image upload process for Category, Activity, Destination, and Package models
"""

import os
import sys
import time
from datetime import datetime
from typing import Dict, List, Tuple
from django.db import transaction

# Add the project root to Python path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Import Django models
from packages.models import Category, Destination, Activity, Package
from accounts.models import Partner

# Import our utilities
from .config import ImageUploaderConfig
from .logger import ImageUploaderLogger
from .folder_matcher import FolderMatcher
from .image_upload_helper import ImageUploadHelper


class ImageUploadProcessor:
    """
    Main class for processing image uploads across all models
    Implements the complete flow as specified in the requirements
    """
    
    def __init__(self, dry_run: bool = False):
        """
        Initialize the image upload processor
        
        Args:
            dry_run: If True, don't actually upload images, just log what would happen
        """
        self.config = ImageUploaderConfig()
        self.logger = ImageUploaderLogger()
        self.dry_run = dry_run
        
        # Initialize components
        self.folder_matcher = FolderMatcher(self.logger)
        self.upload_helper = ImageUploadHelper(self.logger)
        
        # Statistics tracking
        self.stats = {
            'activities': {'processed': 0, 'success': 0, 'skipped': 0, 'errors': 0, 'images_uploaded': 0},
            'destinations': {'processed': 0, 'success': 0, 'skipped': 0, 'errors': 0, 'images_uploaded': 0},
            'categories': {'processed': 0, 'success': 0, 'skipped': 0, 'errors': 0, 'images_uploaded': 0},
            'packages': {'processed': 0, 'success': 0, 'skipped': 0, 'errors': 0, 'images_uploaded': 0}
        }
        
        self.start_time = None
        
    def validate_environment(self) -> bool:
        """
        Validate that the environment is ready for image uploads
        """
        self.logger.info("Validating environment...")
        
        # Check if image source directory exists
        if not os.path.exists(self.config.IMAGES_SOURCE_DIR):
            self.logger.error(f"Images source directory not found: {self.config.IMAGES_SOURCE_DIR}")
            return False
        
        # Validate folder structure
        validation_results = self.folder_matcher.validate_folder_structure()
        
        all_valid = True
        for folder_type, result in validation_results.items():
            if result['exists']:
                self.logger.info(f"✓ {folder_type.title()} folder found: {result['folder_count']} folders")
            else:
                self.logger.error(f"✗ {folder_type.title()} folder not found: {result['path']}")
                all_valid = False
        
        # Log initial model statistics
        initial_stats = self.upload_helper.get_all_model_stats()
        self.logger.info("\n=== INITIAL MODEL STATISTICS ===")
        for model_name, stats in initial_stats.items():
            self.logger.info(f"{model_name.title()}s: {stats['total']} total, {stats['with_media']} with images, {stats['without_media']} without images")
        
        return all_valid
    
    def process_activities(self) -> None:
        """
        Process all activities to upload images
        """
        self.logger.log_section_start("ACTIVITY IMAGE UPLOAD")
        
        # Get all activities (no partner filtering needed)
        activities = Activity.objects.all()
        total_activities = activities.count()
        
        if total_activities == 0:
            self.logger.warning("No activities found in the database")
            self.logger.log_section_end("ACTIVITY IMAGE UPLOAD")
            return
        
        self.logger.log_processing_start(total_activities, "activity")
        
        for i, activity in enumerate(activities, 1):
            try:
                # Find matching folder
                matching_folder = self.folder_matcher.find_matching_folder(activity.title, "activity")
                
                if not matching_folder:
                    self.logger.log_upload_skip("Activity", activity.title, "No matching folder found")
                    self.stats['activities']['skipped'] += 1
                    continue
                
                # Get images from the matched folder
                folder_path = os.path.join(self.config.get_folder_path("activity"), matching_folder)
                image_paths = self.folder_matcher.get_images_from_folder(
                    folder_path, 
                    max_images=self.config.ACTIVITY_MAX_IMAGES
                )
                
                if not image_paths:
                    self.logger.log_upload_skip("Activity", activity.title, f"No images found in folder '{matching_folder}'")
                    self.stats['activities']['skipped'] += 1
                    continue
                
                self.logger.log_upload_attempt("Activity", activity.title, matching_folder, len(image_paths))
                
                # Upload images (or simulate if dry run)
                if self.dry_run:
                    self.logger.info(f"[DRY RUN] Would upload {len(image_paths)} images for activity '{activity.title}'")
                    success_count = len(image_paths)
                    error_count = 0
                else:
                    success_count, error_count = self.upload_helper.upload_activity_images(activity, image_paths)
                
                if success_count > 0:
                    self.logger.log_upload_success("Activity", activity.title, success_count)
                    self.stats['activities']['success'] += 1
                    self.stats['activities']['images_uploaded'] += success_count
                    
                    # Upload same images to related packages
                    self._upload_activity_images_to_packages(activity, image_paths)
                    
                if error_count > 0:
                    self.stats['activities']['errors'] += error_count
                
                self.stats['activities']['processed'] += 1
                
                # Log progress every 10 activities
                if i % 10 == 0:
                    self.logger.log_processing_progress(i, total_activities, "activity")
                
            except Exception as e:
                self.logger.log_upload_error("Activity", activity.title, str(e))
                self.stats['activities']['errors'] += 1
        
        self.logger.log_model_summary(
            "ACTIVITY", 
            self.stats['activities']['processed'],
            self.stats['activities']['success'],
            self.stats['activities']['skipped'],
            self.stats['activities']['errors']
        )
        self.logger.log_section_end("ACTIVITY IMAGE UPLOAD")
    
    def _upload_activity_images_to_packages(self, activity: Activity, image_paths: List[str]) -> None:
        """
        Upload activity images to all packages that include this activity
        """
        try:
            # Find packages that include this activity (no partner filtering needed)
            packages = Package.objects.filter(activities=activity)
            
            if not packages.exists():
                self.logger.debug(f"No packages found for activity '{activity.title}'")
                return
            
            self.logger.info(f"Uploading activity images to {packages.count()} related packages...")
            
            for package in packages:
                try:
                    if self.dry_run:
                        self.logger.info(f"[DRY RUN] Would upload {len(image_paths)} activity images to package '{package.title}'")
                        success_count = len(image_paths)
                    else:
                        success_count, error_count = self.upload_helper.upload_package_images(package, image_paths)
                    
                    if success_count > 0:
                        self.logger.debug(f"Uploaded {success_count} activity images to package '{package.title}'")
                        self.stats['packages']['images_uploaded'] += success_count
                        
                except Exception as e:
                    self.logger.error(f"Error uploading activity images to package '{package.title}': {str(e)}")
                    
        except Exception as e:
            self.logger.error(f"Error finding packages for activity '{activity.title}': {str(e)}")
    
    def process_destinations(self) -> None:
        """
        Process all destinations to upload images
        """
        self.logger.log_section_start("DESTINATION IMAGE UPLOAD")
        
        # Get all destinations (no partner filtering needed)
        destinations = Destination.objects.all()
        total_destinations = destinations.count()
        
        if total_destinations == 0:
            self.logger.warning("No destinations found in the database")
            self.logger.log_section_end("DESTINATION IMAGE UPLOAD")
            return
        
        self.logger.log_processing_start(total_destinations, "destination")
        
        for i, destination in enumerate(destinations, 1):
            try:
                # Find matching folder
                matching_folder = self.folder_matcher.find_matching_folder(destination.title, "destination")
                
                if not matching_folder:
                    self.logger.log_upload_skip("Destination", destination.title, "No matching folder found")
                    self.stats['destinations']['skipped'] += 1
                    continue
                
                # Get images from the matched folder (only first image for destinations)
                folder_path = os.path.join(self.config.get_folder_path("destination"), matching_folder)
                image_paths = self.folder_matcher.get_images_from_folder(
                    folder_path, 
                    max_images=self.config.DESTINATION_MAX_IMAGES  # Only 1 image
                )
                
                if not image_paths:
                    self.logger.log_upload_skip("Destination", destination.title, f"No images found in folder '{matching_folder}'")
                    self.stats['destinations']['skipped'] += 1
                    continue
                
                self.logger.log_upload_attempt("Destination", destination.title, matching_folder, len(image_paths))
                
                # Upload images (or simulate if dry run)
                if self.dry_run:
                    self.logger.info(f"[DRY RUN] Would upload {len(image_paths)} images for destination '{destination.title}'")
                    success_count = len(image_paths)
                    error_count = 0
                else:
                    success_count, error_count = self.upload_helper.upload_destination_images(destination, image_paths)
                
                if success_count > 0:
                    self.logger.log_upload_success("Destination", destination.title, success_count)
                    self.stats['destinations']['success'] += 1
                    self.stats['destinations']['images_uploaded'] += success_count
                    
                    # Upload same image to related packages
                    self._upload_destination_images_to_packages(destination, image_paths)
                    
                if error_count > 0:
                    self.stats['destinations']['errors'] += error_count
                
                self.stats['destinations']['processed'] += 1
                
                # Log progress every 10 destinations
                if i % 10 == 0:
                    self.logger.log_processing_progress(i, total_destinations, "destination")
                
            except Exception as e:
                self.logger.log_upload_error("Destination", destination.title, str(e))
                self.stats['destinations']['errors'] += 1
        
        self.logger.log_model_summary(
            "DESTINATION", 
            self.stats['destinations']['processed'],
            self.stats['destinations']['success'],
            self.stats['destinations']['skipped'],
            self.stats['destinations']['errors']
        )
        self.logger.log_section_end("DESTINATION IMAGE UPLOAD")
    
    def _upload_destination_images_to_packages(self, destination: Destination, image_paths: List[str]) -> None:
        """
        Upload destination images to all packages for this destination
        """
        try:
            # Find packages for this destination (no partner filtering needed)
            packages = Package.objects.filter(destination=destination)
            
            if not packages.exists():
                self.logger.debug(f"No packages found for destination '{destination.title}'")
                return
            
            self.logger.info(f"Uploading destination images to {packages.count()} related packages...")
            
            for package in packages:
                try:
                    if self.dry_run:
                        self.logger.info(f"[DRY RUN] Would upload {len(image_paths)} destination images to package '{package.title}'")
                        success_count = len(image_paths)
                    else:
                        success_count, error_count = self.upload_helper.upload_package_images(package, image_paths)
                    
                    if success_count > 0:
                        self.logger.debug(f"Uploaded {success_count} destination images to package '{package.title}'")
                        self.stats['packages']['images_uploaded'] += success_count
                        
                except Exception as e:
                    self.logger.error(f"Error uploading destination images to package '{package.title}': {str(e)}")
                    
        except Exception as e:
            self.logger.error(f"Error finding packages for destination '{destination.title}': {str(e)}")
    
    def process_categories(self) -> None:
        """
        Process all categories to upload images
        """
        self.logger.log_section_start("CATEGORY IMAGE UPLOAD")
        
        # Get all categories (no partner filtering needed)
        categories = Category.objects.all()
        total_categories = categories.count()
        
        if total_categories == 0:
            self.logger.warning("No categories found in the database")
            self.logger.log_section_end("CATEGORY IMAGE UPLOAD")
            return
        
        self.logger.log_processing_start(total_categories, "category")
        
        for i, category in enumerate(categories, 1):
            try:
                # Find matching folder
                matching_folder = self.folder_matcher.find_matching_folder(category.title, "category")
                
                if not matching_folder:
                    self.logger.log_upload_skip("Category", category.title, "No matching folder found")
                    self.stats['categories']['skipped'] += 1
                    continue
                
                # Get images from the matched folder (only first image for categories)
                folder_path = os.path.join(self.config.get_folder_path("category"), matching_folder)
                image_paths = self.folder_matcher.get_images_from_folder(
                    folder_path, 
                    max_images=self.config.CATEGORY_MAX_IMAGES  # Only 1 image
                )
                
                if not image_paths:
                    self.logger.log_upload_skip("Category", category.title, f"No images found in folder '{matching_folder}'")
                    self.stats['categories']['skipped'] += 1
                    continue
                
                self.logger.log_upload_attempt("Category", category.title, matching_folder, len(image_paths))
                
                # Upload images (or simulate if dry run)
                if self.dry_run:
                    self.logger.info(f"[DRY RUN] Would upload {len(image_paths)} images for category '{category.title}'")
                    success_count = len(image_paths)
                    error_count = 0
                else:
                    success_count, error_count = self.upload_helper.upload_category_images(category, image_paths)
                
                if success_count > 0:
                    self.logger.log_upload_success("Category", category.title, success_count)
                    self.stats['categories']['success'] += 1
                    self.stats['categories']['images_uploaded'] += success_count
                    
                if error_count > 0:
                    self.stats['categories']['errors'] += error_count
                
                self.stats['categories']['processed'] += 1
                
                # Log progress every 10 categories
                if i % 10 == 0:
                    self.logger.log_processing_progress(i, total_categories, "category")
                
            except Exception as e:
                self.logger.log_upload_error("Category", category.title, str(e))
                self.stats['categories']['errors'] += 1
        
        self.logger.log_model_summary(
            "CATEGORY", 
            self.stats['categories']['processed'],
            self.stats['categories']['success'],
            self.stats['categories']['skipped'],
            self.stats['categories']['errors']
        )
        self.logger.log_section_end("CATEGORY IMAGE UPLOAD")
    
    def run(self) -> bool:
        """
        Main method to run the complete image upload process
        """
        self.start_time = time.time()
        
        try:
            self.logger.info("=" * 80)
            self.logger.info("ZUUMM IMAGE UPLOADER SCRIPT STARTED")
            self.logger.info("=" * 80)
            self.logger.info(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            self.logger.info(f"Dry run mode: {'ENABLED' if self.dry_run else 'DISABLED'}")
            
            # Validate environment
            if not self.validate_environment():
                self.logger.error("Environment validation failed. Aborting.")
                return False
            
            # Process each model type according to the specified flow
            self.logger.info("\n" + "=" * 80)
            self.logger.info("STARTING IMAGE UPLOAD PROCESS")
            self.logger.info("=" * 80)
            
            # 1. Process Activities (max 10 images each)
            self.process_activities()
            
            # 2. Process Destinations (max 1 image each)
            self.process_destinations()
            
            # 3. Process Categories (max 1 image each)
            self.process_categories()
            
            # Log final statistics
            self._log_final_statistics()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Fatal error in main process: {str(e)}")
            return False
        
        finally:
            end_time = time.time()
            duration = end_time - self.start_time if self.start_time else 0
            self.logger.info("=" * 80)
            self.logger.info("ZUUMM IMAGE UPLOADER SCRIPT COMPLETED")
            self.logger.info(f"Total execution time: {duration:.2f} seconds")
            self.logger.info("=" * 80)
    
    def _log_final_statistics(self) -> None:
        """
        Log comprehensive final statistics
        """
        self.logger.info("\n" + "=" * 80)
        self.logger.info("FINAL STATISTICS SUMMARY")
        self.logger.info("=" * 80)
        
        total_processed = sum(model_stats['processed'] for model_stats in self.stats.values())
        total_success = sum(model_stats['success'] for model_stats in self.stats.values())
        total_skipped = sum(model_stats['skipped'] for model_stats in self.stats.values())
        total_errors = sum(model_stats['errors'] for model_stats in self.stats.values())
        total_images = sum(model_stats['images_uploaded'] for model_stats in self.stats.values())
        
        # Overall statistics
        overall_stats = {
            'Total Items Processed': total_processed,
            'Successfully Processed': total_success,
            'Skipped (No Folder/Images)': total_skipped,
            'Errors Encountered': total_errors,
            'Total Images Uploaded': total_images,
            'Success Rate': f"{(total_success / total_processed * 100):.1f}%" if total_processed > 0 else "0%"
        }
        
        self.logger.log_stats(overall_stats)
        
        # Per-model breakdown
        self.logger.info("\n--- PER-MODEL BREAKDOWN ---")
        for model_name, model_stats in self.stats.items():
            self.logger.info(f"{model_name.title()}:")
            self.logger.info(f"  Processed: {model_stats['processed']}")
            self.logger.info(f"  Success: {model_stats['success']}")
            self.logger.info(f"  Skipped: {model_stats['skipped']}")
            self.logger.info(f"  Errors: {model_stats['errors']}")
            self.logger.info(f"  Images Uploaded: {model_stats['images_uploaded']}")
        
        # Final model statistics (after upload)
        final_stats = self.upload_helper.get_all_model_stats()
        self.logger.info("\n--- FINAL MODEL STATISTICS ---")
        for model_name, stats in final_stats.items():
            self.logger.info(f"{model_name.title()}s: {stats['total']} total, {stats['with_media']} with images, {stats['without_media']} without images")


def run_image_upload_script(dry_run: bool = False) -> bool:
    """
    Entry point function to run the image upload script
    
    Args:
        dry_run: If True, don't actually upload images, just log what would happen
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        processor = ImageUploadProcessor(dry_run=dry_run)
        return processor.run()
    except Exception as e:
        print(f"Error initializing image upload processor: {str(e)}")
        return False


if __name__ == "__main__":
    # Allow running from command line with optional arguments
    import argparse
    
    parser = argparse.ArgumentParser(description="Upload images to Zuumm models")
    parser.add_argument("--dry-run", action="store_true", help="Run in dry-run mode (don't actually upload)")
    
    args = parser.parse_args()
    
    success = run_image_upload_script(
        dry_run=args.dry_run
    )
    
    sys.exit(0 if success else 1) 