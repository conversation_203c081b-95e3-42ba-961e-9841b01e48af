"""
Configuration settings for the Image Uploader Script
Defines all configurable parameters, file paths, and constants
"""

import os
from pathlib import Path
from base.static import Constants


class ImageUploaderConfig:
    """
    Configuration class for image upload script
    """
    
    # Base directories
    BASE_DIR = Path(__file__).resolve().parent.parent.parent
    SCRIPT_DIR = Path(__file__).resolve().parent
    
    # Source image directory
    IMAGES_SOURCE_DIR = "/home/<USER>/Documents/Zuumm-Images"
    
    # Logging configuration
    LOG_DIR = SCRIPT_DIR / "logs"
    LOG_FILE_PATH = LOG_DIR / "image_uploader.log"
    LOG_LEVEL = "DEBUG"
    LOG_FORMAT = "%(asctime)s - %(levelname)s - %(message)s"
    
    # Image upload limits per model (from Constants)
    CATEGORY_MAX_IMAGES = Constants.CATEGORY_MAX_MEDIA_COUNT  # 1
    DESTINATION_MAX_IMAGES = Constants.DESTINATION_MAX_MEDIA_COUNT  # 1  
    ACTIVITY_MAX_IMAGES = Constants.ACTIVITY_MAX_MEDIA_COUNT  # 10
    PACKAGE_MAX_IMAGES = Constants.PACKAGE_MAX_MEDIA_COUNT  # 20
    
    # Supported image extensions
    SUPPORTED_EXTENSIONS = {
        'jpg', 'jpeg', 'png', 'webp', 'bmp', 'gif', 'tiff', 'tif',
        'JPG', 'JPEG', 'PNG', 'WEBP', 'BMP', 'GIF', 'TIFF', 'TIF'
    }
    
    # Folder names within the source directory
    ACTIVITY_FOLDER = "activity"
    CATEGORY_FOLDER = "category" 
    DESTINATION_FOLDER = "destination"
    PACKAGE_FOLDER = "package"
    
    # Name matching configuration
    MIN_SIMILARITY_SCORE = 0.6  # Minimum similarity score for fuzzy matching
    MAX_SIMILARITY_DISTANCE = 3  # Maximum edit distance for fuzzy matching
    
    # Processing configuration
    BATCH_SIZE = 10  # Process images in batches
    
    # Note: No partner filtering needed since we're creating media entries
    # for existing models that already have their partner relationships set
    
    @classmethod
    def get_folder_path(cls, folder_type):
        """Get the full path for a specific folder type"""
        if folder_type == "activity":
            return os.path.join(cls.IMAGES_SOURCE_DIR, cls.ACTIVITY_FOLDER)
        elif folder_type == "category":
            return os.path.join(cls.IMAGES_SOURCE_DIR, cls.CATEGORY_FOLDER)
        elif folder_type == "destination":
            return os.path.join(cls.IMAGES_SOURCE_DIR, cls.DESTINATION_FOLDER)
        elif folder_type == "package":
            return os.path.join(cls.IMAGES_SOURCE_DIR, cls.PACKAGE_FOLDER)
        else:
            raise ValueError(f"Unknown folder type: {folder_type}")
    
    @classmethod
    def ensure_log_dir(cls):
        """Ensure the log directory exists"""
        cls.LOG_DIR.mkdir(parents=True, exist_ok=True)
        return cls.LOG_DIR 