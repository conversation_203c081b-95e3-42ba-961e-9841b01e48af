"""
Folder Matcher Utility for Image Uploader
Handles intelligent matching of model titles to folder names using various algorithms
"""

import os
import re
from difflib import SequenceMatcher
from typing import List, Optional, Tuple
from .config import ImageUploaderConfig
from .logger import ImageUploaderLogger


class FolderMatcher:
    """
    Utility class for matching model titles to folder names
    Uses multiple algorithms for robust matching including exact, fuzzy, and partial matching
    """
    
    def __init__(self, logger: ImageUploaderLogger):
        self.logger = logger
        self.config = ImageUploaderConfig()
    
    def normalize_text(self, text: str) -> str:
        """
        Normalize text for comparison by removing special characters and converting to lowercase
        """
        if not text:
            return ""
        
        # Convert to lowercase
        text = text.lower().strip()
        
        # Remove quotes and other common characters
        text = re.sub(r'[\'\"``''"""]', '', text)
        
        # Replace multiple spaces/hyphens/underscores with single space
        text = re.sub(r'[\s\-_]+', ' ', text)
        
        # Remove extra whitespace
        text = ' '.join(text.split())
        
        return text
    
    def get_folder_list(self, folder_type: str) -> List[str]:
        """
        Get list of available folders for a specific type
        """
        try:
            folder_path = self.config.get_folder_path(folder_type)
            if not os.path.exists(folder_path):
                self.logger.warning(f"Folder path does not exist: {folder_path}")
                return []
            
            folders = []
            for item in os.listdir(folder_path):
                item_path = os.path.join(folder_path, item)
                if os.path.isdir(item_path):
                    folders.append(item)
            
            return folders
        except Exception as e:
            self.logger.error(f"Error getting folder list for {folder_type}: {str(e)}")
            return []
    
    def calculate_similarity(self, text1: str, text2: str) -> float:
        """
        Calculate similarity between two texts using SequenceMatcher
        """
        normalized1 = self.normalize_text(text1)
        normalized2 = self.normalize_text(text2)
        
        return SequenceMatcher(None, normalized1, normalized2).ratio()
    
    def exact_match(self, title: str, folders: List[str]) -> Optional[str]:
        """
        Try to find exact match (case-insensitive)
        """
        normalized_title = self.normalize_text(title)
        
        for folder in folders:
            normalized_folder = self.normalize_text(folder)
            if normalized_title == normalized_folder:
                self.logger.log_folder_match(title, folder)
                return folder
        
        return None
    
    def contains_match(self, title: str, folders: List[str]) -> Optional[str]:
        """
        Try to find folder that contains the title or vice versa
        """
        normalized_title = self.normalize_text(title)
        
        # First, try to find folder that contains the title
        for folder in folders:
            normalized_folder = self.normalize_text(folder)
            if normalized_title in normalized_folder or normalized_folder in normalized_title:
                similarity = self.calculate_similarity(title, folder)
                if similarity >= self.config.MIN_SIMILARITY_SCORE:
                    self.logger.log_folder_match(title, folder, similarity)
                    return folder
        
        return None
    
    def fuzzy_match(self, title: str, folders: List[str]) -> Optional[str]:
        """
        Try to find best fuzzy match based on similarity score
        """
        best_match = None
        best_score = 0.0
        
        normalized_title = self.normalize_text(title)
        
        for folder in folders:
            similarity = self.calculate_similarity(title, folder)
            
            if similarity > best_score and similarity >= self.config.MIN_SIMILARITY_SCORE:
                best_score = similarity
                best_match = folder
        
        if best_match:
            self.logger.log_folder_match(title, best_match, best_score)
            return best_match
        
        return None
    
    def word_overlap_match(self, title: str, folders: List[str]) -> Optional[str]:
        """
        Try to find match based on word overlap
        """
        normalized_title = self.normalize_text(title)
        title_words = set(normalized_title.split())
        
        best_match = None
        best_overlap = 0
        
        for folder in folders:
            normalized_folder = self.normalize_text(folder)
            folder_words = set(normalized_folder.split())
            
            # Calculate word overlap
            overlap = len(title_words.intersection(folder_words))
            total_words = len(title_words.union(folder_words))
            
            if total_words > 0:
                overlap_ratio = overlap / total_words
                
                # Require at least one word overlap and reasonable ratio
                if overlap > 0 and overlap_ratio >= 0.4:
                    if overlap > best_overlap:
                        best_overlap = overlap
                        best_match = folder
        
        if best_match:
            similarity = self.calculate_similarity(title, best_match)
            self.logger.log_folder_match(title, best_match, similarity)
            return best_match
        
        return None
    
    def partial_word_match(self, title: str, folders: List[str]) -> Optional[str]:
        """
        Try to find match based on partial word matching (for things like "kashmir" matching "Kashmir_07")
        """
        normalized_title = self.normalize_text(title)
        title_words = normalized_title.split()
        
        for folder in folders:
            normalized_folder = self.normalize_text(folder)
            
            # Check if any title word is contained in the folder name or vice versa
            for title_word in title_words:
                if len(title_word) >= 4:  # Only consider words of reasonable length
                    if title_word in normalized_folder:
                        similarity = self.calculate_similarity(title, folder)
                        if similarity >= 0.3:  # Lower threshold for partial matches
                            self.logger.log_folder_match(title, folder, similarity)
                            return folder
            
            # Also check if folder words are in title
            folder_words = normalized_folder.split()
            for folder_word in folder_words:
                if len(folder_word) >= 4:
                    if folder_word in normalized_title:
                        similarity = self.calculate_similarity(title, folder)
                        if similarity >= 0.3:
                            self.logger.log_folder_match(title, folder, similarity)
                            return folder
        
        return None
    
    def find_matching_folder(self, title: str, folder_type: str) -> Optional[str]:
        """
        Main method to find matching folder for a given title
        Uses multiple matching strategies in order of preference
        """
        folders = self.get_folder_list(folder_type)
        if not folders:
            self.logger.warning(f"No folders found for type: {folder_type}")
            return None
        
        # Try different matching strategies in order of preference
        strategies = [
            self.exact_match,
            self.contains_match,
            self.fuzzy_match,
            self.word_overlap_match,
            self.partial_word_match
        ]
        
        for strategy in strategies:
            try:
                match = strategy(title, folders)
                if match:
                    return match
            except Exception as e:
                self.logger.error(f"Error in matching strategy {strategy.__name__} for '{title}': {str(e)}")
                continue
        
        # Log if no match found
        self.logger.log_no_folder_match(title, folders)
        return None
    
    def get_images_from_folder(self, folder_path: str, max_images: int = None) -> List[str]:
        """
        Get list of image files from a folder
        """
        if not os.path.exists(folder_path):
            return []
        
        image_files = []
        try:
            for file in os.listdir(folder_path):
                file_path = os.path.join(folder_path, file)
                if os.path.isfile(file_path):
                    # Check if file has supported extension
                    file_ext = file.split('.')[-1] if '.' in file else ''
                    if file_ext in self.config.SUPPORTED_EXTENSIONS:
                        image_files.append(file_path)
            
            # Sort files for consistent ordering
            image_files.sort()
            
            # Limit number of images if specified
            if max_images and len(image_files) > max_images:
                image_files = image_files[:max_images]
                self.logger.debug(f"Limited to {max_images} images from folder: {folder_path}")
            
            return image_files
            
        except Exception as e:
            self.logger.error(f"Error reading images from folder {folder_path}: {str(e)}")
            return []
    
    def validate_folder_structure(self) -> dict:
        """
        Validate that the expected folder structure exists
        """
        validation_results = {}
        
        for folder_type in ["activity", "category", "destination"]:
            folder_path = self.config.get_folder_path(folder_type)
            validation_results[folder_type] = {
                'exists': os.path.exists(folder_path),
                'path': folder_path,
                'folder_count': 0,
                'folders': []
            }
            
            if validation_results[folder_type]['exists']:
                folders = self.get_folder_list(folder_type)
                validation_results[folder_type]['folder_count'] = len(folders)
                validation_results[folder_type]['folders'] = folders[:10]  # First 10 folders
        
        return validation_results 