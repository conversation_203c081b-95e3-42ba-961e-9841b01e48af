# Zuumm Image Uploader Script

A comprehensive script for bulk uploading images to Category, Activity, Destination, and Package models in the Zuumm backend system.

## Overview

This script automates the process of matching and uploading images from a local folder structure to the corresponding Django models. It uses intelligent folder matching algorithms to associate images with the correct database records.

## Features

- **Intelligent Folder Matching**: Uses multiple algorithms (exact, fuzzy, partial) to match model titles with folder names
- **Bulk Upload Support**: Processes multiple images per model with configurable limits
- **S3 Integration**: Automatically uploads images to S3 via Django FileField
- **Transaction Safety**: Uses database transactions for reliable uploads
- **Comprehensive Logging**: Detailed file and console logging with statistics
- **Dry Run Mode**: Test the process without actually uploading images
- **Progress Tracking**: Real-time progress updates and detailed statistics
- **Error Handling**: Robust error handling with detailed error reporting

## Folder Structure

The script expects images to be organized in the following folder structure:

```
/home/<USER>/Documents/Zuumm-Images/
├── activity/
│   ├── Ayurvedic Massage/
│   ├── Backwater cruises/
│   ├── Beach parties/
│   └── ...
├── category/
│   ├── Adventure/
│   ├── Beach/
│   ├── Budget/
│   └── ...
├── destination/
│   ├── Agra/
│   ├── Bali/
│   ├── Dubai/
│   └── ...
└── package/ (future use)
```

## Image Limits

Each model type has specific image upload limits:

- **Categories**: 1 image maximum
- **Destinations**: 1 image maximum  
- **Activities**: 10 images maximum
- **Packages**: 20 images maximum

## Supported Image Formats

The script supports the following image formats:
- JPG, JPEG
- PNG
- WEBP
- BMP
- GIF
- TIFF, TIF

## Installation

No additional installation required. The script uses existing Django dependencies.

## Usage

### Basic Usage

```bash
cd /home/<USER>/Development/Live-Projects/zuumm-ai-backend
python -m base.image_uploader.run_script
```

### Command Line Options

```bash
# Run in dry-run mode (test without uploading)
python -m base.image_uploader.main_script --dry-run
```

### Interactive Mode

When running `run_script.py`, you'll be prompted for:
1. Dry run mode (y/N)

## Process Flow

1. **Environment Validation**
   - Checks if image source directory exists
   - Validates folder structure
   - Shows initial model statistics

2. **Activity Processing**
   - Matches activity titles to folders
   - Uploads up to 10 images per activity
   - Sets first image as main_display if none exists
   - Propagates images to related packages

3. **Destination Processing**
   - Matches destination titles to folders
   - Uploads 1 image per destination
   - Propagates images to related packages

4. **Category Processing**
   - Matches category titles to folders
   - Uploads 1 image per category

5. **Statistics & Reporting**
   - Comprehensive final statistics
   - Before/after model counts
   - Success/failure breakdown

## Folder Matching Algorithm

The script uses multiple strategies to match model titles with folder names:

1. **Exact Match**: Case-insensitive exact matching
2. **Contains Match**: One contains the other with similarity threshold
3. **Fuzzy Match**: Uses sequence matching with configurable similarity score
4. **Word Overlap**: Matches based on common words
5. **Partial Word Match**: Matches partial words (e.g., "Kashmir" → "Kashmir_07")

## Configuration

Key configuration options in `config.py`:

```python
# Source directory
IMAGES_SOURCE_DIR = "/home/<USER>/Documents/Zuumm-Images"

# Matching thresholds
MIN_SIMILARITY_SCORE = 0.6
MAX_SIMILARITY_DISTANCE = 3

# Partner settings
DEFAULT_PARTNER_ID = 1  # Zuumm partner
```

## Logging

Logs are written to:
- **File**: `base/image_uploader/logs/image_uploader.log`
- **Console**: Real-time progress and summary information

Log levels:
- **DEBUG**: Detailed matching and upload information
- **INFO**: Progress updates and summaries
- **WARNING**: Skipped items and non-critical issues
- **ERROR**: Failed uploads and critical errors

## Statistics Output

The script provides comprehensive statistics including:

- **Overall Summary**: Total processed, success rate, images uploaded
- **Per-Model Breakdown**: Detailed stats for each model type
- **Before/After Counts**: Model counts before and after processing
- **Execution Time**: Total script runtime

## Error Handling

- **File Not Found**: Skips missing images with logging
- **Permission Errors**: Logs and continues with other images
- **S3 Upload Failures**: Individual image failures don't stop batch
- **Database Errors**: Transaction rollback for data integrity
- **Network Issues**: Retry logic for S3 uploads

## Dry Run Mode

Use dry run mode to:
- Test folder matching without uploading
- Verify image counts and paths
- Check for potential issues before actual upload
- Generate statistics without modifying data

## Troubleshooting

### Common Issues

1. **"Images source directory not found"**
   - Verify the path in `config.py` is correct
   - Ensure the directory exists and is accessible

2. **"No matching folder found"**
   - Check folder names match model titles
   - Review matching algorithm logs for similarity scores
   - Consider adding aliases or adjusting similarity thresholds

3. **"S3 upload failed"**
   - Check AWS credentials and permissions
   - Verify S3 bucket configuration
   - Check network connectivity

### Debug Tips

1. **Use Dry Run First**: Always test with `--dry-run` before actual upload
2. **Check Logs**: Review detailed logs in the log file
3. **Verify Paths**: Ensure all folder paths are correct
4. **Test Small Batch**: Start with a subset of images
5. **Check Permissions**: Verify file and directory permissions

## File Structure

```
base/image_uploader/
├── __init__.py              # Module initialization
├── config.py                # Configuration settings
├── logger.py                # Logging utility
├── folder_matcher.py        # Folder matching algorithms
├── image_upload_helper.py   # S3 upload handling
├── main_script.py           # Main processing logic
├── run_script.py            # Interactive script runner
├── README.md                # This documentation
└── logs/                    # Log files directory
    └── image_uploader.log   # Main log file
```

## Integration

The script integrates with:
- **Django ORM**: For database operations
- **AWS S3**: For image storage via Django FileField
- **Package Creator**: Similar structure and logging patterns
- **Admin Panel**: Uses same partner and authentication system

## Future Enhancements

Potential improvements:
- **Package Image Upload**: Direct package image processing
- **Batch Size Configuration**: Configurable batch processing
- **Resume Capability**: Resume from interruption
- **Image Optimization**: Automatic resize/compression
- **Duplicate Detection**: Prevent duplicate uploads
- **Web Interface**: Admin panel integration

## Support

For issues or questions:
1. Check the log files for detailed error information
2. Review this documentation for common solutions
3. Test with dry run mode to isolate issues
4. Verify folder structure and permissions 