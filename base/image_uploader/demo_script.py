#!/usr/bin/env python3
"""
Demo Script for Image Uploader
Tests folder matching functionality without uploading images

This script demonstrates the folder matching capabilities without actually uploading any images.
It's useful for testing and validating the matching algorithm before running the full upload.
"""

import os
import sys
import django
from pathlib import Path

# Add the project root to Python path
current_dir = Path(__file__).resolve().parent
project_root = current_dir.parent.parent
sys.path.insert(0, str(project_root))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'zuumm.settings')
django.setup()

# Import after Django setup
from packages.models import Category, Destination, Activity
from accounts.models import Partner
from .config import ImageUploaderConfig
from .logger import ImageUploaderLogger
from .folder_matcher import FolderMatcher


def test_folder_matching():
    """
    Test folder matching for all models without uploading
    """
    print("=" * 80)
    print("ZUUMM IMAGE UPLOADER - FOLDER MATCHING DEMO")
    print("=" * 80)
    
    # Initialize components
    config = ImageUploaderConfig()
    logger = ImageUploaderLogger()
    folder_matcher = FolderMatcher(logger)
    
    print("Testing folder matching for all models in the database...")
    print()
    
    # Validate folder structure
    validation_results = folder_matcher.validate_folder_structure()
    print("=== FOLDER STRUCTURE VALIDATION ===")
    for folder_type, result in validation_results.items():
        if result['exists']:
            print(f"✓ {folder_type.title()}: {result['folder_count']} folders found")
            print(f"  Sample folders: {', '.join(result['folders'][:5])}")
        else:
            print(f"✗ {folder_type.title()}: Folder not found at {result['path']}")
    print()
    
    # Test Activities
    print("=== ACTIVITY FOLDER MATCHING ===")
    activities = Activity.objects.all()[:10]  # Test first 10
    test_model_matching(activities, "activity", folder_matcher)
    
    # Test Destinations  
    print("=== DESTINATION FOLDER MATCHING ===")
    destinations = Destination.objects.all()[:10]  # Test first 10
    test_model_matching(destinations, "destination", folder_matcher)
    
    # Test Categories
    print("=== CATEGORY FOLDER MATCHING ===")
    categories = Category.objects.all()[:10]  # Test first 10
    test_model_matching(categories, "category", folder_matcher)
    
    print("=" * 80)
    print("DEMO COMPLETED")
    print("=" * 80)


def test_model_matching(model_objects, folder_type, folder_matcher):
    """
    Test folder matching for a specific model type
    """
    total_tested = 0
    matched = 0
    
    for obj in model_objects:
        total_tested += 1
        matching_folder = folder_matcher.find_matching_folder(obj.title, folder_type)
        
        if matching_folder:
            matched += 1
            # Get image count
            folder_path = os.path.join(
                ImageUploaderConfig.get_folder_path(folder_type), 
                matching_folder
            )
            image_paths = folder_matcher.get_images_from_folder(folder_path)
            
            print(f"✓ '{obj.title}' → '{matching_folder}' ({len(image_paths)} images)")
        else:
            print(f"✗ '{obj.title}' → No matching folder found")
    
    if total_tested > 0:
        match_rate = (matched / total_tested) * 100
        print(f"\nMatching Summary: {matched}/{total_tested} ({match_rate:.1f}%) matched")
    print()


def show_available_folders():
    """
    Show all available folders for reference
    """
    print("=== AVAILABLE FOLDERS ===")
    
    config = ImageUploaderConfig()
    logger = ImageUploaderLogger()
    folder_matcher = FolderMatcher(logger)
    
    for folder_type in ["activity", "category", "destination"]:
        folders = folder_matcher.get_folder_list(folder_type)
        print(f"\n{folder_type.title()} folders ({len(folders)} total):")
        for i, folder in enumerate(folders[:20], 1):  # Show first 20
            print(f"  {i:2d}. {folder}")
        if len(folders) > 20:
            print(f"  ... and {len(folders) - 20} more")


def main():
    """
    Main demo function
    """
    print("Choose an option:")
    print("1. Test folder matching (default)")
    print("2. Show available folders")
    print()
    
    choice = input("Enter choice (1-2): ").strip()
    
    if choice == "2":
        show_available_folders()
    else:
        test_folder_matching()


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\nDemo interrupted by user.")
    except Exception as e:
        print(f"\nError: {str(e)}")
        sys.exit(1) 