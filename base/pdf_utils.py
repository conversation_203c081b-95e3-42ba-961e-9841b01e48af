import os
import tempfile
import logging
from datetime import datetime
from io import BytesIO
from django.conf import settings
from django.template.loader import render_to_string
from django.http import HttpResponse

logger = logging.getLogger(__name__)

class PDFGenerator:
    """
    Utility class for generating PDFs from HTML templates
    """
    
    @staticmethod
    def render_to_pdf(template_name, context, file_name=None):
        """
        Generate PDF from HTML template using weasyprint
        
        Args:
            template_name: Template path
            context: Template context
            file_name: Optional file name (will generate if not provided)
            
        Returns:
            str: Path to generated PDF file
        """
        try:
            # Import weasyprint here to avoid dependency issues
            from weasyprint import HTML, CSS
            from weasyprint.text.fonts import FontConfiguration
        except ImportError:
            logger.error("weasyprint is not installed. Please install it with: pip install weasyprint")
            raise ImportError("weasyprint is required for PDF generation")
        
        try:
            # Render HTML template
            html_string = render_to_string(template_name, context)
            
            # Generate filename if not provided
            if not file_name:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                file_name = f"package_pdf_{timestamp}.pdf"
            
            # Create temporary file
            temp_dir = tempfile.gettempdir()
            pdf_path = os.path.join(temp_dir, file_name)
            
            # Font configuration for better text rendering
            font_config = FontConfiguration()
            
            # Create CSS for better PDF rendering
            css_string = """
                @page {
                    size: A4;
                    margin: 0;
                }
                body {
                    font-family: 'Roboto', Arial, sans-serif;
                    margin: 0;
                    padding: 0;
                }
                .page-break {
                    page-break-before: always;
                }
                img {
                    max-width: 100%;
                    height: auto;
                }
            """
            
            # Generate PDF
            html = HTML(string=html_string, base_url=settings.STATIC_URL)
            css = CSS(string=css_string, font_config=font_config)
            
            html.write_pdf(pdf_path, stylesheets=[css], font_config=font_config)
            
            logger.info(f"PDF generated successfully: {pdf_path}")
            return pdf_path
            
        except Exception as e:
            logger.error(f"Error generating PDF: {str(e)}")
            raise
    
    @staticmethod
    def render_to_pdf_response(template_name, context, file_name="document.pdf"):
        """
        Generate PDF and return as HTTP response
        
        Args:
            template_name: Template path
            context: Template context
            file_name: Download filename
            
        Returns:
            HttpResponse: PDF as HTTP response
        """
        try:
            # Import weasyprint here to avoid dependency issues
            from weasyprint import HTML, CSS
            from weasyprint.text.fonts import FontConfiguration
        except ImportError:
            logger.error("weasyprint is not installed. Please install it with: pip install weasyprint")
            raise ImportError("weasyprint is required for PDF generation")
        
        try:
            # Render HTML template
            html_string = render_to_string(template_name, context)
            
            # Font configuration
            font_config = FontConfiguration()
            
            # CSS for PDF
            css_string = """
                @page {
                    size: A4;
                    margin: 0;
                }
                body {
                    font-family: 'Roboto', Arial, sans-serif;
                    margin: 0;
                    padding: 0;
                }
                img {
                    max-width: 100%;
                    height: auto;
                }
            """
            
            # Generate PDF to BytesIO
            pdf_buffer = BytesIO()
            html = HTML(string=html_string, base_url=settings.STATIC_URL)
            css = CSS(string=css_string, font_config=font_config)
            
            html.write_pdf(pdf_buffer, stylesheets=[css], font_config=font_config)
            pdf_buffer.seek(0)
            
            # Create HTTP response
            response = HttpResponse(pdf_buffer.getvalue(), content_type='application/pdf')
            response['Content-Disposition'] = f'attachment; filename="{file_name}"'
            
            return response
            
        except Exception as e:
            logger.error(f"Error generating PDF response: {str(e)}")
            raise

    @staticmethod
    def get_pdf_bytes(template_name, context):
        """
        Generate PDF and return as bytes for S3 upload
        
        Args:
            template_name: Template path
            context: Template context
            
        Returns:
            bytes: PDF content as bytes
        """
        try:
            # Import weasyprint here to avoid dependency issues
            from weasyprint import HTML, CSS
            from weasyprint.text.fonts import FontConfiguration
        except ImportError:
            logger.error("weasyprint is not installed. Please install it with: pip install weasyprint")
            raise ImportError("weasyprint is required for PDF generation")
        
        try:
            # Render HTML template
            html_string = render_to_string(template_name, context)
            
            # Font configuration
            font_config = FontConfiguration()
            
            # CSS for PDF
            css_string = """
                @page {
                    size: A4;
                    margin: 0;
                }
                body {
                    font-family: 'Roboto', Arial, sans-serif;
                    margin: 0;
                    padding: 0;
                }
                img {
                    max-width: 100%;
                    height: auto;
                }
            """
            
            # Generate PDF to BytesIO
            pdf_buffer = BytesIO()
            html = HTML(string=html_string, base_url=settings.STATIC_URL)
            css = CSS(string=css_string, font_config=font_config)
            
            html.write_pdf(pdf_buffer, stylesheets=[css], font_config=font_config)
            
            return pdf_buffer.getvalue()
            
        except Exception as e:
            logger.error(f"Error generating PDF bytes: {str(e)}")
            raise
    
    @staticmethod
    def generate_pdf_from_html(html_content, output_path):
        """
        Generate PDF from raw HTML content (for testing)
        
        Args:
            html_content: Raw HTML string
            output_path: Path to save the PDF
            
        Returns:
            dict: Result with success status
        """
        try:
            # Import weasyprint here to avoid dependency issues
            from weasyprint import HTML, CSS
            from weasyprint.text.fonts import FontConfiguration
        except ImportError:
            error_msg = "weasyprint is not installed. Please install it with: pip install weasyprint"
            logger.error(error_msg)
            return {'success': False, 'error': error_msg}
        
        try:
            # Font configuration
            font_config = FontConfiguration()
            
            # Basic CSS for PDF
            css_string = """
                @page {
                    size: A4;
                    margin: 20px;
                }
                body {
                    font-family: 'Roboto', Arial, sans-serif;
                    line-height: 1.6;
                }
                h1, h2, h3 {
                    color: #333;
                }
                img {
                    max-width: 100%;
                    height: auto;
                }
            """
            
            # Generate PDF
            html = HTML(string=html_content)
            css = CSS(string=css_string, font_config=font_config)
            
            html.write_pdf(output_path, stylesheets=[css], font_config=font_config)
            
            logger.info(f"PDF generated successfully from HTML: {output_path}")
            return {'success': True, 'path': output_path}
            
        except Exception as e:
            error_msg = f"Error generating PDF from HTML: {str(e)}"
            logger.error(error_msg)
            return {'success': False, 'error': error_msg} 