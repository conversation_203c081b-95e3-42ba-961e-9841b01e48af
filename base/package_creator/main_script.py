#!/usr/bin/env python
"""
Main Package Creator Script
Creates multiple packages from document files in a folder
"""

import os
import sys
import django
from pathlib import Path
from django.db import transaction

# Setup Django environment
sys.path.append(str(Path(__file__).parent.parent.parent))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'zuumm.settings')
django.setup()

from .config import PackageCreatorConfig
from .logger import PackageCreatorLogger
from .document_processor import DocumentProcessor
from .openai_helper import OpenAIHelper
from .package_creation_helper import PackageCreationHelper


class PackageCreatorScript:
    """
    Main script class for creating packages from document files
    Handles the complete workflow from file reading to package creation
    """
    
    def __init__(self, documents_folder=None):
        # Initialize components
        self.logger = PackageCreatorLogger()
        self.document_processor = DocumentProcessor(self.logger)
        self.openai_helper = OpenAIHelper(self.logger)
        self.package_helper = PackageCreationHelper(self.logger)
        
        # Set documents folder
        self.documents_folder = documents_folder or PackageCreatorConfig.DEFAULT_DOCUMENTS_FOLDER
        
        # Statistics tracking
        self.stats = {
            'total_files': 0,
            'processed_files': 0,
            'failed_files': 0,
            'total_packages': 0,
            'successful_packages': 0,
            'failed_packages': 0,
            'errors': []
        }
    
    def run(self, documents_folder=None):
        """
        Main entry point for the script
        Processes all documents in the folder and creates packages
        """
        try:
            # Override folder if provided
            if documents_folder:
                self.documents_folder = documents_folder
            
            # Log script start
            self.logger.log_script_start(self.documents_folder)
            
            # Validate setup
            self._validate_setup()
            
            # Get all document files
            document_files = self._get_document_files()
            
            if not document_files:
                self.logger.warning("No document files found in the specified folder")
                return
            
            self.stats['total_files'] = len(document_files)
            
            # Process all files within a transaction
            self._process_all_files(document_files)
            
            # Log completion
            self._log_completion()
            
        except Exception as e:
            self.logger.log_exception(e, "running package creator script")
            self.logger.critical("Script execution failed")
            raise
    
    def _validate_setup(self):
        """Validate script setup and configuration"""
        try:
            # Check OpenAI API key
            if not PackageCreatorConfig.OPENAI_API_KEY:
                raise ValueError("OpenAI API key not configured")
            
            # Check documents folder
            if not os.path.exists(self.documents_folder):
                raise ValueError(f"Documents folder does not exist: {self.documents_folder}")
            
            if not os.path.isdir(self.documents_folder):
                raise ValueError(f"Documents path is not a directory: {self.documents_folder}")
            
            self.logger.info("Setup validation completed successfully")
            
        except Exception as e:
            self.logger.log_exception(e, "validating script setup")
            raise
    
    def _get_document_files(self):
        """Get all document files from the folder"""
        try:
            document_files = self.document_processor.get_files_from_folder(self.documents_folder)
            
            if not document_files:
                self.logger.warning(f"No supported document files found in: {self.documents_folder}")
                return []
            
            self.logger.info(f"Found {len(document_files)} document files to process")
            
            # Apply test mode limitation if enabled
            if PackageCreatorConfig.TEST_MODE:
                original_count = len(document_files)
                document_files = document_files[:PackageCreatorConfig.TEST_MODE_FILE_LIMIT]
                self.logger.info(f"TEST MODE ENABLED: Processing only {len(document_files)} files out of {original_count} total files")
                self.logger.info(f"To process all files, set PackageCreatorConfig.TEST_MODE = False in config.py")
            
            self.logger.info(f"Found {len(document_files)} valid document files")
            
            return document_files
            
        except Exception as e:
            self.logger.log_exception(e, "getting document files")
            raise
    
    def _process_all_files(self, document_files):
        """
        Process all document files with per-package transaction isolation
        Individual packages are wrapped in atomic blocks to prevent cascade failures
        """
        try:
            self.logger.log_transaction_start()
            
            for file_index, file_path in enumerate(document_files, 1):
                try:
                    self._process_single_file(file_path, file_index, len(document_files))
                    self.stats['processed_files'] += 1
                    
                except Exception as e:
                    self.stats['failed_files'] += 1
                    self.stats['errors'].append({
                        'file': str(file_path),
                        'error': str(e)
                    })
                    self.logger.log_exception(e, f"processing file {file_path}")
                    # Continue with next file instead of stopping
                    continue
            
            # Check if we had any successful operations
            if self.stats['successful_packages'] == 0:
                raise Exception("No packages were created successfully")
            
            self.logger.log_transaction_commit()
            
        except Exception as e:
            self.logger.log_transaction_rollback(str(e))
            self.logger.log_exception(e, "processing files in transaction")
            raise
    
    def _process_single_file(self, file_path, file_index, total_files):
        """Process a single document file"""
        try:
            self.logger.log_file_processing_start(file_path, file_index, total_files)
            
            # Extract text from document
            document_text = self._extract_text_from_file(file_path)
            
            # Extract packages data using OpenAI
            packages_data = self._extract_packages_from_text(document_text, file_path)
            
            # Create packages from extracted data
            created_packages = self._create_packages_from_data(packages_data, file_path)
            
            # Update statistics
            self.stats['total_packages'] += len(created_packages)
            self.stats['successful_packages'] += len(created_packages)
            
            self.logger.log_file_processing_end(file_path, len(created_packages), success=True)
            
        except Exception as e:
            self.logger.log_file_processing_end(file_path, 0, success=False)
            raise
    
    def _extract_text_from_file(self, file_path):
        """Extract text content from a document file"""
        try:
            self.logger.debug(f"Extracting text from file: {file_path}")
            
            document_text = self.document_processor.extract_text_from_file(file_path)
            
            if not document_text or not document_text.strip():
                raise ValueError(f"No text content extracted from file: {file_path}")
            
            self.logger.debug(f"Text extraction successful. Length: {len(document_text)} characters")
            return document_text
            
        except Exception as e:
            self.logger.log_exception(e, f"extracting text from {file_path}")
            raise
    
    def _extract_packages_from_text(self, document_text, file_path):
        """Extract multiple packages data from document text using OpenAI"""
        try:
            self.logger.debug(f"Extracting packages data from text for: {file_path}")
            
            packages_data = self.openai_helper.extract_packages_from_document(
                document_text, 
                str(file_path)
            )
            
            if not packages_data:
                raise ValueError(f"No packages data extracted from file: {file_path}")
            
            self.logger.info(f"Extracted {len(packages_data)} packages from file: {file_path}")
            return packages_data
            
        except Exception as e:
            self.logger.log_exception(e, f"extracting packages from text for {file_path}")
            raise
    
    def _create_packages_from_data(self, packages_data, file_path):
        """Create Package objects from extracted data with per-package transaction isolation"""
        try:
            if not packages_data:
                raise Exception("No package data provided")
            
            self.logger.info(f"Extracted {len(packages_data)} packages from file: {file_path}")
            
            successful_packages = []
            failed_packages = []
            
            # Process each package in its own transaction to prevent cascade failures
            for i, package_data in enumerate(packages_data):
                package_title = package_data.get('title', f'Package {i+1}')
                self.logger.info(f"Creating Package {i+1}/{len(packages_data)}: {package_title}")
                
                try:
                    # Individual transaction per package
                    with transaction.atomic():
                        package = self.package_helper.create_package_from_data(package_data)
                        successful_packages.append(package)
                        self.logger.info(f"Package Created Successfully: {package.title} (ID: {package.id})")
                        
                except Exception as e:
                    error_msg = str(e)
                    failed_packages.append({
                        'title': package_title,
                        'data': package_data,
                        'error': error_msg
                    })
                    self.logger.error(f"Package Creation Failed: {package_title}")
                    self.logger.log_exception(e, f"creating package {i+1} from {file_path}")
                    
                    # ✅ Log skipped package to separate file
                    self.openai_helper.log_skipped_package(file_path, package_title, error_msg)
                    
                    # Continue with next package instead of failing entire file
                    continue
            
            # Log results
            success_count = len(successful_packages)
            failure_count = len(failed_packages)
            
            self.logger.info(f"Created {success_count} packages from file: {file_path}")
            
            if failed_packages:
                self.logger.warning(f"Failed to create {failure_count} packages from file: {file_path}")
                for failed in failed_packages:
                    self.logger.warning(f"  - {failed['title']}: {failed['error']}")
            
            # Consider file successful if at least one package was created
            if successful_packages:
                return successful_packages
            else:
                raise Exception(f"No packages created successfully from file: {file_path}")
                
        except Exception as e:
            self.logger.log_exception(e, f"creating packages from data for {file_path}")
            raise
    
    def _log_completion(self):
        """Log script completion with detailed statistics"""
        try:
            # Calculate success rate
            total_operations = self.stats['total_files'] + self.stats['total_packages']
            successful_operations = self.stats['processed_files'] + self.stats['successful_packages']
            
            # Log detailed summary
            self.logger.log_script_end(
                self.stats['total_files'],
                self.stats['total_packages'], 
                successful_operations,
                total_operations - successful_operations
            )
            
            # Log detailed statistics
            self.logger.info("DETAILED STATISTICS:")
            self.logger.info(f"Files - Total: {self.stats['total_files']}, Processed: {self.stats['processed_files']}, Failed: {self.stats['failed_files']}")
            self.logger.info(f"Packages - Total: {self.stats['total_packages']}, Successful: {self.stats['successful_packages']}, Failed: {self.stats['failed_packages']}")
            
            # Log errors if any
            if self.stats['errors']:
                self.logger.warning(f"ERRORS ENCOUNTERED ({len(self.stats['errors'])}):")
                for error in self.stats['errors'][:5]:  # Show only first 5 errors
                    self.logger.warning(f"- {error['file']}: {error['error']}")
                if len(self.stats['errors']) > 5:
                    self.logger.warning(f"... and {len(self.stats['errors']) - 5} more errors")
            
            # Log test mode reminder if enabled
            if PackageCreatorConfig.TEST_MODE:
                self.logger.info("=" * 80)
                self.logger.info("TEST MODE WAS ENABLED - Only first 5 files were processed")
                self.logger.info("To process ALL files, set PackageCreatorConfig.TEST_MODE = False in config.py")
                self.logger.info("=" * 80)
            
            # Log success message
            if self.stats['successful_packages'] > 0:
                self.logger.info(f"✅ SUCCESS: {self.stats['successful_packages']} packages created successfully!")
            else:
                self.logger.error("❌ FAILURE: No packages were created successfully")
            
        except Exception as e:
            self.logger.log_exception(e, "logging completion")


def main():
    """
    Main function to run the package creator script
    Can be called with command line arguments or interactively
    """
    try:
        # Get documents folder from command line or user input
        documents_folder = None
        
        if len(sys.argv) > 1:
            documents_folder = sys.argv[1]
        else:
            # Interactive mode - ask user for folder path
            print("Package Creator Script")
            print("=" * 50)
            print(f"Default folder: {PackageCreatorConfig.DEFAULT_DOCUMENTS_FOLDER}")
            
            user_input = input("Enter documents folder path (or press Enter for default): ").strip()
            
            if user_input:
                documents_folder = user_input
            else:
                documents_folder = PackageCreatorConfig.DEFAULT_DOCUMENTS_FOLDER
        
        # Validate folder exists
        if not os.path.exists(documents_folder):
            print(f"Error: Folder does not exist: {documents_folder}")
            return 1
        
        print(f"Processing documents from: {documents_folder}")
        print("Starting package creation...")
        
        # Create and run script
        script = PackageCreatorScript(documents_folder)
        script.run()
        
        print("\nScript execution completed!")
        print(f"Check log file for detailed information: {PackageCreatorConfig.LOG_FILE_PATH}")
        
        return 0
        
    except KeyboardInterrupt:
        print("\nScript interrupted by user")
        return 1
    except Exception as e:
        print(f"Script failed with error: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 