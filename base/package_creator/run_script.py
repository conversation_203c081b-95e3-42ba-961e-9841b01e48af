#!/usr/bin/env python
"""
Simple runner script for Package Creator
This script can be executed directly to run the package creation process
"""

import os
import sys
import django
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'zuumm.settings')
django.setup()

# Import and run the main script
from base.package_creator.main_script import main

if __name__ == "__main__":
    # Run the main function
    exit_code = main()
    sys.exit(exit_code) 