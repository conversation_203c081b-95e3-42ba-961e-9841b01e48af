"""
OpenAI Helper for Package Creator Script
Handles OpenAI API communication for extracting multiple packages from documents
"""

import json
import requests
import re
from datetime import datetime
from .config import PackageCreatorConfig
from .logger import PackageCreatorLogger
import time
import os


class OpenAIHelper:
    """
    OpenAI API helper class for extracting package data from documents
    Modified to handle multiple packages in a single document
    """
    
    def __init__(self, logger=None):
        self.logger = logger or PackageCreatorLogger()
        self.api_key = PackageCreatorConfig.OPENAI_API_KEY
        self.api_url = PackageCreatorConfig.OPENAI_API_URL
        self.model = PackageCreatorConfig.OPENAI_MODEL
        self.max_tokens = PackageCreatorConfig.OPENAI_MAX_TOKENS
        self.temperature = PackageCreatorConfig.OPENAI_TEMPERATURE
        
        if not self.api_key:
            raise ValueError("OpenAI API key not configured")
    
    def _get_multi_package_extraction_prompt(self):
        """
        Get the system prompt for extracting multiple packages from a single document
        Updated to match the exact admin package creation flow requirements
        """
        return """You are an AI travel package document processor. Your task is to extract MULTIPLE travel package information from a single document and return them as separate JSON objects.

⚠️ CRITICAL: This document contains MULTIPLE travel packages. You MUST:
1. READ THE ENTIRE DOCUMENT THOROUGHLY from start to finish before extracting
2. Identify and separate EVERY individual package in the document
3. Extract complete information for each package separately  
4. Return an array of JSON objects, one for each package found
5. ⚠️ DO NOT MISS ANY PACKAGES - Some documents have 3-4 packages and sometimes 7-8 packages, ensure you find ALL of them

⚠️ CRITICAL PACKAGE DETECTION PATTERNS:
- Look for "Package Name:" followed by package title (MANDATORY INDICATOR)
- Look for "Package No:" or "Package No -" followed by number (MANDATORY INDICATOR)
- ⚠️ PACKAGE NUMBERS CAN BE NON-SEQUENTIAL: e.g., Package No: 33, Package No: 34, Package No: 63 (ALL ARE SEPARATE PACKAGES)
- ⚠️ PACKAGES CAN HAVE LARGE SPACING GAPS between them - this is NORMAL, keep scanning
- Look for repeated sections like "Highlights:", "Inclusions:", "Itinerary:", "About This Tour" which indicate new packages
- Each package will have its own destination, price, and details
- ⚠️ SCAN THE ENTIRE DOCUMENT - packages may be separated by large gaps, different formatting, or appear at the very end

⚠️ THOROUGH DOCUMENT SCANNING INSTRUCTIONS:
1. ⚠️ Read from BEGINNING to ABSOLUTE END of document - do not stop early
2. ⚠️ Look for pattern repetitions (multiple "Package Name:", "Highlights:", "Inclusions:" sections)
3. ⚠️ Count ALL "Package No:" entries in the document before starting extraction
4. ⚠️ If you find "Package No: 33", "Package No: 34", "Package No: 63" - these are THREE separate packages
5. ⚠️ Each complete package should have: title, package_no, destination, price, highlights, inclusions
6. ⚠️ Do not merge similar packages - keep them separate if they have different package numbers or titles
7. ⚠️ IGNORE SPACING AND FORMATTING DIFFERENCES - focus on content patterns
8. ⚠️ PACKAGES CAN APPEAR ANYWHERE IN THE DOCUMENT - beginning, middle, or end

**⚠️ CRITICAL MULTI-PACKAGE DETECTION RULES:**
- If document contains "Package Name: Package A" and later "Package Name: Package B", these are TWO separate packages
- If document contains "Package No: 1" and later "Package No: 2", these are TWO separate packages  
- If document contains "Package No: 33", "Package No: 34", and "Package No: 63", these are THREE separate packages
- ⚠️ PACKAGE NUMBERS DO NOT NEED TO BE SEQUENTIAL - 33, 34, 63 are all valid individual packages
- If you see multiple sets of "Highlights:" and "Inclusions:" sections, these likely belong to different packages
- If you see multiple "About This Tour" sections, these belong to different packages
- If you see multiple pricing sections with different amounts, these are different packages
- ⚠️ ALWAYS scan the ENTIRE document and count ALL distinct package sections before starting extraction
- ⚠️ DO NOT STOP after finding the first few packages - continue scanning to the end

**⚠️ FORMATTING VARIATION AWARENESS:**
- Packages may have different amounts of spacing between them
- Some packages may be longer or shorter than others
- Package numbers may not be sequential (33, 34, 63 are ALL separate packages)
- Formatting styles may vary between packages in the same document
- Always look for the core indicators: "Package Name:" and "Package No:" regardless of surrounding formatting

=== USER-PROVIDED FIELDS (Extract and preserve exactly) ===
Extract these from the document content and preserve exactly as found:

**BASIC FIELDS - EXTRACT AND PRESERVE:**
- title: Extract exact title from document (from "Package Name:" line)
- package_no: ⚠️ CRITICAL - Extract ONLY the number/text after "Package No:" - DO NOT include "Package No." or any prefix text. Example: "Package No: 89" → extract "89", "Package No. 89" → extract "89"
- destination: Extract exact destination name, only convert to lowercase
- categories: Extract categories from "Package Category:" line, convert to lowercase, ensure array format (split by commas)
- activities: ⚠️ CRITICAL - Extract activity names from document for M2M relationships, ensure array format (convert to lowercase)
- popular_activities: ⚠️ CRITICAL - Extract activity names for array field storage, ensure array format (keep original case)
- owner: ⚠️ CRITICAL - Extract exact owner/operator information from document (from "Owner:" line) - Look for "Owner:", "Operator:", "Tour Company:", "Company:" sections
- type: Extract package type from "Type:" line ("Fixed" or "Variable") - ⚠️ DEFAULT: If not found, set to "Fixed"
- duration: ⚠️ PRESERVE FORMAT - Extract duration and keep EXACT format (e.g., "5N & 6D" stays "5N & 6D", do NOT change to "5N/6D"). Fix only spelling mistakes (e.g., "4Nught 6 day" → "4N & 6D")
- price_per_person: ⚠️ CRITICAL - Extract price with rupee symbol. IF PRICE RANGE FOUND (e.g., "INR 45,000 - INR 50,000", "₹75,000 - ₹80,000"), ALWAYS USE THE LOWER LIMIT VALUE ONLY (e.g., "₹45,000", "₹75,000"). Single prices: preserve as-is (e.g., "₹55,590"). If no currency symbol found, add ₹ symbol as default.
- visa_type: ⚠️ EXTRACT CAREFULLY - Look for "Visa Type:", "Visa:", "Visa Required:" sections, ensure array format. DO NOT skip this field if data is found... If no data found, Skip this field.
- about_this_tour: Extract exact description from "About This Tour" section
- highlights: Extract exact highlights from "Highlights" section, ensure array format
- inclusions: Extract exact inclusions from "Inclusions" section, ensure array format
- exclusions: Extract exact exclusions from "Exclusions" section, ensure array format
- itinerary: ⚠️ EXTRACT CONTENT ONLY - Extract exact itinerary content (skip the "Itinerary" heading), convert to HTML format with day structure
- hotels: ⚠️ EXTRACT CONTENT ONLY - Look for "Hotels:", "Accommodation:", "Where to Stay:" sections, extract CONTENT not headings (e.g., skip "3 Star Hotels" heading, extract actual hotel names), ensure array format
- addons: ⚠️ PRESERVE SIMPLE TERMS - Extract exact add-ons from "Add-Ons" section as simple terms. If document shows "Flight, Train, Bus, Insurance and Upgrades" keep as ["Flight", "Train", "Bus", "Insurance", "Upgrades"] - DO NOT expand to detailed descriptions
- best_time_to_visit: ⚠️ EXTRACT - Look for "Best Time to Visit:", "Best Season:", "Ideal Time:" sections
- destination_safety: ⚠️ EXTRACT - Look for "Safety:", "Safety Information:", "Travel Safety:" sections
- popular_restaurants: ⚠️ EXTRACT - Look for "Restaurants:", "Where to Eat:", "Dining:" sections, ensure array format
- cultural_info: ⚠️ EXTRACT - Look for "Culture:", "Cultural Information:", "Cultural Tips:" sections
- what_to_shop: ⚠️ EXTRACT - Look for "Shopping:", "What to Buy:", "Shopping Guide:" sections
- what_to_pack: ⚠️ EXTRACT CONTENT ONLY - Look for "Packing:", "What to Bring:", "What to Pack:" sections, extract content (skip heading), ensure proper format

**SECTION PARSING RULES:**
- "Package Name:" → title field
- "Package No:" or "Package No -" or "Package No." → package_no field (extract ONLY the number/text, not the prefix)
- "Destination:" → destination field
- "Package Category:" or "Package Category -" → categories array (split by commas)
- "Type:" → type field (default to "Fixed" if missing)
- "Duration:" → duration field (preserve & format, do NOT change to /)
- "Price per person:" → price_per_person field (preserve exact format)
- "Owner:" or "Operator:" or "Tour Company:" or "Company:" → owner field ⚠️ CRITICAL - ALWAYS EXTRACT
- "About This Tour" → about_this_tour field
- "Highlights:" → highlights array (split by bullet points or newlines)
- "Inclusions" → inclusions array (split by bullet points or newlines)
- "Exclusions" → exclusions array (split by bullet points or newlines)
- "Itinerary" → itinerary field (convert to HTML format, extract content only)
- "Hotel" or "Accommodation:" → hotels array ⚠️ EXTRACT content, not headings
- "Activities:" or "Things to Do:" or "Popular Activities" → activities array (for M2M relationships) AND popular_activities array (for storage) ⚠️ EXTRACT BOTH
- "Add-Ons:" or "Add-On Services" → addons array (preserve exact simple terms)
- "Best Time to Visit" or "Best Season:" → best_time_to_visit field ⚠️ EXTRACT
- "Rating" → rating field AND rating_description field
- "Currency and Conversion Rate" → currency_conversion_rate field
- "Destination Safety" or "Safety:" → destination_safety field ⚠️ EXTRACT
- "Popular Restaurants" or "Restaurants:" → popular_restaurants field ⚠️ EXTRACT
- "What to Shop" or "Shopping:" → what_to_shop field ⚠️ EXTRACT
- "What to Pack" or "Packing:" → what_to_pack field ⚠️ EXTRACT content, convert to HTML format
- "Cultural Info" or "Culture:" → cultural_info field ⚠️ EXTRACT
- "Visa Type" or "Visa:" → visa_type field ⚠️ EXTRACT CAREFULLY

**CRITICAL ITINERARY HTML FORMAT REQUIREMENT:**
Extract itinerary information (CONTENT ONLY, skip "Itinerary" heading) and convert to proper HTML content with day-wise structure:

INPUT: "Day 1: Ahmedabad Arrival\\nArrive in Ahmedabad. Visit Sabarmati Ashram and Akshardham Temple.\\nDay 2: Rani ki Vav & Modhera Sun Temple\\nDay trip to Rani ki Vav (UNESCO site) and Modhera Sun Temple."

OUTPUT: "<h3>Day 1 - Ahmedabad Arrival</h3><ul><li>Arrive in Ahmedabad</li><li>Visit Sabarmati Ashram and Akshardham Temple</li></ul><h3>Day 2 - Rani ki Vav & Modhera Sun Temple</h3><ul><li>Day trip to Rani ki Vav (UNESCO site)</li><li>Visit Modhera Sun Temple</li></ul>"

**HTML STRUCTURE RULES FOR ITINERARY:**
- Use <h3> tags for day headers: "<h3>Day X - Title</h3>"
- Use <ul><li> tags for activities/descriptions in bullet points
- Break down descriptions into logical bullet points
- Maintain chronological day order
- Extract all itinerary content from document and structure as HTML

**CRITICAL WHAT TO PACK HTML FORMAT REQUIREMENT:**
Convert packing list (CONTENT ONLY, skip "What to Pack" heading) to HTML format with 3-5 logical categories:

INPUT: "Light cotton clothes (Oct–Mar), sunscreen, sunglasses, a scarf or cap, personal medication, and modest attire for temple visits."

OUTPUT: "<h3>Clothing</h3><ul><li>Light cotton clothes (Oct–Mar)</li><li>Modest attire for temple visits</li></ul><h3>Health & Safety</h3><ul><li>Sunscreen</li><li>Personal medication</li></ul><h3>Accessories</h3><ul><li>Sunglasses</li><li>Scarf or cap</li></ul>"

**HTML STRUCTURE RULES FOR WHAT TO PACK:**
- Group items into 3-5 logical categories with descriptive titles
- Use <h3> for category headings and <ul><li> for items
- Each category should have 3-6 items max
- Make categories practical and intuitive like: Clothing, Health & Safety, Essentials, Electronics, etc.
- Avoid too many small categories or putting everything in one category

**WHAT TO SHOP FORMAT REQUIREMENT:**
Convert shopping recommendations to paragraph text (not array):

INPUT: "• Bandhani fabrics • Block-printed textiles • Khadi products"
OUTPUT: "Bandhani fabrics, block-printed textiles, Khadi products, Patola sarees, mirror work handicrafts, and traditional jewelry from Law Garden Night Market."

**RATING DESCRIPTION EXTRACTION:**
Look for text immediately following the rating:

INPUT: "Rating\\n4.5/5\\nTravelers appreciate Ahmedabad for its rich heritage, vibrant culture, architectural marvels, and delicious vegetarian cuisine."

OUTPUT: 
- rating: 4.5
- rating_description: "Travelers appreciate Ahmedabad for its rich heritage, vibrant culture, architectural marvels, and delicious vegetarian cuisine."

**CRITICAL PRESERVATION RULES:**
1. ⚠️ PRESERVE EXACT PRICE FORMAT: Keep "₹45,890" as "₹45,890", add ₹ symbol if currency missing
2. ⚠️ PRESERVE PACKAGE NUMBERS: Extract ONLY number from "Package No: 33" → "33", "Package No. 89" → "89" (no prefix text)
3. ⚠️ PRESERVE SIMPLE ADDONS: If document says "Flight, Train, Bus, Insurance and Upgrades" keep as [\"Flight\", \"Train\", \"Bus\", \"Insurance\", \"Upgrades\"] - DO NOT expand
4. ⚠️ EXTRACT RATING DESCRIPTION: If rating description is provided in document, extract exactly - if not, generate based on highlights and destination
5. ⚠️ EXTRACT OWNER FIELD: Always look for and extract owner/operator information from document - THIS IS CRITICAL
6. ⚠️ EXTRACT BOTH ACTIVITY FIELDS: Extract activities for both M2M relationships AND popular_activities array field
7. ⚠️ FIND ALL PACKAGES: Do not stop at first few packages - scan entire document for all packages, including those with non-sequential numbers
8. ⚠️ PRESERVE DURATION FORMAT: Keep "5N & 6D" format, do NOT change to "5N/6D"
9. ⚠️ SKIP HEADINGS: For hotels, itinerary, what_to_pack - extract content only, skip section headings
10. ⚠️ DEFAULT TYPE: If type not found, set to "Fixed"
11. ⚠️ EXTRACT VISA TYPE: Always look for and extract visa information

=== AUTO-CALCULATED FIELDS (Compute from extracted data) ===
Calculate these ONLY from the extracted basic fields:

- duration_in_nights: Calculate from extracted duration (e.g., "4N & 5D" → 4)
- duration_in_days: Calculate from extracted duration (e.g., "4N & 5D" → 5)
- price: Extract numeric value from extracted price_per_person (but keep original price_per_person unchanged)
- currency: Extract currency code from extracted price_per_person (INR, USD, EUR, GBP)
- best_time_to_visit_months: Extract month names from best_time_to_visit text if provided, convert to array format

=== LLM SOURCED FIELDS (Generate ONLY if not found in document) ===
Generate these fields ONLY if they are NOT found in the document content:

**ABSOLUTELY REQUIRED FIELDS (Must always generate if missing):**
- important_notes: REQUIRED - Generate important travel notes array based on destination and visa requirements
- rating_description: REQUIRED - If not provided in document, generate a brief 2-line description explaining why this package deserves its rating based on highlights, inclusions, and destination appeal

**CONDITIONAL GENERATION (Generate only if missing from document):**
- owner: If not found in document, set to "ZUUMM" as default ⚠️ CRITICAL
- type: If not found in document, set to "Fixed" as default ⚠️ CRITICAL
- best_time_to_visit: Generate detailed travel timing advice for the destination if not found
- best_time_to_visit_months: Generate optimal month names as array if not found
- rating: Generate realistic rating (3.5-4.8 range) if not mentioned
- currency_conversion_rate: Generate current conversion rates if not provided
- destination_safety: Generate safety information if not mentioned in document
- popular_restaurants: Generate restaurant recommendations as array if not mentioned in document
- activities: Generate activity recommendations as array if not mentioned in document ⚠️ FOR M2M RELATIONSHIPS
- popular_activities: Generate activity recommendations as array if not mentioned in document ⚠️ FOR ARRAY FIELD
- what_to_shop: Generate shopping recommendations as paragraph text if not mentioned in document
- what_to_pack: Generate packing list in HTML format with categories if not mentioned in document
- cultural_info: Generate cultural information if not mentioned in document
- hotels: Generate hotel recommendations as array if not mentioned in document
- visa_type: Generate visa requirements as array if not mentioned in document

**LLM GENERATION RULES:**
- Base ALL generated content on the extracted destination
- Use current, accurate information for the destination
- Make ratings realistic (3.5-4.8 range)
- For rating_description: Generate concise 2-line description focusing on key value propositions
- For what_to_pack: Generate well-organized HTML format with 3-5 logical categories
- For important_notes: Generate practical travel notes based on destination and visa requirements
- For hotels: Generate 3-5 hotel recommendations as array
- For popular_restaurants: Generate 3-5 restaurant recommendations as array
- For activities AND popular_activities: Generate 3-5 activity recommendations as array (same content for both fields)
- For destination_safety: Generate practical safety advice
- For cultural_info: Generate cultural etiquette and information
- For owner: Always set to "ZUUMM" if not found in document ⚠️ CRITICAL
- For type: Always set to "Fixed" if not found in document ⚠️ CRITICAL
- For visa_type: Generate appropriate visa requirements as array if not found

=== EXAMPLE OF MULTI-PACKAGE DOCUMENT (EXACTLY LIKE YOUR FORMAT) ===
```
Package Name: Package A Title
Package No: 33
...package A details...

Package Name: Package B Title  
Package No: 34
...package B details...

Package Name: Package C Title
Package No: 63
...package C details...
```
⚠️ THIS CONTAINS THREE PACKAGES: 33, 34, AND 63 - ALL MUST BE EXTRACTED!

CRITICAL REQUIREMENTS:
1. Return ONLY valid JSON array, no explanations or additional text
2. Each package must be a complete, valid JSON object
3. ⚠️ PRESERVE EXACT price_per_person formatting with currency symbols
4. ⚠️ CONVERT itinerary to HTML format with <h3> and <ul><li> tags
5. ⚠️ CONVERT what_to_pack to HTML format with logical categories
6. ⚠️ KEEP what_to_shop as paragraph text (not array)
7. ⚠️ EXTRACT rating_description text exactly as found under rating, generate if missing
8. ⚠️ PRESERVE addons exactly as found, don't expand simple terms
9. ⚠️ EXTRACT owner field from document, default to "ZUUMM" if missing ⚠️ CRITICAL
10. ⚠️ GENERATE all missing LLM fields based on destination
11. ⚠️ EXTRACT BOTH activities (for M2M) AND popular_activities (for array field) - same content, different case
12. Handle both "Package No:" and "Package No -" and "Package No." formats
13. Split categories properly from comma-separated lists
14. Auto-generate best_time_to_visit_months from best_time_to_visit text
15. Auto-generate important_notes array for each package
16. For missing optional fields, generate appropriate content based on destination
17. Ensure all JSON strings are properly escaped
18. Convert destination, categories, activities to lowercase for consistency
19. Keep popular_activities in original case for display purposes
20. ⚠️ MOST CRITICAL: FIND ALL PACKAGES IN THE DOCUMENT - Do not stop at the first few, scan the entire document thoroughly, including packages with non-sequential numbers like 33, 34, 63
21. ⚠️ PRESERVE DURATION FORMAT: Keep "5N & 6D" format, do NOT change to "5N/6D"
22. ⚠️ EXTRACT CONTENT ONLY: Skip headings for hotels, itinerary, what_to_pack sections
23. ⚠️ DEFAULT TYPE TO "Fixed": If type not found, always set to "Fixed"
24. ⚠️ EXTRACT PACKAGE NUMBER ONLY: "Package No: 89" → "89" (no prefix text)
25. ⚠️ EXTRACT VISA TYPE: Always look for and extract visa information carefully

⚠️⚠️⚠️ JSON FORMAT REQUIREMENTS ⚠️⚠️⚠️
- Start response immediately with [ character
- End response with ] character  
- Each package object must be properly formatted JSON
- Use double quotes for all strings
- Escape special characters properly (\\\", \\\\, \\n)
- No trailing commas in objects or arrays
- No comments or explanations outside the JSON array"""

    def extract_packages_from_document(self, document_text, file_path=None):
        """
        Extract multiple packages from document text using simplified Package Name detection
        Returns list of package dictionaries
        """
        try:
            self.logger.log_openai_request(file_path or "unknown", "multi_package_extraction")
            
            # ✅ SIMPLIFIED APPROACH: Just tell OpenAI to find all "Package Name:" occurrences
            self.logger.info(f"🔍 SCANNING: Looking for all 'Package Name:' patterns in {file_path}")
            
            # Single extraction attempt with clear instructions
            packages_data = self._extract_all_packages_by_name_pattern(document_text, file_path)
            
            if not packages_data:
                raise Exception("No packages found in document")
            
            final_count = len(packages_data)
            self.logger.info(f"✅ SUCCESS: Extracted {final_count} packages from {file_path}")
            self.logger.log_openai_response(final_count, file_path or "unknown")
            return packages_data
            
        except Exception as e:
            self.logger.log_exception(e, f"extracting packages from document via OpenAI")
            raise

    def _extract_all_packages_by_name_pattern(self, document_text, file_path):
        """
        Extract all packages by scanning for 'Package Name:' patterns - simplified approach
        """
        try:
            # Prepare the API request
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
            
            # Simplified system prompt focused on Package Name detection
            system_prompt = self._get_simplified_extraction_prompt()
            
            # Clear user prompt with Package Name focus
            user_prompt = f"""Scan this ENTIRE document from start to finish and extract ALL travel packages.

🎯 DETECTION RULE: Every "Package Name:" line starts a new package.

📋 IMPORTANT - EXTRACTION INSTRUCTIONS:
1. Read through the ENTIRE document thoroughly
2. Find EVERY occurrence of "Package Name:" (case insensitive)
3. For each "Package Name:" found, extract the complete package information that follows
4. Continue reading until you find the next "Package Name:" or reach the end of document
5. Return ALL packages as a JSON array

⚠️ CRITICAL: Do NOT pre-count or validate package numbers - just find every "Package Name:" and extract its data.

DOCUMENT TEXT:
{document_text}

Return all packages as a JSON array. Start with [ and end with ]."""
            
            # Combine prompts for Responses API
            combined_prompt = f"{system_prompt}\n\n{user_prompt}"
            
            payload = {
                'model': self.model,
                'input': combined_prompt,
                'temperature': 0.1  # Low temperature for consistency
            }
            
            # ✅ SIMPLE & RELIABLE: Always use maximum timeout for data seeding
            # For one-time bulk upload, reliability > efficiency
            adaptive_timeout = PackageCreatorConfig.OPENAI_MAX_TIMEOUT
            
            self.logger.debug(f"Making OpenAI request (timeout: {adaptive_timeout}s, doc_size: {len(document_text)} chars)")
            self.logger.debug("Using fixed maximum timeout for reliable data seeding")
            
            # ✅ REMOVED: No more retry logic - single attempt only
            try:
                response = requests.post(self.api_url, headers=headers, json=payload, timeout=adaptive_timeout)
                
                if response.status_code != 200:
                    error_msg = f"OpenAI API request failed: {response.status_code} - {response.text}"
                    self.logger.error(error_msg)
                    # Log failed file for tracking
                    self._log_failed_file(file_path, f"API Error {response.status_code}")
                    raise Exception(error_msg)
                    
            except requests.exceptions.Timeout:
                error_msg = f"OpenAI API request timeout after {adaptive_timeout}s"
                self.logger.error(error_msg)
                # Log failed file for tracking
                self._log_failed_file(file_path, f"Timeout after {adaptive_timeout}s")
                raise Exception(error_msg)
            except requests.exceptions.RequestException as e:
                error_msg = f"OpenAI API request error: {str(e)}"
                self.logger.error(error_msg)
                # Log failed file for tracking
                self._log_failed_file(file_path, f"Request Error: {str(e)}")
                raise Exception(error_msg)
            
            # Parse response using Responses API structure
            response_data = response.json()
            
            # Extract content from Responses API structure
            content = self._extract_response_content(response_data)
            
            if not content:
                raise Exception("Empty response from OpenAI API")
            
            # Parse JSON response
            packages_data = self._parse_packages_response(content)
            
            self.logger.info(f"📦 EXTRACTION RESULT: Found {len(packages_data)} packages")
            for i, package in enumerate(packages_data):
                self.logger.debug(f"  Package {i+1}: {package.get('title', 'Unknown')} (No: {package.get('package_no', 'Unknown')})")
            
            return packages_data
            
        except Exception as e:
            self.logger.error(f"Error in simplified extraction: {str(e)}")
            raise
    
    def _get_simplified_extraction_prompt(self):
        """
        Get simplified system prompt focused on Package Name pattern detection
        """
        return """You are a travel package document processor. Your task is to find ALL travel packages in a document by locating "Package Name:" patterns.

🎯 CORE DETECTION RULE:
- Every line with "Package Name:" (case insensitive) starts a new travel package
- Extract complete information for each package found
- Continue reading until next "Package Name:" or end of document

⚠️ CRITICAL SCANNING INSTRUCTIONS:
1. Scan the ENTIRE document from beginning to absolute end
2. Do NOT stop after finding first few packages - keep scanning to the end
3. Look for EVERY "Package Name:" occurrence regardless of spacing or formatting
4. Each "Package Name:" = one complete package in your output
5. Extract all package data that follows each "Package Name:" until the next "Package Name:" or end of document

=== FIELD EXTRACTION RULES ===
Extract these fields for each package found:

**REQUIRED FIELDS:**
- title: Extract from "Package Name:" line
- package_no: Extract from "Package No:" or "Package No -" line (preserve exact format)
- destination: Extract from "Destination:" line (convert to lowercase)
- categories: Extract from "Package Category:" line (convert to lowercase array)
- type: Extract from "Type:" line ("Fixed" or "Variable")
- duration: Extract from "Duration:" line (format as "4N & 5D")
- price_per_person: ⚠️ HANDLE PRICE RANGES: If range found (e.g., "₹45,000 - ₹50,000"), use LOWER value only (e.g., "₹45,000")
- visa_type: Extract from "Visa Type:" line (as array)
- about_this_tour: Extract from "About This Tour" section
- highlights: Extract from "Highlights" section (as array) - ⚠️ ENSURE NOT EMPTY
- inclusions: Extract from "Inclusions" section (as array)
- exclusions: Extract from "Exclusions" section (as array)
- itinerary: Extract from "Itinerary" section (convert to HTML with <h3> and <ul><li>)

**OPTIONAL FIELDS (Extract if found):**
- owner: Extract from "Owner:" line (default to "ZUUMM" if missing)
- activities: Extract activities for M2M relationships (lowercase array)
- popular_activities: Extract activities for display (original case array)
- hotels: Extract from "Hotels:" section (as array)
- addons: Extract from "Add-Ons:" section (as array)
- best_time_to_visit: Extract from "Best Time to Visit:" section
- rating: Extract rating number
- rating_description: Extract rating description text
- destination_safety: Extract from "Safety:" section
- popular_restaurants: Extract from "Restaurants:" section (as array)
- cultural_info: Extract from "Cultural Info:" section
- what_to_shop: Extract from "Shopping:" section (as paragraph text)
- what_to_pack: Extract from "Packing:" section (convert to HTML)
- currency_conversion_rate: Extract currency information

**GENERATION RULES (Only if missing from document):**
- If owner missing: Set to "ZUUMM"
- If highlights empty: Generate based on inclusions and destination
- If activities missing: Generate based on destination
- If rating missing: Generate realistic 3.5-4.8 rating
- If rating_description missing: Generate based on highlights

**OUTPUT FORMAT:**
- Return valid JSON array starting with [ and ending with ]
- Each package as complete JSON object
- Proper string escaping for all text fields
- Convert specified fields to HTML format where required

⚠️ MOST IMPORTANT: Find EVERY "Package Name:" in the document - do not miss any packages!"""

    def _extract_response_content(self, response_data):
        """
        Extract content from OpenAI response with multiple fallback strategies
        """
        content = None
        try:
            # Main strategy: output[0].content[0].text
            output_array = response_data.get("output", [])
            if output_array and len(output_array) > 0:
                content_array = output_array[0].get("content", [])
                if content_array and len(content_array) > 0:
                    content = content_array[0].get("text")
                    if content:
                        self.logger.debug("Successfully extracted content using Responses API structure")
            
            # Fallback strategies
            if not content:
                # Try direct response field
                if 'response' in response_data:
                    content = response_data['response']
                    self.logger.debug("Extracted content using 'response' field")
                # Try direct output field (if it's a string)
                elif 'output' in response_data and isinstance(response_data['output'], str):
                    content = response_data['output']
                    self.logger.debug("Extracted content using direct 'output' field")
                # Try choices structure (fallback)
                elif 'choices' in response_data and response_data['choices']:
                    choice = response_data['choices'][0]
                    if 'message' in choice and 'content' in choice['message']:
                        content = choice['message']['content']
                        self.logger.debug("Extracted content using 'choices' structure")
                    elif 'text' in choice:
                        content = choice['text']
                        self.logger.debug("Extracted content using 'choices.text' structure")
            
            if not content:
                self.logger.error(f"Could not extract content from OpenAI response structure: {list(response_data.keys())}")
                raise Exception(f"OpenAI API returned unexpected response structure. Available keys: {list(response_data.keys())}")
            
            # Ensure content is a string
            if isinstance(content, list):
                if content:
                    content = str(content[0]) if len(content) == 1 else ' '.join(str(item) for item in content)
                else:
                    content = ""
            elif content is None:
                content = ""
            else:
                content = str(content)
            
            return content.strip()
            
        except (KeyError, IndexError, TypeError) as e:
            self.logger.error(f"Error parsing OpenAI response structure: {str(e)}")
            raise Exception(f"Failed to parse OpenAI response structure: {str(e)}")

    def _parse_packages_response(self, response_content):
        """Parse and validate the packages JSON response from OpenAI with robust error handling"""
        try:
            # Clean the response content
            cleaned_content = self._clean_json_response(response_content)
            
            # Try multiple parsing strategies in order of preference
            packages_data = None
            parse_attempts = [
                ("Direct JSON Parse", lambda: json.loads(cleaned_content)),
                ("Repaired JSON Parse", lambda: self._parse_repaired_json(cleaned_content)),
                ("Partial JSON Extract", lambda: self._extract_partial_json(cleaned_content)),
                ("Regex Package Extract", lambda: self._extract_packages_with_regex(cleaned_content)),
                ("Line-by-line Parse", lambda: self._parse_line_by_line(cleaned_content))
            ]
            
            for strategy_name, parse_func in parse_attempts:
                try:
                    self.logger.debug(f"Trying parsing strategy: {strategy_name}")
                    packages_data = parse_func()
                    if packages_data:
                        self.logger.info(f"Successfully parsed JSON using: {strategy_name}")
                        break
                except Exception as e:
                    self.logger.debug(f"{strategy_name} failed: {str(e)}")
                    continue
            
            if not packages_data:
                # Last resort: save the problematic response for debugging
                debug_file = f"failed_response_{int(time.time())}.txt"
                try:
                    with open(debug_file, 'w', encoding='utf-8') as f:
                        f.write(f"ORIGINAL RESPONSE:\n{response_content}\n\n")
                        f.write(f"CLEANED RESPONSE:\n{cleaned_content}\n")
                    self.logger.error(f"All parsing strategies failed. Response saved to: {debug_file}")
                except:
                    pass
                
                raise Exception(f"All JSON parsing strategies failed. Response length: {len(cleaned_content)}")
            
            # Ensure it's a list
            if not isinstance(packages_data, list):
                if isinstance(packages_data, dict):
                    # Single package returned as dict, convert to list
                    packages_data = [packages_data]
                else:
                    raise Exception("Response is not a valid packages array")
            
            # Validate each package
            validated_packages = []
            for i, package_data in enumerate(packages_data):
                try:
                    validated_package = self._validate_package_data(package_data, i + 1)
                    validated_packages.append(validated_package)
                except Exception as e:
                    self.logger.warning(f"Package {i + 1} validation failed: {e}")
                    continue
            
            if not validated_packages:
                raise Exception("No valid packages found in response")
            
            self.logger.debug(f"Successfully parsed {len(validated_packages)} packages")
            return validated_packages
            
        except Exception as e:
            self.logger.log_exception(e, "parsing packages response")
            raise

    def _parse_repaired_json(self, content):
        """Attempt to repair common JSON syntax errors and parse"""
        try:
            # Common repairs for malformed JSON
            repaired = content
            
            # Fix missing commas between objects/arrays
            repaired = re.sub(r'}\s*{', '},{', repaired)
            repaired = re.sub(r']\s*\[', '],[', repaired)
            repaired = re.sub(r'}\s*\[', '},[', repaired)
            repaired = re.sub(r']\s*{', '],{', repaired)
            
            # Fix missing commas between key-value pairs
            repaired = re.sub(r'"\s*\n\s*"', '",\n"', repaired)
            repaired = re.sub(r'",\s*\n\s*}', '"\n}', repaired)  # Remove trailing commas
            
            # Fix unescaped quotes in strings
            repaired = re.sub(r'(?<=: ")(.*?)(?="[,}\]])', lambda m: m.group(1).replace('"', '\\"'), repaired)
            
            # Fix trailing commas
            repaired = re.sub(r',(\s*[}\]])', r'\1', repaired)
            
            # Fix missing quotes around keys
            repaired = re.sub(r'([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:', r'\1"\2":', repaired)
            
            return json.loads(repaired)
            
        except Exception as e:
            self.logger.debug(f"JSON repair failed: {e}")
            raise

    def _extract_partial_json(self, content):
        """Extract valid JSON objects even if the overall structure is broken"""
        try:
            packages = []
            
            # Find individual package objects using bracket matching
            brace_count = 0
            start_pos = -1
            
            for i, char in enumerate(content):
                if char == '{':
                    if brace_count == 0:
                        start_pos = i
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                    if brace_count == 0 and start_pos != -1:
                        # Found a complete object
                        try:
                            obj_str = content[start_pos:i+1]
                            package_obj = json.loads(obj_str)
                            if isinstance(package_obj, dict) and 'title' in package_obj:
                                packages.append(package_obj)
                        except:
                            continue
                        start_pos = -1
            
            if not packages:
                raise Exception("No valid package objects found")
                
            return packages
            
        except Exception as e:
            self.logger.debug(f"Partial JSON extraction failed: {e}")
            raise

    def _extract_packages_with_regex(self, content):
        """Extract package data using regex patterns as last resort"""
        try:
            packages = []
            
            # Find package patterns in the text
            package_pattern = r'[\{"]?\s*title[\s"]*:[\s"]*([^",\n]+)[",]?.*?package_no[\s"]*:[\s"]*([^",\n]+)'
            matches = re.findall(package_pattern, content, re.IGNORECASE | re.DOTALL)
            
            for i, (title, package_no) in enumerate(matches):
                package = {
                    'title': title.strip().strip('"'),
                    'package_no': package_no.strip().strip('"'),
                    'destination': 'Unknown',
                    'type': 'General',
                    'duration': '2N/3D',
                    'price_per_person': 10000,
                    'currency': 'INR',
                    'about_this_tour': f'Package extracted from malformed response: {title}',
                    'highlights': ['Package data recovered from error'],
                    'inclusions': ['Basic inclusions'],
                    'categories': ['General'],
                    'activities': ['Sightseeing']
                }
                packages.append(package)
            
            if not packages:
                raise Exception("No packages found with regex extraction")
                
            self.logger.warning(f"Used regex fallback to extract {len(packages)} packages")
            return packages
            
        except Exception as e:
            self.logger.debug(f"Regex extraction failed: {e}")
            raise

    def _parse_line_by_line(self, content):
        """Parse JSON line by line to find valid structures"""
        try:
            lines = content.split('\n')
            packages = []
            current_package = {}
            in_package = False
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                    
                # Look for package start
                if '"title"' in line.lower() or '"package_no"' in line.lower():
                    if current_package:
                        packages.append(current_package)
                    current_package = {}
                    in_package = True
                
                # Extract key-value pairs
                if in_package and ':' in line:
                    try:
                        # Simple key-value extraction
                        key_match = re.search(r'"([^"]+)"\s*:', line)
                        value_match = re.search(r':\s*"([^"]*)"', line)
                        
                        if key_match and value_match:
                            key = key_match.group(1)
                            value = value_match.group(1)
                            current_package[key] = value
                    except:
                        continue
            
            # Add the last package
            if current_package:
                packages.append(current_package)
            
            # Set defaults for missing fields
            for package in packages:
                if 'title' not in package:
                    package['title'] = f'Recovered Package {len(packages)}'
                if 'package_no' not in package:
                    package['package_no'] = f'PKG{len(packages):03d}'
                # Add other required defaults...
                package.setdefault('destination', 'Unknown')
                package.setdefault('type', 'General')
                package.setdefault('duration', '2N/3D')
                package.setdefault('price_per_person', 10000)
                package.setdefault('currency', 'INR')
                package.setdefault('about_this_tour', 'Package data recovered from parsing error')
                package.setdefault('highlights', ['Data recovered from error'])
                package.setdefault('inclusions', ['Basic inclusions'])
                package.setdefault('categories', ['General'])
                package.setdefault('activities', ['Sightseeing'])
            
            if not packages:
                raise Exception("No packages found with line-by-line parsing")
                
            self.logger.warning(f"Used line-by-line fallback to extract {len(packages)} packages")
            return packages
            
        except Exception as e:
            self.logger.debug(f"Line-by-line parsing failed: {e}")
            raise
    
    def _clean_json_response(self, content):
        """Clean and prepare JSON response content"""
        try:
            # Remove markdown code blocks if present
            content = re.sub(r'^```json\s*', '', content, flags=re.MULTILINE)
            content = re.sub(r'^```\s*$', '', content, flags=re.MULTILINE)
            content = content.strip()
            
            # Remove any leading/trailing non-JSON text
            # Find the first [ or { and last ] or }
            start_idx = -1
            end_idx = -1
            
            for i, char in enumerate(content):
                if char in '[{':
                    start_idx = i
                    break
            
            for i in range(len(content) - 1, -1, -1):
                if content[i] in ']}':
                    end_idx = i + 1
                    break
            
            if start_idx != -1 and end_idx != -1:
                content = content[start_idx:end_idx]
            
            return content
            
        except Exception as e:
            self.logger.log_exception(e, "cleaning JSON response")
            return content
    
    def _validate_package_data(self, package_data, package_index):
        """Validate and standardize package data"""
        try:
            if not isinstance(package_data, dict):
                raise ValueError(f"Package {package_index} is not a valid dictionary")
            
            # ✅ PRE-PROCESS: Fix empty highlights BEFORE validation
            if 'highlights' in package_data and not package_data.get('highlights'):
                destination = package_data.get('destination', 'amazing destination')
                inclusions = package_data.get('inclusions', [])
                if inclusions and len(inclusions) >= 2:
                    package_data['highlights'] = inclusions[:3]  # Use first 3 inclusions
                else:
                    package_data['highlights'] = [
                        f"Explore the beauty of {destination}",
                        f"Experience authentic {destination} culture",
                        f"Comfortable accommodation and meals"
                    ]
                self.logger.debug(f"Generated highlights for package {package_index}: {package_data['highlights']}")
            
            # Check required fields
            required_fields = PackageCreatorConfig.REQUIRED_PACKAGE_FIELDS
            for field in required_fields:
                if field not in package_data or not package_data[field]:
                    # Only log error if we can't set a default
                    if field == 'package_no':
                        package_data[field] = f"PKG{package_index:03d}"
                    elif field == 'type':
                        package_data[field] = 'General'
                    elif field == 'currency':
                        package_data[field] = 'INR'
                    elif field in ['inclusions', 'exclusions', 'categories', 'activities']:
                        package_data[field] = []
                    elif field == 'highlights':
                        # This should already be handled above, but add safety net
                        if not package_data.get('highlights'):
                            package_data['highlights'] = [f"Experience {package_data.get('destination', 'this destination')}"]
                    else:
                        # Only log error for truly missing required fields that can't be defaulted
                        self.logger.log_validation_error(field, package_data.get(field), f"Required field missing for package {package_index}")
                        raise ValueError(f"Cannot set default for required field: {field}")
            
            # Validate and convert data types
            package_data = self._convert_package_data_types(package_data)
            
            # Validate specific field formats
            self._validate_package_fields(package_data)
            
            return package_data
            
        except Exception as e:
            self.logger.log_exception(e, f"validating package {package_index}")
            raise
    
    def _convert_package_data_types(self, package_data):
        """Convert and standardize data types"""
        try:
            # Convert price to float - handle various formats
            if 'price_per_person' in package_data:
                try:
                    price_str = str(package_data['price_per_person'])
                    
                    # ✅ MATCH ADMIN LOGIC: Add ₹ symbol if no currency symbol present
                    currency_symbol = re.sub(r'[\d\.,\s]', '', price_str).strip()
                    if not currency_symbol:
                        # No currency symbol found, add ₹ as default (matching admin behavior)
                        price_str = f"₹{price_str.strip()}"
                        self.logger.debug(f"Added default ₹ symbol: {package_data['price_per_person']} → {price_str}")
                        currency_symbol = '₹'
                    
                    # ✅ PRESERVE price_per_person as STRING with currency symbol (for display)
                    package_data['price_per_person'] = price_str
                    
                    # Extract numeric value for separate price field (for database calculations)
                    numeric_part = re.sub(r'[^\d\.]', '', price_str.replace(',', ''))
                    if numeric_part:
                        package_data['price'] = float(numeric_part)
                    else:
                        package_data['price'] = 0.0
                    
                    # Determine currency code from symbol (matching admin logic)
                    if '₹' in currency_symbol or 'INR' in price_str.upper():
                        package_data['currency'] = 'INR'
                    elif '$' in currency_symbol or 'USD' in price_str.upper():
                        package_data['currency'] = 'USD'
                    elif '€' in currency_symbol or 'EUR' in price_str.upper():
                        package_data['currency'] = 'EUR'
                    elif '£' in currency_symbol or 'GBP' in price_str.upper():
                        package_data['currency'] = 'GBP'
                    else:
                        package_data['currency'] = 'INR'  # Default
                    
                except (ValueError, TypeError):
                    package_data['price_per_person'] = "₹0"
                    package_data['price'] = 0.0
                    package_data['currency'] = 'INR'
            
            # Ensure arrays are lists
            array_fields = ['highlights', 'inclusions', 'exclusions', 'categories', 'activities']
            for field in array_fields:
                if field in package_data:
                    if isinstance(package_data[field], str):
                        # Split string by common delimiters
                        package_data[field] = [item.strip() for item in 
                                             re.split(r'[,;|•\n]', package_data[field]) 
                                             if item.strip()]
                    elif not isinstance(package_data[field], list):
                        package_data[field] = []
            
            # Validate dates
            date_fields = ['validity_from', 'validity_to']
            for field in date_fields:
                if field in package_data and package_data[field]:
                    try:
                        # Try to parse and reformat date
                        date_str = str(package_data[field])
                        # Simple date validation - you can enhance this
                        if not re.match(r'\d{4}-\d{2}-\d{2}', date_str):
                            package_data[field] = None
                    except:
                        package_data[field] = None
            
            return package_data
            
        except Exception as e:
            self.logger.log_exception(e, "converting package data types")
            raise
    
    def _validate_package_fields(self, package_data):
        """Validate specific package fields"""
        try:
            # Validate duration format - handle both "/" and "&" separators
            if 'duration' in package_data and package_data['duration']:
                duration = str(package_data['duration']).upper()
                
                # Try multiple duration patterns
                if not re.match(r'\d+N[/&]\d+D', duration):
                    # Try to extract nights and days with various patterns
                    nights_match = re.search(r'(\d+)\s*[N|NIGHT]', duration)
                    days_match = re.search(r'(\d+)\s*[D|DAY]', duration)
                    
                    if nights_match and days_match:
                        nights = nights_match.group(1)
                        days = days_match.group(1)
                        package_data['duration'] = f"{nights}N/{days}D"
                    else:
                        self.logger.log_validation_error('duration', duration, 'Invalid duration format')
                else:
                    # Convert "&" to "/" for consistency
                    package_data['duration'] = duration.replace('&', '/')
            
            # Validate currency
            if 'currency' in package_data and package_data['currency']:
                currency = str(package_data['currency']).upper()
                valid_currencies = ['INR', 'USD', 'EUR', 'GBP']
                if currency not in valid_currencies:
                    package_data['currency'] = 'INR'  # Default to INR
            
            # Validate group sizes
            for field in ['group_size_min', 'group_size_max']:
                if field in package_data and package_data[field]:
                    try:
                        package_data[field] = int(package_data[field])
                    except (ValueError, TypeError):
                        package_data[field] = None
            
        except Exception as e:
            self.logger.log_exception(e, "validating package fields")
            raise
    
    def generate_llm_fields(self, package_data):
        """Generate LLM-based fields for a package"""
        try:
            self.logger.debug(f"Generating LLM fields for package: {package_data.get('title', 'Unknown')}")
            
            # Prepare content for LLM field generation
            content_text = f"""
            Title: {package_data.get('title', '')}
            Destination: {package_data.get('destination', '')}
            About: {package_data.get('about_this_tour', '')}
            Highlights: {', '.join(package_data.get('highlights', []))}
            Inclusions: {', '.join(package_data.get('inclusions', []))}
            Activities: {', '.join(package_data.get('activities', []))}
            """
            
            # Generate SEO title
            seo_title = self._generate_seo_title(content_text, package_data)
            
            # Generate meta description
            meta_description = self._generate_meta_description(content_text, package_data)
            
            # Generate keywords
            keywords = self._generate_keywords(content_text, package_data)
            
            return {
                'seo_title': seo_title,
                'meta_description': meta_description,
                'keywords': keywords
            }
            
        except Exception as e:
            self.logger.log_exception(e, f"generating LLM fields for package {package_data.get('title', 'Unknown')}")
            return {
                'seo_title': package_data.get('title', ''),
                'meta_description': package_data.get('about_this_tour', '')[:160],
                'keywords': ', '.join(package_data.get('categories', []))
            }
    
    def _generate_seo_title(self, content_text, package_data):
        """Generate SEO-optimized title"""
        try:
            title = package_data.get('title', '')
            destination = package_data.get('destination', '')
            duration = package_data.get('duration', '')
            
            # Create SEO title
            seo_title = f"{title}"
            if destination and destination.lower() not in title.lower():
                seo_title += f" - {destination}"
            if duration:
                seo_title += f" {duration}"
            
            # Limit to 60 characters for SEO
            if len(seo_title) > 60:
                seo_title = seo_title[:57] + "..."
            
            return seo_title
            
        except Exception as e:
            self.logger.log_exception(e, "generating SEO title")
            return package_data.get('title', '')
    
    def _generate_meta_description(self, content_text, package_data):
        """Generate meta description"""
        try:
            about = package_data.get('about_this_tour', '')
            if about:
                # Limit to 160 characters for meta description
                meta_desc = about[:157] + "..." if len(about) > 160 else about
                return meta_desc
            
            # Fallback: create from highlights
            highlights = package_data.get('highlights', [])
            if highlights:
                meta_desc = f"Explore {package_data.get('destination', 'amazing destinations')} with {', '.join(highlights[:3])}"
                return meta_desc[:157] + "..." if len(meta_desc) > 160 else meta_desc
            
            return f"Discover {package_data.get('destination', 'amazing destinations')} with our {package_data.get('duration', '')} travel package"
            
        except Exception as e:
            self.logger.log_exception(e, "generating meta description")
            return package_data.get('about_this_tour', '')[:160]
    
    def _generate_keywords(self, content_text, package_data):
        """Generate keywords"""
        try:
            keywords = []
            
            # Add destination
            if package_data.get('destination'):
                keywords.append(package_data['destination'])
            
            # Add categories
            keywords.extend(package_data.get('categories', []))
            
            # Add type
            if package_data.get('type'):
                keywords.append(package_data['type'])
            
            # Add activities (first 3)
            activities = package_data.get('activities', [])[:3]
            keywords.extend(activities)
            
            # Remove duplicates and join
            unique_keywords = list(dict.fromkeys(keywords))  # Preserve order
            return ', '.join(unique_keywords)
            
        except Exception as e:
            self.logger.log_exception(e, "generating keywords")
            return ', '.join(package_data.get('categories', []))

    def _log_failed_file(self, file_path, error_reason):
        """Log failed files to a separate tracking file"""
        try:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            file_name = os.path.basename(file_path) if file_path else "Unknown File"
            
            log_entry = f"[{timestamp}] FAILED: {file_name} - Reason: {error_reason}\n"
            
            with open("failed_packages.log", 'a', encoding='utf-8') as f:
                f.write(log_entry)
                
            self.logger.warning(f"📝 Failed file logged: {file_name} - {error_reason}")
            
        except Exception as e:
            self.logger.debug(f"Could not write to failed files log: {e}")

    def log_skipped_package(self, file_path, package_name, reason):
        """Log individual packages that were skipped during processing"""
        try:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            file_name = os.path.basename(file_path) if file_path else "Unknown File"
            
            log_entry = f"[{timestamp}] SKIPPED PACKAGE: {file_name}.{package_name} - Reason: {reason}\n"
            
            with open("skipped_packages.log", 'a', encoding='utf-8') as f:
                f.write(log_entry)
                
            self.logger.warning(f"📝 Skipped package logged: {file_name}.{package_name} - {reason}")
            
        except Exception as e:
            self.logger.debug(f"Could not write to skipped packages log: {e}") 