"""
Logger utility for Package Creator Script
Provides comprehensive logging functionality with file and console output
"""

import logging
import sys
from datetime import datetime
from pathlib import Path
from .config import PackageCreatorConfig


class PackageCreatorLogger:
    """
    Custom logger class for package creation script
    Provides both file and console logging with different levels
    """
    
    def __init__(self, log_file_path=None, log_level=None):
        self.log_file_path = log_file_path or PackageCreatorConfig.LOG_FILE_PATH
        self.log_level = log_level or PackageCreatorConfig.LOG_LEVEL
        self.logger = None
        self._setup_logger()
    
    def _setup_logger(self):
        """Setup logger with file and console handlers"""
        # Create logger
        self.logger = logging.getLogger('PackageCreator')
        self.logger.setLevel(getattr(logging, self.log_level))
        
        # Remove existing handlers to avoid duplication
        self.logger.handlers.clear()
        
        # Create formatter
        formatter = logging.Formatter(PackageCreatorConfig.LOG_FORMAT)
        
        # Create file handler
        file_handler = logging.FileHandler(self.log_file_path)
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        
        # Create console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        
        # Add handlers to logger
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
    
    def info(self, message):
        """Log info message"""
        self.logger.info(message)
    
    def debug(self, message):
        """Log debug message"""
        self.logger.debug(message)
    
    def warning(self, message):
        """Log warning message"""
        self.logger.warning(message)
    
    def error(self, message):
        """Log error message"""
        self.logger.error(message)
    
    def critical(self, message):
        """Log critical message"""
        self.logger.critical(message)
    
    def log_script_start(self, documents_folder):
        """Log script initialization"""
        self.info("=" * 80)
        self.info("PACKAGE CREATOR SCRIPT STARTED")
        self.info("=" * 80)
        self.info(f"Start Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        self.info(f"Documents Folder: {documents_folder}")
        self.info(f"Log File: {self.log_file_path}")
        self.info("=" * 80)
    
    def log_script_end(self, total_files, total_packages, success_count, error_count):
        """Log script completion summary"""
        self.info("=" * 80)
        self.info("PACKAGE CREATOR SCRIPT COMPLETED")
        self.info("=" * 80)
        self.info(f"End Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        self.info(f"Total Files Processed: {total_files}")
        self.info(f"Total Packages Created: {total_packages}")
        self.info(f"Successful Operations: {success_count}")
        self.info(f"Failed Operations: {error_count}")
        self.info("=" * 80)
    
    def log_file_processing_start(self, file_path, file_index, total_files):
        """Log start of individual file processing"""
        self.info("-" * 60)
        self.info(f"PROCESSING FILE {file_index}/{total_files}")
        self.info(f"File: {file_path}")
        self.info("-" * 60)
    
    def log_file_processing_end(self, file_path, packages_count, success=True):
        """Log end of individual file processing"""
        status = "SUCCESS" if success else "FAILED"
        self.info(f"File Processing {status}: {file_path}")
        if success:
            self.info(f"Packages Created from File: {packages_count}")
        self.info("-" * 60)
    
    def log_package_creation_start(self, package_index, total_packages, package_title):
        """Log start of individual package creation"""
        self.info(f"Creating Package {package_index}/{total_packages}: {package_title}")
    
    def log_package_creation_end(self, package_title, package_id, success=True):
        """Log end of individual package creation"""
        if success:
            self.info(f"Package Created Successfully: {package_title} (ID: {package_id})")
        else:
            self.error(f"Package Creation Failed: {package_title}")
    
    def log_openai_request(self, file_path, request_type="package_extraction"):
        """Log OpenAI API request"""
        self.debug(f"Sending OpenAI Request - Type: {request_type}, File: {file_path}")
    
    def log_openai_response(self, packages_count, file_path):
        """Log OpenAI API response"""
        self.debug(f"OpenAI Response Received - Packages Found: {packages_count}, File: {file_path}")
    
    def log_entity_creation(self, entity_type, entity_name, created=True):
        """Log entity (destination/category/activity) creation or retrieval"""
        action = "Created" if created else "Retrieved Existing"
        self.debug(f"{entity_type} {action}: {entity_name}")
    
    def log_relationship_creation(self, relationship_type, source, target):
        """Log M2M relationship creation"""
        self.debug(f"Relationship Created - {relationship_type}: {source} -> {target}")
    
    def log_validation_error(self, field_name, value, error_message):
        """Log validation errors"""
        self.warning(f"Validation Error - Field: {field_name}, Value: {value}, Error: {error_message}")
    
    def log_transaction_start(self):
        """Log start of database transaction"""
        self.info("Starting Database Transaction")
    
    def log_transaction_commit(self):
        """Log successful transaction commit"""
        self.info("Database Transaction Committed Successfully")
    
    def log_transaction_rollback(self, error_message):
        """Log transaction rollback"""
        self.error(f"Database Transaction Rolled Back - Error: {error_message}")
    
    def log_exception(self, exception, context=""):
        """Log exception with context"""
        self.error(f"Exception Occurred {context}: {str(exception)}")
        self.debug(f"Exception Details: {repr(exception)}") 