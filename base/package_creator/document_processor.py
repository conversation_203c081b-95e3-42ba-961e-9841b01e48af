"""
Document Processor for Package Creator Scrip<PERSON>
Handles reading and extracting text from various document formats
"""

import os
from pathlib import Path
from docx import Document
import zipfile
from .config import PackageCreatorConfig
from .logger import PackageCreatorLogger


class DocumentProcessor:
    """
    Document processor class for extracting text from document files
    Supports .docx and .doc formats
    """
    
    def __init__(self, logger=None):
        self.logger = logger or PackageCreatorLogger()
        self.supported_extensions = PackageCreatorConfig.SUPPORTED_EXTENSIONS
        self.max_file_size = PackageCreatorConfig.MAX_FILE_SIZE
    
    def is_supported_file(self, file_path):
        """Check if file is supported for processing"""
        try:
            file_path = Path(file_path)
            
            # Check if file exists
            if not file_path.exists():
                self.logger.error(f"File does not exist: {file_path}")
                return False
            
            # Check file extension
            if file_path.suffix.lower() not in self.supported_extensions:
                self.logger.warning(f"Unsupported file extension: {file_path.suffix}")
                return False
            
            # Check file size
            file_size = file_path.stat().st_size
            if file_size > self.max_file_size:
                self.logger.error(f"File size too large: {file_size} bytes (max: {self.max_file_size})")
                return False
            
            # Check if file is not empty
            if file_size == 0:
                self.logger.error(f"File is empty: {file_path}")
                return False
            
            self.logger.debug(f"File validation passed: {file_path}")
            return True
            
        except Exception as e:
            self.logger.log_exception(e, f"validating file {file_path}")
            return False
    
    def extract_text_from_docx(self, file_path):
        """Extract text from .docx file"""
        try:
            self.logger.debug(f"Extracting text from DOCX: {file_path}")
            
            # Try to open as DOCX file
            doc = Document(file_path)
            text_content = []
            
            # Extract text from paragraphs
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_content.append(paragraph.text.strip())
            
            # Extract text from tables
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        if cell.text.strip():
                            text_content.append(cell.text.strip())
            
            # Join all text content
            full_text = '\n'.join(text_content)
            
            if not full_text.strip():
                raise ValueError("No readable text content found in document")
            
            self.logger.debug(f"Successfully extracted {len(full_text)} characters from DOCX")
            return full_text
            
        except Exception as e:
            self.logger.log_exception(e, f"extracting text from DOCX {file_path}")
            raise
    
    def extract_text_from_doc(self, file_path):
        """Extract text from .doc file (legacy format)"""
        try:
            self.logger.debug(f"Extracting text from DOC: {file_path}")
            
            # For .doc files, we'll try to open with python-docx first
            # If that fails, we'll need alternative methods
            try:
                # Try with python-docx (sometimes works with .doc files)
                return self.extract_text_from_docx(file_path)
            except Exception:
                # If python-docx fails, try alternative approach
                self.logger.warning(f"Failed to read .doc file with python-docx: {file_path}")
                
                # Alternative: Try reading as text (may not work perfectly)
                try:
                    with open(file_path, 'rb') as f:
                        content = f.read()
                        # Simple text extraction (not perfect for .doc files)
                        text = content.decode('utf-8', errors='ignore')
                        
                        # Clean up the text
                        lines = []
                        for line in text.split('\n'):
                            clean_line = ''.join(char for char in line if char.isprintable())
                            if clean_line.strip():
                                lines.append(clean_line.strip())
                        
                        full_text = '\n'.join(lines)
                        
                        if not full_text.strip():
                            raise ValueError("No readable text content found in document")
                        
                        self.logger.warning(f"Used fallback text extraction for .doc file: {file_path}")
                        return full_text
                        
                except Exception as fallback_error:
                    self.logger.log_exception(fallback_error, f"fallback text extraction from {file_path}")
                    raise ValueError(f"Unable to extract text from .doc file: {file_path}")
            
        except Exception as e:
            self.logger.log_exception(e, f"extracting text from DOC {file_path}")
            raise
    
    def extract_text_from_file(self, file_path):
        """Main method to extract text from any supported file format"""
        try:
            file_path = Path(file_path)
            
            # Validate file
            if not self.is_supported_file(file_path):
                raise ValueError(f"File is not supported or invalid: {file_path}")
            
            self.logger.debug(f"Starting text extraction from: {file_path}")
            
            # Extract text based on file extension
            if file_path.suffix.lower() == '.docx':
                text_content = self.extract_text_from_docx(file_path)
            elif file_path.suffix.lower() == '.doc':
                text_content = self.extract_text_from_doc(file_path)
            else:
                raise ValueError(f"Unsupported file extension: {file_path.suffix}")
            
            # Validate extracted content
            if not text_content or not text_content.strip():
                raise ValueError("No text content extracted from file")
            
            # Clean and normalize text
            cleaned_text = self._clean_text(text_content)
            
            self.logger.debug(f"Text extraction completed. Content length: {len(cleaned_text)} characters")
            return cleaned_text
            
        except Exception as e:
            self.logger.log_exception(e, f"extracting text from file {file_path}")
            raise
    
    def _clean_text(self, text):
        """Clean and normalize extracted text"""
        try:
            # Remove excessive whitespace
            lines = []
            for line in text.split('\n'):
                cleaned_line = ' '.join(line.split())
                if cleaned_line:
                    lines.append(cleaned_line)
            
            # Join lines with single newline
            cleaned_text = '\n'.join(lines)
            
            # Remove any non-printable characters except newlines and tabs
            cleaned_text = ''.join(char for char in cleaned_text 
                                 if char.isprintable() or char in '\n\t')
            
            return cleaned_text
            
        except Exception as e:
            self.logger.log_exception(e, "cleaning extracted text")
            return text  # Return original text if cleaning fails
    
    def get_files_from_folder(self, folder_path):
        """Get all supported document files from a folder"""
        try:
            folder_path = Path(folder_path)
            
            if not folder_path.exists():
                raise ValueError(f"Folder does not exist: {folder_path}")
            
            if not folder_path.is_dir():
                raise ValueError(f"Path is not a directory: {folder_path}")
            
            self.logger.info(f"Scanning folder for document files: {folder_path}")
            
            # Find all supported files
            supported_files = []
            for ext in self.supported_extensions:
                pattern = f"*{ext}"
                files = list(folder_path.glob(pattern))
                supported_files.extend(files)
            
            # Filter and validate files
            valid_files = []
            for file_path in supported_files:
                if self.is_supported_file(file_path):
                    valid_files.append(file_path)
            
            # Sort files for consistent processing order
            valid_files.sort()
            
            self.logger.info(f"Found {len(valid_files)} valid document files")
            for i, file_path in enumerate(valid_files, 1):
                self.logger.debug(f"File {i}: {file_path.name}")
            
            return valid_files
            
        except Exception as e:
            self.logger.log_exception(e, f"scanning folder {folder_path}")
            raise 