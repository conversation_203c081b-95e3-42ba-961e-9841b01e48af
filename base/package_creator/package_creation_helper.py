"""
Package Creation Helper for Package Creator Script
Handles all database operations for creating packages and related entities
"""

import re
import random
from decimal import Decimal, InvalidOperation
from datetime import datetime
from django.db import transaction
from django.utils.text import slugify

# Import models
from packages.models import (
    Package, Destination, Category, Activity, 
    DestinationCategory, DestinationActivity,
    PackageCategory, PackageActivity,
    PackageAddon, PackageInclusion, PackageHighlight
)
from accounts.models import Partner
from accounts.choices import PartnerTypeChoices

from .config import PackageCreatorConfig
from .logger import PackageCreatorLogger
from .openai_helper import OpenAIHelper


class PackageCreationHelper:
    """
    Helper class for creating packages and managing related entities
    Handles all database operations with proper relationships
    """
    
    def __init__(self, logger=None):
        self.logger = logger or PackageCreatorLogger()
        self.openai_helper = OpenAIHelper(logger)
        self.icon_classes = PackageCreatorConfig.ICON_CLASSES
        self.icon_descriptions = PackageCreatorConfig.ICON_DESCRIPTIONS
        self.zuumm_partner = self._get_zuumm_partner()
        
        # ✅ ADD: Performance caching for database entities
        self._destination_cache = {}  # Cache for destinations
        self._category_cache = {}     # Cache for categories
        self._activity_cache = {}     # Cache for activities
    
    def _get_zuumm_partner(self):
        """
        Get the Zuumm Partner instance
        All created objects will be assigned to this partner
        """
        try:
            partner = Partner.objects.filter(partner_type=PartnerTypeChoices.ZUUMM.value).first()
            if not partner:
                raise ValueError("Zuumm Partner not found. Please ensure a Partner with partner_type='ZUUMM' exists.")
            self.logger.debug(f"Using Zuumm Partner: {partner.entity_name} (ID: {partner.id})")
            return partner
        except Exception as e:
            self.logger.log_exception(e, "getting Zuumm Partner")
            raise
    
    def create_package_from_data(self, package_data):
        """
        Create a complete package with all relationships from package data
        Returns the created Package instance
        """
        try:
            package_title = package_data.get('title', 'Unknown Package')
            self.logger.debug(f"Starting package creation: {package_title}")
            
            # ⚠️ DEBUG: Log both activity fields received from OpenAI
            activities_m2m = package_data.get('activities', [])
            popular_activities_array = package_data.get('popular_activities', [])
            owner_field = package_data.get('owner', '').strip()
            
            self.logger.debug(f"🔍 PACKAGE DATA DEBUG for '{package_title}':")
            self.logger.debug(f"  - Owner: '{owner_field}' (empty: {not owner_field})")
            self.logger.debug(f"  - Activities (M2M): {activities_m2m}")
            self.logger.debug(f"  - Popular Activities (Array): {popular_activities_array}")
            self.logger.debug(f"  - Best Time to Visit: '{package_data.get('best_time_to_visit', '')}'")
            self.logger.debug(f"  - Destination Safety: '{package_data.get('destination_safety', '')}'")
            self.logger.debug(f"  - Hotels: {package_data.get('hotels', [])}")
            self.logger.debug(f"  - Popular Restaurants: {package_data.get('popular_restaurants', [])}")
            self.logger.debug(f"  - Cultural Info: '{package_data.get('cultural_info', '')}'")
            
            # Create or get destination
            destination = self._create_or_get_destination(package_data)
            
            # Create categories and activities
            categories = self._create_or_get_categories(package_data)
            activities = self._create_or_get_activities(package_data)  # This will handle M2M relationships
            
            # Generate auto-calculated fields
            auto_fields = self._generate_auto_calculated_fields(package_data)
            
            # Generate LLM fields
            llm_fields = self.openai_helper.generate_llm_fields(package_data)
            
            # Create the main package
            package = self._create_package_instance(package_data, destination, auto_fields, llm_fields)
            
            # Create M2M relationships
            self._create_package_relationships(package, destination, categories, activities)
            
            # Create package inclusions, highlights, and addons
            self._create_package_details(package, package_data)
            
            self.logger.debug(f"✅ Package created successfully: {package.title} (ID: {package.id})")
            self.logger.debug(f"  - Final Owner: '{package.owner}'")
            self.logger.debug(f"  - Final Popular Activities Array: {package.popular_activities}")
            self.logger.debug(f"  - M2M Activities Count: {package.activities.count()}")
            return package
            
        except Exception as e:
            self.logger.log_exception(e, f"creating package from data: {package_data.get('title', 'Unknown')}")
            raise
    
    def _create_or_get_destination(self, package_data):
        """Create or retrieve destination with caching"""
        try:
            destination_name = package_data.get('destination', '').strip()
            if not destination_name:
                raise ValueError("Destination name is required")
            
            # ✅ CHECK CACHE FIRST
            cache_key = f"{destination_name.lower()}_{self.zuumm_partner.id}"
            if cache_key in self._destination_cache:
                self.logger.debug(f"Using cached destination: {destination_name}")
                return self._destination_cache[cache_key]
            
            # Check if destination exists for the Zuumm partner
            destination, created = Destination.objects.get_or_create(
                title__iexact=destination_name,
                partner=self.zuumm_partner,
                defaults={
                    'title': destination_name.lower(),  # Store in lowercase as per pattern
                    'partner': self.zuumm_partner,
                    'description': f'Destination for {destination_name}',
                    'is_active': PackageCreatorConfig.DEFAULT_ENTITY_STATUS['is_active']
                }
            )
            
            # ✅ CACHE THE RESULT
            self._destination_cache[cache_key] = destination
            
            self.logger.log_entity_creation('Destination', destination_name, created)
            return destination
            
        except Exception as e:
            self.logger.log_exception(e, f"creating/getting destination: {package_data.get('destination')}")
            raise
    
    def _create_or_get_categories(self, package_data):
        """Create or retrieve categories with caching"""
        try:
            categories_data = package_data.get('categories', [])
            if isinstance(categories_data, str):
                categories_data = [cat.strip() for cat in categories_data.split(',') if cat.strip()]
            
            categories = []
            for category_name in categories_data:
                if not category_name.strip():
                    continue
                
                # ✅ CHECK CACHE FIRST
                cache_key = f"{category_name.strip().lower()}_{self.zuumm_partner.id}"
                if cache_key in self._category_cache:
                    self.logger.debug(f"Using cached category: {category_name}")
                    categories.append(self._category_cache[cache_key])
                    continue
                
                category, created = Category.objects.get_or_create(
                    title__iexact=category_name.strip(),
                    partner=self.zuumm_partner,
                    defaults={
                        'title': category_name.strip().lower(),  # Store in lowercase as per pattern
                        'partner': self.zuumm_partner,
                        'description': f'Category for {category_name.strip()}',
                        'is_active': PackageCreatorConfig.DEFAULT_ENTITY_STATUS['is_active']
                    }
                )
                
                # ✅ CACHE THE RESULT
                self._category_cache[cache_key] = category
                categories.append(category)
                self.logger.log_entity_creation('Category', category_name, created)
            
            return categories
            
        except Exception as e:
            self.logger.log_exception(e, f"creating/getting categories: {package_data.get('categories')}")
            raise
    
    def _create_or_get_activities(self, package_data):
        """Create or retrieve activities for M2M relationships with caching"""
        try:
            # ⚠️ CRITICAL: Use 'activities' field for M2M relationships, not 'popular_activities'
            activities_data = package_data.get('activities', [])
            
            # Fallback to popular_activities if activities field is missing (but convert to lowercase)
            if not activities_data:
                popular_activities = package_data.get('popular_activities', [])
                if popular_activities:
                    activities_data = [act.lower().strip() for act in popular_activities]
                    self.logger.debug(f"Using popular_activities as fallback for M2M activities: {activities_data}")
            
            if isinstance(activities_data, str):
                activities_data = [act.strip() for act in activities_data.split(',') if act.strip()]
            
            self.logger.debug(f"Processing activities for M2M relationships: {activities_data}")
            
            activities = []
            for activity_name in activities_data:
                if not activity_name.strip():
                    continue
                
                # Ensure lowercase for consistency
                activity_name_clean = activity_name.strip().lower()
                
                # ✅ CHECK CACHE FIRST
                cache_key = f"{activity_name_clean}_{self.zuumm_partner.id}"
                if cache_key in self._activity_cache:
                    self.logger.debug(f"Using cached activity: {activity_name_clean}")
                    activities.append(self._activity_cache[cache_key])
                    continue
                
                activity, created = Activity.objects.get_or_create(
                    title__iexact=activity_name_clean,
                    partner=self.zuumm_partner,
                    defaults={
                        'title': activity_name_clean,
                        'partner': self.zuumm_partner,
                        'description': f'Activity for {activity_name_clean}',
                        'is_active': PackageCreatorConfig.DEFAULT_ENTITY_STATUS['is_active']
                    }
                )
                
                # ✅ CACHE THE RESULT
                self._activity_cache[cache_key] = activity
                activities.append(activity)
                self.logger.log_entity_creation('Activity', activity_name_clean, created)
            
            self.logger.debug(f"Total activities prepared for M2M: {len(activities)}")
            return activities
            
        except Exception as e:
            self.logger.log_exception(e, f"creating/getting activities: {package_data.get('activities')}")
            raise
    
    def _create_package_instance(self, package_data, destination, auto_fields, llm_fields):
        """Create the Package instance with proper field handling"""
        try:
            # Parse duration to get nights and days
            duration_info = self._parse_duration(package_data.get('duration', ''))
            nights = duration_info.get('nights', 0)
            days = duration_info.get('days', 0)
            
            # Auto-calculate best_time_to_visit_months from best_time_to_visit text
            best_time_months = self._extract_months_from_text(package_data.get('best_time_to_visit', ''))
            if not best_time_months and package_data.get('best_time_to_visit_months'):
                # Use provided months if auto-extraction failed
                best_time_months = package_data.get('best_time_to_visit_months', [])
            
            # Ensure important_notes is provided (required field)
            important_notes = package_data.get('important_notes', [])
            if not important_notes:
                # Generate basic important notes if missing
                important_notes = self._generate_basic_important_notes(package_data)
            
            # ⚠️ CRITICAL: Handle owner field properly
            owner = package_data.get('owner', '').strip()
            if not owner:
                owner = "ZUUMM"  # Default fallback
                self.logger.warning(f"Owner field missing for package {package_data.get('title', 'Unknown')}, defaulting to 'ZUUMM'")
            
            # ⚠️ CRITICAL: Handle popular_activities array field (separate from M2M activities)
            popular_activities_list = package_data.get('popular_activities', [])
            if not popular_activities_list:
                # Fallback to activities field if popular_activities is missing
                activities_from_m2m = package_data.get('activities', [])
                if activities_from_m2m:
                    # Convert to proper case for display
                    popular_activities_list = [act.title() for act in activities_from_m2m]
            
            # Create Package instance
            package = Package.objects.create(
                # Basic required fields
                title=package_data.get('title', ''),
                package_no=package_data.get('package_no', ''),
                destination=destination,
                partner=self._get_zuumm_partner(),
                type=package_data.get('type', 'General'),
                owner=owner,  # ⚠️ CRITICAL: Set owner field
                
                # Duration fields
                duration_in_nights=nights,
                duration_in_days=days,
                duration=package_data.get('duration', ''),
                
                # Pricing fields
                price_per_person=str(package_data.get('price_per_person', '₹0')),  # String with currency symbol for display
                price=Decimal(str(package_data.get('price', 0))),  # Numeric value for database calculations
                currency=package_data.get('currency', 'INR'),
                
                # Content fields
                about_this_tour=package_data.get('about_this_tour', ''),
                
                # Status fields (as per requirements)
                is_published=False,  # As per requirements
                is_active=True,      # As per requirements
                
                # Optional fields with proper type handling
                visa_type=self._ensure_list(package_data.get('visa_type', [])),
                best_time_to_visit=package_data.get('best_time_to_visit', ''),
                best_time_to_visit_months=self._ensure_list(best_time_months),
                rating=self._parse_rating(package_data.get('rating')),
                rating_description=package_data.get('rating_description', ''),
                currency_conversion_rate=package_data.get('currency_conversion_rate', ''),
                destination_safety=package_data.get('destination_safety', ''),
                popular_restaurants=self._ensure_list(package_data.get('popular_restaurants', [])),
                cultural_info=package_data.get('cultural_info', ''),
                
                # ⚠️ CRITICAL FIELD FIXES:
                what_to_shop=package_data.get('what_to_shop', ''),  # Paragraph text, not array
                what_to_pack=package_data.get('what_to_pack', ''),  # HTML content, not array  
                itinerary=package_data.get('itinerary', ''),  # HTML content, not JSON array
                
                hotels=self._ensure_list(package_data.get('hotels', [])),
                popular_activities=self._ensure_list(popular_activities_list),  # ⚠️ CRITICAL: Array field for activities
                important_notes=self._ensure_list(important_notes),  # Required field as array
                exclusions=self._ensure_list(package_data.get('exclusions', [])),
                explore_order=0,
                
                # No PackageUploader FK as per requirements
                package_uploaded=None
            )
            
            self.logger.debug(f"Package instance created: {package.title} (ID: {package.id})")
            self.logger.debug(f"  - Owner: {package.owner}")
            self.logger.debug(f"  - Popular Activities: {package.popular_activities}")
            return package
            
        except Exception as e:
            self.logger.log_exception(e, f"creating package instance: {package_data.get('title', 'Unknown')}")
            raise
    
    def _create_package_relationships(self, package, destination, categories, activities):
        """Create all M2M relationships for the package"""
        try:
            self.logger.debug(f"Creating relationships for package: {package.title}")
            self.logger.debug(f"  - Categories count: {len(categories)}")
            self.logger.debug(f"  - Activities count: {len(activities)}")
            
            # Link categories to destination and package
            for category in categories:
                # Destination-Category relationship
                dest_cat, created = DestinationCategory.objects.get_or_create(
                    destination=destination,
                    category=category
                )
                if created:
                    self.logger.log_relationship_creation('DestinationCategory', destination.title, category.title)
                
                # Package-Category relationship
                pack_cat, created = PackageCategory.objects.get_or_create(
                    package=package,
                    category=category
                )
                if created:
                    self.logger.log_relationship_creation('PackageCategory', package.title, category.title)
            
            # Link activities to destination and package
            activity_count = 0
            for activity in activities:
                # Destination-Activity relationship
                dest_act, created = DestinationActivity.objects.get_or_create(
                    destination=destination,
                    activity=activity
                )
                if created:
                    self.logger.log_relationship_creation('DestinationActivity', destination.title, activity.title)
                
                # Package-Activity relationship
                pack_act, created = PackageActivity.objects.get_or_create(
                    package=package,
                    activity=activity
                )
                if created:
                    self.logger.log_relationship_creation('PackageActivity', package.title, activity.title)
                    activity_count += 1
                else:
                    self.logger.debug(f"PackageActivity relationship already exists: {package.title} - {activity.title}")
                    activity_count += 1
            
            self.logger.debug(f"Successfully created {activity_count} activity relationships for package: {package.title}")
            
        except Exception as e:
            self.logger.log_exception(e, f"creating package relationships for: {package.title}")
            raise
    
    def _create_package_details(self, package, package_data):
        """Create package inclusions, highlights, and addons with bulk operations"""
        try:
            # ✅ BULK CREATE: Prepare lists for bulk creation
            highlights_to_create = []
            inclusions_to_create = []
            addons_to_create = []
            
            # Prepare Package Highlights
            highlights = package_data.get('highlights', [])
            if isinstance(highlights, str):
                highlights = [h.strip() for h in highlights.split(',') if h.strip()]
            
            for highlight_text in highlights[:10]:  # Limit to 10 highlights
                if highlight_text.strip():
                    highlights_to_create.append(
                        PackageHighlight(
                            package=package,
                            value=highlight_text.strip(),
                            icon_class=self._get_intelligent_icon_class(highlight_text, 'highlight')
                        )
                    )
            
            # Prepare Package Inclusions
            inclusions = package_data.get('inclusions', [])
            if isinstance(inclusions, str):
                inclusions = [inc.strip() for inc in inclusions.split(',') if inc.strip()]
            
            for inclusion_text in inclusions[:15]:  # Limit to 15 inclusions
                if inclusion_text.strip():
                    inclusions_to_create.append(
                        PackageInclusion(
                            package=package,
                            value=inclusion_text.strip(),
                            icon_class=self._get_intelligent_icon_class(inclusion_text, 'inclusion')
                        )
                    )
            
            # Prepare Package Addons (from exclusions or special services)
            exclusions = package_data.get('exclusions', [])
            if isinstance(exclusions, str):
                exclusions = [exc.strip() for exc in exclusions.split(',') if exc.strip()]
            
            # Convert some exclusions to addons (optional services)
            addon_keywords = ['visa', 'insurance', 'flight', 'tips', 'personal', 'optional']
            for exclusion_text in exclusions[:10]:
                if exclusion_text.strip() and any(keyword in exclusion_text.lower() for keyword in addon_keywords):
                    addons_to_create.append(
                        PackageAddon(
                            package=package,
                            value=exclusion_text.strip(),
                            icon_class=self._get_intelligent_icon_class(exclusion_text, 'addon')
                        )
                    )
            
            # ✅ BULK CREATE: Execute bulk operations for better performance
            if highlights_to_create:
                PackageHighlight.objects.bulk_create(highlights_to_create, batch_size=100)
                self.logger.debug(f"Bulk created {len(highlights_to_create)} highlights for package: {package.title}")
            
            if inclusions_to_create:
                PackageInclusion.objects.bulk_create(inclusions_to_create, batch_size=100)
                self.logger.debug(f"Bulk created {len(inclusions_to_create)} inclusions for package: {package.title}")
            
            if addons_to_create:
                PackageAddon.objects.bulk_create(addons_to_create, batch_size=100)
                self.logger.debug(f"Bulk created {len(addons_to_create)} addons for package: {package.title}")
            
            self.logger.debug(f"Package details created efficiently for: {package.title}")
            
        except Exception as e:
            self.logger.log_exception(e, f"creating package details for: {package.title}")
            raise
    
    def _parse_rating(self, rating_value):
        """Parse rating value to decimal or None"""
        try:
            if rating_value is None or rating_value == '':
                return None
            
            # Convert to string first, then extract numeric part
            rating_str = str(rating_value)
            
            # Handle formats like "4.5/5" or "4.5"
            if '/' in rating_str:
                rating_str = rating_str.split('/')[0]
            
            # Convert to decimal
            rating_decimal = Decimal(rating_str)
            
            # Ensure it's within valid range (0-5)
            if 0 <= rating_decimal <= 5:
                return rating_decimal
            else:
                return None
                
        except (ValueError, TypeError, InvalidOperation):
            return None
    
    def _generate_auto_calculated_fields(self, package_data):
        """Generate auto-calculated fields for package"""
        try:
            # Since Package model doesn't have slug or icon_class fields, 
            # we'll return empty dict but keep the method for future use
            return {}
            
        except Exception as e:
            self.logger.log_exception(e, "generating auto-calculated fields")
            return {}
    
    def _get_intelligent_icon_class(self, text, entity_type):
        """Intelligently select icon class based on text content"""
        try:
            text_lower = text.lower()
            
            # Score each icon class
            icon_scores = {}
            for icon_class, description in self.icon_descriptions.items():
                score = 0
                description_words = description.lower().split()
                
                for word in description_words:
                    if word in text_lower:
                        score += 1
                
                # Additional scoring for exact matches
                if icon_class in text_lower:
                    score += 3
                
                icon_scores[icon_class] = score
            
            # Get the highest scoring icon
            best_icon = max(icon_scores, key=icon_scores.get) if icon_scores else 'activity'
            
            # If no good match found, use defaults based on entity type
            if icon_scores[best_icon] == 0:
                defaults = {
                    'destination': 'island',
                    'category': 'activity',
                    'activity': 'activity',
                    'package': 'sunrise',
                    'highlight': 'sunrise',
                    'inclusion': 'breakfast',
                    'addon': 'passenger'
                }
                best_icon = defaults.get(entity_type, 'activity')
            
            return best_icon
            
        except Exception as e:
            self.logger.log_exception(e, f"selecting icon class for: {text}")
            return 'activity'
    
    def _parse_duration(self, duration_str):
        """Parse duration string to extract nights and days"""
        try:
            if not duration_str:
                return {'nights': 0, 'days': 0}
            
            # Initialize defaults
            nights = 0
            days = 0
            
            duration_str = str(duration_str).upper()
            
            # Handle various formats like "4N & 5D", "4N/5D", "4 nights 5 days"
            import re
            
            # Extract nights
            night_match = re.search(r'(\d+)\s*[NnW]', duration_str)  # N for nights, W for weeks
            if night_match:
                nights = int(night_match.group(1))
            
            # Extract days  
            day_match = re.search(r'(\d+)\s*[Dd]', duration_str)
            if day_match:
                days = int(day_match.group(1))
            
            # If only nights found, calculate days as nights + 1
            if nights > 0 and days == 0:
                days = nights + 1
            
            # If only days found, calculate nights as days - 1
            if days > 0 and nights == 0:
                nights = max(0, days - 1)
            
            return {'nights': nights, 'days': days}
            
        except Exception as e:
            self.logger.debug(f"Error parsing duration '{duration_str}': {e}")
            return {'nights': 0, 'days': 0}
    
    def _parse_date(self, date_str):
        """Parse date string and return date object"""
        try:
            if not date_str:
                return None
            
            date_str = str(date_str).strip()
            
            # Try different date formats
            date_formats = [
                '%Y-%m-%d',
                '%d-%m-%Y',
                '%d/%m/%Y',
                '%Y/%m/%d',
                '%d.%m.%Y',
                '%Y.%m.%d'
            ]
            
            for fmt in date_formats:
                try:
                    return datetime.strptime(date_str, fmt).date()
                except ValueError:
                    continue
            
            return None
            
        except Exception as e:
            self.logger.log_exception(e, f"parsing date: {date_str}")
            return None
    
    def validate_package_data(self, package_data):
        """Validate package data before creation"""
        try:
            errors = []
            
            # Check required fields
            required_fields = PackageCreatorConfig.REQUIRED_PACKAGE_FIELDS
            for field in required_fields:
                if field not in package_data or not package_data[field]:
                    errors.append(f"Missing required field: {field}")
            
            # Validate price
            try:
                # ✅ PRICE SANITIZATION: Handle price ranges and formats
                price_per_person = package_data.get('price_per_person', '')
                sanitized_price = self._sanitize_price_field(price_per_person)
                package_data['price'] = sanitized_price  # Set numeric price field
                
                # ✅ DEBUG: Log price data for debugging
                self.logger.debug(f"Price validation for {package_data.get('title', 'Unknown')}:")
                self.logger.debug(f"  - Original price_per_person: '{price_per_person}'")
                self.logger.debug(f"  - Sanitized price: {sanitized_price}")
                
                if sanitized_price < 0:
                    errors.append("Price cannot be negative")
                if sanitized_price > 99999999.99:  # Database field limit
                    errors.append(f"Price too large: {sanitized_price}. Maximum allowed: 99,999,999.99")
                    
            except (ValueError, TypeError) as e:
                self.logger.debug(f"Price validation error: {e}")
                errors.append("Invalid price format")
            
            # Validate duration
            duration = package_data.get('duration', '')
            if duration:
                duration_info = self._parse_duration(duration)
                if duration_info['nights'] == 0 and duration_info['days'] == 0:
                    errors.append("Invalid duration format")
            
            if errors:
                error_msg = '; '.join(errors)
                self.logger.error(f"Package validation failed: {error_msg}")
                raise ValueError(error_msg)
            
            return True
            
        except Exception as e:
            self.logger.log_exception(e, "validating package data")
            raise
    
    def _ensure_list(self, value):
        """Ensure the value is always a list, handling None values"""
        if value is None:
            return []
        elif isinstance(value, list):
            return value
        elif isinstance(value, (str, tuple, set)):
            return list(value) if not isinstance(value, str) else [value]
        else:
            return []
    
    def _extract_months_from_text(self, text):
        """Extract month names from text and return as array"""
        try:
            if not text:
                return []
            
            # List of month names to search for
            months = [
                'january', 'february', 'march', 'april', 'may', 'june',
                'july', 'august', 'september', 'october', 'november', 'december',
                'jan', 'feb', 'mar', 'apr', 'may', 'jun',
                'jul', 'aug', 'sep', 'oct', 'nov', 'dec'
            ]
            
            # Convert to lowercase for matching
            text_lower = text.lower()
            found_months = []
            
            # Check for month ranges like "Oct-Mar" or "October to March"
            import re
            
            # Month range patterns
            range_patterns = [
                r'(january|february|march|april|may|june|july|august|september|october|november|december)\s*(?:to|-|through)\s*(january|february|march|april|may|june|july|august|september|october|november|december)',
                r'(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)\s*(?:to|-|through)\s*(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)'
            ]
            
            month_order = ['january', 'february', 'march', 'april', 'may', 'june',
                          'july', 'august', 'september', 'october', 'november', 'december']
            
            month_mapping = {
                'jan': 'january', 'feb': 'february', 'mar': 'march', 'apr': 'april',
                'may': 'may', 'jun': 'june', 'jul': 'july', 'aug': 'august',
                'sep': 'september', 'oct': 'october', 'nov': 'november', 'dec': 'december'
            }
            
            # Try to find month ranges first
            for pattern in range_patterns:
                matches = re.findall(pattern, text_lower)
                if matches:
                    start_month, end_month = matches[0]
                    
                    # Convert abbreviated months to full names
                    if start_month in month_mapping:
                        start_month = month_mapping[start_month]
                    if end_month in month_mapping:
                        end_month = month_mapping[end_month]
                    
                    # Find range of months
                    if start_month in month_order and end_month in month_order:
                        start_idx = month_order.index(start_month)
                        end_idx = month_order.index(end_month)
                        
                        # Handle wrapping around year (e.g., Oct to Mar)
                        if start_idx <= end_idx:
                            range_months = month_order[start_idx:end_idx + 1]
                        else:
                            range_months = month_order[start_idx:] + month_order[:end_idx + 1]
                        
                        # Capitalize month names
                        found_months = [month.capitalize() for month in range_months]
                        break
            
            # If no ranges found, look for individual months
            if not found_months:
                for month in months:
                    if month in text_lower:
                        full_month = month_mapping.get(month, month)
                        if full_month.capitalize() not in found_months:
                            found_months.append(full_month.capitalize())
            
            # Sort months chronologically if multiple found
            if len(found_months) > 1:
                sorted_months = []
                for month in month_order:
                    if month.capitalize() in found_months:
                        sorted_months.append(month.capitalize())
                found_months = sorted_months
            
            return found_months
            
        except Exception as e:
            self.logger.debug(f"Error extracting months from text: {e}")
            return []
    
    def _generate_basic_important_notes(self, package_data):
        """Generate basic important notes if missing"""
        try:
            notes = []
            destination = package_data.get('destination', '').lower()
            
            # Add basic travel notes
            notes.append("Carry valid photo identification for all travelers")
            notes.append("Check weather conditions before travel")
            
            # Add visa-related notes if visa_type is provided
            visa_types = package_data.get('visa_type', [])
            if visa_types:
                notes.append("Ensure passport validity of at least 6 months")
                if any('visa' in str(v).lower() for v in visa_types):
                    notes.append("Apply for visa well in advance")
            
            # Add destination-specific notes
            if 'india' in destination or any(city in destination for city in ['delhi', 'mumbai', 'bangalore', 'chennai', 'kolkata']):
                notes.append("Respect local customs and dress modestly at religious sites")
                
            # Add booking-related notes
            notes.append("Book accommodations and activities in advance during peak season")
            notes.append("Keep emergency contact numbers handy")
            
            return notes
            
        except Exception as e:
            self.logger.debug(f"Error generating basic important notes: {e}")
            return ["Carry valid identification", "Check local weather", "Respect local customs"]

    def _sanitize_price_field(self, price_str):
        """Sanitize price field to ensure it's a valid number"""
        try:
            if not price_str:
                return 0
            
            # Remove any non-numeric characters
            sanitized_str = re.sub(r'[^0-9.]', '', price_str)
            
            # Convert to decimal
            sanitized_decimal = Decimal(sanitized_str)
            
            # Ensure it's within valid range (0-99,999,999.99)
            if 0 <= sanitized_decimal <= 99999999.99:
                return sanitized_decimal
            else:
                return 0
                
        except (ValueError, TypeError) as e:
            self.logger.debug(f"Error sanitizing price field: {e}")
            return 0 