"""
Configuration file for Package Creator Script
Contains all constants, settings and configuration for bulk package creation
"""

import os
from django.conf import settings


class PackageCreatorConfig:
    """Configuration class for package creator script"""
    
    # =============================================
    # TEST MODE CONFIGURATION
    # =============================================
    # Set to True to process only first N files for testing
    # Set to False for production (processes all files)
    TEST_MODE = True  # ✅ SAFETY: Keep True by default for safety
    TEST_MODE_FILE_LIMIT = 10  # ✅ INCREASED: Process more files in test mode for better testing
    
    # ✅ PRODUCTION MODE: When ready for full processing, set TEST_MODE = False
    # This will process ALL files in the directory
    # WARNING: Ensure you have backups before running in production mode!
    
    # =============================================
    # FILE PROCESSING CONFIGURATION
    # =============================================
    SUPPORTED_EXTENSIONS = ['.docx', '.doc']
    MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB max file size
    
    # OpenAI settings
    OPENAI_API_KEY = getattr(settings, 'OPENAI_API_KEY', None)
    OPENAI_API_URL = getattr(settings, 'OPENAI_API_URL', 'https://api.openai.com/v1/responses')
    OPENAI_MODEL = 'gpt-4o-mini'
    OPENAI_MAX_TOKENS = 8000
    OPENAI_TEMPERATURE = 0.2
    
    # Adaptive timeout settings for multi-package processing - RESTORED TO WORKING VALUES
    OPENAI_BASE_TIMEOUT = 90   # Increased base timeout for multi-package documents
    OPENAI_PER_PACKAGE_TIMEOUT = 30  # Increased per-package timeout
    OPENAI_MAX_TIMEOUT = 300   # Restored max timeout (5 minutes) for complex documents
    
    # Package creation settings
    DEFAULT_PACKAGE_STATUS = {
        'is_published': False,
        'is_active': True
    }
    
    DEFAULT_ENTITY_STATUS = {
        'is_active': True
    }
    
    # Icon classes for package elements
    ICON_CLASSES = [
        "island", "beach", "ship", "sunrise", "activity", "passenger",
        "breakfast", "taxi", "bus", "hotel", "flight", "visa", 
        "insurance", "forex_card"
    ]
    
    # Icon class descriptions for intelligent mapping
    ICON_DESCRIPTIONS = {
        "island": "Islands, archipelago, tropical destinations, island hopping, remote locations",
        "beach": "Beaches, swimming, sunbathing, coastal activities, ocean views, seaside",
        "ship": "Boat rides, cruises, ferry transport, water transportation, sailing, maritime",
        "sunrise": "Scenic views, sunrises/sunsets, viewpoints, panoramic experiences, nature beauty, photography spots",
        "activity": "Adventure activities, sports, recreational activities, experiences, tours, excursions",
        "passenger": "Group activities, passenger limits, capacity-related features, people-focused services, group size",
        "breakfast": "Meals, dining, food services, culinary experiences, restaurants, catering, food included",
        "taxi": "Private transport, transfers, individual transportation, car services, private vehicle", 
        "bus": "Group transport, shared transfers, bus services, group transportation, coach travel",
        "hotel": "Accommodation, lodging, stays, hotels, resorts, housing, room bookings",
        "flight": "Air travel, flights, aviation services, airport transfers, domestic/international flights",
        "visa": "Documentation, visa services, permits, legal requirements, paperwork, travel documents",
        "insurance": "Protection services, safety, insurance coverage, security, travel protection",
        "forex_card": "Currency services, financial services, money exchange, payment options, foreign exchange"
    }
    
    # Logging settings
    LOG_LEVEL = 'INFO'
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    LOG_FILE_PATH = 'package_creator.log'
    
    # Default folder path (will be overridden by user input)
    DEFAULT_DOCUMENTS_FOLDER = "/home/<USER>/Documents/Zuumm-Packages"
    
    # Package validation settings
    REQUIRED_PACKAGE_FIELDS = [
        'title', 'package_no', 'destination', 'type', 'duration',
        'price_per_person', 'about_this_tour', 'highlights'
    ]
    
    # Duration parsing patterns
    DURATION_PATTERNS = {
        'nights': r'(\d+)\s*[N|NIGHT]',
        'days': r'(\d+)\s*[D|DAY]'
    }
    
    # Currency patterns for price extraction
    CURRENCY_PATTERNS = {
        'INR': r'[₹]',
        'USD': r'[\$]',
        'EUR': r'[€]',
        'GBP': r'[£]'
    } 