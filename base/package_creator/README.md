# Package Creator Script

A comprehensive Python script for bulk creation of travel packages from document files. This script processes multiple document files containing package information and creates Package model objects with all necessary relationships.

## Features

- **Multi-Package Processing**: Extract multiple packages from a single document
- **Document Support**: Supports .docx and .doc file formats
- **AI-Powered Extraction**: Uses OpenAI GPT-4o-mini for intelligent data extraction
- **Complete Relationships**: Creates all M2M relationships (Destinations, Categories, Activities)
- **Transaction Safety**: All operations wrapped in atomic transactions
- **Comprehensive Logging**: Detailed logging for monitoring and debugging
- **Production Ready**: Designed for production data seeding with data consistency

## Requirements

- Python 3.8+
- Django project setup
- OpenAI API key configured
- Required Python packages:
  - python-docx
  - requests
  - django

## Installation

1. The script is already created in the `base/package_creator/` folder
2. Ensure your OpenAI API key is configured in Django settings
3. Install required packages if not already installed:
   ```bash
   pip install python-docx requests
   ```

## Usage

### Method 1: Direct Script Execution

```bash
# Navigate to project root
cd /path/to/your/project

# Run with specific folder path
python base/package_creator/run_script.py "/home/<USER>/Documents/Zuumm-Packages/"

# Run in interactive mode (will prompt for folder path)
python base/package_creator/run_script.py
```

### Method 2: Programmatic Usage

```python
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from base.package_creator.main_script import PackageCreatorScript

# Create and run script
script = PackageCreatorScript()
script.run("/path/to/your/documents/folder")
```

### Method 3: Test Partner Assignment (Recommended First Step)

Before running the main script, test that Partner assignment is working correctly:

```bash
# Test Partner assignment logic
python base/package_creator/test_partner_assignment.py
```

This test will:
- Check if Zuumm Partner exists in your database
- Validate PackageCreationHelper initialization
- Provide setup instructions if Partner is missing
- Perform a dry run validation of package data

## Configuration

### Document Folder Structure

```
/your/documents/folder/
├── package_file_1.docx
├── package_file_2.docx
├── package_file_3.doc
└── ...
```

### Supported File Formats

- `.docx` - Microsoft Word (recommended)
- `.doc` - Legacy Microsoft Word format

### OpenAI Configuration

Ensure your Django settings include:

```python
OPENAI_API_KEY = "your-openai-api-key-here"
OPENAI_API_URL = "https://api.openai.com/v1/chat/completions"  # Optional
```

## Script Behavior

### What the Script Does

1. **Scans Folder**: Finds all supported document files
2. **Extracts Text**: Reads content from each document
3. **AI Processing**: Sends text to OpenAI to extract package data
4. **Data Validation**: Validates extracted package information
5. **Database Operations**: Creates Package objects with relationships
6. **Logging**: Provides detailed logs of all operations

### Package Creation Process

For each package found in documents:

1. **Partner Assignment**:
   - **ALL objects are assigned to the Zuumm Partner**
   - Zuumm Partner is identified by `partner_type='ZUUMM'`
   - Package, Destination, Category, Activity objects all get `partner=zuumm_partner`
   - This ensures consistent ownership across all created entities

2. **Create/Get Destination**: 
   - Checks if destination exists for Zuumm partner
   - Creates new destination if needed (with Zuumm partner assignment)
   - Sets appropriate icon class

3. **Create/Get Categories**: 
   - Processes all categories for the package
   - Creates missing categories (with Zuumm partner assignment)
   - Links to destination and package

4. **Create/Get Activities**: 
   - Processes all activities for the package
   - Creates missing activities (with Zuumm partner assignment)
   - Links to destination and package

5. **Create Package Object**:
   - Creates main Package instance (with Zuumm partner assignment)
   - Sets all basic fields (title, price, duration, etc.)
   - Generates auto-calculated fields (slug, icon_class)
   - Generates LLM fields (SEO title, meta description, keywords)

6. **Create M2M Relationships**:
   - Links Package with Categories (PackageCategory)
   - Links Package with Activities (PackageActivity)
   - Links Destination with Categories (DestinationCategory)
   - Links Destination with Activities (DestinationActivity)

7. **Create Package Details**:
   - Creates PackageHighlight objects
   - Creates PackageInclusion objects
   - Creates PackageAddon objects (from exclusions)

### Default Package Settings

All created packages will have:
- `partner = zuumm_partner` (Zuumm Partner instance with partner_type='ZUUMM')
- `is_published = False` (as per requirements)
- `is_active = True` (as per requirements)
- `package_uploader = None` (no FK to PackageUploader)

All created entities (Destinations, Categories, Activities) will have:
- `partner = zuumm_partner` (Zuumm Partner instance with partner_type='ZUUMM')
- `is_active = True`

### Partner Requirements

**CRITICAL**: Before running the script, ensure that a Zuumm Partner exists in your database:

```python
# In Django shell or management command
from accounts.models import Partner
from accounts.choices import PartnerTypeChoices

# Check if Zuumm Partner exists
zuumm_partner = Partner.objects.filter(partner_type=PartnerTypeChoices.ZUUMM.value).first()

if not zuumm_partner:
    # Create Zuumm Partner if it doesn't exist
    zuumm_partner = Partner.objects.create(
        entity_name="Zuumm",
        subdomain="zuumm", 
        partner_type=PartnerTypeChoices.ZUUMM.value,
        primary_theme="#your-theme-color"
        # Add other required fields as needed
    )
    print("Zuumm Partner created successfully")
else:
    print(f"Zuumm Partner found: {zuumm_partner.entity_name} (ID: {zuumm_partner.id})")
```

The script will automatically detect and use this Zuumm Partner for all created objects. If no Zuumm Partner is found, the script will raise an error and stop execution.

## Logging

### Log Files

- **File Location**: `package_creator.log` (in project root)
- **Log Levels**: DEBUG, INFO, WARNING, ERROR, CRITICAL
- **Console Output**: INFO level and above
- **File Output**: All levels (DEBUG and above)

### Log Contents

- Script start/end with timestamps
- File processing progress
- Package creation progress
- OpenAI API requests and responses
- Database operations (entity creation, relationships)
- Validation errors and warnings
- Exception details with context
- Transaction status (start, commit, rollback)
- Final statistics and summary

### Sample Log Output

```
2024-01-15 10:30:00 - PackageCreator - INFO - ================================================================================
2024-01-15 10:30:00 - PackageCreator - INFO - PACKAGE CREATOR SCRIPT STARTED
2024-01-15 10:30:00 - PackageCreator - INFO - ================================================================================
2024-01-15 10:30:00 - PackageCreator - INFO - Start Time: 2024-01-15 10:30:00
2024-01-15 10:30:00 - PackageCreator - INFO - Documents Folder: /path/to/documents
2024-01-15 10:30:00 - PackageCreator - INFO - Found 29 document files to process
2024-01-15 10:30:01 - PackageCreator - INFO - Starting Database Transaction
2024-01-15 10:30:01 - PackageCreator - INFO - ------------------------------------------------------------
2024-01-15 10:30:01 - PackageCreator - INFO - PROCESSING FILE 1/29
2024-01-15 10:30:01 - PackageCreator - INFO - File: package_file_1.docx
2024-01-15 10:30:05 - PackageCreator - INFO - Extracted 3 packages from file: package_file_1.docx
2024-01-15 10:30:05 - PackageCreator - INFO - Creating Package 1/3: Rajasthan Heritage Tour
2024-01-15 10:30:06 - PackageCreator - INFO - Package Created Successfully: Rajasthan Heritage Tour (ID: 123)
```

## Error Handling

### Transaction Safety

- **Atomic Operations**: All database operations are wrapped in a single transaction
- **Rollback on Failure**: If any critical error occurs, all changes are rolled back
- **Data Consistency**: Ensures no partial data is left in the database

### Error Recovery

- **File-Level Errors**: If one file fails, script continues with next file
- **Package-Level Errors**: If one package fails, script continues with next package
- **Detailed Error Logging**: All errors are logged with context for debugging

### Common Issues and Solutions

1. **OpenAI API Errors**:
   - Check API key configuration
   - Verify internet connection
   - Check API rate limits

2. **Document Reading Errors**:
   - Ensure files are not corrupted
   - Check file permissions
   - Verify supported file formats

3. **Database Errors**:
   - Check Django database configuration
   - Verify model field constraints
   - Ensure sufficient disk space

## Performance

### Expected Processing Time

- **Small Files** (<1MB): 10-30 seconds per file
- **Medium Files** (1-5MB): 30-90 seconds per file
- **Large Files** (>5MB): 90+ seconds per file

Processing time depends on:
- File size and content
- Number of packages per file
- OpenAI API response time
- Database performance

### Optimization Tips

1. **Batch Processing**: Process files in smaller batches if needed
2. **Network**: Ensure stable internet for OpenAI API calls
3. **Database**: Use database optimization for large datasets
4. **Monitoring**: Use logs to monitor progress and identify bottlenecks

## Support

For issues or questions:

1. Check log files for detailed error information
2. Verify configuration and requirements
3. Test with a single small file first
4. Review this documentation for troubleshooting tips

## Example Document Structure

The script expects documents containing package information like:

```
Package Name: Romantic Switzerland and Paris
Destination: Europe
Package No: 34
Package Category: Luxury, Family
Type: Fixed
Duration: 9N & 10D
Price per person: INR 2,56,500
Visa Type: Stamp Visa
Owner: ZUUMM

About This Tour
Embark on a magical 10-day journey through the breathtaking landscapes...

Highlights
Lake Geneva Cruise between Lausanne and Montreux
Scenic Cable Car Ride to Glacier 3000 with activities...

Inclusions
Enjoy a cruise on Lake Geneva between Lausanne and Montreux
Go up the cable car to Glacier 3000...

Exclusions
Tipping is expected from anyone providing your service...

Itinerary
Day 1: Arrive in Geneva, transfer to hotel and check-in...

Package Name: Grand Tour of Europe - Premium
Destination: Europe
Package No - 35
Package Category - Family, Luxury
Type - Fixed
Duration: 16N & 17D
Price per person: INR 4,95,000
...
```

### Supported Package Format Patterns

The AI will intelligently identify and separate multiple packages from documents using these patterns:

**Package Identification:**
- `Package Name:` followed by package title
- `Package No:` or `Package No -` followed by number
- Clear section breaks between packages

**Field Extraction Patterns:**
- `Destination:` → destination field
- `Package Category:` → categories array (comma-separated)
- `Type:` → type field (Fixed, Variable, etc.)
- `Duration:` → duration field (supports "9N & 10D" or "9N/10D" formats)
- `Price per person:` → price_per_person field (preserves format like "INR 2,56,500")
- `Visa Type:` → visa_type field
- `Owner:` → owner field

**Content Section Patterns:**
- `About This Tour` → about_this_tour field
- `Highlights` → highlights array
- `Inclusions` → inclusions array  
- `Exclusions` → exclusions array
- `Itinerary` → itinerary field
- `Hotel` → hotels array
- `Popular Activities` → popular_activities array
- `Add-Ons` → addons array
- `Best Time to Visit` → best_time_to_visit field
- `Rating` → rating field
- `Currency and Conversion Rate` → currency_conversion_rate field
- `Destination Safety` → destination_safety field
- `Popular Restaurants` → popular_restaurants field
- `What to Shop` → what_to_shop field
- `What to Pack` → what_to_pack field
- `Cultural Info` → cultural_info field

### Multiple Packages Per Document

The script is specifically designed to handle documents containing **3-8 packages** per file. It will:

1. **Identify Package Boundaries**: Looks for "Package Name:" headers as primary separators
2. **Extract Individual Package Data**: Processes each package's complete information separately
3. **Handle Shared Information**: If common information appears at the end (like destination details), includes it for all packages
4. **Preserve Exact Formatting**: Maintains original price formats, package numbers, and text content
5. **Create Separate Database Records**: Each package becomes its own Package model instance

### Price Format Handling

The script correctly handles various price formats:
- `INR 2,56,500` (Indian comma formatting)
- `$49,000` (US formatting)
- `€15.500` (European formatting)
- `£12,500` (UK formatting)

All formats are automatically parsed to extract:
- Numeric value for calculations
- Currency code for database storage
- Original formatting preserved in logs 