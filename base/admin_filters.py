from django.contrib.admin import SimpleListFilter
from django.utils.translation import gettext_lazy as _


class ActiveStatusFilter(SimpleListFilter):
    """Custom filter for is_active field with better labels"""
    title = _('Active Status')
    parameter_name = 'is_active'

    def lookups(self, request, model_admin):
        return (
            ('1', _('Active')),
            ('0', _('Inactive')),
        )

    def queryset(self, request, queryset):
        if self.value() == '1':
            return queryset.filter(is_active=True)
        if self.value() == '0':
            return queryset.filter(is_active=False)
        return queryset


class EmailVerificationFilter(SimpleListFilter):
    """Custom filter for is_email_verified field with better labels"""
    title = _('Email Verification Status')
    parameter_name = 'is_email_verified'

    def lookups(self, request, model_admin):
        return (
            ('1', _('Verified')),
            ('0', _('Not Verified')),
        )

    def queryset(self, request, queryset):
        if self.value() == '1':
            return queryset.filter(is_email_verified=True)
        if self.value() == '0':
            return queryset.filter(is_email_verified=False)
        return queryset


class PrimarySuperAdminFilter(SimpleListFilter):
    """Custom filter for is_primary_superadmin field with better labels"""
    title = _('Primary SuperAdmin Status')
    parameter_name = 'is_primary_superadmin'

    def lookups(self, request, model_admin):
        return (
            ('1', _('Primary SuperAdmin')),
            ('0', _('Regular SuperAdmin')),
        )

    def queryset(self, request, queryset):
        if self.value() == '1':
            return queryset.filter(is_primary_superadmin=True)
        if self.value() == '0':
            return queryset.filter(is_primary_superadmin=False)
        return queryset


class InternationalDestinationFilter(SimpleListFilter):
    """Custom filter for is_international field with better labels"""
    title = _('Destination Type')
    parameter_name = 'is_international'

    def lookups(self, request, model_admin):
        return (
            ('1', _('International')),
            ('0', _('Domestic')),
        )

    def queryset(self, request, queryset):
        if self.value() == '1':
            return queryset.filter(is_international=True)
        if self.value() == '0':
            return queryset.filter(is_international=False)
        return queryset


class TrendingDestinationFilter(SimpleListFilter):
    """Custom filter for is_trending field with better labels"""
    title = _('Trending Status')
    parameter_name = 'is_trending'

    def lookups(self, request, model_admin):
        return (
            ('1', _('Trending')),
            ('0', _('Not Trending')),
        )

    def queryset(self, request, queryset):
        if self.value() == '1':
            return queryset.filter(is_trending=True)
        if self.value() == '0':
            return queryset.filter(is_trending=False)
        return queryset


class FeaturedActivityFilter(SimpleListFilter):
    """Custom filter for is_featured field with better labels"""
    title = _('Featured Status')
    parameter_name = 'is_featured'

    def lookups(self, request, model_admin):
        return (
            ('1', _('Featured')),
            ('0', _('Not Featured')),
        )

    def queryset(self, request, queryset):
        if self.value() == '1':
            return queryset.filter(is_featured=True)
        if self.value() == '0':
            return queryset.filter(is_featured=False)
        return queryset


class PublishStatusFilter(SimpleListFilter):
    """Custom filter for is_published field with better labels"""
    title = _('Publish Status')
    parameter_name = 'is_published'

    def lookups(self, request, model_admin):
        return (
            ('1', _('Published')),
            ('0', _('Draft')),
        )

    def queryset(self, request, queryset):
        if self.value() == '1':
            return queryset.filter(is_published=True)
        if self.value() == '0':
            return queryset.filter(is_published=False)
        return queryset


class BlogStatusFilter(SimpleListFilter):
    """Custom filter for blog published status with better labels"""
    title = _('Published Status')
    parameter_name = 'published_status'

    def lookups(self, request, model_admin):
        return (
            ('published', _('Published')),
            ('unpublished', _('Unpublished')),
        )

    def queryset(self, request, queryset):
        if self.value() == 'published':
            return queryset.filter(is_active=True)
        elif self.value() == 'unpublished':
            return queryset.filter(is_active=False)
        return queryset 


class ApprovedAffiliateFilter(SimpleListFilter):
    """Custom filter to show only approved affiliates"""
    title = _('Affiliate')
    parameter_name = 'affiliate'

    def lookups(self, request, model_admin):
        # Import here to avoid circular imports
        from accounts.models import Affiliate
        from accounts.choices import AffiliateStatusChoices
        
        # Get only approved affiliates
        approved_affiliates = Affiliate.objects.filter(status=AffiliateStatusChoices.APPROVED.value)
        
        # Return tuples of (value, display_name)
        return [(affiliate.id, f"{affiliate.user.email} ({affiliate.user.full_name or 'No name'})") 
                for affiliate in approved_affiliates]

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(affiliate__id=self.value())
        return queryset


class TripjackHotelDestinationFilter(SimpleListFilter):
    """Custom filter for TripJack Hotels by destination with partner-aware filtering"""
    title = _('Destination')
    parameter_name = 'destination'

    def lookups(self, request, model_admin):
        # Import here to avoid circular imports
        from packages.models import Destination
        from packages.admin import get_user_effective_partner

        # Get effective partner for the user
        effective_partner = get_user_effective_partner(request)

        if effective_partner:
            # Filter destinations by partner and active status
            destinations = Destination.objects.filter(
                partner=effective_partner,
                is_active=True
            ).order_by('title')
        else:
            # If no effective partner, return empty queryset
            destinations = Destination.objects.none()

        # Return tuples of (value, display_name)
        return [(dest.id, dest.title) for dest in destinations]

    def queryset(self, request, queryset):
        if self.value():
            # Filter TripJack Hotels that are mapped to the selected destination
            return queryset.filter(destinations__id=self.value())
        return queryset