"""
Base models for the project.
"""
import uuid
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django_softdelete.models import SoftDeleteModel
from rest_framework.utils import model_meta


class BaseModel(SoftDeleteModel):
    """
    Abstract base model that provides common fields and soft delete functionality.
    Inherits from SoftDeleteModel for soft delete functionality.
    """
    external_id = models.UUIDField(
        default=uuid.uuid4,
        editable=False
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        abstract = True
        ordering = ['-created_at']

    def get_related_objects(self):
        info = model_meta.get_field_info(self)
        fields = []
        for field in self.__class__.__dict__:
            if (
                field in info.relations
                and info.relations[field].reverse
                and info.relations[field].has_through_model is False
            ):
                # print all reverse relations
                fields.append(field)
        return fields

    def delete_related_objects(self):
        for key in self.get_related_objects():
            obj = getattr(self, key, None)
            if obj is None:
                continue
            if hasattr(obj, "pk"):
                obj.delete()
            else:
                if obj.exists():
                    for related in obj.all():
                        related.delete()
