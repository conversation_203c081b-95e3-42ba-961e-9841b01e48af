import os
import json
from pathlib import Path
from typing import Dict, Any, Optional
from functools import lru_cache
import boto3
from dotenv import load_dotenv

class SettingsManager:
    """Generic settings manager that handles both local .env and AWS Secrets Manager."""
    
    ENV_LOCAL = 'local'
    ENV_DEV = 'dev'
    ENV_UAT = 'uat'
    ENV_PROD = 'prod'
    
    VALID_ENVIRONMENTS = [ENV_LOCAL, ENV_DEV, ENV_UAT, ENV_PROD]
    
    def __init__(self, base_dir: Path):
        self.base_dir = base_dir
        self._environment = self._get_environment()
        self._settings = self._load_settings()
    
    def _get_environment(self) -> str:
        """Get the current environment from environment file."""
        env_file = self.base_dir / 'environment'
        if not env_file.exists():
            return self.ENV_LOCAL
        
        with open(env_file, 'r') as f:
            env = f.read().strip().lower()
            if env not in self.VALID_ENVIRONMENTS:
                raise ValueError(f"Invalid environment: {env}. Must be one of {self.VALID_ENVIRONMENTS}")
            return env
    
    @property
    def environment(self) -> str:
        """Get the current environment."""
        return self._environment
    
    @property
    def is_local(self) -> bool:
        """Check if current environment is local."""
        return self._environment == self.ENV_LOCAL
    
    def _load_local_settings(self) -> Dict[str, Any]:
        """Load settings from .env file for local environment."""
        env_path = self.base_dir / '.env'
        if not env_path.exists():
            raise FileNotFoundError(f".env file not found at {env_path}")
        
        # Load .env file into environment variables
        load_dotenv(env_path, override=True)
        
        # Convert all environment variables to a dictionary
        settings = {}
        for key, value in os.environ.items():
            # Try to parse as JSON if it looks like JSON
            if value.startswith('{') or value.startswith('['):
                try:
                    settings[key] = json.loads(value)
                except json.JSONDecodeError:
                    settings[key] = value
            else:
                settings[key] = value
        return settings
    
    def _load_aws_settings(self) -> Dict[str, Any]:
        """Load settings from AWS Secrets Manager for non-local environments."""
        secret_name = f"zuumm/{self._environment}/secrets"
        region_name = 'ap-south-1'
        
        session = boto3.session.Session()
        client = session.client(
            service_name='secretsmanager',
            region_name=region_name
        )
        
        try:
            response = client.get_secret_value(SecretId=secret_name)
            secrets = json.loads(response['SecretString'])
            
            # Ensure all values are strings for consistency
            return {k: str(v) if not isinstance(v, (dict, list)) else v 
                   for k, v in secrets.items()}
        except Exception as e:
            raise Exception(f"Error fetching secrets from AWS: {str(e)}")
    
    @lru_cache()
    def _load_settings(self) -> Dict[str, Any]:
        """Load settings based on environment."""
        if self.is_local:
            return self._load_local_settings()
        return self._load_aws_settings()
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get a setting value with optional default."""
        return self._settings.get(key, default)
    
    def get_required(self, key: str) -> Any:
        """Get a required setting value. Raises KeyError if not found."""
        value = self.get(key)
        if value is None:
            raise KeyError(f"Required setting '{key}' not found in {self._environment} environment")
        return value
    
    def get_int(self, key: str, default: Optional[int] = None) -> Optional[int]:
        """Get a setting value as integer."""
        value = self.get(key, default)
        if value is None:
            return default
        try:
            return int(value)
        except (ValueError, TypeError):
            raise ValueError(f"Setting '{key}' must be an integer")
    
    def get_bool(self, key: str, default: Optional[bool] = None) -> Optional[bool]:
        """Get a setting value as boolean."""
        value = self.get(key, default)
        if value is None:
            return default
        if isinstance(value, bool):
            return value
        return str(value).lower() in ('true', '1', 'yes', 'y', 'on')
    
    def get_list(self, key: str, default: Optional[list] = None, separator: str = ',') -> Optional[list]:
        """Get a setting value as list."""
        value = self.get(key, default)
        if value is None:
            return default
        if isinstance(value, list):
            return value
        return [item.strip() for item in str(value).split(separator) if item.strip()]
    
    def get_json(self, key: str, default: Optional[dict] = None) -> Optional[dict]:
        """Get a setting value as JSON/dict."""
        value = self.get(key, default)
        if value is None:
            return default
        if isinstance(value, dict):
            return value
        try:
            return json.loads(value)
        except json.JSONDecodeError:
            raise ValueError(f"Invalid JSON for setting '{key}'")

# Create a singleton instance
_settings_manager = None

def init_settings(base_dir: Path) -> SettingsManager:
    """Initialize the settings manager."""
    global _settings_manager
    if _settings_manager is None:
        _settings_manager = SettingsManager(base_dir)
    return _settings_manager

def get_settings() -> SettingsManager:
    """Get the settings manager instance."""
    if _settings_manager is None:
        raise RuntimeError("Settings manager not initialized. Call init_settings first.")
    return _settings_manager

def get_secret(key, default=None):
    """Get a specific secret value."""

    secrets = get_settings()._settings
    return secrets.get(key, default) 
