from django.template.loader import render_to_string
from django.conf import settings
from .sendgrid import Mailer
from typing import List, Dict, Any, Optional
from accounts.choices import OTPSourceChoices
import logging

logger = logging.getLogger(__name__)

class EmailContentManager:
    """
    Manages email content templates and subjects for different types of emails
    """
    @staticmethod
    def get_email_content(email_type, context):
        """
        Get email subject and body based on email type and context
        """
        subject = ""
        body = ""

        if email_type == 'partner_registration':
            subject = "New Partner Registration"
            body = render_to_string(
                'partner_admin/partner_registration.html', 
                context
            )
        elif email_type == 'partner_referral_registration':
            subject = "Welcome to Zuumm Referral Program!"
            body = render_to_string(
                'partner_admin/partner_referral_registration.html',
                context
            )
        elif email_type == 'partner_deactivation':
            subject = f"Partner Account Deactivated: {context.get('partner_name', '')}"
            body = render_to_string(
                'partner_admin/partner_deactivation.html',
                context
            )
        elif email_type == 'partner_activation':
            subject = f"Partner Account Reactivated: {context.get('partner_name', '')}"
            body = render_to_string(
                'partner_admin/partner_activation.html',
                context
            )
        elif email_type == 'partner_activation_non_admin':
            subject = f"Partner Account Reactivated: {context.get('partner_name', '')}"
            body = render_to_string(
                'partner_admin/partner_activation_non_admin.html',
                context
            )
        elif email_type == 'superadmin_welcome':
            subject = "Welcome to Zuumm Admin Portal - Super Admin Access"
            body = render_to_string(
                'partner_admin/superadmin_welcome.html',
                context
            )
        elif email_type == 'partner_registration_otp':
            subject = "Your Zuumm Verification Code"
            body = render_to_string(
                'emails/partner_registration_otp_verification.html',
                context
            )
        elif email_type == OTPSourceChoices.LOGIN_SIGNUP.value:
            subject = "Your Zuumm Login/Signup Verification Code"
            body = render_to_string(
                'emails/login_signup_otp_verification.html',
                context
            )
        elif email_type == 'voucher_confirmation':
            subject = f"Booking Confirmed - {context.get('booking_id', '')} | {context.get('partner_name', '')}"
            body = render_to_string(
                'emails/voucher_confirmation.html',
                context
            )

        elif email_type == 'user_signup':
            subject = "Zuumm Signup Successfully"
            body = render_to_string(
                'emails/signup_success.html',
                context
            )

        elif email_type == 'affiliate_approved':
            subject = "Affiliate Request Approved"
            body = render_to_string(
                'emails/affiliate_request_approved.html',
                context
            )
        
        elif email_type == 'affiliate_rejected':
            subject = "Affiliate Request Rejected"
            body = render_to_string(
                'emails/affiliate_request_rejected.html',
                context
            )

        return subject, body


def send_mail_task(subject, body, recipient_list, attachments=None):
    """
    Generic function to send emails using SendGrid
    
    Args:
        subject: Email subject
        html_message: HTML content of the email
        recipient_list: List of recipient email addresses
        from_email: Sender email (defaults to settings.DEFAULT_FROM_EMAIL)
        attachments: List of attachments (optional)
        fail_silently: Whether to raise exceptions or just log them
    
    Returns:
        bool: True if email was sent successfully, False otherwise
    """
    logger.info(f"[EmailUtils] Starting send_mail_task - subject: {subject}, recipients: {recipient_list}, attachments: {len(attachments) if attachments else 0}")
    try:
        for recipient in recipient_list:
            logger.debug(f"[EmailUtils] Sending email to: {recipient}")
            mailer = Mailer(
                subject=subject,
                body=body,
                to_email=recipient,
                attachments=attachments
            )
            mailer.send()
            logger.debug(f"[EmailUtils] Email sent successfully to: {recipient}")
        
        logger.info(f"[EmailUtils] All emails sent successfully")
        return True
    except Exception as e:
        logger.error(f"[EmailUtils] Failed to send email: {str(e)}", exc_info=True)
        return False
