import os
import sys
import django
import logging
import requests
from typing import List, Dict, Optional
from django.conf import settings

# Setup Django environment
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.append(project_root)

# Initialize Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

# Import models after Django setup
from dynamic_packages.models import Hotel, Destination

logger = logging.getLogger(__name__)


class TripjackHelper:
    """Helper class for interacting with Tripjack Hotel Static API"""

    def __init__(self):
        self.api_key = settings.TRIPJACK_API_KEY
        self.base_url = settings.TRIPJACK_API_URL 

    def _get_headers(self):
        """Generate headers for Tripjack API requests."""
        return {
            "Content-Type": "application/json",
            "apikey": self.api_key,
        }

    def _handle_response(self, response):
        """Helper to process API responses uniformly."""
        try:
            data = response.json()
        except Exception:
            logger.error("Failed to decode Tripjack response as JSON")
            return {"error": "Invalid JSON response"}

        if response.status_code != 200 or not data.get("status", {}).get("success"):
            logger.error(f"Tripjack API error: {response.text}")
            return {"error": data}

        return data

    def fetch_hotels_initial(self, last_update_time=None):
        """
        Fetch first set of static hotels data.
        Args:
            last_update_time (str, optional): Format 'YYYY-MM-DDTHH:MM'
        Returns:
            dict: API response
        """
        api_url = f"{self.base_url}/hms/v1/fetch-static-hotels"
        payload = {}
        if last_update_time:
            payload["lastUpdateTime"] = last_update_time

        try:
            response = requests.post(
                url=api_url,
                headers=self._get_headers(),
                json=payload
            )
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"Tripjack fetch_hotels_initial error: {str(e)}")
            return {"error": str(e)}

    def fetch_hotels_next(self, next_token):
        """
        Fetch next set of static hotels data using 'next' token (Type 2).

        Args:
            next_token (str): Token from previous response to fetch next page.
        Returns:
            dict: API response
        """
        api_url = self.base_url + "/hms/v1/fetch-static-hotels"
        try:
            response = requests.post(
                url=api_url,
                headers=self._get_headers(),
                json={"next": next_token}
            )
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"Tripjack fetch_hotels_next error: {str(e)}")
            return {"error": str(e)}

    def search_hotels_by_ids(
            self, 
            hotel_ids, 
            checkin_date="2025-08-20",  # Updated to future date
            checkout_date="2025-08-21",  # Updated to future date
            adults=2, 
            children=0
        ):
        """
        Search hotels using hotel IDs (hids).
        Args:
            hotel_ids (list): List of Tripjack hotel IDs (int or str)
            checkin_date (str): Format 'YYYY-MM-DD'
            checkout_date (str): Format 'YYYY-MM-DD'
            adults (int): Number of adults
            children (int): Number of children
        Returns:
            dict: API response
        """
        api_url = f"{self.base_url}/hms/v1/hotel-searchquery-list"
        payload = {
            "searchQuery": {
                "checkinDate": checkin_date,
                "checkoutDate": checkout_date,
                "roomInfo": [
                    {
                        "numberOfAdults": adults,
                        "numberOfChild": children
                    }
                ],
                "searchCriteria": {
                    "nationality": "106",  # India
                    "currency": "INR"
                },
                "searchPreferences": {
                    "hids": hotel_ids,
                    "fsc": True
                }
            },
            "sync": True
        }

        try:
            response = requests.post(
                url=api_url,
                headers=self._get_headers(),
                json=payload
            )
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"Tripjack search_hotels_by_ids error: {str(e)}")
            return {"error": str(e)}

    def get_hotel_detail(self, hotel_search_id):
        """
        Get hotel details by Tripjack hotel search ID.
        Args:
            hotel_search_id (str): e.g., 'hsid6999619891-61886608'
        Returns:
            dict: API response
        """
        api_url = f"{self.base_url}/hms/v1/hotelDetail-search"
        payload = {"id": hotel_search_id}

        try:
            response = requests.post(
                url=api_url,
                headers=self._get_headers(),
                json=payload
            )
            return self._handle_response(response)
        except Exception as e:
            logger.error(f"Tripjack get_hotel_detail error: {str(e)}")
            return {"error": str(e)}


class TripjackHotelFetcher:
    """
    Legacy hotel fetcher class - DEPRECATED
    Use TripJackHotelsFetcher from main.py instead for the new modular approach
    """
    
    def __init__(self, tripjack_helper, s3_bucket_name: str = None):
        logger.warning("TripjackHotelFetcher is deprecated. Use TripJackHotelsFetcher from main.py instead.")
        self.tripjack_helper = tripjack_helper
        
        # Import the new classes
        try:
            from .destination_matcher import TripJackDestinationMatcher
            from .image_downloader import TripJackImageDownloader  
            from .data_processor import TripJackDataProcessor
            from .logger import TripJackLogger
            
            self.logger = TripJackLogger()
            self.destination_matcher = TripJackDestinationMatcher(self.logger)
            self.image_downloader = TripJackImageDownloader(self.logger)
            self.data_processor = TripJackDataProcessor(self.logger)
            
        except ImportError as e:
            logger.error(f"Failed to import new modules: {e}")
            logger.error("Please use TripJackHotelsFetcher from main.py instead")
            raise
        
        self.processed_count = 0
        self.skipped_count = 0
        self.error_count = 0
    
    def fetch_and_process_all_hotels(self, last_update_time: str = None):
        """
        Main method to fetch and process all hotels
        DEPRECATED: Use TripJackHotelsFetcher.run() instead
        """
        logger.warning("fetch_and_process_all_hotels is deprecated. Use TripJackHotelsFetcher.run() instead.")
        
        try:
            # Start with initial fetch
            response = self.tripjack_helper.fetch_hotels_initial(last_update_time)
            
            if response.get('error'):
                logger.error(f"Failed to fetch initial hotels: {response['error']}")
                return
            
            # Process first batch
            hotels_data = response.get('hotels', [])
            self._process_hotels_batch(hotels_data)
            
            # Process remaining batches using next token
            next_token = response.get('next')
            
            while next_token:
                logger.info(f"Fetching next batch with token: {next_token}")
                
                response = self.tripjack_helper.fetch_hotels_next(next_token)
                
                if response.get('error'):
                    logger.error(f"Failed to fetch next batch: {response['error']}")
                    break
                
                hotels_data = response.get('hotels', [])
                self._process_hotels_batch(hotels_data)
                
                next_token = response.get('next')
            
            logger.info(f"Hotel fetching completed. Processed: {self.processed_count}, "
                       f"Skipped: {self.skipped_count}, Errors: {self.error_count}")
            
        except Exception as e:
            logger.error(f"Error in main fetching process: {str(e)}")
    
    def _process_hotels_batch(self, hotels_data: List[Dict]):
        """Process a batch of hotels"""
        for hotel_data in hotels_data:
            try:
                self._process_single_hotel(hotel_data)
            except Exception as e:
                logger.error(f"Error processing hotel {hotel_data.get('name', 'Unknown')}: {str(e)}")
                self.error_count += 1
    
    def _process_single_hotel(self, hotel_data: Dict):
        """Process a single hotel"""
        hotel_name = hotel_data.get('name', 'Unknown')
        hotel_id = hotel_data.get('hotelId', '')
        
        # Skip if hotel is deleted
        if hotel_data.get('isDeleted', False):
            logger.info(f"Skipping deleted hotel: {hotel_name}")
            self.skipped_count += 1
            return
        
        # Check if hotel already exists
        if Hotel.objects.filter(tripjack_static_id=hotel_id).exists():
            logger.info(f"Hotel already exists: {hotel_name}")
            self.skipped_count += 1
            return
        
        # Use the new destination matcher
        is_valid, destination_title, destination_obj = self.destination_matcher.validate_destination_match(hotel_data)
        
        if not is_valid or not destination_obj:
            logger.info(f"No matching destination for hotel: {hotel_name}")
            self.skipped_count += 1
            return
        
        # Search for hotel to get search result
        search_result = self._search_hotel(hotel_id)
        if not search_result:
            logger.warning(f"No search result for hotel: {hotel_name}")
            self.skipped_count += 1
            return
        
        # Get hotel details
        search_id = search_result.get('id', '')
        detail_data = self._get_hotel_details(search_id)
        
        # Create hotel using new data processor
        created_hotel = self.data_processor.process_hotel_data(
            hotel_data, destination_obj, search_result, detail_data or {}
        )
        
        if created_hotel:
            self.processed_count += 1
            logger.info(f"Successfully processed hotel: {hotel_name}")
        else:
            self.error_count += 1
    
    def _search_hotel(self, hotel_id: str) -> Optional[Dict]:
        """Search for hotel using hotel ID"""
        try:
            search_response = self.tripjack_helper.search_hotels_by_ids([hotel_id])
            
            if search_response.get('error'):
                logger.error(f"Search failed for hotel ID {hotel_id}: {search_response['error']}")
                return None
            
            # Get first hotel from search results
            search_result = search_response.get('searchResult', {})
            hotels = search_result.get('his', [])
            
            if hotels:
                return hotels[0]
            
            return None
            
        except Exception as e:
            logger.error(f"Error searching hotel {hotel_id}: {str(e)}")
            return None
    
    def _get_hotel_details(self, search_id: str) -> Optional[Dict]:
        """Get detailed hotel information"""
        try:
            detail_response = self.tripjack_helper.get_hotel_detail(search_id)
            
            if detail_response.get('error'):
                logger.error(f"Detail fetch failed for search ID {search_id}: {detail_response['error']}")
                return None
            
            return detail_response
            
        except Exception as e:
            logger.error(f"Error getting hotel details {search_id}: {str(e)}")
            return None
