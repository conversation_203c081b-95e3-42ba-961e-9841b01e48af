"""
Logger utility for TripJack Hotels Fetcher Script
Provides comprehensive logging functionality with file and console output
"""

import logging
import sys
from datetime import datetime
from pathlib import Path
from .config import TripJackConfig


class TripJackLogger:
    """
    Custom logger class for TripJack hotels fetcher script
    Provides both file and console logging with different levels
    """
    
    def __init__(self, log_file_path=None, log_level=None):
        self.log_file_path = log_file_path or TripJackConfig.MAIN_LOG_FILE
        self.log_level = log_level or TripJackConfig.LOG_LEVEL
        self.logger = None
        self._setup_logger()
        
        # Setup separate loggers for different error types
        self._setup_error_loggers()
    
    def _setup_logger(self):
        """Setup main logger with file and console handlers"""
        # Create logger
        self.logger = logging.getLogger('TripJackHotelsFetcher')
        self.logger.setLevel(getattr(logging, self.log_level))
        
        # Remove existing handlers to avoid duplication
        self.logger.handlers.clear()
        
        # Create formatter
        formatter = logging.Formatter(TripJackConfig.LOG_FORMAT)
        
        # Create file handler
        file_handler = logging.FileHandler(self.log_file_path)
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        
        # Create console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        
        # Add handlers to logger
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
    
    def _setup_error_loggers(self):
        """Setup separate loggers for different error types"""
        # Failed hotels logger
        self.failed_hotels_logger = self._create_file_logger(
            'TripJackFailedHotels',
            TripJackConfig.FAILED_HOTELS_LOG
        )
        
        # No destination logger
        self.no_destination_logger = self._create_file_logger(
            'TripJackNoDestination',
            TripJackConfig.NO_DESTINATION_LOG
        )
        
        # Empty response logger
        self.empty_response_logger = self._create_file_logger(
            'TripJackEmptyResponse',
            TripJackConfig.EMPTY_RESPONSE_LOG
        )
        
        # API error logger
        self.api_error_logger = self._create_file_logger(
            'TripJackAPIError',
            TripJackConfig.API_ERROR_LOG
        )
    
    def _create_file_logger(self, name, file_path):
        """Create a file-only logger for specific error types"""
        logger = logging.getLogger(name)
        logger.setLevel(logging.INFO)
        
        # Remove existing handlers
        logger.handlers.clear()
        
        # Create file handler
        file_handler = logging.FileHandler(file_path)
        file_handler.setLevel(logging.INFO)
        
        # Simple formatter for error logs
        formatter = logging.Formatter('%(asctime)s - %(message)s')
        file_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        return logger
    
    # Main logging methods
    def debug(self, message):
        """Log debug message"""
        self.logger.debug(message)
    
    def info(self, message):
        """Log info message"""
        self.logger.info(message)
    
    def warning(self, message):
        """Log warning message"""
        self.logger.warning(message)
    
    def error(self, message):
        """Log error message"""
        self.logger.error(message)
    
    def critical(self, message):
        """Log critical message"""
        self.logger.critical(message)
    
    # Specialized logging methods
    def log_script_start(self):
        """Log script start with banner"""
        banner = """
================================================================
              TRIPJACK HOTELS FETCHER STARTED
================================================================
Start Time: {start_time}
Test Mode: {test_mode}
Download Images: {download_images}
================================================================
        """.format(
            start_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            test_mode=TripJackConfig.TEST_MODE,
            download_images=TripJackConfig.DOWNLOAD_IMAGES
        ).strip()
        
        self.info(banner)
    
    def log_script_end(self, stats):
        """Log script completion with statistics"""
        banner = """
================================================================
              TRIPJACK HOTELS FETCHER COMPLETED
================================================================
Execution Time: {execution_time:.2f} seconds
Total Hotels Processed: {total_processed}
Successful Hotels: {successful}
Failed Hotels: {failed}
Skipped Hotels: {skipped}
Hotels with No Destination Match: {no_destination}
API Errors: {api_errors}
================================================================
        """.format(
            execution_time=stats.get('execution_time', 0),
            total_processed=stats.get('total_processed', 0),
            successful=stats.get('successful', 0),
            failed=stats.get('failed', 0),
            skipped=stats.get('skipped', 0),
            no_destination=stats.get('no_destination', 0),
            api_errors=stats.get('api_errors', 0)
        ).strip()
        
        self.info(banner)
    
    def log_hotel_processing_start(self, hotel_name, index, total):
        """Log start of hotel processing"""
        self.info(f"[{index}/{total}] Processing hotel: {hotel_name}")
    
    def log_hotel_processing_end(self, hotel_name, success=True, error_msg=None):
        """Log end of hotel processing"""
        if success:
            self.info(f"✓ Successfully processed hotel: {hotel_name}")
        else:
            error_info = f" - {error_msg}" if error_msg else ""
            self.error(f"✗ Failed to process hotel: {hotel_name}{error_info}")
    
    def log_api_request(self, endpoint, hotel_id=None, params=None):
        """Log API request details"""
        hotel_info = f" for hotel {hotel_id}" if hotel_id else ""
        self.debug(f"Making API request to {endpoint}{hotel_info}")
        
        if TripJackConfig.VERBOSE_LOGGING and params:
            self.debug(f"Request parameters: {params}")
    
    def log_api_response(self, endpoint, status_code, data_count=None):
        """Log API response details"""
        count_info = f" with {data_count} items" if data_count is not None else ""
        self.debug(f"API response from {endpoint}: {status_code}{count_info}")
    
    def log_api_error(self, endpoint, error_msg, hotel_id=None):
        """Log API errors to separate file and main log"""
        hotel_info = f" (Hotel ID: {hotel_id})" if hotel_id else ""
        error_msg = f"API Error - {endpoint}: {error_msg}{hotel_info}"
        
        self.error(error_msg)
        self.api_error_logger.error(error_msg)
    
    def log_destination_match(self, hotel_name, city, state, country, matched_destination):
        """Log destination matching results"""
        if matched_destination:
            self.debug(f"Hotel '{hotel_name}' matched to destination: {matched_destination}")
        else:
            location_str = f"{city}, {state}, {country}".strip(', ')
            no_match_msg = f"Hotel '{hotel_name}' - No destination match for location: {location_str}"
            
            self.warning(no_match_msg)
            self.no_destination_logger.info(no_match_msg)
    
    def log_hotel_creation(self, hotel_name, hotel_id, destination, rooms_count=0):
        """Log successful hotel creation"""
        self.info(f"Created hotel: {hotel_name} (ID: {hotel_id}) in destination: {destination} with {rooms_count} rooms")
    
    def log_hotel_skip(self, hotel_name, reason):
        """Log why a hotel was skipped"""
        self.info(f"Skipped hotel '{hotel_name}': {reason}")
    
    def log_hotel_failure(self, hotel_name, hotel_id, error_msg):
        """Log hotel processing failure to separate file and main log"""
        failure_msg = f"Failed to process hotel: {hotel_name} (ID: {hotel_id}) - {error_msg}"
        
        self.error(failure_msg)
        self.failed_hotels_logger.error(failure_msg)
    
    def log_image_processing(self, hotel_name, image_count, successful_count):
        """Log image processing results"""
        if successful_count == image_count:
            self.debug(f"Successfully processed all {image_count} images for {hotel_name}")
        else:
            self.warning(f"Processed {successful_count}/{image_count} images for {hotel_name}")
    
    def log_batch_progress(self, current_batch, total_batches, hotels_in_batch):
        """Log batch processing progress"""
        self.info(f"Processing batch {current_batch}/{total_batches} ({hotels_in_batch} hotels)")
    
    def log_pagination_info(self, page_info):
        """Log pagination information"""
        if page_info.get('next'):
            self.debug(f"Next page token available: {page_info['next'][:20]}...")
        else:
            self.info("Reached last page of hotel data")
    
    def log_validation_error(self, hotel_name, field, error):
        """Log data validation errors"""
        self.warning(f"Validation error for hotel '{hotel_name}' - {field}: {error}")
    
    def log_database_operation(self, operation, model, count=1, success=True):
        """Log database operations"""
        status = "successful" if success else "failed"
        self.debug(f"Database {operation} for {model}: {count} records {status}")
    
    def log_memory_usage(self, message=""):
        """Log current memory usage (if available)"""
        try:
            import psutil
            process = psutil.Process()
            memory_mb = process.memory_info().rss / 1024 / 1024
            self.debug(f"Memory usage: {memory_mb:.1f} MB {message}")
        except ImportError:
            pass  # psutil not available
    
    def log_exception(self, exception, context=""):
        """Log exception with context"""
        context_str = f" during {context}" if context else ""
        self.error(f"Exception{context_str}: {str(exception)}")
        
        # Log full traceback in debug mode
        if TripJackConfig.DEBUG_MODE:
            import traceback
            self.debug(f"Full traceback: {traceback.format_exc()}")
    
    def log_empty_response(self, endpoint, params=None):
        """Log empty API responses to separate file"""
        empty_msg = f"Empty response from {endpoint}"
        if params:
            empty_msg += f" with params: {params}"
        
        self.warning(empty_msg)
        self.empty_response_logger.info(empty_msg)
    
    def log_rate_limit(self, wait_time):
        """Log rate limiting delays"""
        self.info(f"Rate limiting: waiting {wait_time} seconds before next request")
    
    def log_duplicate_hotel(self, hotel_name, hotel_id):
        """Log when a hotel already exists"""
        self.debug(f"Hotel already exists in database: {hotel_name} (ID: {hotel_id})")
    
    def log_search_results(self, hotel_id, search_results_count):
        """Log hotel search results"""
        if search_results_count > 0:
            self.debug(f"Hotel search for {hotel_id} returned {search_results_count} results")
        else:
            self.warning(f"Hotel search for {hotel_id} returned no results")
    
    def log_detail_fetch(self, search_id, success=True):
        """Log hotel detail fetch results"""
        if success:
            self.debug(f"Successfully fetched hotel details for search ID: {search_id}")
        else:
            self.warning(f"Failed to fetch hotel details for search ID: {search_id}")
    
    def log_room_processing(self, hotel_name, rooms_processed, rooms_failed):
        """Log room processing results"""
        if rooms_failed == 0:
            self.debug(f"Successfully processed all {rooms_processed} rooms for {hotel_name}")
        else:
            self.warning(f"Processed {rooms_processed} rooms, {rooms_failed} failed for {hotel_name}")
    
    def log_facility_processing(self, hotel_name, facilities_count):
        """Log facility processing results"""
        self.debug(f"Processed {facilities_count} facilities for {hotel_name}")
    
    def log_contact_processing(self, hotel_name, has_contact_info):
        """Log contact information processing"""
        if has_contact_info:
            self.debug(f"Processed contact information for {hotel_name}")
        else:
            self.debug(f"No contact information available for {hotel_name}")
    
    def log_configuration_loaded(self):
        """Log configuration loading"""
        self.info("TripJack configuration loaded successfully")
        
        if TripJackConfig.TEST_MODE:
            self.warning(f"Running in TEST MODE - limited to {TripJackConfig.TEST_MODE_HOTEL_LIMIT} hotels")
        
        if not TripJackConfig.DOWNLOAD_IMAGES:
            self.warning("Image downloading is DISABLED")
    
    def log_prerequisites_check(self, api_key_valid, db_connected):
        """Log prerequisites validation"""
        self.info("Checking prerequisites...")
        
        if api_key_valid:
            self.info("✓ TripJack API key is configured")
        else:
            self.error("✗ TripJack API key is missing or invalid")
        
        if db_connected:
            self.info("✓ Database connection is working")
        else:
            self.error("✗ Database connection failed")
        
        if api_key_valid and db_connected:
            self.info("All prerequisites satisfied")
            return True
        else:
            self.error("Prerequisites check failed")
            return False 