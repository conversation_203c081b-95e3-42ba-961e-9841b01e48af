"""
Image downloader utility for TripJack Hotels Fetcher
Downloads images from TripJack URLs and stores them in S3 storage
"""

import requests
import os
import time
import hashlib
from urllib.parse import urlparse
from typing import Optional, Dict, List, Any
from django.core.files.base import ContentFile
from django.core.files.storage import default_storage
from base.storage_utils import get_random_name, get_mime_type_for_extension
from .config import TripJackConfig
from .logger import TripJackLogger


class TripJackImageDownloader:
    """Download and store TripJack hotel images to S3"""
    
    def __init__(self, logger=None, max_hotel_images=None, max_room_images=None):
        self.logger = logger or TripJackLogger()
        
        # Set image limits from parameters or use config defaults
        self.max_hotel_images = max_hotel_images or TripJackConfig.MAX_IMAGES_PER_HOTEL
        self.max_room_images = max_room_images or TripJackConfig.MAX_IMAGES_PER_ROOM
        
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
        
        # Statistics tracking
        self.stats = {
            'total_downloads_attempted': 0,
            'successful_downloads': 0,
            'failed_downloads': 0,
            'skipped_downloads': 0,
            'total_bytes_downloaded': 0,
            'average_download_time': 0,
            'cache_hits': 0
        }
        
        # Cache downloaded images by URL hash to avoid duplicates
        self._download_cache = {}
        
        self.logger.info("TripJack Image Downloader initialized")
        
        if not TripJackConfig.DOWNLOAD_IMAGES:
            self.logger.warning("Image downloading is DISABLED in configuration")
    
    def close(self):
        """Close the requests session"""
        try:
            if hasattr(self, 'session') and self.session:
                self.session.close()
        except Exception as e:
            if hasattr(self, 'logger'):
                self.logger.log_exception(e, "closing image downloader session")
    
    def download_and_store_hotel_images(self, images_data: List[Dict], hotel_id: str, 
                                      hotel_name: str) -> List[Optional[str]]:
        """
        Download and store multiple hotel images
        
        Args:
            images_data: List of image data from TripJack API
            hotel_id: TripJack hotel ID
            hotel_name: Hotel name for logging
            
        Returns:
            List of uploaded file paths (None for failed downloads)
        """
        if not TripJackConfig.DOWNLOAD_IMAGES:
            self.logger.debug(f"Image downloading disabled, skipping {len(images_data)} images for {hotel_name}")
            return []
        
        if not images_data:
            self.logger.debug(f"No images to download for hotel: {hotel_name}")
            return []
        
        # Limit number of images
        max_images = self.max_hotel_images
        if len(images_data) > max_images:
            self.logger.info(f"Limiting hotel images from {len(images_data)} to {max_images} for {hotel_name}")
            images_data = images_data[:max_images]
        
        uploaded_files = []
        successful_count = 0
        
        for i, image_data in enumerate(images_data):
            try:
                # Extract image URL
                self.logger.debug(f"Processing image {i+1}: {image_data}")
                image_url = self._extract_image_url(image_data)
                
                if not image_url:
                    self.logger.warning(f"No valid image URL found in image data: {image_data}")
                    uploaded_files.append(None)
                    continue
                
                self.logger.debug(f"Extracted image URL: {image_url}")
                
                # Generate filename
                filename = self._generate_filename(image_url, f"hotel_{hotel_id}_{i}")
                
                # Download and upload image
                result = self.download_and_store_image(image_url, filename, f"hotel_{hotel_name}")
                
                if result['success']:
                    uploaded_files.append(result['file_path'])
                    successful_count += 1
                    self.logger.debug(f"Successfully processed hotel image {i+1}: {result['file_path']}")
                else:
                    uploaded_files.append(None)
                    self.logger.warning(f"Failed to download hotel image {i+1}: {result['error']}")
                
                # Rate limiting between downloads
                if i < len(images_data) - 1:  # Don't sleep after last image
                    time.sleep(TripJackConfig.IMAGE_RETRY_DELAY)
                    
            except Exception as e:
                self.logger.log_exception(e, f"processing hotel image {i+1} for {hotel_name}")
                uploaded_files.append(None)
        
        # Log results
        self.logger.log_image_processing(hotel_name, len(images_data), successful_count)
        
        return uploaded_files
    
    def download_and_store_room_images(self, images_data: List[Dict], room_name: str, 
                                     hotel_name: str) -> List[Optional[str]]:
        """
        Download and store room images
        
        Args:
            images_data: List of image data from TripJack API
            room_name: Room name for logging
            hotel_name: Hotel name for logging
            
        Returns:
            List of uploaded file paths (None for failed downloads)
        """
        if not TripJackConfig.DOWNLOAD_IMAGES:
            self.logger.debug(f"Image downloading disabled, skipping room images for {room_name}")
            return []
        
        if not images_data:
            return []
        
        # Limit number of room images
        max_images = self.max_room_images
        if len(images_data) > max_images:
            images_data = images_data[:max_images]
        
        uploaded_files = []
        successful_count = 0
        
        for i, image_data in enumerate(images_data):
            try:
                # Extract image URL
                image_url = self._extract_image_url(image_data)
                
                if not image_url:
                    uploaded_files.append(None)
                    continue
                
                # Generate filename
                safe_room_name = self._sanitize_filename(room_name)
                filename = self._generate_filename(image_url, f"room_{safe_room_name}_{i}")
                
                # Download and upload image
                result = self.download_and_store_image(image_url, filename, f"room_{room_name}")
                
                if result['success']:
                    uploaded_files.append(result['file_path'])
                    successful_count += 1
                else:
                    uploaded_files.append(None)
                
                # Rate limiting
                if i < len(images_data) - 1:
                    time.sleep(TripJackConfig.IMAGE_RETRY_DELAY)
                    
            except Exception as e:
                self.logger.log_exception(e, f"processing room image {i+1} for {room_name}")
                uploaded_files.append(None)
        
        if successful_count > 0:
            self.logger.debug(f"Downloaded {successful_count}/{len(images_data)} images for room: {room_name}")
        
        return uploaded_files
    
    def download_and_store_image(self, image_url: str, filename: str, context: str = "") -> Dict[str, Any]:
        """
        Download image from URL and store in S3
        
        Args:
            image_url: URL of the image to download
            filename: Filename for the stored image
            context: Context for logging (e.g., "hotel_HotelName")
            
        Returns:
            dict: {'success': bool, 'file_path': str, 'error': str}
        """
        start_time = time.time()
        self.stats['total_downloads_attempted'] += 1
        
        try:
            # Validate URL
            if not self._is_valid_image_url(image_url):
                error_msg = f"Invalid image URL: {image_url}"
                self.stats['failed_downloads'] += 1
                return {'success': False, 'file_path': None, 'error': error_msg}
            
            # Check cache
            url_hash = self._get_url_hash(image_url)
            if url_hash in self._download_cache:
                self.stats['cache_hits'] += 1
                cached_path = self._download_cache[url_hash]
                self.logger.debug(f"Cache hit for image: {image_url} -> {cached_path}")
                return {'success': True, 'file_path': cached_path, 'error': None}
            
            # Download image
            download_result = self._download_image_content(image_url)
            
            if not download_result['success']:
                self.stats['failed_downloads'] += 1
                return download_result
            
            # Validate image content
            if not self._validate_image_content(download_result['content'], image_url):
                error_msg = "Invalid image content"
                self.stats['failed_downloads'] += 1
                return {'success': False, 'file_path': None, 'error': error_msg}
            
            # Store image
            storage_result = self._store_image_content(
                download_result['content'], 
                filename,
                download_result['content_type']
            )
            
            if storage_result['success']:
                # Update cache
                self._download_cache[url_hash] = storage_result['file_path']
                
                # Update statistics
                self.stats['successful_downloads'] += 1
                self.stats['total_bytes_downloaded'] += len(download_result['content'])
                
                download_time = time.time() - start_time
                self._update_average_download_time(download_time)
                
                self.logger.debug(f"Successfully downloaded and stored image: {filename} "
                                f"({len(download_result['content'])} bytes in {download_time:.2f}s)")
                
                return storage_result
            else:
                self.stats['failed_downloads'] += 1
                return storage_result
                
        except Exception as e:
            self.stats['failed_downloads'] += 1
            error_msg = f"Exception during image download: {str(e)}"
            self.logger.log_exception(e, f"downloading image {image_url} for {context}")
            return {'success': False, 'file_path': None, 'error': error_msg}
    
    def _extract_image_url(self, image_data: Dict) -> Optional[str]:
        """Extract the best image URL from TripJack image data"""
        if isinstance(image_data, str):
            return image_data
        
        if isinstance(image_data, dict):
            # Try different URL fields
            url_fields = ['url', 'src', 'href', 'link']
            
            for field in url_fields:
                if field in image_data and image_data[field]:
                    url = image_data[field].strip()
                    if self._is_valid_image_url(url):
                        return url
            
            # Try to get the highest quality image
            if 'sz' in image_data:
                # TripJack often has different sizes - prefer larger sizes
                size = image_data.get('sz', '').lower()
                url = image_data.get('url', '')
                
                # Prefer larger sizes
                if size in ['xl', 'xxl', 'large', 'standard'] and url:
                    return url
        
        return None
    
    def _is_valid_image_url(self, url: str) -> bool:
        """Validate if URL is a valid image URL"""
        if not url or not isinstance(url, str):
            return False
        
        # Must start with http/https
        if not url.startswith(('http://', 'https://')):
            return False
        
        # Check for valid image extensions
        parsed_url = urlparse(url.lower())
        path = parsed_url.path
        
        # Extract extension
        if '.' in path:
            extension = path.split('.')[-1].split('?')[0]  # Remove query params
            if extension in TripJackConfig.ALLOWED_IMAGE_EXTENSIONS:
                return True
        
        # Some image URLs might not have extensions, check domain patterns
        valid_domains = [
            'travelapi.com',
            'tripjack.com',
            'cloudfront.net',
            'amazonaws.com'
        ]
        
        domain = parsed_url.netloc.lower()
        return any(valid_domain in domain for valid_domain in valid_domains)
    
    def _download_image_content(self, image_url: str) -> Dict[str, Any]:
        """Download image content from URL"""
        try:
            response = self.session.get(
                image_url,
                timeout=TripJackConfig.IMAGE_DOWNLOAD_TIMEOUT,
                stream=True
            )
            
            # Check response status
            if response.status_code != 200:
                return {
                    'success': False,
                    'content': None,
                    'content_type': None,
                    'error': f"HTTP {response.status_code}"
                }
            
            # Check content type
            content_type = response.headers.get('content-type', '').lower()
            if not content_type.startswith('image/'):
                return {
                    'success': False,
                    'content': None,
                    'content_type': content_type,
                    'error': f"Invalid content type: {content_type}"
                }
            
            # Download content with size limit
            max_size = TripJackConfig.MAX_IMAGE_SIZE_MB * 1024 * 1024
            content = b''
            
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    content += chunk
                    if len(content) > max_size:
                        return {
                            'success': False,
                            'content': None,
                            'content_type': content_type,
                            'error': f"Image too large (>{TripJackConfig.MAX_IMAGE_SIZE_MB}MB)"
                        }
            
            return {
                'success': True,
                'content': content,
                'content_type': content_type,
                'error': None
            }
            
        except requests.exceptions.Timeout:
            return {
                'success': False,
                'content': None,
                'content_type': None,
                'error': f"Download timeout after {TripJackConfig.IMAGE_DOWNLOAD_TIMEOUT} seconds"
            }
        except requests.exceptions.RequestException as e:
            return {
                'success': False,
                'content': None,
                'content_type': None,
                'error': f"Request error: {str(e)}"
            }
        except Exception as e:
            return {
                'success': False,
                'content': None,
                'content_type': None,
                'error': f"Download error: {str(e)}"
            }
    
    def _validate_image_content(self, content: bytes, url: str) -> bool:
        """Validate downloaded image content"""
        if not content:
            return False
        
        # Check minimum size (avoid tiny placeholder images)
        if len(content) < 1024:  # Less than 1KB
            self.logger.debug(f"Image too small ({len(content)} bytes): {url}")
            return False
        
        # Check for common image file signatures
        image_signatures = [
            b'\xFF\xD8\xFF',  # JPEG
            b'\x89PNG\r\n\x1A\n',  # PNG
            b'GIF87a',  # GIF87a
            b'GIF89a',  # GIF89a
            b'RIFF',  # WEBP (starts with RIFF)
            b'BM',  # BMP
        ]
        
        content_start = content[:20]
        for signature in image_signatures:
            if content_start.startswith(signature):
                return True
        
        # For WEBP, check if RIFF is followed by WEBP
        if content_start.startswith(b'RIFF') and b'WEBP' in content[:20]:
            return True
        
        self.logger.debug(f"Unknown image format for URL: {url}")
        return True  # Allow unknown formats for now
    
    def _store_image_content(self, content: bytes, filename: str, content_type: str) -> Dict[str, Any]:
        """Store image content to S3"""
        try:
            # Create ContentFile
            image_file = ContentFile(content, name=filename)
            
            # Generate unique filename to avoid conflicts
            unique_filename = get_random_name(filename)
            
            # Store in default storage (S3)
            file_path = default_storage.save(unique_filename, image_file)
            
            return {
                'success': True,
                'file_path': file_path,
                'error': None
            }
            
        except Exception as e:
            error_msg = f"Storage error: {str(e)}"
            self.logger.log_exception(e, f"storing image content for {filename}")
            return {
                'success': False,
                'file_path': None,
                'error': error_msg
            }
    
    def _generate_filename(self, url: str, prefix: str = "") -> str:
        """Generate filename from URL and prefix"""
        try:
            parsed = urlparse(url)
            original_name = os.path.basename(parsed.path)
            
            # Extract extension
            extension = 'jpg'  # Default extension
            if '.' in original_name:
                ext = original_name.split('.')[-1].lower()
                if ext in TripJackConfig.ALLOWED_IMAGE_EXTENSIONS:
                    extension = ext
            
            # Create filename
            if prefix:
                safe_prefix = self._sanitize_filename(prefix)
                filename = f"{safe_prefix}_{int(time.time())}.{extension}"
            else:
                filename = f"tripjack_image_{int(time.time())}.{extension}"
            
            return filename
            
        except Exception:
            # Fallback filename
            return f"{prefix}_{int(time.time())}.jpg" if prefix else f"tripjack_image_{int(time.time())}.jpg"
    
    def _sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for safe storage"""
        if not filename:
            return "unknown"
        
        # Replace spaces and special characters
        safe_chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-_"
        sanitized = ''.join(c if c in safe_chars else '_' for c in filename)
        
        # Remove multiple consecutive underscores
        while '__' in sanitized:
            sanitized = sanitized.replace('__', '_')
        
        # Trim and ensure not empty
        sanitized = sanitized.strip('_')
        return sanitized if sanitized else "unknown"
    
    def _get_url_hash(self, url: str) -> str:
        """Generate hash for URL caching"""
        return hashlib.md5(url.encode('utf-8')).hexdigest()
    
    def _update_average_download_time(self, download_time: float):
        """Update average download time statistic"""
        current_avg = self.stats['average_download_time']
        successful_count = self.stats['successful_downloads']
        
        if successful_count == 1:
            self.stats['average_download_time'] = download_time
        else:
            # Calculate running average
            self.stats['average_download_time'] = ((current_avg * (successful_count - 1)) + download_time) / successful_count
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get download statistics"""
        return self.stats.copy()
    
    def log_statistics(self):
        """Log download statistics"""
        stats = self.get_statistics()
        total_attempted = stats['total_downloads_attempted']
        success_rate = (stats['successful_downloads'] / max(total_attempted, 1)) * 100
        
        self.logger.info(f"""Image Downloader Statistics:
        Total Downloads Attempted: {stats['total_downloads_attempted']}
        Successful Downloads: {stats['successful_downloads']}
        Failed Downloads: {stats['failed_downloads']}
        Skipped Downloads: {stats['skipped_downloads']}
        Success Rate: {success_rate:.1f}%
        Total Bytes Downloaded: {stats['total_bytes_downloaded']:,}
        Average Download Time: {stats['average_download_time']:.2f}s
        Cache Hits: {stats['cache_hits']}""")
    
    def clear_cache(self):
        """Clear the download cache"""
        self._download_cache.clear()
        self.stats['cache_hits'] = 0
        self.logger.debug("Image download cache cleared")
    
    def __enter__(self):
        """Context manager entry"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit with cleanup"""
        self.close() 