# TripJack Hotels Fetcher

## Overview
This script fetches hotel data from the TripJack API and stores it in our database with proper relationships and S3 image storage. It automatically matches hotels with destinations in our platform and creates comprehensive hotel records with rooms, facilities, contact information, and images.

==============================================================
# Simple test run (50 hotels, 3 destinations)
python fetch_tripjack_hotels.py

# Production run (all hotels)
python fetch_tripjack_hotels.py --production

# Without images (faster)
python fetch_tripjack_hotels.py --no-images
===============================================================

## Key Features
- 🔄 **Automated Hotel Fetching**: Retrieves hotels from TripJack API with pagination support
- 🎯 **Smart Destination Matching**: Automatically matches hotel locations with platform destinations using fuzzy matching
- 🏨 **Complete Hotel Records**: Creates hotels with rooms, pricing, facilities, and contact information
- 🖼️ **Image Management**: Downloads and stores hotel images in S3 with deduplication
- 🔍 **Three-Step Process**: List → Search → Detail API calls for complete hotel data
- 📊 **Comprehensive Logging**: Detailed logs for monitoring, debugging, and error tracking
- 🧪 **Test Mode**: Limited processing for safe testing and development

## Quick Start

### Test Mode (Recommended First Run)
```bash
# Process limited hotels for testing (default: 50 hotels, 3 destinations)
python manage.py shell -c "
import sys
sys.path.append('base/tripjack_hotels_fetcher')
from main import main
main()
"
```

### Production Mode
```bash
# Process all hotels (disable test mode in config.py first)
python manage.py shell -c "
import sys
sys.path.append('base/tripjack_hotels_fetcher')
from main import main
main()
"
```

### Single Hotel Testing
```bash
# Process a specific hotel ID for debugging
python manage.py shell -c "
import sys
sys.path.append('base/tripjack_hotels_fetcher')
from main import TripJackHotelsFetcher
fetcher = TripJackHotelsFetcher()
fetcher.process_single_hotel_by_id('12345678')
"
```

## Configuration

### Test Mode Settings
**Test Mode (Default: ON)**: Processes limited hotels and destinations for safe testing.

- **Enable**: Set `TEST_MODE = True` in `config.py` (default)
- **Disable**: Set `TEST_MODE = False` in `config.py`
- **Hotel Limit**: `TEST_MODE_HOTEL_LIMIT = 50`
- **Destination Limit**: `TEST_MODE_DESTINATION_LIMIT = 3`

### API Configuration
Update the following in your Django settings or `config.py`:

```python
# Required in settings.py
TRIPJACK_API_KEY = 'your_tripjack_api_key_here'
TRIPJACK_API_URL = 'https://api.tripjack.com'  # or your TripJack API URL
```

### Feature Toggles
Enable/disable specific features in `config.py`:
```python
ENABLE_ROOM_PROCESSING = True      # Process hotel rooms and pricing
ENABLE_FACILITY_PROCESSING = True  # Process hotel facilities/amenities
ENABLE_CONTACT_PROCESSING = True   # Process hotel contact information
ENABLE_IMAGE_PROCESSING = True     # Download and store hotel images
DOWNLOAD_IMAGES = True             # Set to False to skip image downloads
```

## Data Flow

### 1. Hotel List Fetching
- Calls TripJack List Hotels API (paginated, ~100 hotels per call)
- Processes all available hotels with proper pagination handling
- Filters out deleted hotels and existing duplicates

### 2. Destination Matching
- Extracts city, state, country from hotel address
- Matches against platform destinations using:
  - **Exact matching**: Direct string matches
  - **Fuzzy matching**: Similarity-based matching (80% threshold)
  - **Partial matching**: Substring matching for compound names
- Only processes hotels that match existing destinations

### 3. Hotel Search & Details
- Calls Search API to get pricing and availability data
- Calls Detail API to get comprehensive hotel information
- Extracts room data, facilities, and pricing

### 4. Data Storage
- Creates hotel records with all related data
- Processes rooms with pricing information
- Stores facilities and contact information
- Downloads and stores images to S3
- Links hotels to matched destinations

## Log Files & Monitoring

### Main Log Files
- **Primary Log**: `logs/tripjack_hotels_fetcher.log` - Main execution log
- **Failed Hotels**: `logs/tripjack_failed_hotels.log` - Hotels that failed processing
- **No Destination Match**: `logs/tripjack_no_destination.log` - Hotels without destination matches
- **Empty Responses**: `logs/tripjack_empty_response.log` - API calls that returned no data
- **API Errors**: `logs/tripjack_api_errors.log` - API-related errors and issues

### Monitoring Commands
```bash
# Monitor real-time execution
tail -f logs/tripjack_hotels_fetcher.log

# Check hotels without destination matches
cat logs/tripjack_no_destination.log

# View failed hotel processing
cat logs/tripjack_failed_hotels.log

# Check API errors
grep "API Error" logs/tripjack_api_errors.log
```

## Destination Matching

### Supported Destinations
The script matches hotels against your platform destinations:

**Countries**: vietnam, usa, turkey, thailand, sri lanka, singapore, russia, philippines, etc.

**Indian States**: kerala, rajasthan, karnataka, maharashtra, tamil nadu, etc.

**Cities**: goa, delhi, agra, varanasi, pondicherry, etc.

### Destination Matching Logic
1. **Exact Match**: Direct string comparison
2. **Partial Match**: Substring matching for compound names
3. **Fuzzy Match**: Similarity-based matching (80% threshold)
4. **Fallback**: Platform destinations list matching

### Adding New Destinations
Update `PLATFORM_DESTINATIONS` in `config.py` or add destinations to your database.

## Troubleshooting

### Common Issues

| Issue | Log Location | Solution |
|-------|-------------|----------|
| **API Authentication Error** | `tripjack_api_errors.log` | Update `TRIPJACK_API_KEY` in settings |
| **No Hotels for Destination** | `tripjack_no_destination.log` | Check if destinations exist in TripJack |
| **Hotel Creation Failed** | `tripjack_failed_hotels.log` | Check database constraints and data validation |
| **Image Download Issues** | `tripjack_hotels_fetcher.log` | Usually temporary - will retry on next run |
| **Database Connection** | `tripjack_hotels_fetcher.log` | Check Django database settings |

### Debug Mode
Enable debug mode in `config.py`:
```python
DEBUG_MODE = True
VERBOSE_LOGGING = True
SAVE_RAW_API_RESPONSES = True  # For API debugging
```

### Memory Management
For large datasets:
```python
# In config.py
MAX_HOTELS_IN_MEMORY = 500      # Reduce if memory issues
MEMORY_CLEANUP_INTERVAL = 50    # Clean up every N hotels
```

## API Rate Limiting

### Default Settings
```python
REQUEST_DELAY = 1.0     # 1 second between requests
MAX_RETRIES = 3         # 3 retry attempts
REQUEST_TIMEOUT = 30    # 30 second timeout
```

### Adjusting for Heavy Load
```python
REQUEST_DELAY = 2.0     # Slower requests
MAX_RETRIES = 5         # More retries
```

## Image Processing

### Image Settings
```python
MAX_IMAGES_PER_HOTEL = 10      # Limit hotel images
MAX_IMAGES_PER_ROOM = 5        # Limit room images
MAX_IMAGE_SIZE_MB = 10         # Size limit per image
IMAGE_DOWNLOAD_TIMEOUT = 30    # Download timeout
```

### Disabling Images
```python
DOWNLOAD_IMAGES = False         # Skip all image downloads
ENABLE_IMAGE_PROCESSING = False # Skip image processing entirely
```

## Statistics & Reporting

The script provides comprehensive statistics:

### Execution Statistics
- Total hotels processed
- Destination match rates
- Success/failure rates
- API call statistics
- Image processing results

### Component Statistics
- API client performance
- Destination matcher cache hits
- Data processor results
- Image downloader metrics

## Database Schema

### Main Models
- **Hotel**: Core hotel information
- **HotelAddress**: Address with geolocation
- **Room**: Room types and pricing
- **HotelMedia**: Hotel images stored in S3
- **Facility**: Hotel amenities and facilities
- **HotelContact**: Contact information

### Relationships
- Hotel → Destination (Many-to-One)
- Hotel → Rooms (One-to-Many)
- Hotel → Address (One-to-One)
- Hotel → Media (One-to-Many)
- Hotel → Facilities (One-to-Many)

## Performance Tips

### For Large Datasets
1. **Enable Test Mode** initially to validate setup
2. **Monitor Memory Usage** and adjust limits if needed
3. **Process in Batches** during off-peak hours
4. **Disable Image Downloads** for faster initial runs
5. **Check Logs Regularly** for early error detection

### Production Deployment
1. Set `TEST_MODE = False`
2. Configure proper API rate limiting
3. Set up log rotation
4. Monitor S3 storage usage
5. Schedule regular runs with incremental updates

## Error Recovery

### Resuming Failed Runs
The script supports incremental updates:
```python
# Resume from specific timestamp
fetcher.run(last_update_time="2025-01-15T10:30")
```

### Handling Duplicates
```python
ENABLE_DUPLICATE_CHECK = True    # Skip existing hotels
SKIP_EXISTING_HOTELS = True      # Skip hotels already in database
```

## Support

### Getting Help
1. Check the log files for specific errors
2. Enable debug mode for detailed information  
3. Use test mode to isolate issues
4. Process single hotels for debugging

### Common Commands
```bash
# Check configuration
python manage.py shell -c "from base.tripjack_hotels_fetcher.config import TripJackConfig; print(TripJackConfig.TEST_MODE)"

# Test API connection
python manage.py shell -c "
from base.tripjack_hotels_fetcher.api_client import TripJackAPIClient
from base.tripjack_hotels_fetcher.logger import TripJackLogger
client = TripJackAPIClient(TripJackLogger())
print(client.test_api_connection())
"

# Check destination count
python manage.py shell -c "from packages.models import Destination; print(f'Destinations: {Destination.objects.count()}')"
``` 