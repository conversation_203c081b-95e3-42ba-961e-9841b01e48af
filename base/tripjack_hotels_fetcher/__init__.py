"""
TripJack Hotels Fetcher Package
Fetches and processes hotel data from TripJack API with comprehensive destination matching
"""

__version__ = "1.0.0"
__author__ = "Your Development Team"

# Package components
from .main import TripJackHotelsFetcher
from .config import TripJackConfig
from .logger import <PERSON><PERSON><PERSON><PERSON>og<PERSON>
from .api_client import Trip<PERSON>ack<PERSON><PERSON><PERSON>
from .data_processor import TripJackDataProcessor
from .destination_matcher import TripJackDestinationMatcher
from .image_downloader import TripJackImageDownloader

__all__ = [
    'TripJackHotelsFetcher',
    'TripJackConfig',
    'TripJackLogger',
    'TripJackAPIClient', 
    'TripJackDataProcessor',
    'TripJackDestinationMatcher',
    'TripJackImageDownloader'
] 