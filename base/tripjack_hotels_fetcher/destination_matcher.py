"""
Destination Matcher for TripJack Hotels Fetcher
Handles matching hotel locations with platform destinations
"""

import os
import sys
import django
from typing import List, Dict, Optional, Any
from difflib import SequenceMatcher

# Setup Django environment
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.append(project_root)

# Initialize Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

# Import models after Django setup
from packages.models import Destination
from .config import TripJackConfig
from .logger import TripJackLogger


class TripJackDestinationMatcher:
    """
    Handles matching hotel locations with platform destinations
    Supports exact matching and fuzzy matching for better coverage
    """
    
    def __init__(self, logger=None):
        self.logger = logger or TripJackLogger()
        self.destinations = self._load_destinations()
        self.platform_destinations = TripJackConfig.PLATFORM_DESTINATIONS
        self.fuzzy_threshold = TripJackConfig.FUZZY_MATCH_THRESHOLD
        self.enable_fuzzy = TripJackConfig.ENABLE_FUZZY_MATCHING
        
        # Cache for performance
        self._match_cache = {}
        
        self.logger.info(f"Loaded {len(self.destinations)} destinations from database")
        self.logger.info(f"Platform destinations: {len(self.platform_destinations)}")
        
        if TripJackConfig.TEST_MODE:
            self.logger.warning(f"Test mode: Limited to {TripJackConfig.TEST_MODE_DESTINATION_LIMIT} destinations")
    
    def _load_destinations(self) -> List[str]:
        """Load all destination titles from database"""
        try:
            destinations = list(Destination.objects.values_list('title', flat=True))
            
            if TripJackConfig.TEST_MODE:
                # Limit destinations in test mode
                destinations = destinations[:TripJackConfig.TEST_MODE_DESTINATION_LIMIT]
            
            return [dest.lower().strip() for dest in destinations if dest]
            
        except Exception as e:
            self.logger.log_exception(e, "loading destinations from database")
            return []
    
    def find_matching_destination(self, city: str, state: str, country: str) -> Optional[str]:
        """
        Find matching destination based on city, state, country
        Returns destination title if found, None otherwise
        
        Args:
            city: City name from hotel data
            state: State name from hotel data  
            country: Country name from hotel data
            
        Returns:
            str: Matched destination title or None
        """
        try:
            # Create cache key
            cache_key = f"{city}|{state}|{country}".lower()
            
            # Check cache first
            if cache_key in self._match_cache:
                cached_result = self._match_cache[cache_key]
                if cached_result:
                    self.logger.debug(f"Cache hit for location: {cache_key} -> {cached_result}")
                return cached_result
            
            # Clean and normalize location strings
            locations = [
                self._clean_location_string(city),
                self._clean_location_string(state), 
                self._clean_location_string(country)
            ]
            
            # Remove empty locations
            locations = [loc for loc in locations if loc]
            
            if not locations:
                self._match_cache[cache_key] = None
                return None
            
            # Try exact matching first
            matched_destination = self._exact_match(locations)
            
            # Try fuzzy matching if exact match fails and fuzzy matching is enabled
            if not matched_destination and self.enable_fuzzy:
                matched_destination = self._fuzzy_match(locations)
            
            # Cache the result
            self._match_cache[cache_key] = matched_destination
            
            # Log the match result
            self.logger.log_destination_match(
                f"{city}, {state}, {country}",
                city, state, country,
                matched_destination
            )
            
            return matched_destination
            
        except Exception as e:
            self.logger.log_exception(e, f"matching destination for {city}, {state}, {country}")
            return None
    
    def _clean_location_string(self, location: str) -> str:
        """Clean and normalize location string"""
        if not location:
            return ""
        
        # Convert to lowercase and strip whitespace
        cleaned = location.lower().strip()
        
        # Remove common prefixes/suffixes
        prefixes_to_remove = ['city of ', 'state of ', 'province of ']
        suffixes_to_remove = [' city', ' state', ' province', ' region']
        
        for prefix in prefixes_to_remove:
            if cleaned.startswith(prefix):
                cleaned = cleaned[len(prefix):]
        
        for suffix in suffixes_to_remove:
            if cleaned.endswith(suffix):
                cleaned = cleaned[:-len(suffix)]
        
        # Replace common variations
        replacements = {
            'republic of ': '',
            'united states': 'usa',
            'united kingdom': 'uk',
            'united arab emirates': 'uae',
            'new delhi': 'delhi',
            'bombay': 'mumbai',
            'calcutta': 'kolkata',
            'madras': 'chennai',
            'bangalore': 'bengaluru',
            'poona': 'pune'
        }
        
        for old, new in replacements.items():
            cleaned = cleaned.replace(old, new)
        
        return cleaned.strip()
    
    def _exact_match(self, locations: List[str]) -> Optional[str]:
        """Try exact matching with destinations"""
        for location in locations:
            if not location:
                continue
                
            # Direct match in database destinations
            if location in self.destinations:
                self.logger.debug(f"Exact match found in DB destinations: {location}")
                return location
            
            # Direct match in platform destinations  
            if location in self.platform_destinations:
                self.logger.debug(f"Exact match found in platform destinations: {location}")
                return location
            
            # Partial match for compound names
            for dest in self.destinations:
                if location in dest or dest in location:
                    self.logger.debug(f"Partial match found: {location} <-> {dest}")
                    return dest
            
            for dest in self.platform_destinations:
                if location in dest or dest in location:
                    self.logger.debug(f"Partial match found: {location} <-> {dest}")
                    return dest
        
        return None
    
    def _fuzzy_match(self, locations: List[str]) -> Optional[str]:
        """Try fuzzy matching with destinations using similarity scores"""
        best_match = None
        best_score = 0
        
        all_destinations = self.destinations + self.platform_destinations
        
        for location in locations:
            if not location:
                continue
                
            for destination in all_destinations:
                # Calculate similarity score
                similarity = self._calculate_similarity(location, destination)
                
                if similarity >= self.fuzzy_threshold and similarity > best_score:
                    best_score = similarity
                    best_match = destination
                    
                # Also check if location words are contained in destination
                location_words = set(location.split())
                dest_words = set(destination.split())
                
                # If all location words are in destination words
                if location_words and location_words.issubset(dest_words):
                    word_match_score = len(location_words) / len(dest_words) * 100
                    if word_match_score >= self.fuzzy_threshold and word_match_score > best_score:
                        best_score = word_match_score
                        best_match = destination
        
        if best_match:
            self.logger.debug(f"Fuzzy match found: {locations} -> {best_match} (score: {best_score:.1f})")
        
        return best_match
    
    def _calculate_similarity(self, str1: str, str2: str) -> float:
        """Calculate similarity score between two strings"""
        return SequenceMatcher(None, str1, str2).ratio() * 100
    
    def get_destination_object(self, destination_title: str) -> Optional['Destination']:
        """Get destination object from database by title"""
        try:
            return Destination.objects.get(title=destination_title.lower())
        except Destination.DoesNotExist:
            self.logger.warning(f"Destination not found in database: {destination_title}")
            return None
        except Exception as e:
            self.logger.log_exception(e, f"getting destination object for {destination_title}")
            return None
    
    def validate_destination_match(self, hotel_data: Dict) -> tuple[bool, Optional[str], Optional['Destination']]:
        """
        Validate and find destination match for hotel
        
        Args:
            hotel_data: Hotel data from TripJack API
            
        Returns:
            tuple: (is_valid, destination_title, destination_object)
        """
        try:
            # Extract location information
            address = hotel_data.get('address', {})
            city = address.get('city', {}).get('name', '') if isinstance(address.get('city'), dict) else str(address.get('city', ''))
            state = address.get('state', {}).get('name', '') if isinstance(address.get('state'), dict) else str(address.get('state', ''))
            country = address.get('country', {}).get('name', '') if isinstance(address.get('country'), dict) else str(address.get('country', ''))
            
            # Also check cityName and countryName fields
            if not city:
                city = hotel_data.get('cityName', '')
            if not country:
                country = hotel_data.get('countryName', '')
            
            # Find matching destination
            matched_destination = self.find_matching_destination(city, state, country)
            
            if not matched_destination:
                return False, None, None
            
            # Get destination object from database
            destination_obj = self.get_destination_object(matched_destination)
            
            if not destination_obj:
                return False, matched_destination, None
            
            return True, matched_destination, destination_obj
            
        except Exception as e:
            self.logger.log_exception(e, f"validating destination match for hotel {hotel_data.get('name', 'Unknown')}")
            return False, None, None
    
    def get_match_statistics(self) -> Dict[str, Any]:
        """Get matching statistics"""
        return {
            'total_destinations_loaded': len(self.destinations),
            'platform_destinations_count': len(self.platform_destinations),
            'cache_size': len(self._match_cache),
            'cache_hits': sum(1 for v in self._match_cache.values() if v),
            'cache_misses': sum(1 for v in self._match_cache.values() if not v),
            'fuzzy_matching_enabled': self.enable_fuzzy,
            'fuzzy_threshold': self.fuzzy_threshold
        }
    
    def clear_cache(self):
        """Clear the matching cache"""
        self._match_cache.clear()
        self.logger.debug("Destination matching cache cleared")
    
    def get_cache_stats(self) -> Dict[str, int]:
        """Get cache statistics"""
        total_entries = len(self._match_cache)
        successful_matches = sum(1 for v in self._match_cache.values() if v)
        failed_matches = total_entries - successful_matches
        
        return {
            'total_cache_entries': total_entries,
            'successful_matches': successful_matches,
            'failed_matches': failed_matches,
            'hit_rate': (successful_matches / max(total_entries, 1)) * 100
        }
    
    def log_cache_statistics(self):
        """Log cache statistics"""
        stats = self.get_cache_stats()
        self.logger.info(f"""Destination Matcher Cache Statistics:
        Total Cache Entries: {stats['total_cache_entries']}
        Successful Matches: {stats['successful_matches']}
        Failed Matches: {stats['failed_matches']}
        Hit Rate: {stats['hit_rate']:.1f}%""")
    
    def find_similar_destinations(self, location: str, limit: int = 5) -> List[tuple[str, float]]:
        """
        Find similar destinations for debugging purposes
        
        Args:
            location: Location string to match
            limit: Maximum number of results to return
            
        Returns:
            List of (destination, similarity_score) tuples
        """
        location = self._clean_location_string(location)
        all_destinations = self.destinations + self.platform_destinations
        
        similarities = []
        for dest in all_destinations:
            score = self._calculate_similarity(location, dest)
            similarities.append((dest, score))
        
        # Sort by similarity score (descending) and return top results
        similarities.sort(key=lambda x: x[1], reverse=True)
        return similarities[:limit]
    
    def debug_location_match(self, city: str, state: str, country: str) -> Dict[str, Any]:
        """
        Debug location matching - useful for troubleshooting
        
        Args:
            city: City name
            state: State name  
            country: Country name
            
        Returns:
            Dict with detailed matching information
        """
        cleaned_locations = [
            self._clean_location_string(city),
            self._clean_location_string(state),
            self._clean_location_string(country)
        ]
        
        debug_info = {
            'original_location': f"{city}, {state}, {country}",
            'cleaned_locations': cleaned_locations,
            'exact_matches': [],
            'fuzzy_matches': [],
            'final_match': None
        }
        
        # Check exact matches
        for location in cleaned_locations:
            if location in self.destinations:
                debug_info['exact_matches'].append(('DB', location))
            if location in self.platform_destinations:
                debug_info['exact_matches'].append(('Platform', location))
        
        # Check fuzzy matches
        if self.enable_fuzzy:
            for location in cleaned_locations:
                if location:
                    similar = self.find_similar_destinations(location, 3)
                    debug_info['fuzzy_matches'].extend([(location, dest, score) for dest, score in similar])
        
        # Get final match
        debug_info['final_match'] = self.find_matching_destination(city, state, country)
        
        return debug_info 