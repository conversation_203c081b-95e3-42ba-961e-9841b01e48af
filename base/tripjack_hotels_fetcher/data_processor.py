"""
Data Processor for TripJack Hotels Fetcher
Handles data transformation from TripJack API response to our hotel model format
"""

import os
import sys
import django
import json
from decimal import Decimal, InvalidOperation
from typing import List, Dict, Optional, Any
from datetime import datetime
from django.core.files.base import ContentFile
from django.contrib.gis.geos import Point
from django.db import transaction
from urllib.parse import urlparse

# Setup Django environment
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.append(project_root)

# Initialize Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

# Import models after Django setup
from dynamic_packages.models import (
    Destination, Hotel, Room, HotelAddress, HotelMedia, RoomMedia,
    Country, State, City, Facility, HotelContact
)
from .config import TripJackConfig
from .logger import TripJackLogger
from .image_downloader import TripJackImageDownloader


class TripJackDataProcessor:
    """
    Data processor for transforming TripJack API responses
    Maps API data to our hotel model format with proper validation
    """
    
    def __init__(self, logger=None):
        self.logger = logger or TripJackLogger()
        self.image_downloader = TripJackImageDownloader(self.logger)
        self.field_mapping = TripJackConfig.TRIPJACK_TO_MODEL_FIELD_MAPPING
        self.room_field_mapping = TripJackConfig.ROOM_FIELD_MAPPING
        self.default_values = TripJackConfig.DEFAULT_HOTEL_VALUES
        
        # Statistics tracking
        self.stats = {
            'hotels_processed': 0,
            'hotels_created': 0,
            'hotels_failed': 0,
            'rooms_created': 0,
            'facilities_created': 0,
            'images_processed': 0,
            'validation_errors': 0
        }
        
        self.logger.info("TripJack Data Processor initialized")
    
    def process_hotel_data(self, hotel_data: Dict, destination: Destination, 
                          search_result: Dict, detail_data: Dict) -> Optional[Hotel]:
        """
        Process complete hotel data and create hotel with all related records
        
        Args:
            hotel_data: Hotel data from TripJack list API
            destination: Destination object to associate hotel with
            search_result: Search result data from search API
            detail_data: Detailed hotel data from detail API
            
        Returns:
            Hotel: Created hotel object or None if failed
        """
        try:
            self.stats['hotels_processed'] += 1
            
            # Validate required fields
            if not self._validate_hotel_data(hotel_data):
                self.stats['validation_errors'] += 1
                return None
            
            # Create hotel with all related data in transaction
            with transaction.atomic():
                hotel = self._create_hotel_record(hotel_data, destination, search_result, detail_data)
                
                if hotel:
                    self.stats['hotels_created'] += 1
                    self.logger.log_hotel_creation(
                        hotel.name, 
                        hotel.tripjack_static_id, 
                        destination.title,
                        hotel.rooms.count()
                    )
                    return hotel
                else:
                    self.stats['hotels_failed'] += 1
                    return None
                    
        except Exception as e:
            self.stats['hotels_failed'] += 1
            hotel_name = hotel_data.get('name', 'Unknown')
            self.logger.log_hotel_failure(hotel_name, hotel_data.get('hotelId', ''), str(e))
            self.logger.log_exception(e, f"processing hotel data for {hotel_name}")
            return None
    
    def _validate_hotel_data(self, hotel_data: Dict) -> bool:
        """Validate hotel data has required fields"""
        validation_rules = TripJackConfig.HOTEL_VALIDATION_RULES
        
        # Check required fields
        for field in validation_rules['required_fields']:
            if field not in hotel_data or not hotel_data[field]:
                self.logger.log_validation_error(
                    hotel_data.get('name', 'Unknown'), 
                    field, 
                    'Missing required field'
                )
                return False
        
        # Validate name length
        name = hotel_data.get('name', '')
        min_length = validation_rules['min_name_length']
        max_length = validation_rules['max_name_length']
        
        if len(name) < min_length or len(name) > max_length:
            self.logger.log_validation_error(
                name, 
                'name', 
                f'Length must be between {min_length}-{max_length} characters'
            )
            return False
        
        # Validate rating if present
        rating = hotel_data.get('rating')
        if rating is not None and rating not in validation_rules['valid_ratings']:
            self.logger.log_validation_error(
                name, 
                'rating', 
                f'Invalid rating: {rating}'
            )
            return False
        
        return True
    
    def _create_hotel_record(self, hotel_data: Dict, destination: Destination, 
                           search_result: Dict, detail_data: Dict) -> Optional[Hotel]:
        """Create hotel record with all related data"""
        try:
            # Create address
            address = self._create_hotel_address(hotel_data)
            
            # Extract basic hotel info
            hotel_info = self._extract_hotel_info(hotel_data)
            
            # Extract hotel ID from detail API response
            tripjack_hotel_id = ''
            if detail_data and detail_data.get('hotel'):
                # Extract the hotel ID from detail API response (like "hsid8112566514-61886608")
                tripjack_hotel_id = detail_data['hotel'].get('id', '')
            
            # Create hotel
            hotel = Hotel.objects.create(
                destination=destination,
                tripjack_static_id=hotel_info['tripjack_static_id'],
                tripjack_hotel_id=tripjack_hotel_id,
                address=address,
                name=hotel_info['name'],
                description=hotel_info['description'],
                rating=hotel_info['rating'],
                currency=self.default_values['currency'],
                nationality=self.default_values['nationality'],
                meta_info={
                    'tripjack_list_data': hotel_data,
                    'search_data': search_result,
                    'detail_data': detail_data,
                    'processed_at': datetime.now().isoformat()
                }
            )
            
            # Process related data
            if TripJackConfig.ENABLE_ROOM_PROCESSING:
                self._process_rooms(hotel, search_result, detail_data)
            
            if TripJackConfig.ENABLE_FACILITY_PROCESSING:
                self._process_facilities(hotel, hotel_data.get('facilities', []))
            
            if TripJackConfig.ENABLE_CONTACT_PROCESSING:
                self._process_contact(hotel, hotel_data.get('contact', {}))
            
            if TripJackConfig.ENABLE_IMAGE_PROCESSING:
                # Check for images in both static data and detail data
                static_images = hotel_data.get('images', [])
                detail_images = []
                
                # Extract images from detail data if available
                if detail_data and detail_data.get('hotel'):
                    detail_hotel = detail_data['hotel']
                    detail_images = detail_hotel.get('images', []) or detail_hotel.get('img', [])
                
                # Combine images from both sources (detail data takes priority)
                all_images = detail_images + static_images
                
                self.logger.debug(f"Found {len(static_images)} static images and {len(detail_images)} detail images for {hotel.name}")
                
                if all_images:
                    self._process_hotel_images(hotel, all_images)
                else:
                    self.logger.debug(f"No images found for hotel: {hotel.name}")
                    # Log the available keys for debugging
                    if detail_data and detail_data.get('hotel'):
                        detail_keys = list(detail_data['hotel'].keys())
                        self.logger.debug(f"Available detail data keys: {detail_keys}")
                    static_keys = list(hotel_data.keys())
                    self.logger.debug(f"Available static data keys: {static_keys}")
            
            return hotel
            
        except Exception as e:
            self.logger.log_exception(e, f"creating hotel record for {hotel_data.get('name', 'Unknown')}")
            return None
    
    def _extract_hotel_info(self, hotel_data: Dict) -> Dict[str, Any]:
        """Extract and clean hotel basic information"""
        return {
            'tripjack_static_id': str(hotel_data.get('hotelId', '')),
            'name': hotel_data.get('name', '').strip(),
            'description': self._clean_description(hotel_data.get('description', '')),
            'rating': hotel_data.get('rating'),
        }
    
    def _clean_description(self, description: str) -> str:
        """Clean hotel description from JSON format if needed"""
        if not description:
            return ''
        
        try:
            # TripJack sometimes returns JSON-encoded descriptions
            if description.startswith('{') and description.endswith('}'):
                desc_data = json.loads(description)
                
                # Extract meaningful description parts
                parts = []
                for key, value in desc_data.items():
                    if value and key in ['location', 'rooms', 'attractions', 'business_amenities']:
                        parts.append(f"{key.replace('_', ' ').title()}: {value}")
                
                return ' | '.join(parts)
            else:
                return description.strip()
                
        except (json.JSONDecodeError, Exception):
            return description.strip()
    
    def _create_hotel_address(self, hotel_data: Dict) -> Optional[HotelAddress]:
        """Create hotel address with location data"""
        try:
            address_data = hotel_data.get('address', {})
            geolocation = hotel_data.get('geolocation', {})
            
            # Get or create location entities
            country = self._get_or_create_country(address_data.get('country', {}))
            state = self._get_or_create_state(address_data.get('state', {}))
            city = self._get_or_create_city(address_data.get('city', {}))
            
            # Create location point
            location = None
            if geolocation:
                try:
                    lng = float(geolocation.get('ln', 0))
                    lat = float(geolocation.get('lt', 0))
                    if lng != 0 and lat != 0:  # Avoid 0,0 coordinates
                        location = Point(lng, lat)
                except (ValueError, TypeError):
                    self.logger.warning("Invalid geolocation data in hotel address")
            
            # Create address
            address = HotelAddress.objects.create(
                address_line=address_data.get('adr', ''),
                postal_code=address_data.get('postalCode', ''),
                city=city,
                state=state,
                country=country,
                location=location
            )
            
            return address
            
        except Exception as e:
            self.logger.log_exception(e, "creating hotel address")
            return None
    
    def _get_or_create_country(self, country_data: Dict) -> Optional[Country]:
        """Get or create country object"""
        if not country_data:
            return None
            
        country_name = country_data.get('name', '').lower().strip()
        country_code = country_data.get('code', '')
        
        if not country_name:
            return None
            
        try:
            country, created = Country.objects.get_or_create(
                code=country_code or country_name[:10],
                defaults={'name': country_name}
            )
            
            if created:
                self.logger.debug(f"Created new country: {country_name}")
                
            return country
            
        except Exception as e:
            self.logger.log_exception(e, f"getting or creating country {country_name}")
            return None
    
    def _get_or_create_state(self, state_data: Dict) -> Optional[State]:
        """Get or create state object"""
        if not state_data:
            return None
            
        state_name = state_data.get('name', '').lower().strip()
        
        if not state_name:
            return None
            
        try:
            # Use name as code if no code provided
            state_code = state_data.get('code', state_name[:10])
            
            state, created = State.objects.get_or_create(
                code=state_code,
                defaults={'name': state_name}
            )
            
            if created:
                self.logger.debug(f"Created new state: {state_name}")
                
            return state
            
        except Exception as e:
            self.logger.log_exception(e, f"getting or creating state {state_name}")
            return None
    
    def _get_or_create_city(self, city_data: Dict) -> Optional[City]:
        """Get or create city object"""
        if not city_data:
            return None
            
        city_name = city_data.get('name', '').lower().strip()
        city_code = city_data.get('code', '')
        
        if not city_name:
            return None
            
        try:
            city, created = City.objects.get_or_create(
                code=city_code or city_name[:20],
                defaults={'name': city_name}
            )
            
            if created:
                self.logger.debug(f"Created new city: {city_name}")
            
            return city
            
        except Exception as e:
            self.logger.log_exception(e, f"getting or creating city {city_name}")
            return None
    
    def _process_rooms(self, hotel: Hotel, search_result: Dict, detail_data: Dict):
        """Process and create room objects"""
        try:
            # Get room data from detail API response, not search result
            # According to API docs, room data is in detail_data.hotel.ops[].ris[]
            rooms_processed = 0
            rooms_failed = 0
            
            # Log the structure we're working with
            self.logger.debug(f"Processing rooms for hotel {hotel.name}")
            self.logger.debug(f"Detail data keys: {list(detail_data.keys()) if detail_data else 'No detail data'}")
            
            # Extract hotel data from detail response
            hotel_detail = detail_data.get('hotel', {})
            if not hotel_detail:
                self.logger.warning(f"No hotel detail data found for {hotel.name}")
                return
            
            self.logger.debug(f"Hotel detail keys: {list(hotel_detail.keys())}")
            
            # Get room data from hotel.ops[].ris[] in detail API response
            ops = hotel_detail.get('ops', [])
            if not ops:
                self.logger.debug(f"No ops data found in detail response for hotel {hotel.name}")
                return
            
            self.logger.debug(f"Found {len(ops)} ops entries for hotel {hotel.name}")
            
            for op_index, op in enumerate(ops):
                room_infos = op.get('ris', [])
                self.logger.debug(f"Op {op_index}: Found {len(room_infos)} room infos")
                
                for room_index, room_info in enumerate(room_infos):
                    try:
                        self.logger.debug(f"Processing room {room_index} with keys: {list(room_info.keys())}")
                        room = self._create_room_record(hotel, room_info, op)
                        if room:
                            rooms_processed += 1
                            self.stats['rooms_created'] += 1
                            self.logger.debug(f"Successfully created room: {room.name} (${room.price})")
                            
                            # Update hotel base price (minimum room price)
                            if not hotel.base_price or room.price < hotel.base_price:
                                hotel.base_price = room.price
                                hotel.save()
                        else:
                            rooms_failed += 1
                            
                    except Exception as e:
                        rooms_failed += 1
                        self.logger.log_exception(e, f"creating room for hotel {hotel.name}")
            
            self.logger.log_room_processing(hotel.name, rooms_processed, rooms_failed)
            
            # Log debug info if no rooms found
            if rooms_processed == 0 and rooms_failed == 0:
                self.logger.debug(f"No room data found for hotel {hotel.name}. Detail data keys: {list(hotel_detail.keys())}")
                if ops:
                    self.logger.debug(f"Ops structure: {[list(op.keys()) for op in ops]}")
            
        except Exception as e:
            self.logger.log_exception(e, f"processing rooms for hotel {hotel.name}")
    
    def _create_room_record(self, hotel: Hotel, room_info: Dict, op_info: Dict) -> Optional[Room]:
        """Create individual room record"""
        try:
            # Validate room data
            validation_rules = TripJackConfig.ROOM_VALIDATION_RULES
            
            for field in validation_rules['required_fields']:
                if field not in room_info:
                    self.logger.warning(f"Missing required room field '{field}' for hotel {hotel.name}")
                    return None
            
            # Extract room data using field mapping
            room_data = {}
            for api_field, model_field in self.room_field_mapping.items():
                if api_field in room_info:
                    room_data[model_field] = room_info[api_field]
            
            # Validate price
            price = room_data.get('price', 0)
            if price < validation_rules['min_price']:
                self.logger.warning(f"Invalid room price {price} for hotel {hotel.name}")
                return None
            
            # Create a meaningful room name from available data
            room_name = self._build_room_name(room_info, room_data)
            
            # Create room
            room = Room.objects.create(
                hotel=hotel,
                name=room_name,
                description=room_data.get('description', ''),
                price=Decimal(str(price)),
                max_adults=min(room_data.get('max_adults', 2), validation_rules['max_adults']),
                max_children=min(room_data.get('max_children', 0), validation_rules['max_children']),
                meal_plan=room_data.get('meal_plan', ''),
                meta_info={
                    'tripjack_room_info': room_info,
                    'tripjack_op_info': op_info,
                    'room_id': room_data.get('room_id', ''),
                    'room_type': room_data.get('room_type', ''),
                    'standard_name': room_data.get('standard_name', '')
                }
            )
            
            # Process room facilities if available
            if TripJackConfig.ENABLE_FACILITY_PROCESSING:
                room_facilities = room_info.get('fcs', [])
                if room_facilities:
                    self._process_room_facilities(room, room_facilities)
            
            return room
            
        except Exception as e:
            self.logger.log_exception(e, f"creating room record for hotel {hotel.name}")
            return None
    
    def _build_room_name(self, room_info: Dict, room_data: Dict) -> str:
        """Build a meaningful room name from available data"""
        # Priority order for room name: standard_name > room_category > room_type > default
        name_options = [
            room_data.get('standard_name', ''),
            room_data.get('name', ''),  # This is room category (rc)
            room_data.get('room_type', ''),
            'Standard Room'
        ]
        
        # Use the first non-empty option
        for name in name_options:
            if name and name.strip():
                return name.strip()
        
        return 'Standard Room'
    
    def _process_facilities(self, hotel: Hotel, facilities: List[Dict]):
        """Process and create facility objects for hotel only (no room facilities)"""
        try:
            facilities_created = 0
            processed_amenities = set()  # Track processed amenities to avoid duplicates
            
            for facility_data in facilities:
                try:
                    facility_name = facility_data.get('name', '')
                    
                    # Only process if we have a facility name and it's not already processed
                    if facility_name and facility_name not in processed_amenities:
                        # Create facility with hotel association only - type must be 'Hotel'
                        Facility.objects.create(
                            hotel=hotel,
                            room=None,  # Explicitly set to None for hotel facilities
                            amenities=[facility_name],
                            type='Hotel'  # Always set to 'Hotel' for hotel-level facilities
                        )
                        facilities_created += 1
                        processed_amenities.add(facility_name)
                        
                except Exception as e:
                    self.logger.log_exception(e, f"creating facility for hotel {hotel.name}")
            
            self.stats['facilities_created'] += facilities_created
            self.logger.log_facility_processing(hotel.name, facilities_created)
            
        except Exception as e:
            self.logger.log_exception(e, f"processing facilities for hotel {hotel.name}")
    
    def _process_room_facilities(self, room: Room, room_facilities: List[str]):
        """Process and create facility objects for room"""
        try:
            facilities_created = 0
            
            for facility_name in room_facilities:
                try:
                    # Only process if we have a facility name
                    if facility_name and facility_name.strip():
                        # Create facility with room association - type must be 'Room'
                        Facility.objects.create(
                            hotel=room.hotel,
                            room=room,
                            amenities=[facility_name.strip()],
                            type='Room'  # Set to 'Room' for room-level facilities
                        )
                        facilities_created += 1
                        
                except Exception as e:
                    self.logger.log_exception(e, f"creating room facility for room {room.name}")
            
            if facilities_created > 0:
                self.logger.debug(f"Created {facilities_created} room facilities for {room.name}")
            
        except Exception as e:
            self.logger.log_exception(e, f"processing room facilities for room {room.name}")
    
    def _process_contact(self, hotel: Hotel, contact_data: Dict):
        """Process and create contact information"""
        try:
            phone = contact_data.get('ph', '')
            email = contact_data.get('em', '')
            fax = contact_data.get('fax', '')
            website = contact_data.get('wb', '')
            
            # Parse email if it's a string
            email_list = []
            if email:
                if isinstance(email, str):
                    # Remove brackets and split by comma
                    email_clean = email.strip('[]')
                    email_list = [e.strip() for e in email_clean.split(',') if e.strip()]
                elif isinstance(email, list):
                    email_list = email
            
            # Only create contact if we have some information
            if phone or email_list or fax or website:
                HotelContact.objects.create(
                    hotel=hotel,
                    phone=phone,
                    email=email_list if email_list else None,
                    fax=fax,
                    website=website
                )
                
                self.logger.log_contact_processing(hotel.name, True)
            else:
                self.logger.log_contact_processing(hotel.name, False)
            
        except Exception as e:
            self.logger.log_exception(e, f"processing contact for hotel {hotel.name}")
    
    def _process_hotel_images(self, hotel: Hotel, images: List[Dict]):
        """Process and upload hotel images"""
        try:
            if not images:
                self.logger.debug(f"No images to process for hotel: {hotel.name}")
                return
            
            # Download and store images
            uploaded_files = self.image_downloader.download_and_store_hotel_images(
                images, hotel.tripjack_static_id, hotel.name
            )
            
            # Create HotelMedia records for successful uploads
            images_created = 0
            for file_path in uploaded_files:
                if file_path:
                    try:
                        # Create ContentFile from the stored path
                        HotelMedia.objects.create(
                            hotel=hotel,
                            media=file_path
                        )
                        images_created += 1
                    except Exception as e:
                        self.logger.log_exception(e, f"creating HotelMedia record for {hotel.name}")
            
            self.stats['images_processed'] += images_created
            self.logger.debug(f"Created {images_created} HotelMedia records for {hotel.name}")
            
        except Exception as e:
            self.logger.log_exception(e, f"processing images for hotel {hotel.name}")
    
    def get_statistics(self) -> Dict[str, int]:
        """Get processing statistics"""
        return self.stats.copy()
    
    def log_statistics(self):
        """Log processing statistics"""
        stats = self.get_statistics()
        success_rate = (stats['hotels_created'] / max(stats['hotels_processed'], 1)) * 100
        
        self.logger.info(f"""Data Processor Statistics:
        Hotels Processed: {stats['hotels_processed']}
        Hotels Created: {stats['hotels_created']}
        Hotels Failed: {stats['hotels_failed']}
        Rooms Created: {stats['rooms_created']}
        Facilities Created: {stats['facilities_created']}
        Images Processed: {stats['images_processed']}
        Validation Errors: {stats['validation_errors']}
        Success Rate: {success_rate:.1f}%""")
    
    def reset_statistics(self):
        """Reset processing statistics"""
        self.stats = {
            'hotels_processed': 0,
            'hotels_created': 0,
            'hotels_failed': 0,
            'rooms_created': 0,
            'facilities_created': 0,
            'images_processed': 0,
            'validation_errors': 0
        }
    
    def close(self):
        """Clean up resources"""
        try:
            if hasattr(self, 'image_downloader') and self.image_downloader:
                self.image_downloader.close()
        except Exception as e:
            if hasattr(self, 'logger'):
                self.logger.log_exception(e, "closing data processor")
    
    def __enter__(self):
        """Context manager entry"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit with cleanup"""
        self.close()
