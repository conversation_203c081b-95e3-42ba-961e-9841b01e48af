"""
Configuration file for TripJack Hotels Fetcher Script
Contains all constants, settings and configuration for fetching hotels data from TripJack API
"""

import os
from django.conf import settings
from pathlib import Path


class TripJackConfig:
    """Configuration class for TripJack hotels fetcher script"""
    
    # =============================================
    # API CONFIGURATION
    # =============================================
    
    # TripJack API Configuration
    TRIPJACK_API_KEY = settings.TRIPJACK_API_KEY
    TRIPJACK_BASE_URL = settings.TRIPJACK_API_URL
    
    # Request Headers
    TRIPJACK_HEADERS = {
        'Content-Type': 'application/json',
        'apikey': TRIPJACK_API_KEY,
        'Accept': 'application/json',
        'User-Agent': 'TripJack-Hotels-Fetcher/1.0'
    }
    
    # API Request Parameters
    DEFAULT_SEARCH_PARAMS = {
        'checkinDate': '2025-08-20',  # Updated to future date
        'checkoutDate': '2025-08-21',  # Updated to future date
        'roomInfo': [
            {
                'numberOfAdults': 2,
                'numberOfChild': 0
            }
        ],
        'searchCriteria': {
            'nationality': '106',  # India
            'currency': 'INR'
        },
        'searchPreferences': {
            'ratings': [1, 2, 3, 4, 5],
            'currency': 'INR',
            'fsc': True
        }
    }
    
    # Rate Limiting
    REQUEST_DELAY = 1.0  # Delay between API requests
    MAX_RETRIES = 3      # Number of retries for failed requests
    REQUEST_TIMEOUT = 180  # 3 minutes as originally configured by user
    RETRY_DELAY = 2.0    # Delay between retries in seconds
    
    # =============================================
    # LOGGING CONFIGURATION
    # =============================================
    
    # Log files
    PROJECT_ROOT = Path(__file__).parent.parent.parent
    LOG_DIR = PROJECT_ROOT / 'logs'
    LOG_DIR.mkdir(exist_ok=True)
    
    MAIN_LOG_FILE = LOG_DIR / 'tripjack_hotels_fetcher.log'
    FAILED_HOTELS_LOG = LOG_DIR / 'tripjack_failed_hotels.log'
    NO_DESTINATION_LOG = LOG_DIR / 'tripjack_no_destination.log'
    EMPTY_RESPONSE_LOG = LOG_DIR / 'tripjack_empty_response.log'
    API_ERROR_LOG = LOG_DIR / 'tripjack_api_errors.log'
    
    # Log format and level
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    LOG_LEVEL = 'DEBUG'
    
    # =============================================
    # DATA PROCESSING CONFIGURATION
    # =============================================
    
    # Test mode settings
    TEST_MODE = True  # Set to False for production run
    TEST_MODE_HOTEL_LIMIT = 50  # Process only first 50 hotels in test mode
    TEST_MODE_DESTINATION_LIMIT = 3  # Process only first 3 matching destinations in test mode
    
    # Data processing settings
    DOWNLOAD_IMAGES = True  # Set to False to skip image downloading
    MAX_IMAGES_PER_HOTEL = 10
    MAX_IMAGES_PER_ROOM = 5
    
    # Image processing settings
    IMAGE_DOWNLOAD_TIMEOUT = 30  # Timeout for image downloads in seconds
    IMAGE_RETRY_DELAY = 0.5  # Delay between image downloads in seconds
    MAX_IMAGE_SIZE_MB = 10  # Maximum image size in MB
    ALLOWED_IMAGE_EXTENSIONS = ['jpg', 'jpeg', 'png', 'webp']
    
    # Data validation settings
    MIN_HOTEL_FIELDS = ['hotelId', 'name']  # Minimum required fields from API response
    
    # S3 Storage settings
    S3_BUCKET_NAME = getattr(settings, 'AWS_STORAGE_BUCKET_NAME', 'dev-zuumm-public')
    
    # Batch processing settings
    BULK_CREATE_BATCH_SIZE = 100
    HOTELS_PER_API_CALL = 100  # TripJack returns 100 hotels per API call
    
    # =============================================
    # DESTINATION MATCHING CONFIGURATION
    # =============================================
    
    # Your platform destinations list (all lowercase)
    PLATFORM_DESTINATIONS = [
        # Countries
        'south america', 'vietnam', 'usa', 'turkey', 'thailand', 'sri lanka',
        'south africa', 'singapore', 'seychelles', 'russia', 'philippines',
        'new zealand', 'nepal', 'mauritius', 'maldives', 'malaysia', 'japan',
        'hong kong', 'finland', 'europe', 'egypt', 'dubai', 'abu dhabi',
        'china', 'canada', 'bali', 'azerbaijan', 'australia', 'africa',
        
        # Cities / City-Regions
        'goa', 'varanasi', 'pondicherry', 'lonavala', 'leh, pangong and tso moriri',
        'delhi', 'daman & diu', 'ahmedabad', 'agra',
        
        # States / Regions within Countries (mostly Indian states)
        'west bengal', 'uttarakhand', 'uttar pradesh', 'tamil nadu', 'odisha',
        'maharashtra', 'madhya pradesh', 'lakshadweep', 'ladakh', 'kerala',
        'coorg', 'karnataka', 'rajasthan', 'sikkim', 'sikkim, west bengal',
        'meghalaya and assam', 'meghalaya', 'andhra pradesh', 'andaman',
        'punjab', 'kashmir'
    ]
    
    # Destination matching settings
    ENABLE_FUZZY_MATCHING = True
    FUZZY_MATCH_THRESHOLD = 80  # Percentage similarity for fuzzy matching
    
    # =============================================
    # ERROR HANDLING CONFIGURATION
    # =============================================
    
    # Error categories for logging
    ERROR_CATEGORIES = {
        'API_ERROR': 'API Request Failed',
        'HOTEL_NOT_FOUND': 'Hotel Not Found',
        'DESTINATION_NOT_FOUND': 'Destination Not Found',
        'EMPTY_RESPONSE': 'Empty API Response',
        'DATA_VALIDATION_ERROR': 'Data Validation Failed',
        'DATABASE_ERROR': 'Database Operation Failed',
        'PARSING_ERROR': 'Data Parsing Failed',
        'IMAGE_DOWNLOAD_ERROR': 'Image Download Failed',
        'SEARCH_FAILED': 'Hotel Search Failed',
        'DETAIL_FETCH_FAILED': 'Hotel Detail Fetch Failed'
    }
    
    # Skip rules
    SKIP_DELETED_HOTELS = True
    SKIP_EXISTING_HOTELS = True
    SKIP_HOTELS_WITHOUT_DESTINATION_MATCH = True
    SKIP_HOTELS_WITH_MISSING_REQUIRED_FIELDS = True
    
    # =============================================
    # FIELD MAPPING CONFIGURATION
    # =============================================
    
    # Map TripJack API response fields to our model fields
    TRIPJACK_TO_MODEL_FIELD_MAPPING = {
        # Hotel basic info
        'hotelId': 'tripjack_static_id',
        'name': 'name',
        'description': 'description',
        'rating': 'rating',
        'propertyType': 'property_type',
        
        # Address and location
        'address': 'address_data',
        'geolocation': 'location_data',
        'cityName': 'city_name',
        'countryName': 'country_name',
        
        # Media and facilities
        'images': 'images_data',
        'facilities': 'facilities_data',
        'contact': 'contact_data',
        
        # Status
        'isDeleted': 'is_deleted'
    }
    
    # Room data mapping based on TripJack API documentation
    ROOM_FIELD_MAPPING = {
        'rc': 'name',           # Room category -> name
        'rt': 'room_type',      # Room type
        'srn': 'standard_name', # Standard room name
        'des': 'description',   # Room description
        'tp': 'price',          # Total price
        'adt': 'max_adults',    # Number of adults
        'chd': 'max_children',  # Number of children
        'mb': 'meal_plan',      # Meal basis
        'id': 'room_id'         # Internal room identifier
    }
    
    # Default values for hotel creation
    DEFAULT_HOTEL_VALUES = {
        'currency': 'INR',
        'nationality': '106',
        'rating': None,
        'base_price': None
    }
    
    # =============================================
    # API ENDPOINTS CONFIGURATION
    # =============================================
    
    # TripJack API endpoints
    API_ENDPOINTS = {
        'FETCH_STATIC_HOTELS': '/hms/v1/fetch-static-hotels',
        'HOTEL_SEARCH_LIST': '/hms/v1/hotel-searchquery-list',
        'HOTEL_DETAIL': '/hms/v1/hotelDetail-search'
    }
    
    # =============================================
    # VALIDATION RULES
    # =============================================
    
    # Hotel validation rules
    HOTEL_VALIDATION_RULES = {
        'min_name_length': 3,
        'max_name_length': 255,
        'required_fields': ['hotelId', 'name'],
        'valid_ratings': [1, 2, 3, 4, 5],
        'max_description_length': 5000
    }
    
    # Room validation rules based on TripJack API documentation
    ROOM_VALIDATION_RULES = {
        'min_price': 0,
        'max_adults': 10,
        'max_children': 5,
        'required_fields': ['id', 'tp']  # Updated to use 'id' and 'tp' (total price) as required fields
    }
    
    # =============================================
    # PERFORMANCE SETTINGS
    # =============================================
    
    # Database connection settings
    DB_CONNECTION_POOL_SIZE = 5
    DB_CONNECTION_TIMEOUT = 30
    
    # Memory management
    MAX_HOTELS_IN_MEMORY = 1000
    MEMORY_CLEANUP_INTERVAL = 100  # Clean up every N hotels processed
    
    # Monitoring and reporting
    PROGRESS_REPORT_INTERVAL = 50  # Report progress every N hotels
    DETAILED_LOGGING = True
    
    # =============================================
    # FEATURE FLAGS
    # =============================================
    
    # Feature toggles
    ENABLE_ROOM_PROCESSING = True
    ENABLE_FACILITY_PROCESSING = True
    ENABLE_CONTACT_PROCESSING = True
    ENABLE_IMAGE_PROCESSING = True
    ENABLE_DUPLICATE_CHECK = True
    ENABLE_DATA_VALIDATION = True
    ENABLE_PROGRESS_TRACKING = True
    
    # Debugging features
    DEBUG_MODE = False
    SAVE_RAW_API_RESPONSES = False  # Save raw responses for debugging
    VERBOSE_LOGGING = True 