"""
API Client for TripJack Hotels Fetcher
Handles all API requests to TripJack with proper error handling and rate limiting
"""

import requests
import time
import json
import uuid
from typing import List, Dict, Optional, Any
from urllib.parse import urlencode
from .config import TripJackConfig
from .logger import TripJ<PERSON>Logger


class TripJackAPIClient:
    """
    API client for making requests to TripJack Hotel APIs
    Handles rate limiting, retries, and error management
    """
    
    def __init__(self, logger=None):
        self.logger = logger or TripJackLogger()
        self.base_url = TripJackConfig.TRIPJACK_BASE_URL
        self.headers = TripJackConfig.TRIPJACK_HEADERS.copy()
        
        # Rate limiting and retry settings
        self.request_delay = TripJackConfig.REQUEST_DELAY
        self.max_retries = TripJackConfig.MAX_RETRIES
        self.timeout = TripJackConfig.REQUEST_TIMEOUT
        
        # Track last request time for rate limiting
        self._last_request_time = 0
        
        # Circuit breaker pattern for API health
        self.consecutive_failures = 0
        self.max_consecutive_failures = 25  # Increased from 10 to 25 - less aggressive
        self.circuit_breaker_timeout = 60   # Reduced from 300 to 60 seconds - faster recovery
        self.circuit_breaker_triggered_at = None
        
        # Create a persistent session for better performance
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        
        # Statistics tracking
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'rate_limited_requests': 0,
            'retry_attempts': 0,
            'circuit_breaker_triggered': 0
        }
    
    def log_api_health_status(self):
        """Log current API health and statistics"""
        total_requests = self.stats['total_requests']
        if total_requests == 0:
            self.logger.info("No API requests made yet")
            return
        
        success_rate = (self.stats['successful_requests'] / total_requests) * 100
        failure_rate = (self.stats['failed_requests'] / total_requests) * 100
        
        status = "🟢 HEALTHY" if success_rate >= 80 else "🟡 DEGRADED" if success_rate >= 50 else "🔴 UNHEALTHY"
        
        if self._is_circuit_breaker_open():
            status = "⭕ CIRCUIT BREAKER OPEN"
        
        self.logger.info(f"""
        TripJack API Health Status: {status}
        =====================================
        Total Requests: {total_requests}
        Successful: {self.stats['successful_requests']} ({success_rate:.1f}%)
        Failed: {self.stats['failed_requests']} ({failure_rate:.1f}%)
        Rate Limited: {self.stats['rate_limited_requests']}
        Retry Attempts: {self.stats['retry_attempts']}
        Circuit Breaker Triggered: {self.stats['circuit_breaker_triggered']} times
        Consecutive Failures: {self.consecutive_failures}
        """)
    
    def close(self):
        """Close the requests session"""
        try:
            if hasattr(self, 'session') and self.session:
                self.session.close()
        except Exception as e:
            if hasattr(self, 'logger'):
                self.logger.log_exception(e, "closing API client session")
    
    def validate_configuration(self):
        """Validate API client configuration"""
        try:
            # Check API key
            if not TripJackConfig.TRIPJACK_API_KEY:
                self.logger.error("TripJack API key is not configured")
                return False
            
            # Check base URL
            if not TripJackConfig.TRIPJACK_BASE_URL:
                self.logger.error("TripJack base URL is not configured")
                return False
            
            self.logger.info("API client configuration is valid")
            return True
            
        except Exception as e:
            self.logger.log_exception(e, "validating API client configuration")
            return False
    
    def test_api_connection(self):
        """Test basic API connectivity"""
        try:
            # Try to fetch first page of hotels to test connection
            test_response = self.fetch_static_hotels_initial()
            
            if test_response and not test_response.get('error'):
                self.logger.info("API connection test successful")
                return True
            else:
                self.logger.error("API connection test failed")
                return False
                
        except Exception as e:
            self.logger.log_exception(e, "testing API connection")
            return False
    
    def fetch_static_hotels_initial(self, last_update_time: Optional[str] = None) -> Optional[Dict]:
        """
        Fetch first set of static hotels data from TripJack
        
        Args:
            last_update_time: Optional timestamp in format 'YYYY-MM-DDTHH:MM'
            
        Returns:
            dict: API response data with hotels or None if failed
        """
        try:
            endpoint = TripJackConfig.API_ENDPOINTS['FETCH_STATIC_HOTELS']
            
            # Prepare request payload
            payload = {}
            if last_update_time:
                payload['lastUpdateTime'] = last_update_time
            
            # Log API request
            self.logger.log_api_request(endpoint, params=payload)
            
            # Make API request with retries
            response_data = self._make_request_with_retries(
                'POST', endpoint, json_payload=payload, context="fetch_static_hotels_initial"
            )
            
            if response_data is None:
                self.logger.log_empty_response(endpoint, payload)
                return None
            
            # Extract hotels from response
            hotels = response_data.get('hotels', [])
            next_token = response_data.get('next')
            
            # Log API response
            self.logger.log_api_response(endpoint, 200, len(hotels))
            
            if not hotels:
                self.logger.log_empty_response(endpoint, payload)
                return None
            
            self.logger.info(f"Successfully fetched {len(hotels)} hotels from initial API call")
            
            # Log pagination info
            if next_token:
                self.logger.log_pagination_info({'next': next_token})
            
            return response_data
            
        except Exception as e:
            self.logger.log_exception(e, "fetching initial static hotels")
            return None
    
    def fetch_static_hotels_next(self, next_token: str) -> Optional[Dict]:
        """
        Fetch next set of static hotels data using pagination token
        
        Args:
            next_token: Token from previous response to fetch next page
            
        Returns:
            dict: API response data with hotels or None if failed
        """
        try:
            endpoint = TripJackConfig.API_ENDPOINTS['FETCH_STATIC_HOTELS']
            
            # Prepare request payload
            payload = {'next': next_token}
            
            # Log API request
            self.logger.log_api_request(endpoint, params=payload)
            
            # Make API request with retries
            response_data = self._make_request_with_retries(
                'POST', endpoint, json_payload=payload, context="fetch_static_hotels_next"
            )
            
            if response_data is None:
                self.logger.log_empty_response(endpoint, payload)
                return None
            
            # Extract hotels from response
            hotels = response_data.get('hotels', [])
            next_token = response_data.get('next')
            
            # Log API response
            self.logger.log_api_response(endpoint, 200, len(hotels))
            
            self.logger.info(f"Successfully fetched {len(hotels)} hotels from paginated API call")
            
            # Log pagination info
            self.logger.log_pagination_info({'next': next_token})
            
            return response_data
            
        except Exception as e:
            self.logger.log_exception(e, f"fetching next static hotels with token {next_token}")
            return None
    
    def search_hotels_by_ids(self, hotel_ids: List[str], 
                           checkin_date: str = None,
                           checkout_date: str = None,
                           adults: int = 2, children: int = 0) -> Optional[Dict]:
        """
        Search hotels using hotel IDs to get pricing and availability
        
        Args:
            hotel_ids: List of TripJack hotel IDs
            checkin_date: Check-in date in YYYY-MM-DD format (optional, uses config default)
            checkout_date: Check-out date in YYYY-MM-DD format (optional, uses config default)
            adults: Number of adults
            children: Number of children
            
        Returns:
            dict: API response data with search results or None if failed
        """
        try:
            endpoint = TripJackConfig.API_ENDPOINTS['HOTEL_SEARCH_LIST']
            
            # Generate unique search ID
            search_id = f"hsid{int(time.time())}{uuid.uuid4().hex[:8]}"
            
            # Prepare request payload using default params as base
            search_params = TripJackConfig.DEFAULT_SEARCH_PARAMS.copy()
            
            # Override dates only if provided
            if checkin_date:
                search_params['checkinDate'] = checkin_date
            if checkout_date:
                search_params['checkoutDate'] = checkout_date
                
            search_params['roomInfo'][0]['numberOfAdults'] = adults
            search_params['roomInfo'][0]['numberOfChild'] = children
            search_params['searchPreferences']['hids'] = hotel_ids
            
            payload = {
                'searchQuery': search_params,
                'searchId': search_id,
                'sync': True
            }
            
            # Log API request
            self.logger.log_api_request(endpoint, f"hotel_ids={hotel_ids}", payload)
            
            # Make API request with retries
            response_data = self._make_request_with_retries(
                'POST', endpoint, json_payload=payload, context=f"search_hotels_by_ids_{hotel_ids}"
            )
            
            if response_data is None:
                self.logger.log_empty_response(endpoint, payload)
                return None
            
            # Extract search results
            search_results = response_data.get('searchResult', {})
            hotels = search_results.get('his', [])
            
            # Log API response
            self.logger.log_api_response(endpoint, 200, len(hotels))
            
            if hotels:
                self.logger.log_search_results(hotel_ids, len(hotels))
            else:
                self.logger.log_search_results(hotel_ids, 0)
            
            return response_data
            
        except Exception as e:
            self.logger.log_exception(e, f"searching hotels by IDs {hotel_ids}")
            return None
    
    def get_hotel_detail(self, hotel_search_id: str) -> Optional[Dict]:
        """
        Get detailed hotel information using search ID
        
        Args:
            hotel_search_id: Hotel search ID from search results (e.g., 'hsid6999619891-61886608')
            
        Returns:
            dict: API response data with hotel details or None if failed
        """
        try:
            endpoint = TripJackConfig.API_ENDPOINTS['HOTEL_DETAIL']
            
            # Prepare request payload
            payload = {'id': hotel_search_id}
            
            # Log API request
            self.logger.log_api_request(endpoint, hotel_search_id, payload)
            
            # Make API request with retries
            response_data = self._make_request_with_retries(
                'POST', endpoint, json_payload=payload, context=f"get_hotel_detail_{hotel_search_id}"
            )
            
            if response_data is None:
                self.logger.log_empty_response(endpoint, payload)
                self.logger.log_detail_fetch(hotel_search_id, success=False)
                return None
            
            # Log API response
            self.logger.log_api_response(endpoint, 200, 1)
            self.logger.log_detail_fetch(hotel_search_id, success=True)
            
            return response_data
            
        except Exception as e:
            self.logger.log_exception(e, f"getting hotel detail for search ID {hotel_search_id}")
            self.logger.log_detail_fetch(hotel_search_id, success=False)
            return None
    
    def _make_request_with_retries(self, method: str, endpoint: str, 
                                  json_payload: dict = None, context: str = "unknown") -> Optional[Dict]:
        """Make HTTP request with retries and improved error handling"""
        
        # Check circuit breaker
        if self._is_circuit_breaker_open():
            self.logger.warning(f"Circuit breaker is open, skipping request for {context}")
            return None
        
        url = f"{self.base_url}{endpoint}"
        
        for attempt in range(self.max_retries):
            try:
                # Apply rate limiting
                self._apply_rate_limiting()
                
                # Update statistics
                self.stats['total_requests'] += 1
                
                # Use base timeout without progressive increase to avoid server timeouts
                timeout = self.timeout
                
                # Log attempt
                if attempt > 0:
                    self.logger.warning(f"Retry attempt {attempt + 1}/{self.max_retries} for {context}")
                
                # Make the request
                if method.upper() == 'POST':
                    response = self.session.post(url, json=json_payload, timeout=timeout)
                else:
                    response = self.session.get(url, timeout=timeout)
                
                # Handle response
                if response.status_code == 200:
                    self.stats['successful_requests'] += 1
                    self._reset_circuit_breaker()  # Reset on success
                    return response.json()
                elif response.status_code == 504:
                    # Gateway timeout - wait longer before retry
                    self.logger.warning(f"Gateway timeout (504) for {context}, attempt {attempt + 1}")
                    if attempt < self.max_retries - 1:
                        delay = TripJackConfig.RETRY_DELAY * (2 ** attempt)  # Exponential backoff
                        self.logger.info(f"Waiting {delay} seconds before retry...")
                        time.sleep(delay)
                        continue
                elif response.status_code == 429:
                    # Rate limited
                    self.stats['rate_limited_requests'] += 1
                    self.logger.warning(f"Rate limited (429) for {context}")
                    if attempt < self.max_retries - 1:
                        delay = TripJackConfig.RETRY_DELAY * (2 ** attempt)
                        self.logger.info(f"Rate limited - waiting {delay} seconds...")
                        time.sleep(delay)
                        continue
                else:
                    # Other HTTP errors
                    self.logger.error(f"HTTP {response.status_code} error for {context}: {response.text[:200]}...")
                    if attempt < self.max_retries - 1 and response.status_code >= 500:
                        # Retry server errors
                        delay = TripJackConfig.RETRY_DELAY * (2 ** attempt)
                        time.sleep(delay)
                        continue
                
                # If we get here, the request failed and we should stop retrying non-server errors
                if response.status_code < 500:
                    break
                    
            except requests.exceptions.Timeout as e:
                self.logger.warning(f"Timeout error for {context} (attempt {attempt + 1}): {str(e)}")
                if attempt < self.max_retries - 1:
                    delay = TripJackConfig.RETRY_DELAY * (2 ** attempt)  # Exponential backoff
                    self.logger.info(f"Waiting {delay} seconds before retry...")
                    time.sleep(delay)
                    continue
            except requests.exceptions.ConnectionError as e:
                self.logger.warning(f"Connection error for {context} (attempt {attempt + 1}): {str(e)}")
                if attempt < self.max_retries - 1:
                    delay = TripJackConfig.RETRY_DELAY * (2 ** attempt)
                    time.sleep(delay)
                    continue
            except requests.exceptions.RequestException as e:
                self.logger.error(f"Request error for {context} (attempt {attempt + 1}): {str(e)}")
                if attempt < self.max_retries - 1:
                    delay = TripJackConfig.RETRY_DELAY
                    time.sleep(delay)
                    continue
            except Exception as e:
                self.logger.error(f"Unexpected error for {context} (attempt {attempt + 1}): {str(e)}")
                break
            
            self.stats['retry_attempts'] += 1
        
        # All retries exhausted - increment failure count
        self.stats['failed_requests'] += 1
        self._increment_failure_count()
        self.logger.error(f"All {self.max_retries} retry attempts failed for {context}")
        return None
    
    def _is_circuit_breaker_open(self) -> bool:
        """Check if circuit breaker is currently open"""
        if self.circuit_breaker_triggered_at is None:
            return False
        
        # Check if timeout period has passed
        if time.time() - self.circuit_breaker_triggered_at >= self.circuit_breaker_timeout:
            # Reset circuit breaker
            self.circuit_breaker_triggered_at = None
            self.consecutive_failures = 0
            self.logger.info("🔄 Circuit breaker timeout expired, AUTO-RESETTING to closed state")
            return False
        
        return True
    
    def _increment_failure_count(self):
        """Increment failure count and potentially trigger circuit breaker"""
        self.consecutive_failures += 1
        
        if self.consecutive_failures >= self.max_consecutive_failures:
            self.circuit_breaker_triggered_at = time.time()
            self.stats['circuit_breaker_triggered'] += 1
            self.logger.error(f"⭕ CIRCUIT BREAKER TRIGGERED after {self.consecutive_failures} consecutive failures. "
                            f"API requests will be suspended for {self.circuit_breaker_timeout} seconds.")
            self.logger.warning(f"⏰ Circuit breaker will auto-reset at: {time.ctime(time.time() + self.circuit_breaker_timeout)}")
    
    def _reset_circuit_breaker(self):
        """Reset circuit breaker on successful request"""
        if self.consecutive_failures > 0:
            self.logger.info(f"✅ Resetting circuit breaker after successful request "
                           f"(had {self.consecutive_failures} consecutive failures)")
            self.consecutive_failures = 0
            self.circuit_breaker_triggered_at = None
    
    def force_reset_circuit_breaker(self):
        """Force reset circuit breaker - use with caution"""
        self.logger.warning("🔄 MANUALLY FORCING circuit breaker reset")
        self.consecutive_failures = 0
        self.circuit_breaker_triggered_at = None
    
    def _handle_response(self, response: requests.Response, endpoint: str, context: str) -> Optional[Dict]:
        """Handle and validate API response"""
        try:
            # Check HTTP status code
            if response.status_code == 200:
                # Try to parse JSON
                try:
                    response_data = response.json()
                except json.JSONDecodeError as e:
                    raise Exception(f"Invalid JSON response: {str(e)}")
                
                # Check TripJack API status
                status = response_data.get('status', {})
                if status.get('success') is True:
                    self.stats['successful_requests'] += 1
                    return response_data
                else:
                    # TripJack API returned error
                    error_msg = status.get('httpStatus', 'Unknown error')
                    raise Exception(f"TripJack API error: {error_msg}")
            
            elif response.status_code == 401:
                raise Exception("Authentication failed - check API key")
            
            elif response.status_code == 403:
                raise Exception("Access forbidden - check API permissions")
            
            elif response.status_code == 429:
                # Rate limit exceeded
                self.stats['rate_limited_requests'] += 1
                wait_time = self.request_delay * 2
                self.logger.log_rate_limit(wait_time)
                time.sleep(wait_time)
                raise Exception("Rate limit exceeded")
            
            elif response.status_code == 404:
                raise Exception("Endpoint not found")
            
            elif response.status_code >= 500:
                raise Exception(f"Server error: HTTP {response.status_code}")
            
            else:
                raise Exception(f"HTTP {response.status_code}: {response.text}")
                
        except Exception as e:
            error_msg = f"Response handling error: {str(e)}"
            self.logger.log_api_error(endpoint, error_msg)
            raise e
    
    def _apply_rate_limiting(self):
        """Apply rate limiting between requests"""
        if self._last_request_time > 0:
            time_since_last_request = time.time() - self._last_request_time
            if time_since_last_request < self.request_delay:
                sleep_time = self.request_delay - time_since_last_request
                time.sleep(sleep_time)
    
    def get_statistics(self) -> Dict[str, int]:
        """Get API client statistics"""
        return self.stats.copy()
    
    def reset_statistics(self):
        """Reset API client statistics"""
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'rate_limited_requests': 0,
            'retry_attempts': 0
        }
    
    def log_statistics(self):
        """Log current API statistics"""
        stats = self.get_statistics()
        success_rate = (stats['successful_requests'] / max(stats['total_requests'], 1)) * 100
        
        self.logger.info(f"""API Client Statistics:
        Total Requests: {stats['total_requests']}
        Successful Requests: {stats['successful_requests']}
        Failed Requests: {stats['failed_requests']}
        Rate Limited Requests: {stats['rate_limited_requests']}
        Retry Attempts: {stats['retry_attempts']}
        Success Rate: {success_rate:.1f}%""")
        
    def __enter__(self):
        """Context manager entry"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit with cleanup"""
        self.close() 