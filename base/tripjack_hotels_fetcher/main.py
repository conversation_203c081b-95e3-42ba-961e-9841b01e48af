"""
Main TripJack Hotels Fetcher Script
Orchestrates the entire process of fetching and storing TripJack hotel data
"""

import time
import os
import sys
import django
from datetime import datetime
from typing import Dict, Any, Optional

# Setup Django environment
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.append(project_root)

# Initialize Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

# Import components after Django setup
try:
    # Try relative imports first (when used as module)
    from .config import TripJackConfig
    from .logger import TripJackLogger
    from .api_client import <PERSON><PERSON>ack<PERSON><PERSON>lient
    from .data_processor import TripJackDataProcessor
    from .destination_matcher import TripJackDestinationMatcher
    from .image_downloader import TripJackImageDownloader
except ImportError:
    # Fallback to absolute imports (when run standalone)
    from config import TripJackConfig
    from logger import <PERSON><PERSON><PERSON><PERSON>ogger
    from api_client import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
    from data_processor import <PERSON><PERSON>ackDataProcessor
    from destination_matcher import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Matcher
    from image_downloader import TripJackImageDownloader

from dynamic_packages.models import Hotel


class TripJackHotelsFetcher:
    """
    Main class that orchestrates the entire TripJack hotels fetching process
    Coordinates API calls, destination matching, data processing, and database operations
    """
    
    def __init__(self):
        # Initialize components
        self.logger = TripJackLogger()
        self.api_client = TripJackAPIClient(self.logger)
        self.data_processor = TripJackDataProcessor(self.logger)
        self.destination_matcher = TripJackDestinationMatcher(self.logger)
        
        # Initialize statistics
        self.stats = {
            'total_hotels_fetched': 0,
            'hotels_with_destinations': 0,
            'hotels_without_destinations': 0,
            'successful_hotels': 0,
            'failed_hotels': 0,
            'skipped_hotels': 0,
            'duplicate_hotels': 0,
            'api_calls_made': 0,
            'api_errors': 0,
            'total_batches_processed': 0,
            'start_time': None,
            'end_time': None,
            'execution_time': 0
        }
        
        # Load configuration
        self.logger.log_configuration_loaded()
    
    def cleanup(self):
        """Clean up resources"""
        try:
            if hasattr(self, 'api_client') and self.api_client:
                self.api_client.close()
            if hasattr(self, 'data_processor') and self.data_processor:
                self.data_processor.close()
            if hasattr(self, 'destination_matcher') and self.destination_matcher:
                self.destination_matcher.clear_cache()
        except Exception as e:
            if hasattr(self, 'logger'):
                self.logger.log_exception(e, "cleaning up resources")
    
    def run(self, last_update_time: Optional[str] = None) -> bool:
        """
        Main execution method
        Runs the complete hotel fetching process
        
        Args:
            last_update_time: Optional timestamp in format 'YYYY-MM-DDTHH:MM'
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Log script start
            self.stats['start_time'] = datetime.now()
            self.logger.log_script_start()
            
            # Validate prerequisites
            if not self._validate_prerequisites():
                self.logger.error("Prerequisites validation failed. Exiting.")
                return False
            
            # Start fetching process
            success = self._fetch_and_process_all_hotels(last_update_time)
            
            # Log completion
            self.stats['end_time'] = datetime.now()
            self.stats['execution_time'] = (self.stats['end_time'] - self.stats['start_time']).total_seconds()
            
            # Log final statistics
            self._log_final_statistics()
            
            # Log final API health status
            self.api_client.log_api_health_status()
            
            return success
            
        except KeyboardInterrupt:
            self.logger.warning("Script interrupted by user")
            return False
        except Exception as e:
            self.logger.log_exception(e, "main execution")
            return False
        finally:
            # Always cleanup resources
            self.cleanup()
    
    def _validate_prerequisites(self) -> bool:
        """Validate all prerequisites before starting"""
        self.logger.info("Validating prerequisites...")
        
        try:
            # Validate API client configuration
            api_valid = self.api_client.validate_configuration()
            
            # Test API connection
            api_connected = self.api_client.test_api_connection() if api_valid else False
            
            # Test database connection
            db_connected = self._test_database_connection()
            
            # Log prerequisites check results
            return self.logger.log_prerequisites_check(api_valid and api_connected, db_connected)
            
        except Exception as e:
            self.logger.log_exception(e, "validating prerequisites")
            return False
    
    def _test_database_connection(self) -> bool:
        """Test database connectivity"""
        try:
            # Try to perform a simple database query
            Hotel.objects.count()
            return True
        except Exception as e:
            self.logger.log_exception(e, "testing database connection")
            return False
    
    def _fetch_and_process_all_hotels(self, last_update_time: Optional[str] = None) -> bool:
        """Fetch and process all hotels from TripJack"""
        try:
            self.logger.info("Starting hotel fetching and processing...")
            
            # Start with initial fetch
            response = self.api_client.fetch_static_hotels_initial(last_update_time)
            self.stats['api_calls_made'] += 1
            
            if not response:
                self.logger.error("Failed to fetch initial hotels batch")
                self.stats['api_errors'] += 1
                return False
            
            # Process first batch
            hotels_data = response.get('hotels', [])
            if hotels_data:
                self._process_hotels_batch(hotels_data, 1)
            
            # Process remaining batches using pagination
            next_token = response.get('next')
            batch_number = 2
            
            while next_token:
                self.logger.info(f"Fetching batch {batch_number} with pagination token")
                
                response = self.api_client.fetch_static_hotels_next(next_token)
                self.stats['api_calls_made'] += 1
                
                if not response:
                    self.logger.error(f"Failed to fetch batch {batch_number}")
                    self.stats['api_errors'] += 1
                    break
                
                hotels_data = response.get('hotels', [])
                if hotels_data:
                    self._process_hotels_batch(hotels_data, batch_number)
                
                next_token = response.get('next')
                batch_number += 1
                
                # Test mode limit
                if TripJackConfig.TEST_MODE and batch_number > 3:
                    self.logger.warning("Test mode: Stopping after 3 batches")
                    break
            
            self.logger.info(f"Hotel fetching completed. Processed {self.stats['total_batches_processed']} batches")
            return True
            
        except Exception as e:
            self.logger.log_exception(e, "fetching and processing all hotels")
            return False
    
    def _process_hotels_batch(self, hotels_data: list, batch_number: int):
        """Process a batch of hotels"""
        try:
            self.stats['total_batches_processed'] += 1
            
            # Test mode limit
            if TripJackConfig.TEST_MODE:
                hotels_data = hotels_data[:TripJackConfig.TEST_MODE_HOTEL_LIMIT]
                self.logger.warning(f"Test mode: Limited batch to {len(hotels_data)} hotels")
            
            self.logger.log_batch_progress(batch_number, "Unknown", len(hotels_data))
            
            for i, hotel_data in enumerate(hotels_data, 1):
                try:
                    hotel_name = hotel_data.get('name', 'Unknown')
                    hotel_id = hotel_data.get('hotelId', '')
                    
                    # Log progress periodically
                    if i % TripJackConfig.PROGRESS_REPORT_INTERVAL == 0:
                        self.logger.info(f"Processed {i}/{len(hotels_data)} hotels in batch {batch_number}")
                    
                    # Process single hotel
                    success = self._process_single_hotel(hotel_data, i, len(hotels_data))
                    
                    if success:
                        self.stats['successful_hotels'] += 1
                    else:
                        self.stats['failed_hotels'] += 1
                
                except Exception as e:
                    self.stats['failed_hotels'] += 1
                    hotel_name = hotel_data.get('name', 'Unknown')
                    self.logger.log_exception(e, f"processing hotel {hotel_name} in batch {batch_number}")
                    continue
            
            # Log memory usage periodically
            if batch_number % 5 == 0:
                self.logger.log_memory_usage(f"after batch {batch_number}")
                # Also log API health status
                self.api_client.log_api_health_status()
            
        except Exception as e:
            self.logger.log_exception(e, f"processing hotels batch {batch_number}")
    
    def _process_single_hotel(self, hotel_data: Dict, index: int, total: int) -> bool:
        """Process a single hotel through the complete pipeline"""
        try:
            hotel_name = hotel_data.get('name', 'Unknown')
            hotel_id = hotel_data.get('hotelId', '')
            
            self.stats['total_hotels_fetched'] += 1
            
            # Log hotel processing start
            self.logger.log_hotel_processing_start(hotel_name, index, total)
            
            # Skip deleted hotels
            if TripJackConfig.SKIP_DELETED_HOTELS and hotel_data.get('isDeleted', False):
                self.logger.log_hotel_skip(hotel_name, "Hotel is marked as deleted")
                self.stats['skipped_hotels'] += 1
                return False
            
            # Check for duplicates
            if TripJackConfig.ENABLE_DUPLICATE_CHECK and self._is_duplicate_hotel(hotel_id):
                self.logger.log_duplicate_hotel(hotel_name, hotel_id)
                self.stats['duplicate_hotels'] += 1
                self.stats['skipped_hotels'] += 1
                return False
            
            # Validate destination match
            is_valid, destination_title, destination_obj = self.destination_matcher.validate_destination_match(hotel_data)
            
            if not is_valid:
                self.stats['hotels_without_destinations'] += 1
                if TripJackConfig.SKIP_HOTELS_WITHOUT_DESTINATION_MATCH:
                    self.logger.log_hotel_skip(hotel_name, "No matching destination found")
                    self.stats['skipped_hotels'] += 1
                    return False
            else:
                self.stats['hotels_with_destinations'] += 1
            
            # Search for hotel to get pricing and availability
            search_result = self._search_hotel(hotel_id)
            if not search_result:
                self.logger.log_hotel_skip(hotel_name, "Hotel search failed")
                self.stats['skipped_hotels'] += 1
                return False
            
            # Get hotel details
            search_id = search_result.get('id', '')
            detail_data = self._get_hotel_details(search_id)
            
            # Create hotel in database
            created_hotel = self.data_processor.process_hotel_data(
                hotel_data, destination_obj, search_result, detail_data or {}
            )
            
            if created_hotel:
                self.logger.log_hotel_processing_end(hotel_name, success=True)
                return True
            else:
                self.logger.log_hotel_processing_end(hotel_name, success=False, error_msg="Hotel creation failed")
                return False
                
        except Exception as e:
            hotel_name = hotel_data.get('name', 'Unknown')
            hotel_id = hotel_data.get('hotelId', '')
            self.logger.log_hotel_failure(hotel_name, hotel_id, str(e))
            self.logger.log_exception(e, f"processing single hotel {hotel_name}")
            return False
    
    def _is_duplicate_hotel(self, hotel_id: str) -> bool:
        """Check if hotel already exists in database"""
        try:
            return Hotel.objects.filter(tripjack_static_id=hotel_id).exists()
        except Exception as e:
            self.logger.log_exception(e, f"checking duplicate for hotel {hotel_id}")
            return False
    
    def _search_hotel(self, hotel_id: str) -> Optional[Dict]:
        """Search for hotel to get pricing and availability"""
        try:
            search_response = self.api_client.search_hotels_by_ids([hotel_id])
            self.stats['api_calls_made'] += 1
            
            if not search_response:
                self.stats['api_errors'] += 1
                return None
            
            # Extract first hotel from search results
            search_result = search_response.get('searchResult', {})
            hotels = search_result.get('his', [])
            
            if hotels:
                return hotels[0]
            else:
                self.logger.warning(f"No search results found for hotel ID: {hotel_id}")
                return None
                
        except Exception as e:
            self.stats['api_errors'] += 1
            self.logger.log_exception(e, f"searching hotel {hotel_id}")
            return None
    
    def _get_hotel_details(self, search_id: str) -> Optional[Dict]:
        """Get detailed hotel information"""
        try:
            detail_response = self.api_client.get_hotel_detail(search_id)
            self.stats['api_calls_made'] += 1
            
            if not detail_response:
                self.stats['api_errors'] += 1
                return None
            
            return detail_response
            
        except Exception as e:
            self.stats['api_errors'] += 1
            self.logger.log_exception(e, f"getting hotel details for search ID {search_id}")
            return None
    
    def _log_final_statistics(self):
        """Log comprehensive final statistics"""
        self.logger.log_script_end(self.stats)
        
        # Log component statistics
        self.api_client.log_statistics()
        self.data_processor.log_statistics()
        self.destination_matcher.log_cache_statistics()
        
        # Log detailed breakdown
        total_processed = self.stats['total_hotels_fetched']
        if total_processed > 0:
            destination_match_rate = (self.stats['hotels_with_destinations'] / total_processed) * 100
            success_rate = (self.stats['successful_hotels'] / total_processed) * 100
            
            self.logger.info(f"""
Detailed Statistics:
- Destination Match Rate: {destination_match_rate:.1f}%
- Overall Success Rate: {success_rate:.1f}%
- API Calls Made: {self.stats['api_calls_made']}
- API Error Rate: {(self.stats['api_errors'] / max(self.stats['api_calls_made'], 1)) * 100:.1f}%
- Batches Processed: {self.stats['total_batches_processed']}
            """)
    
    def get_current_statistics(self) -> Dict[str, Any]:
        """Get current execution statistics"""
        return self.stats.copy()
    
    def process_single_hotel_by_id(self, hotel_id: str) -> bool:
        """
        Process a single hotel by ID (for testing/debugging)
        
        Args:
            hotel_id: TripJack hotel ID to process
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.logger.info(f"Processing single hotel ID: {hotel_id}")
            
            # Validate prerequisites
            if not self._validate_prerequisites():
                self.logger.error("Prerequisites validation failed")
                return False
            
            # First try to find hotel in list API (this would require fetching all hotels)
            # For now, we'll simulate hotel data for testing
            hotel_data = {
                'hotelId': hotel_id,
                'name': f'Test Hotel {hotel_id}',
                'description': 'Test hotel for debugging',
                'rating': 3,
                'address': {'city': {'name': 'Test City'}, 'country': {'name': 'India'}},
                'images': [],
                'facilities': [],
                'contact': {}
            }
            
            # Process the hotel
            success = self._process_single_hotel(hotel_data, 1, 1)
            
            if success:
                self.logger.info(f"Successfully processed hotel ID: {hotel_id}")
            else:
                self.logger.error(f"Failed to process hotel ID: {hotel_id}")
            
            return success
            
        except Exception as e:
            self.logger.log_exception(e, f"processing single hotel ID: {hotel_id}")
            return False


def main():
    """
    Main entry point for the script
    Can be called directly or imported
    """
    try:
        # Create and run the fetcher
        fetcher = TripJackHotelsFetcher()
        
        # Run the main process
        success = fetcher.run()
        
        if success:
            print("\n" + "="*60)
            print("TRIPJACK HOTELS FETCHER COMPLETED SUCCESSFULLY")
            print("="*60)
            return 0
        else:
            print("\n" + "="*60)
            print("TRIPJACK HOTELS FETCHER COMPLETED WITH ERRORS")
            print("="*60)
            return 1
    
    except Exception as e:
        print(f"\nFATAL ERROR: {str(e)}")
        return 1


if __name__ == "__main__":
    import sys
    sys.exit(main())
