#!/usr/bin/env python3
"""
TripJack Hotels Fetcher - Standalone Script
Run this script directly from terminal to fetch hotels from TripJack API
"""

import os
import sys
import django
import argparse
from pathlib import Path
from django.conf import settings
def setup_django():
    """Setup Django environment"""
    current_dir = Path(__file__).parent.absolute()
    project_root = current_dir.parent.parent
    sys.path.insert(0, str(project_root))
    
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'zuumm.settings')
    
    # Hardcode API credentials
    os.environ['TRIPJACK_API_KEY'] = settings.TRIPJACK_API_KEY
    os.environ['TRIPJACK_API_URL'] = settings.TRIPJACK_API_URL
    
    django.setup()

def main():
    """Main function to run the fetcher"""
    parser = argparse.ArgumentParser(description='TripJack Hotels Fetcher')
    parser.add_argument('--production', action='store_true', 
                       help='Run in production mode (fetch all hotels, not just test batch)')
    parser.add_argument('--no-images', action='store_true',
                       help='Skip image downloading')
    parser.add_argument('--max-hotels', type=int, default=50,
                       help='Maximum number of hotels to process (default: 50)')
    parser.add_argument('--max-destinations', type=int, default=3,
                       help='Maximum number of destinations to process (default: 3)')
    
    args = parser.parse_args()
    
    try:
        # Setup Django
        setup_django()
        
        # Import after Django setup - use absolute imports
        sys.path.append(os.path.join(os.path.dirname(__file__)))
        from main import TripJackHotelsFetcher
        from config import TripJackConfig
        
        print("🚀 Starting TripJack Hotels Fetcher...")
        print("="*60)
        
        # Configure based on arguments
        if args.production:
            TripJackConfig.TEST_MODE = False
            TripJackConfig.TEST_MAX_HOTELS = None
            TripJackConfig.TEST_MAX_DESTINATIONS = None
            print("🔥 Running in PRODUCTION mode - fetching ALL hotels")
        else:
            TripJackConfig.TEST_MODE = True
            TripJackConfig.TEST_MAX_HOTELS = args.max_hotels
            TripJackConfig.TEST_MAX_DESTINATIONS = args.max_destinations
            print(f"🧪 Running in TEST mode - max {args.max_hotels} hotels, {args.max_destinations} destinations")
        
        if args.no_images:
            TripJackConfig.ENABLE_IMAGE_PROCESSING = False
            print("📷 Image downloading DISABLED")
        else:
            print("📷 Image downloading ENABLED")
        
        print("="*60)
        
        # Create and run fetcher
        fetcher = TripJackHotelsFetcher()
        success = fetcher.run()
        
        print("\n" + "="*60)
        if success:
            print("✅ TripJack Hotels Fetcher completed successfully!")
            
            # Display statistics
            api_stats = fetcher.api_client.get_statistics()
            data_stats = fetcher.data_processor.get_statistics()
            
            print(f"""
📊 FINAL STATISTICS:
   Hotels Fetched: {api_stats.get('hotels_fetched', 0)}
   Hotels Created: {data_stats.get('hotels_created', 0)}
   Hotels Failed: {data_stats.get('hotels_failed', 0)}
   Rooms Created: {data_stats.get('rooms_created', 0)}
   Images Processed: {data_stats.get('images_processed', 0)}
   Success Rate: {(data_stats.get('hotels_created', 0) / max(data_stats.get('hotels_processed', 1), 1) * 100):.1f}%
            """)
        else:
            print("❌ TripJack Hotels Fetcher failed!")
            print("Check the logs for details:")
            print("   - Main log: base/tripjack_hotels_fetcher/logs/tripjack_fetcher.log")
            print("   - API errors: base/tripjack_hotels_fetcher/logs/tripjack_api_errors.log")
            print("   - Failed hotels: base/tripjack_hotels_fetcher/logs/failed_hotels.log")
            
        print("="*60)
        
    except KeyboardInterrupt:
        print("\n⚠️  Process interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        print("Check that:")
        print("  1. TRIPJACK_API_KEY environment variable is set")
        print("  2. Django settings are properly configured")
        print("  3. Database is accessible")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main() 