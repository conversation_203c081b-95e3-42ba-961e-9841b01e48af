from django.db.models import TextChoices


class SuccessMessages(TextChoices): 
    """Success messages enum"""
    # Generic Messages
    pass


class ErrorMessages(TextChoices):
    """Error messages enum"""

    # File Size Error Messages
    VALIDATE_IMAGE_SIZE = "The file size exceeds the maximum limit of {}. Kindly upload a smaller file"


class Constants:
    FILE_SIZE_FIVE_MB = 5 * 1024 * 1024
    PARTNER_LOGO_EXTENSIONS = ["jpg", "jpeg", "png", "webp", "JPG", "JPEG", "PNG", "WEBP"]
    OTP_WAIT_MINUTES = 1
    OTP_EXPIRATION_MINUTES = 60
    
    # Blog Constants
    BLOG_MIN_LENGTH = 200  # Minimum word count before generating additional content
    BLOG_TARGET_LENGTH = 800  # Target word count for generated content
    
    # Package Constants
    PACKAGE_MAX_MEDIA_COUNT = 20
    PACKAGE_MEDIA_EXTENSIONS = ["jpg", "jpeg", "png", "webp", "mp4", "mov", "JPG", "JPEG", "PNG", "WEB<PERSON>", "MP4", "MOV"]
    PACKAGE_FILE_EXTENSIONS = ["json", "docx", "doc", "JSON", "DOCX", "DOC"]

    # Category Constants
    CATEGORY_MAX_MEDIA_COUNT = 1
    CATEGORY_MEDIA_EXTENSIONS = ["jpg", "jpeg", "png", "webp", "JPG", "JPEG", "PNG", "WEBP"]

    # Activity Constants
    ACTIVITY_MAX_MEDIA_COUNT = 10
    ACTIVITY_MEDIA_EXTENSIONS = ["jpg", "jpeg", "png", "webp", "JPG", "JPEG", "PNG", "WEBP"]
    
    # Custom Activity Constants
    CUSTOM_ACTIVITY_HIGHLIGHTS_LINES = 4  # Number of highlight lines to generate

    # Destination Constants
    DESTINATION_MAX_MEDIA_COUNT = 1
    DESTINATION_MEDIA_EXTENSIONS = ["jpg", "jpeg", "png", "webp", "JPG", "JPEG", "PNG", "WEBP"]
    
    ICON_CLASS = [
        "island",
        "beach", 
        "ship",
        "sunrise",
        "activity",
        "passenger",
        "breakfast",
        "taxi",
        "bus",
        "hotel",
        "flight",
        "visa",
        "insurance",
        "forex_card",
    ]
    
    # TripJack Hotel Fetcher Constants
    TRIPJACK_MAX_HOTEL_IMAGES = 10
    TRIPJACK_MAX_ROOM_IMAGES = 10