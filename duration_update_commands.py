#!/usr/bin/env python3
"""
Duration Update Commands for All Destinations
This script contains all the commands to update durations for different destinations

Usage:
    python duration_update_commands.py

Individual commands can also be copied and run manually:
    python update_durations_for_activities.py --destination "turkey" 51
"""

import subprocess
import sys
from datetime import datetime

def run_command(destination_name, destination_id):
    """
    Run duration update command for a specific destination
    
    Args:
        destination_name (str): Name of destination for GetYourGuide API
        destination_id (int): Database ID of destination
    """
    print(f"\n{'='*60}")
    print(f"Updating durations for {destination_name.title()} (ID: {destination_id})")
    print(f"{'='*60}")
    
    try:
        # Build command
        cmd = [
            "python", 
            "update_durations_for_activities.py", 
            "--destination", 
            destination_name, 
            str(destination_id)
        ]
        
        # Run command
        result = subprocess.run(
            cmd, 
            capture_output=True, 
            text=True, 
            timeout=300  # 5 minute timeout
        )
        
        # Print output
        if result.stdout:
            print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        
        # Check result
        if result.returncode == 0:
            print(f"✅ SUCCESS: {destination_name.title()} durations updated successfully!")
        else:
            print(f"❌ FAILED: {destination_name.title()} duration update failed with exit code {result.returncode}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ TIMEOUT: {destination_name.title()} duration update timed out after 5 minutes")
        return False
    except Exception as e:
        print(f"❌ ERROR: {destination_name.title()} duration update failed: {str(e)}")
        return False
    
    return True

def main():
    """Main execution function"""
    print("🚀 Starting Duration Updates for All Destinations")
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*80)
    
    # Define destinations to update
    destinations = [
        ("usa", 52),
        ("turkey", 51),
        ("thailand", 50),
        ("tamil nadu", 49),
        ("sri lanka", 48),
        # Add more destinations here as needed:
        # ("destination_name", destination_id),
    ]
    
    # Track results
    successful = []
    failed = []
    
    # Process each destination
    for destination_name, destination_id in destinations:
        success = run_command(destination_name, destination_id)
        
        if success:
            successful.append((destination_name, destination_id))
        else:
            failed.append((destination_name, destination_id))
    
    # Print final summary
    print("\n" + "="*80)
    print("📊 FINAL SUMMARY")
    print("="*80)
    print(f"Total Destinations Processed: {len(destinations)}")
    print(f"Successful Updates: {len(successful)}")
    print(f"Failed Updates: {len(failed)}")
    
    if successful:
        print(f"\n✅ Successful Updates:")
        for name, id in successful:
            print(f"   - {name.title()} (ID: {id})")
    
    if failed:
        print(f"\n❌ Failed Updates:")
        for name, id in failed:
            print(f"   - {name.title()} (ID: {id})")
    
    print(f"\nCompleted at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Return appropriate exit code
    return 0 if len(failed) == 0 else 1


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n⚠️  Process interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")
        sys.exit(1)


# Individual Commands (for manual execution):
"""
python update_durations_for_activities.py --destination "usa" 52
python update_durations_for_activities.py --destination "turkey" 51  
python update_durations_for_activities.py --destination "thailand" 50
python update_durations_for_activities.py --destination "tamil nadu" 49
python update_durations_for_activities.py --destination "sri lanka" 48

Template for additional destinations:
python update_durations_for_activities.py --destination "DESTINATION_NAME" DESTINATION_ID
""" 