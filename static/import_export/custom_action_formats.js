$("select[name='action']").change(function() {
    let selectedText = $(this).find(":selected").val();
    console.log(selectedText);
    let label = $("label").has("select[name='file_format']");

    if (selectedText == 'export_admin_action') {
        $('#changelist-form select[name="file_format"]').parent().addClass('ml-2').show();
    } else {
        $('#changelist-form select[name="file_format"]').parent().removeClass('ml-2').hide();
    }
});

/**
 * Logout Confirmation Script for Zuumm Admin
 * This script intercepts logout clicks and shows a confirmation dialog
 */

(function() {
    'use strict';
    
    // Function to show confirmation dialog
    function showLogoutConfirmation(logoutUrl) {
        // Create a custom confirmation dialog
        const dialog = document.createElement('div');
        dialog.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
        `;
        
        const dialogContent = document.createElement('div');
        dialogContent.style.cssText = `
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            text-align: center;
            max-width: 400px;
            width: 90%;
        `;
        
        dialogContent.innerHTML = `
            <div style="font-size: 48px; margin-bottom: 20px;">⚠️</div>
            <h3 style="margin: 0 0 20px 0; color: #333;">Logout Confirmation</h3>
            <p style="margin: 0 0 30px 0; color: #666;">Are you sure you want to logout?</p>
            <div style="display: flex; gap: 15px; justify-content: center;">
                <button id="logout-confirm-yes" style="
                    padding: 12px 24px;
                    background: #dc3545;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    cursor: pointer;
                    font-size: 14px;
                    font-weight: 500;
                ">Yes, Logout</button>
                <button id="logout-confirm-cancel" style="
                    padding: 12px 24px;
                    background: #6c757d;
                    border: none;
                    border-radius: 6px;
                    cursor: pointer;
                    font-size: 14px;
                    font-weight: 500;
                ">Cancel</button>
            </div>
        `;
        
        dialog.appendChild(dialogContent);
        document.body.appendChild(dialog);
        
        // Add event listeners
        document.getElementById('logout-confirm-yes').addEventListener('click', function() {
            document.body.removeChild(dialog);
            window.location.href = logoutUrl;
        });
        
        document.getElementById('logout-confirm-cancel').addEventListener('click', function() {
            document.body.removeChild(dialog);
        });
        
        // Close on outside click
        dialog.addEventListener('click', function(e) {
            if (e.target === dialog) {
                document.body.removeChild(dialog);
            }
        });
    }
    
    // Function to add logout confirmation to a link
    function addLogoutConfirmation(link) {
        if (link.hasAttribute('data-logout-confirmation-added')) {
            return; // Already processed
        }
        
        link.setAttribute('data-logout-confirmation-added', 'true');
        link.addEventListener('click', function(e) {
            e.preventDefault();
            showLogoutConfirmation(this.href);
        });
    }
    
    // Function to process logout links
    function processLogoutLinks() {
        // Find all logout links
        const logoutLinks = document.querySelectorAll('a[href*="logout"], a[href*="admin:logout"]');
        logoutLinks.forEach(addLogoutConfirmation);
        
        // Also check for logout links in user dropdown menus
        const userDropdowns = document.querySelectorAll('.dropdown-menu, .user-menu, .profile-menu');
        userDropdowns.forEach(function(dropdown) {
            const dropdownLogoutLinks = dropdown.querySelectorAll('a[href*="logout"]');
            dropdownLogoutLinks.forEach(addLogoutConfirmation);
        });
    }
    
    // Process logout links when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', processLogoutLinks);
    } else {
        processLogoutLinks();
    }
    
    // Also process logout links after a short delay to catch dynamically added ones
    setTimeout(processLogoutLinks, 1000);
    
    // Observe DOM changes for dynamically added logout links
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                // Check if any new logout links were added
                const newLogoutLinks = document.querySelectorAll('a[href*="logout"]:not([data-logout-confirmation-added])');
                newLogoutLinks.forEach(addLogoutConfirmation);
            }
        });
    });
    
    // Start observing when body is available
    if (document.body) {
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    } else {
        document.addEventListener('DOMContentLoaded', function() {
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        });
    }
})();
