.django-ckeditor-widget img {
    width: 100%;
}
.brand-text {
    font-weight: bold !important;
}

img[src$="icon-yes.svg"],
img[src$="icon-no.svg"] {
    width: 18px !important;
    height: 18px !important;
    vertical-align: middle !important;
    object-fit: contain;
    display: inline-block;
}

.login-box img,
.brand-logo img {
    width: 184px !important;
    height: auto !important;
}
body.jazzmin-login-page .login-logo img {
    width: 184px !important;
    height: auto !important;
    object-fit: contain;
    display: block;
    margin: 0 auto;
}

/* Fix success message link visibility - Updated selectors */
/* Fix success message hyperlink color */
#content .alert-success.alert-dismissible a:not(.dropdown-item):not(.btn-app):not(.nav-link):not(.brand-link):not(.page-link):not(.btn) {
    color: #ffffff !important;
    text-decoration: underline !important;
}

.field-destination .related-widget-wrapper-link {
    display: none !important;
}