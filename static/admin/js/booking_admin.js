/**
 * Booking Admin JavaScript
 * Handles dynamic field visibility and AJAX updates for booking itinerary
 */

(function() {
    'use strict';
    
    function waitForDjangoAdmin() {
        if (typeof window.django !== 'undefined' && typeof window.django.jQuery !== 'undefined') {
            initializeWithDjangoJQuery(window.django.jQuery);
        } else if (typeof window.jQuery !== 'undefined') {
            initializeWithDjangoJQuery(window.jQuery);
        } else {
            setTimeout(waitForDjangoAdmin, 100);
        }
    }
    
    function initializeWithDjangoJQuery($) {
        $(document).ready(function() {
            setTimeout(function() {
                initializeBookingAdmin($);
            }, 750);
        });
    }
    
    waitForDjangoAdmin();

    function initializeBookingAdmin($) {
        const isBookingAdmin = window.location.href.includes('/admin/bookings/booking/');
        if (!isBookingAdmin) return;

        console.log('[BookingAdmin] Initializing booking admin functionality');

        // Initialize cache for API responses
        window.BookingAdmin = {
            cache: {
                hotels: {},
                activities: {},
                flights: {},
                transfers: {}
            }
        };

        // Initialize features
        handleDestinationChange($);
        handleItineraryTypeChange($);
        initializeExistingRows($);
        setupFormsetHandlers($);
    }

    function handleDestinationChange($) {
        const destinationField = $('#id_destination');

        if (destinationField.length) {
            destinationField.on('change', function() {
                const destinationId = $(this).val();
                console.log('[BookingAdmin] Destination changed:', destinationId);
                
                if (destinationId) {
                    // Clear cache to ensure fresh data when destination changes
                    clearAllCache();
                    showLoadingMessage($, 'Updating options for selected destination...');
                    updateInlineFieldOptions($, destinationId);
                } else {
                    clearInlineFieldOptions($);
                }
            });

            // Trigger change if destination is already selected
            const currentValue = destinationField.val();
            if (currentValue && !destinationField.prop('readonly')) {
                destinationField.trigger('change');
            }
        }
    }

    function handleItineraryTypeChange($) {
        // Use event delegation for dynamically added inline forms
        $(document).on('change', '.booking-type-selector', function() {
            const $this = $(this);
            const selectedType = $this.val();
            const $inline = $this.closest('.inline-related');
            
            console.log('[BookingAdmin] Type changed to:', selectedType, 'in inline:', $inline.attr('id'));
            
            hideAllTypeFields($inline);
            
            if (selectedType) {
                showTypeFields($inline, selectedType, $);
            }
        });
    }

    function hideAllTypeFields($inline) {
        console.log('[BookingAdmin] Hiding all type fields for inline');
        $inline.find('.flight-fields, .hotel-fields, .activity-fields, .transfer-fields').addClass('hidden');
    }

    function showTypeFields($inline, type, $) {
        console.log('[BookingAdmin] Showing fields for type:', type);
        
        const destinationId = $('#id_destination').val();
        if (!destinationId && (type === 'Hotel' || type === 'Activity')) {
            showAlert($, 'Please select a destination first to load ' + type.toLowerCase() + ' options.');
            return;
        }

        switch (type) {
            case 'Flight':
                showFlightFields($inline, $);
                break;
            case 'Hotel':
                showHotelFields($inline, $, destinationId);
                break;
            case 'Activity':
                showActivityFields($inline, $, destinationId);
                break;
            case 'Transfer':
                showTransferFields($inline, $);
                break;
        }
    }

    function showFlightFields($inline, $) {
        console.log('[BookingAdmin] Setting up flight fields');
        
        const $flightSection = $inline.find('.flight-fields');
        $flightSection.removeClass('hidden');
        
        // Create flight form fields if they don't exist
        if ($flightSection.find('.flight-form-content').length === 0) {
            const flightFormHtml = `
                <div class="flight-form-content">
                    <div class="form-row">
                        <div class="field-box">
                            <label>From Destination:</label>
                            <select class="flight-from-destination" name="flight_from_destination">
                                <option value="">Select departure city</option>
                            </select>
                        </div>
                        <div class="field-box">
                            <label>To Destination:</label>
                            <select class="flight-to-destination" name="flight_to_destination">
                                <option value="">Select arrival city</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="field-box">
                            <label>Departure Date:</label>
                            <input type="date" class="flight-departure-date" name="flight_departure_date">
                        </div>
                        <div class="field-box">
                            <label>
                                <input type="checkbox" class="flight-return-checkbox" name="flight_return"> 
                                Return Flight
                            </label>
                        </div>
                        <div class="field-box return-date-field" style="display: none;">
                            <label>Return Date:</label>
                            <input type="date" class="flight-return-date" name="flight_return_date">
                        </div>
                    </div>
                    <div class="form-row">
                        <button type="button" class="search-flights-btn">Search Flights</button>
                    </div>
                    <div class="flight-results" style="display: none;">
                        <!-- Flight results will be populated here -->
                    </div>
                </div>
            `;
            $flightSection.append(flightFormHtml);
            
            // Load destination options
            loadDestinationOptions($flightSection);
            
            // Handle return flight checkbox
            $flightSection.find('.flight-return-checkbox').on('change', function() {
                const $returnDateField = $flightSection.find('.return-date-field');
                if ($(this).is(':checked')) {
                    $returnDateField.show();
                } else {
                    $returnDateField.hide();
                }
            });
            
            // Handle flight search
            $flightSection.find('.search-flights-btn').on('click', function() {
                searchFlights($flightSection, $);
            });
        }
    }

    function showHotelFields($inline, $, destinationId) {
        console.log('[BookingAdmin] Setting up hotel fields for destination:', destinationId);
        
        const $hotelSection = $inline.find('.hotel-fields');
        $hotelSection.removeClass('hidden');
        
        // Create hotel form fields if they don't exist
        if ($hotelSection.find('.hotel-form-content').length === 0) {
            const hotelFormHtml = `
                <div class="hotel-form-content">
                    <div class="loading-message" style="display: none;">Loading hotels...</div>
                    <div class="hotel-selection" style="display: none;">
                        <div class="form-row">
                            <div class="field-box">
                                <label>Select Hotel:</label>
                                <select class="hotel-select" name="hotel_id">
                                    <option value="">Select a hotel</option>
                                </select>
                            </div>
                        </div>
                        <div class="hotel-details" style="display: none;">
                            <div class="form-row">
                                <div class="field-box">
                                    <label>Check-in Date:</label>
                                    <input type="date" name="check_in_date" class="hotel-checkin">
                                </div>
                                <div class="field-box">
                                    <label>Check-out Date:</label>
                                    <input type="date" name="check_out_date" class="hotel-checkout">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="field-box">
                                    <label>Number of Rooms:</label>
                                    <input type="number" name="number_of_rooms" min="1" value="1" class="hotel-rooms">
                                </div>
                                <div class="field-box">
                                    <label>Adults:</label>
                                    <input type="number" name="number_of_adults" min="1" value="2" class="hotel-adults">
                                </div>
                                <div class="field-box">
                                    <label>Children:</label>
                                    <input type="number" name="number_of_children" min="0" value="0" class="hotel-children">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="field-box">
                                    <label>Room Type:</label>
                                    <input type="text" name="room_type" class="hotel-room-type" placeholder="e.g., Deluxe, Suite">
                                </div>
                                <div class="field-box">
                                    <label>Amount:</label>
                                    <input type="number" name="hotel_amount" step="0.01" class="hotel-amount">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            $hotelSection.append(hotelFormHtml);
            
            // Handle hotel selection
            $hotelSection.find('.hotel-select').on('change', function() {
                const hotelId = $(this).val();
                if (hotelId) {
                    $hotelSection.find('.hotel-details').show();
                } else {
                    $hotelSection.find('.hotel-details').hide();
                }
            });
        }
        
        // Load hotels for the destination
        loadHotels($hotelSection, $, destinationId);
    }

    function showActivityFields($inline, $, destinationId) {
        console.log('[BookingAdmin] Setting up activity fields for destination:', destinationId);
        
        const $activitySection = $inline.find('.activity-fields');
        $activitySection.removeClass('hidden');
        
        // Create activity form fields if they don't exist
        if ($activitySection.find('.activity-form-content').length === 0) {
            const activityFormHtml = `
                <div class="activity-form-content">
                    <div class="loading-message" style="display: none;">Loading activities...</div>
                    <div class="activity-selection" style="display: none;">
                        <div class="form-row">
                            <div class="field-box">
                                <label>Select Activity:</label>
                                <select class="activity-select" name="activity_id">
                                    <option value="">Select an activity</option>
                                </select>
                            </div>
                        </div>
                        <div class="activity-details" style="display: none;">
                            <div class="form-row">
                                <div class="field-box">
                                    <label>Activity Date:</label>
                                    <input type="date" name="activity_date" class="activity-date">
                                </div>
                                <div class="field-box">
                                    <label>Activity Time:</label>
                                    <input type="time" name="activity_time" class="activity-time">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="field-box">
                                    <label>Participants:</label>
                                    <input type="number" name="participants_count" min="1" value="2" class="activity-participants">
                                </div>
                                <div class="field-box">
                                    <label>Amount:</label>
                                    <input type="number" name="activity_amount" step="0.01" class="activity-amount">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="field-box">
                                    <label>Provider:</label>
                                    <input type="text" name="activity_provider" value="GetYourGuide" class="activity-provider">
                                </div>
                                <div class="field-box">
                                    <label>Provider Booking ID:</label>
                                    <input type="text" name="provider_booking_id" class="activity-provider-id">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            $activitySection.append(activityFormHtml);
            
            // Handle activity selection
            $activitySection.find('.activity-select').on('change', function() {
                const activityId = $(this).val();
                if (activityId) {
                    $activitySection.find('.activity-details').show();
                    
                    // Pre-fill activity amount from selected option
                    const selectedOption = $(this).find('option:selected');
                    const price = selectedOption.data('price');
                    if (price) {
                        $activitySection.find('.activity-amount').val(price);
                    }
                } else {
                    $activitySection.find('.activity-details').hide();
                }
            });
        }
        
        // Load activities for the destination
        loadActivities($activitySection, $, destinationId);
    }

    function showTransferFields($inline, $) {
        console.log('[BookingAdmin] Setting up transfer fields');
        
        const $transferSection = $inline.find('.transfer-fields');
        $transferSection.removeClass('hidden');
        
        // Create transfer form fields if they don't exist
        if ($transferSection.find('.transfer-form-content').length === 0) {
            const transferFormHtml = `
                <div class="transfer-form-content">
                    <div class="form-row">
                        <div class="field-box">
                            <label>From Location:</label>
                            <input type="text" name="pickup_location" class="transfer-from" placeholder="Pickup location">
                        </div>
                        <div class="field-box">
                            <label>To Location:</label>
                            <input type="text" name="dropoff_location" class="transfer-to" placeholder="Drop-off location">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="field-box">
                            <label>Pickup Date & Time:</label>
                            <input type="datetime-local" name="pickup_datetime" class="transfer-datetime">
                        </div>
                        <div class="field-box">
                            <label>Passengers:</label>
                            <input type="number" name="passenger_count" min="1" value="2" class="transfer-passengers">
                        </div>
                    </div>
                    <div class="form-row">
                        <button type="button" class="search-transfers-btn">Search Transfers</button>
                    </div>
                    <div class="transfer-results" style="display: none;">
                        <!-- Transfer results will be populated here -->
                    </div>
                </div>
            `;
            $transferSection.append(transferFormHtml);
            
            // Handle transfer search
            $transferSection.find('.search-transfers-btn').on('click', function() {
                searchTransfers($transferSection, $);
            });
        }
    }

    function loadDestinationOptions($section) {
        // This should load destination options from your destinations API
        // For now, using placeholder data
        const destinations = [
            { id: 1, name: 'Mumbai' },
            { id: 2, name: 'Delhi' },
            { id: 3, name: 'Bangalore' },
            { id: 4, name: 'Goa' },
            { id: 5, name: 'Jaipur' }
        ];
        
        const $fromSelect = $section.find('.flight-from-destination');
        const $toSelect = $section.find('.flight-to-destination');
        
        destinations.forEach(dest => {
            $fromSelect.append(`<option value="${dest.id}">${dest.name}</option>`);
            $toSelect.append(`<option value="${dest.id}">${dest.name}</option>`);
        });
    }

    function loadHotels($section, $, destinationId) {
        const cacheKey = `destination_${destinationId}`;
        
        // Check cache first
        if (window.BookingAdmin.cache.hotels[cacheKey]) {
            populateHotels($section, window.BookingAdmin.cache.hotels[cacheKey]);
            return;
        }
        
        $section.find('.loading-message').show();
        $section.find('.hotel-selection').hide();
        
        // Make AJAX call to get hotels
        $.ajax({
            url: '/admin/bookings/booking/ajax/get-hotels/',
            data: { destination_id: destinationId },
            method: 'GET',
            success: function(response) {
                console.log('[BookingAdmin] Hotels loaded:', response);
                window.BookingAdmin.cache.hotels[cacheKey] = response.hotels;
                populateHotels($section, response.hotels);
            },
            error: function(xhr, status, error) {
                console.error('[BookingAdmin] Error loading hotels:', error);
                showAlert($, 'Error loading hotels. Please try again.');
                $section.find('.loading-message').hide();
            }
        });
    }

    function populateHotels($section, hotels) {
        $section.find('.loading-message').hide();
        $section.find('.hotel-selection').show();
        
        const $select = $section.find('.hotel-select');
        $select.empty().append('<option value="">Select a hotel</option>');
        
        hotels.forEach(hotel => {
            $select.append(`<option value="${hotel.id}" data-rating="${hotel.rating}">
                ${hotel.hotel_name} ${hotel.rating ? '(' + hotel.rating + '★)' : ''}
            </option>`);
        });
    }

    function loadActivities($section, $, destinationId) {
        const cacheKey = `destination_${destinationId}`;
        
        // Check cache first
        if (window.BookingAdmin.cache.activities[cacheKey]) {
            populateActivities($section, window.BookingAdmin.cache.activities[cacheKey]);
            return;
        }
        
        $section.find('.loading-message').show();
        $section.find('.activity-selection').hide();
        
        // Make AJAX call to get activities
        $.ajax({
            url: '/admin/bookings/booking/ajax/get-activities/',
            data: { destination_id: destinationId },
            method: 'GET',
            success: function(response) {
                console.log('[BookingAdmin] Activities loaded:', response);
                window.BookingAdmin.cache.activities[cacheKey] = response.activities;
                populateActivities($section, response.activities);
            },
            error: function(xhr, status, error) {
                console.error('[BookingAdmin] Error loading activities:', error);
                showAlert($, 'Error loading activities. Please try again.');
                $section.find('.loading-message').hide();
            }
        });
    }

    function populateActivities($section, activities) {
        $section.find('.loading-message').hide();
        $section.find('.activity-selection').show();
        
        const $select = $section.find('.activity-select');
        $select.empty().append('<option value="">Select an activity</option>');
        
        activities.forEach(activity => {
            $select.append(`<option value="${activity.id}" data-price="${activity.price_per_person}">
                ${activity.activity_name} ${activity.price_per_person ? '(₹' + activity.price_per_person + ')' : ''}
            </option>`);
        });
    }

    function searchFlights($section, $) {
        const fromDest = $section.find('.flight-from-destination').val();
        const toDest = $section.find('.flight-to-destination').val();
        const depDate = $section.find('.flight-departure-date').val();
        const isReturn = $section.find('.flight-return-checkbox').is(':checked');
        const retDate = $section.find('.flight-return-date').val();
        
        if (!fromDest || !toDest || !depDate) {
            showAlert($, 'Please fill in all required flight search fields.');
            return;
        }
        
        if (isReturn && !retDate) {
            showAlert($, 'Please select return date for round trip.');
            return;
        }
        
        const $resultsDiv = $section.find('.flight-results');
        $resultsDiv.html('<div class="loading-message">Searching flights...</div>').show();
        
        $.ajax({
            url: '/admin/bookings/booking/ajax/search-flights/',
            data: {
                from_destination: fromDest,
                to_destination: toDest,
                departure_date: depDate,
                return_flight: isReturn,
                return_date: retDate
            },
            method: 'GET',
            success: function(response) {
                console.log('[BookingAdmin] Flight search results:', response);
                displayFlightResults($resultsDiv, response.flights);
            },
            error: function(xhr, status, error) {
                console.error('[BookingAdmin] Error searching flights:', error);
                $resultsDiv.html('<div class="error-message">Error searching flights. Please try again.</div>');
            }
        });
    }

    function displayFlightResults($container, flights) {
        let html = '<h4>Available Flights:</h4>';
        
        if (flights.length === 0) {
            html += '<p>No flights found for the selected criteria.</p>';
        } else {
            html += '<div class="flight-options">';
            flights.forEach((flight, index) => {
                html += `
                    <div class="flight-option" data-flight-id="${flight.flight_id}">
                        <div class="flight-info">
                            <strong>${flight.airline}</strong> - ${flight.flight_id}<br>
                            ${flight.departure_time} - ${flight.arrival_time} (${flight.duration})<br>
                            <strong>₹${flight.price}</strong>
                        </div>
                        <button type="button" class="select-flight-btn" data-flight='${JSON.stringify(flight)}'>
                            Select This Flight
                        </button>
                    </div>
                `;
            });
            html += '</div>';
        }
        
        $container.html(html);
        
        // Handle flight selection
        $container.find('.select-flight-btn').on('click', function() {
            const flightData = JSON.parse($(this).attr('data-flight'));
            console.log('[BookingAdmin] Flight selected:', flightData);
            
            // You can populate hidden fields or show more detailed forms here
            showAlert($, `Flight ${flightData.flight_id} selected. Price: ₹${flightData.price}`);
        });
    }

    function searchTransfers($section, $) {
        const fromLocation = $section.find('.transfer-from').val();
        const toLocation = $section.find('.transfer-to').val();
        const pickupDate = $section.find('.transfer-datetime').val();
        
        if (!fromLocation || !toLocation || !pickupDate) {
            showAlert($, 'Please fill in all required transfer search fields.');
            return;
        }
        
        const $resultsDiv = $section.find('.transfer-results');
        $resultsDiv.html('<div class="loading-message">Searching transfers...</div>').show();
        
        $.ajax({
            url: '/admin/bookings/booking/ajax/search-transfers/',
            data: {
                from_location: fromLocation,
                to_location: toLocation,
                pickup_date: pickupDate
            },
            method: 'GET',
            success: function(response) {
                console.log('[BookingAdmin] Transfer search results:', response);
                displayTransferResults($resultsDiv, response.transfers);
            },
            error: function(xhr, status, error) {
                console.error('[BookingAdmin] Error searching transfers:', error);
                $resultsDiv.html('<div class="error-message">Error searching transfers. Please try again.</div>');
            }
        });
    }

    function displayTransferResults($container, transfers) {
        let html = '<h4>Available Transfers:</h4>';
        
        if (transfers.length === 0) {
            html += '<p>No transfers found for the selected criteria.</p>';
        } else {
            html += '<div class="transfer-options">';
            transfers.forEach((transfer, index) => {
                html += `
                    <div class="transfer-option" data-transfer-id="${transfer.transfer_id}">
                        <div class="transfer-info">
                            <strong>${transfer.vehicle_type}</strong> (${transfer.capacity} passengers)<br>
                            Duration: ${transfer.duration}<br>
                            <strong>₹${transfer.price}</strong>
                        </div>
                        <button type="button" class="select-transfer-btn" data-transfer='${JSON.stringify(transfer)}'>
                            Select This Transfer
                        </button>
                    </div>
                `;
            });
            html += '</div>';
        }
        
        $container.html(html);
        
        // Handle transfer selection
        $container.find('.select-transfer-btn').on('click', function() {
            const transferData = JSON.parse($(this).attr('data-transfer'));
            console.log('[BookingAdmin] Transfer selected:', transferData);
            
            showAlert($, `Transfer ${transferData.vehicle_type} selected. Price: ₹${transferData.price}`);
        });
    }

    function initializeExistingRows($) {
        // Initialize any existing inline rows on page load
        $('.booking-type-selector').each(function() {
            const selectedType = $(this).val();
            if (selectedType) {
                const $inline = $(this).closest('.inline-related');
                showTypeFields($inline, selectedType, $);
            }
        });
    }

    function setupFormsetHandlers($) {
        // Handle adding new inline forms
        $(document).on('click', '.add-row a', function() {
            setTimeout(function() {
                // Re-initialize the newly added row
                $('.booking-type-selector').last().trigger('change');
            }, 100);
        });
    }

    function updateInlineFieldOptions($, destinationId) {
        // This function can be expanded to update existing inline forms
        // when destination changes
        console.log('[BookingAdmin] Updating inline field options for destination:', destinationId);
    }

    function clearInlineFieldOptions($) {
        // Clear all cached options when destination is cleared
        clearAllCache();
        console.log('[BookingAdmin] Cleared inline field options');
    }

    function clearAllCache() {
        if (window.BookingAdmin && window.BookingAdmin.cache) {
            window.BookingAdmin.cache.hotels = {};
            window.BookingAdmin.cache.activities = {};
            window.BookingAdmin.cache.flights = {};
            window.BookingAdmin.cache.transfers = {};
        }
    }

    function showLoadingMessage($, message) {
        // You can implement a loading overlay here
        console.log('[BookingAdmin] Loading:', message);
    }

    function showAlert($, message) {
        // Simple alert for now - you can enhance this with better UI
        alert(message);
    }

    // Make function available globally for the HTML onchange handler
    window.handleTypeChange = function(selectElement) {
        const $ = window.django.jQuery || window.jQuery;
        if ($) {
            $(selectElement).trigger('change');
        }
    };

})(); 