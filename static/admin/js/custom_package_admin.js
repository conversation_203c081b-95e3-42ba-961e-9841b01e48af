(function($) {
    'use strict';

    $(document.body).on('formset:added', function(event, $row, formsetName) {
        if (formsetName === 'itineraries') {
            // A slight delay ensures the new row's HTML is fully in the DOM
            // and that Django's own scripts have initialized any widgets.
            setTimeout(function() {
                // Find the specific new row that was added
                const $newRow = $(event.target);
                updateSingleRow($newRow, $);
            }, 100);
        }
    });

    $(document).ready(function() {
        // Wait for other admin scripts to finish rendering, especially Select2
        setTimeout(function() {
            initializeCustomPackageAdmin($);
        }, 500);
    });

    function initializeCustomPackageAdmin($) {
        console.log("Custom Package Admin JS Initializing after delay...");

        // --- Feature 1: RE-INTEGRATED AI Icon Generation Loader ---
        initializeIconGenerationLoader($);

        // --- Feature 2: Dependent Autocomplete Fields ---
        const $destinationField = $('#id_destination');
        if ($destinationField.length) {
            $destinationField.on('change', function() {
                clearAutocompleteFields($);
                updateAllAutocompleteUrls($);
            });
        }
        
        updateAllAutocompleteUrls($);
    }

    function getDestinationId($) {
        const $destinationDropdown = $('#id_destination');
        if ($destinationDropdown.length && $destinationDropdown.val()) {
            return $destinationDropdown.val();
        }

        const $readOnlyLink = $('.field-destination .readonly a');
        if ($readOnlyLink.length) {
            const href = $readOnlyLink.attr('href');
            const match = href.match(/\/(\d+)\/change/);
            if (match && match[1]) {
                return match[1];
            }
        }
        
        console.error("CRITICAL: Could not find destination ID.");
        return null;
    }

    function updateAllAutocompleteUrls($) {
        const destinationId = getDestinationId($);
        if (!destinationId) return;

        console.log("Updating ALL rows with destination_id:", destinationId);
        $('.dynamic-itineraries').not('.empty-form').each(function() {
            updateSingleRow($(this), $, destinationId);
        });
    }

    function updateSingleRow($inlineRow, $, destinationId) {
        // If destinationId isn't passed, get it.
        destinationId = destinationId || getDestinationId($);
        if (!destinationId) return;

        const $hotelSelect = $inlineRow.find('select[id$="-hotel"].admin-autocomplete');
        const $activitySelect = $inlineRow.find('select[id$="-activity"].admin-autocomplete');

        if ($hotelSelect.length) {
             const hotelUrl = $hotelSelect.data('ajax--url');
             if (hotelUrl) {
                 const newHotelUrl = hotelUrl.split('?')[0] + '?destination_id=' + destinationId;
                 $hotelSelect.data('ajax--url', newHotelUrl);
                 $hotelSelect.select2('destroy').djangoAdminSelect2();
             }
        }

        if ($activitySelect.length) {
             const activityUrl = $activitySelect.data('ajax--url');
             if (activityUrl) {
                 const newActivityUrl = activityUrl.split('?')[0] + '?destination_id=' + destinationId;
                 $activitySelect.data('ajax--url', newActivityUrl);
                 $activitySelect.select2('destroy').djangoAdminSelect2();
             }
        }
    }

    function clearAutocompleteFields($) {
        $('.dynamic-itineraries').not('.empty-form').each(function() {
            $(this).find('select[id$="-hotel"].admin-autocomplete').val(null).trigger('change');
            $(this).find('select[id$="-activity"].admin-autocomplete').val(null).trigger('change');
        });
    }

    function initializeIconGenerationLoader($) {
        const overlayHtml = `
            <div id="icon-generation-overlay">
                <div class="icon-generation-modal">
                    <div class="icon-generation-spinner"></div>
                    <div class="icon-generation-title">Generating Smart Icons</div>
                    <div class="icon-generation-message">AI is analyzing your content to assign the most appropriate icons...</div>
                    <div class="icon-generation-items">Processing highlights, inclusions, and add-ons</div>
                    <div class="icon-generation-note">This may take a few seconds</div>
                </div>
            </div>`;
        if (!$('#icon-generation-overlay').length) {
            $('body').append(overlayHtml);
        }

        function hasAIContent() {
            const highlights = $('#id_highlights').val();
            const inclusions = $('#id_inclusions').val();
            const addons = $('#id_addons').val();
            return (highlights && highlights.trim()) || (inclusions && inclusions.trim()) || (addons && addons.trim());
        }

        function showLoader() {
            $('#icon-generation-overlay').addClass('show');
        }

        function hideLoader() {
            const $overlay = $('#icon-generation-overlay');
            $('.icon-generation-title').text('Icons Generated Successfully!');
            $('.icon-generation-message').text('Your package has been saved with smart icons.');
            $('.icon-generation-spinner').hide();
            setTimeout(() => {
                $overlay.removeClass('show');
                setTimeout(() => {
                    $('.icon-generation-title').text('Generating Smart Icons');
                    $('.icon-generation-message').text('AI is analyzing your content to assign the most appropriate icons...');
                    $('.icon-generation-spinner').show();
                }, 300);
            }, 1500);
        }

        let loaderActive = false;
        $(document).on('click', 'input[name="_save"], input[name="_addanother"], input[name="_continue"]', function() {
            if (hasAIContent() && ($('.errorlist').length === 0)) {
                showLoader();
                loaderActive = true;
            }
        });

        $(window).on('beforeunload', function() {
            if (loaderActive) {
                // Let the new page load remove the overlay
            }
        });
    }

})(django.jQuery);