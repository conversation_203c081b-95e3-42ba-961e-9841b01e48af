// Infinite Scroll Widget JavaScript with proper logging and fixed functionality
(function() {
    'use strict';

    console.log('Infinite scroll widget script loaded');

    window.initInfiniteScrollWidget = function(config) {
        console.log('initInfiniteScrollWidget called with config:', config);
        
        const container = document.querySelector(`[data-widget-id="${config.widgetId}"]`);
        if (!container) {
            console.error('Widget container not found:', config.widgetId);
            return;
        }
        
        console.log('Found widget container:', container);

        // Get all the elements
        const hiddenInput = container.querySelector('input[type="hidden"]');
        const displayArea = container.querySelector('.infinite-scroll-display');
        const selectedText = container.querySelector('.selected-text');
        const dropdownArrow = container.querySelector('.dropdown-arrow');
        const dropdown = container.querySelector('.infinite-scroll-dropdown');
        const searchInput = container.querySelector('.search-input');
        const optionsContainer = container.querySelector('.options-container');
        const loadingIndicator = container.querySelector('.loading-indicator');
        const loadMoreIndicator = container.querySelector('.load-more-indicator');

        // Validate all elements exist
        if (!hiddenInput || !displayArea || !selectedText || !dropdown || !searchInput || !optionsContainer) {
            console.error('Required widget elements not found:', {
                hiddenInput: !!hiddenInput,
                displayArea: !!displayArea,
                selectedText: !!selectedText,
                dropdown: !!dropdown,
                searchInput: !!searchInput,
                optionsContainer: !!optionsContainer
            });
            return;
        }

        console.log('All widget elements found successfully');

        // State variables
        let currentPage = 1;
        let isLoading = false;
        let hasMore = true;
        let searchTimeout = null;
        let currentSearchQuery = '';
        let isDropdownOpen = false;

        // Initialize with initial value if present
        if (config.initialValue) {
            console.log('Setting initial value:', config.initialValue);
            updateSelectedDisplay(config.initialValue.text);
        }

        // Event Listeners
        console.log('Setting up event listeners');
        
        // Main display area click - toggle dropdown
        displayArea.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('Display area clicked, current dropdown state:', isDropdownOpen);
            toggleDropdown();
        });

        // Search input events
        searchInput.addEventListener('input', handleSearch);
        searchInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                console.log('Enter pressed in search input');
            }
        });

        // Options container scroll
        if (optionsContainer) {
            optionsContainer.addEventListener('scroll', handleScroll);
        }

        // Load more indicator click
        if (loadMoreIndicator) {
            loadMoreIndicator.addEventListener('click', loadMoreItems);
        }
        
        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!container.contains(e.target)) {
                console.log('Clicked outside widget, closing dropdown');
                hideDropdown();
            }
        });

        function toggleDropdown() {
            if (isDropdownOpen) {
                hideDropdown();
            } else {
                showDropdown();
            }
        }

        function showDropdown() {
            console.log('Showing dropdown');
            dropdown.style.display = 'block';
            isDropdownOpen = true;
            
            // Update arrow
            if (dropdownArrow) {
                dropdownArrow.textContent = '▲';
            }
            
            // Focus search input
            setTimeout(() => {
                searchInput.focus();
            }, 100);
            
            // Load initial data if not loaded
            if (optionsContainer.children.length === 0) {
                console.log('Loading initial data');
                loadData();
            }
        }

        function hideDropdown() {
            console.log('Hiding dropdown');
            dropdown.style.display = 'none';
            isDropdownOpen = false;
            
            // Update arrow
            if (dropdownArrow) {
                dropdownArrow.textContent = '▼';
            }
        }

        function handleSearch(e) {
            const query = e.target.value.trim();
            console.log('Search input changed:', query);
            
            // Clear previous timeout
            if (searchTimeout) {
                clearTimeout(searchTimeout);
            }
            
            // Debounce search to avoid too many requests
            searchTimeout = setTimeout(() => {
                console.log('Executing search with query:', query);
                currentSearchQuery = query;
                currentPage = 1;
                hasMore = true;
                optionsContainer.innerHTML = '';
                loadData();
            }, 300);
        }

        function handleScroll() {
            if (!optionsContainer) return;
            
            const threshold = 50; // pixels from bottom
            const isNearBottom = optionsContainer.scrollTop + optionsContainer.clientHeight >= 
                                optionsContainer.scrollHeight - threshold;
            
            if (isNearBottom && hasMore && !isLoading) {
                console.log('Near bottom of scroll, loading more items');
                loadMoreItems();
            }
        }

        function loadMoreItems() {
            if (!hasMore || isLoading) {
                console.log('Cannot load more items - hasMore:', hasMore, 'isLoading:', isLoading);
                return;
            }
            
            currentPage++;
            console.log('Loading more items, page:', currentPage);
            loadData(false); // Don't clear existing options
        }

        function loadData(clearExisting = true) {
            if (isLoading) {
                console.log('Already loading data, skipping request');
                return;
            }
            
            console.log('Loading data - page:', currentPage, 'search:', currentSearchQuery, 'clearExisting:', clearExisting);
            
            isLoading = true;
            showLoading();
            
            if (clearExisting) {
                optionsContainer.innerHTML = '';
            }

            // Build request parameters
            const params = new URLSearchParams({
                model: config.modelName,
                search: currentSearchQuery,
                page: currentPage,
                search_field: config.searchField,
                display_field: config.displayField
            });
            
            // Add extra filters if provided
            if (config.extraFilters) {
                Object.keys(config.extraFilters).forEach(key => {
                    params.append(key, config.extraFilters[key]);
                });
            }

            const requestUrl = `${config.ajaxUrl}?${params}`;
            console.log('Making AJAX request to:', requestUrl);

            fetch(requestUrl)
                .then(response => {
                    console.log('AJAX response status:', response.status);
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('AJAX response data:', data);
                    handleDataResponse(data, clearExisting);
                })
                .catch(error => {
                    console.error('AJAX request failed:', error);
                    handleError(error);
                })
                .finally(() => {
                    isLoading = false;
                    hideLoading();
                });
        }

        function handleDataResponse(data, clearExisting) {
            console.log('Handling data response:', {
                itemsCount: data.items ? data.items.length : 0,
                hasNext: data.has_next,
                totalCount: data.total_count,
                clearExisting: clearExisting
            });

            if (clearExisting) {
                optionsContainer.innerHTML = '';
                
                // Add "None" option at the top to allow clearing the selection
                const noneOption = document.createElement('div');
                noneOption.className = 'dropdown-option none-option';
                noneOption.style.cssText = `
                    padding: 8px 12px;
                    cursor: pointer;
                    border-bottom: 1px solid #ddd;
                    font-size: 13px;
                    font-style: italic;
                    color: #666;
                    background-color: #f9f9f9;
                    transition: background-color 0.2s;
                `;
                noneOption.textContent = '— None —';
                noneOption.dataset.value = '';
                
                // Mark as selected if no value is currently selected
                if (!hiddenInput.value || hiddenInput.value === '') {
                    noneOption.classList.add('selected');
                    noneOption.style.backgroundColor = '#e6f3ff';
                    noneOption.style.color = '#0066cc';
                    noneOption.style.fontStyle = 'normal';
                    console.log('Marked none option as selected');
                }
                
                // Hover effects for none option
                noneOption.addEventListener('mouseenter', function() {
                    if (!this.classList.contains('selected')) {
                        this.style.backgroundColor = '#f0f8ff';
                    }
                });
                
                noneOption.addEventListener('mouseleave', function() {
                    if (!this.classList.contains('selected')) {
                        this.style.backgroundColor = '#f9f9f9';
                    }
                });
                
                // Click handler for none option
                noneOption.addEventListener('click', function() {
                    console.log('None option clicked');
                    selectOption({id: '', text: ''});
                });
                
                optionsContainer.appendChild(noneOption);
            }

            if (data.items && data.items.length > 0) {
                data.items.forEach(item => {
                    const option = createOptionElement(item);
                    optionsContainer.appendChild(option);
                });
                
                hasMore = data.has_next;
                updateLoadMoreIndicator();
                console.log('Added', data.items.length, 'items to dropdown');
            } else if (clearExisting && optionsContainer.children.length === 1) {
                // No results (only the "None" option exists)
                console.log('No results found');
                const noResults = document.createElement('div');
                noResults.className = 'no-results';
                noResults.style.cssText = 'padding: 15px; text-align: center; color: #999; font-style: italic;';
                noResults.textContent = currentSearchQuery ? 
                    `No ${config.modelVerboseName.toLowerCase()}s found for "${currentSearchQuery}"` : 
                    `No ${config.modelVerboseName.toLowerCase()}s available`;
                optionsContainer.appendChild(noResults);
                hasMore = false;
            }
        }

        function createOptionElement(item) {
            const option = document.createElement('div');
            option.className = 'dropdown-option';
            option.style.cssText = `
                padding: 8px 12px;
                cursor: pointer;
                border-bottom: 1px solid #f5f5f5;
                font-size: 13px;
                transition: background-color 0.2s;
            `;
            option.textContent = item.text;
            option.dataset.value = item.id;
            
            // Mark as selected if it matches current value
            if (hiddenInput.value == item.id) {
                option.classList.add('selected');
                option.style.backgroundColor = '#e6f3ff';
                option.style.color = '#0066cc';
                console.log('Marked option as selected:', item);
            }
            
            // Hover effects
            option.addEventListener('mouseenter', function() {
                if (!this.classList.contains('selected')) {
                    this.style.backgroundColor = '#f0f8ff';
                }
            });
            
            option.addEventListener('mouseleave', function() {
                if (!this.classList.contains('selected')) {
                    this.style.backgroundColor = '';
                }
            });
            
            option.addEventListener('click', function() {
                console.log('Option clicked:', item);
                selectOption(item);
            });
            
            return option;
        }

        function selectOption(item) {
            console.log('Selecting option:', item);
            
            // Update hidden input
            hiddenInput.value = item.id || '';
            
            // Update display
            if (item.id && item.text) {
                updateSelectedDisplay(item.text);
            } else {
                // Handle "None" selection - clear the selection
                const placeholder = `Select ${config.modelVerboseName.toLowerCase()}...`;
                updateSelectedDisplay(placeholder);
                console.log('Cleared selection, showing placeholder:', placeholder);
            }
            
            // Update option selection visual
            optionsContainer.querySelectorAll('.dropdown-option').forEach(opt => {
                opt.classList.remove('selected');
                opt.style.backgroundColor = '';
                opt.style.color = '';
                opt.style.fontStyle = '';
            });
            
            // Apply special styling to none option when deselected
            const noneOption = optionsContainer.querySelector('.none-option');
            if (noneOption && (!item.id || item.id === '')) {
                noneOption.style.backgroundColor = '#f9f9f9';
                noneOption.style.color = '#666';
                noneOption.style.fontStyle = 'italic';
            }
            
            const selectedOption = optionsContainer.querySelector(`[data-value="${item.id || ''}"]`);
            if (selectedOption) {
                selectedOption.classList.add('selected');
                selectedOption.style.backgroundColor = '#e6f3ff';
                selectedOption.style.color = '#0066cc';
                
                // Keep none option italic unless it's selected
                if (!selectedOption.classList.contains('none-option')) {
                    selectedOption.style.fontStyle = 'normal';
                } else {
                    selectedOption.style.fontStyle = 'normal'; // Remove italic when selected
                }
            }
            
            // Hide dropdown
            hideDropdown();
            
            // Trigger change event for form validation
            const changeEvent = new Event('change', { bubbles: true });
            hiddenInput.dispatchEvent(changeEvent);
            console.log('Dispatched change event for form validation');
        }

        function updateSelectedDisplay(text) {
            const displayText = text || `Select ${config.modelVerboseName.toLowerCase()}...`;
            selectedText.textContent = displayText;
            console.log('Updated display text:', displayText);
        }

        function showLoading() {
            if (loadingIndicator) {
                loadingIndicator.style.display = 'block';
            }
            console.log('Showing loading indicator');
        }

        function hideLoading() {
            if (loadingIndicator) {
                loadingIndicator.style.display = 'none';
            }
        }

        function updateLoadMoreIndicator() {
            if (!loadMoreIndicator) return;
            
            if (hasMore && optionsContainer.children.length > 0) {
                loadMoreIndicator.style.display = 'block';
                loadMoreIndicator.textContent = 'Scroll for more...';
            } else {
                loadMoreIndicator.style.display = 'none';
            }
        }

        function handleError(error) {
            console.error('Widget error occurred:', error);
            
            // Show error in options container
            optionsContainer.innerHTML = `
                <div style="padding: 15px; text-align: center; color: #dc3545; font-style: italic;">
                    Error loading ${config.modelVerboseName.toLowerCase()}s. Please try again.
                </div>
            `;
        }

        console.log('Infinite scroll widget initialized successfully for:', config.widgetId);
    };

    console.log('Infinite scroll widget functions defined');
})(); 