/**
 * Custom Package Admin CSS
 * Styles for CustomPackage admin interface
 */

/* Destination Section Emphasis */
.destination-section {
    background-color: #e8f4fd !important;
    padding: 20px !important;
    border-radius: 8px !important;
    border: 3px solid #417690 !important;
    margin-bottom: 10px !important;
}

.destination-section .form-row {
    margin-bottom: 0;
}

.destination-section select {
    width: 100% !important;
    padding: 12px !important;
    font-size: 16px !important;
    border: 2px solid #417690 !important;
    border-radius: 5px !important;
    background-color: white !important;
}

.destination-section .description {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
    padding: 10px;
    border-radius: 4px;
    margin-top: 10px;
    font-weight: bold;
}

/* Itinerary Section Styling */
.inline-group {
    order: 2 !important;
    margin-top: 0 !important;
    background-color: #f8f9fa;
    border: 2px solid #417690;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px !important;
}

.inline-group h2 {
    background-color: #417690 !important;
    color: white !important;
    padding: 15px 20px !important;
    margin: -15px -15px 20px -15px !important;
    border-radius: 6px 6px 0 0 !important;
    font-size: 18px !important;
    font-weight: bold !important;
    border-bottom: 2px solid #2c5973;
}

/* Collapsible sections */
.collapse h2 {
    background-color: #6c757d;
    color: white;
    padding: 8px 15px;
    cursor: pointer;
    border-radius: 4px;
    font-size: 14px;
}

.collapse.collapsed .form-row {
    display: none;
}

/* Element ordering */
.form-row.field-destination {
    order: 1;
}

.inline-group {
    order: 2;
}

.module {
    order: 3;
}

/* Itinerary form styling */
.custom-package-itinerary-inline .form-row {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #f9f9f9;
}

.custom-package-itinerary-inline .form-row:hover {
    background-color: #f0f0f0;
}

.custom-package-itinerary-inline .field-day_number {
    min-width: 80px;
}

.custom-package-itinerary-inline .field-day_number input {
    width: 60px;
    text-align: center;
}

.custom-package-itinerary-inline .field-type {
    min-width: 120px;
}

.custom-package-itinerary-inline .field-type select {
    width: 100%;
    padding: 5px;
    border: 1px solid #ccc;
    border-radius: 3px;
}

.custom-package-itinerary-inline .field-activity,
.custom-package-itinerary-inline .field-hotel {
    min-width: 200px;
    flex-grow: 1;
}

.custom-package-itinerary-inline .field-activity select,
.custom-package-itinerary-inline .field-hotel select {
    width: 100%;
    padding: 5px;
    border: 1px solid #ccc;
    border-radius: 3px;
}

.custom-package-itinerary-inline .field-activity.hidden,
.custom-package-itinerary-inline .field-hotel.hidden {
    display: none !important;
}

.custom-package-itinerary-inline .required label:after {
    content: " *";
    color: #e74c3c;
    font-weight: bold;
}

/* Button styling */
.add-row a {
    background-color: #417690;
    color: white;
    padding: 8px 16px;
    text-decoration: none;
    border-radius: 4px;
    display: inline-block;
    margin-top: 10px;
}

.add-row a:hover {
    background-color: #205067;
    color: white;
}

.delete {
    text-align: center;
    vertical-align: middle;
}

.delete input[type="checkbox"] {
    transform: scale(1.2);
}

/* Responsive design */
@media (max-width: 768px) {
    .custom-package-itinerary-inline .form-row {
        flex-direction: column;
        align-items: stretch;
    }
    
    .custom-package-itinerary-inline .field-activity,
    .custom-package-itinerary-inline .field-hotel {
        min-width: auto;
        width: 100%;
    }
}

/* Loading states */
.loading-options {
    background-color: #f8f9fa;
    opacity: 0.7;
    pointer-events: none;
}

.loading-options:after {
    content: "Loading...";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #666;
    font-style: italic;
}

.loading-message {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
    padding: 10px 15px;
    border-radius: 5px;
    margin-bottom: 15px;
    font-weight: bold;
    text-align: center;
    animation: fadeInOut 2s ease-in-out;
}

@keyframes fadeInOut {
    0% { opacity: 0; transform: translateY(-10px); }
    20% { opacity: 1; transform: translateY(0); }
    80% { opacity: 1; transform: translateY(0); }
    100% { opacity: 0; transform: translateY(-10px); }
}

/* Table styling for inline forms */
.inline-group .tabular {
    border-collapse: collapse;
    width: 100%;
}

.inline-group .tabular th {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    padding: 10px 8px;
    text-align: left;
    font-weight: 600;
    color: #495057;
}

.inline-group .tabular td {
    border: 1px solid #dee2e6;
    padding: 8px;
    vertical-align: top;
}

.inline-group .tabular .original {
    background-color: #f8f9fa;
}

/* Destination dependent message */
.destination-dependent-message {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
    padding: 10px;
    border-radius: 4px;
    margin: 10px 0;
    font-style: italic;
}

.destination-dependent-message.hidden {
    display: none;
}

/* Icon Generation Loading Overlay */
#icon-generation-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    z-index: 999999;
    align-items: center;
    justify-content: center;
}

#icon-generation-overlay.show {
    display: flex !important;
}

.icon-generation-modal {
    background: white;
    border-radius: 12px;
    padding: 40px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    text-align: center;
    max-width: 420px;
    width: 90%;
    transform: scale(0.8);
    transition: transform 0.3s ease;
}

#icon-generation-overlay.show .icon-generation-modal {
    transform: scale(1);
}

.icon-generation-spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #417690;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: icon-spin 1s linear infinite;
    margin: 0 auto 25px;
}

@keyframes icon-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.icon-generation-title {
    font-size: 20px;
    font-weight: bold;
    color: #417690;
    margin-bottom: 15px;
}

.icon-generation-message {
    color: #666;
    margin-bottom: 20px;
    font-size: 15px;
    line-height: 1.4;
}

.icon-generation-items {
    font-size: 13px;
    color: #888;
    margin-top: 15px;
    font-style: italic;
}

.icon-generation-note {
    margin-top: 20px;
    font-size: 12px;
    color: #999;
    line-height: 1.3;
}