/* Infinite Scroll Widget CSS - Updated for new structure */
.infinite-scroll-select-container {
    position: relative;
    width: 100%;
    max-width: 100%;
}

.infinite-scroll-display {
    border: 1px solid #ddd;
    padding: 8px 12px;
    background: #fff;
    cursor: pointer;
    border-radius: 4px;
    min-height: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: border-color 0.2s, box-shadow 0.2s;
}

.infinite-scroll-display:hover {
    border-color: #79aec8;
}

.infinite-scroll-display:focus-within {
    border-color: #79aec8;
    box-shadow: 0 0 0 2px rgba(121, 174, 200, 0.2);
}

.selected-text {
    flex: 1;
    color: #333;
    font-size: 13px;
}

.dropdown-arrow {
    color: #666;
    font-size: 12px;
    margin-left: 8px;
    transition: transform 0.2s;
}

.infinite-scroll-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #fff;
    border: 1px solid #ddd;
    border-top: none;
    border-radius: 0 0 4px 4px;
    max-height: 300px;
    overflow: hidden;
    z-index: 1000;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: none;
}

.search-header {
    padding: 8px;
    border-bottom: 1px solid #eee;
    background: #f9f9f9;
}

.search-input {
    width: 100%;
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 13px;
    outline: none;
    box-sizing: border-box;
}

.search-input:focus {
    border-color: #79aec8;
    box-shadow: 0 0 0 2px rgba(121, 174, 200, 0.2);
}

.options-container {
    max-height: 200px;
    overflow-y: auto;
}

.dropdown-option {
    padding: 8px 12px;
    cursor: pointer;
    border-bottom: 1px solid #f5f5f5;
    font-size: 13px;
    transition: background-color 0.2s;
    color: #333;
}

.dropdown-option:hover {
    background: #f0f8ff;
}

.dropdown-option:last-child {
    border-bottom: none;
}

.dropdown-option.selected {
    background: #e6f3ff;
    color: #0066cc;
    font-weight: 500;
}

.loading-indicator {
    padding: 15px;
    text-align: center;
    color: #666;
    font-size: 12px;
    font-style: italic;
    background: #f9f9f9;
}

.load-more-indicator {
    padding: 10px;
    text-align: center;
    color: #666;
    font-size: 12px;
    background: #f5f5f5;
    cursor: pointer;
    border-top: 1px solid #eee;
    transition: background-color 0.2s;
}

.load-more-indicator:hover {
    background: #efefef;
}

.no-results {
    padding: 15px;
    text-align: center;
    color: #999;
    font-style: italic;
    font-size: 12px;
}

/* Error state */
.infinite-scroll-select-container.error .infinite-scroll-display {
    border-color: #dc3545;
}

.infinite-scroll-select-container.error .search-input {
    border-color: #dc3545;
}

/* Loading state */
.infinite-scroll-select-container.loading .dropdown-arrow {
    opacity: 0.6;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .infinite-scroll-dropdown {
        max-height: 250px;
    }
    
    .options-container {
        max-height: 150px;
    }
} 