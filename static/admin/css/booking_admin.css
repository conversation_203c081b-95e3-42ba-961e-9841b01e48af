/* Booking Admin CSS */

/* Hide type-specific fields by default */
.hidden {
    display: none !important;
}

/* Basic form styling */
.form-row {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 15px;
}

.field-box {
    flex: 1;
    min-width: 200px;
}

.field-box label {
    display: block;
    font-weight: bold;
    margin-bottom: 5px;
    color: #333;
}

.field-box input,
.field-box select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.field-box input:focus,
.field-box select:focus {
    outline: none;
    border-color: #79aec8;
    box-shadow: 0 0 5px rgba(121, 174, 200, 0.3);
}

/* Type-specific sections */
.flight-fields,
.hotel-fields,
.activity-fields,
.transfer-fields {
    border: 2px solid #e1e1e1;
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
    background-color: #f9f9f9;
}

.flight-fields {
    border-color: #4CAF50;
    background-color: #f1f8e9;
}

.hotel-fields {
    border-color: #2196F3;
    background-color: #e3f2fd;
}

.activity-fields {
    border-color: #FF9800;
    background-color: #fff3e0;
}

.transfer-fields {
    border-color: #9C27B0;
    background-color: #f3e5f5;
}

/* Section headers */
.flight-fields::before {
    content: "✈️ Flight Details";
    display: block;
    font-weight: bold;
    font-size: 16px;
    color: #2e7d32;
    margin-bottom: 15px;
    padding-bottom: 5px;
    border-bottom: 1px solid #4CAF50;
}

.hotel-fields::before {
    content: "🏨 Hotel Details";
    display: block;
    font-weight: bold;
    font-size: 16px;
    color: #1565c0;
    margin-bottom: 15px;
    padding-bottom: 5px;
    border-bottom: 1px solid #2196F3;
}

.activity-fields::before {
    content: "🎯 Activity Details";
    display: block;
    font-weight: bold;
    font-size: 16px;
    color: #ef6c00;
    margin-bottom: 15px;
    padding-bottom: 5px;
    border-bottom: 1px solid #FF9800;
}

.transfer-fields::before {
    content: "🚗 Transfer Details";
    display: block;
    font-weight: bold;
    font-size: 16px;
    color: #6a1b9a;
    margin-bottom: 15px;
    padding-bottom: 5px;
    border-bottom: 1px solid #9C27B0;
}

/* Button styling */
.search-flights-btn,
.search-transfers-btn,
.select-flight-btn,
.select-transfer-btn {
    background-color: #79aec8;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;
}

.search-flights-btn:hover,
.search-transfers-btn:hover,
.select-flight-btn:hover,
.select-transfer-btn:hover {
    background-color: #6ba1c2;
}

.select-flight-btn,
.select-transfer-btn {
    background-color: #28a745;
    padding: 5px 15px;
    font-size: 12px;
}

.select-flight-btn:hover,
.select-transfer-btn:hover {
    background-color: #218838;
}

/* Loading and message styling */
.loading-message {
    text-align: center;
    padding: 20px;
    color: #666;
    font-style: italic;
}

.error-message {
    color: #dc3545;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    padding: 10px;
    margin: 10px 0;
}

/* Results styling */
.flight-results,
.transfer-results {
    margin-top: 20px;
    border-top: 1px solid #ddd;
    padding-top: 15px;
}

.flight-options,
.transfer-options {
    display: grid;
    gap: 15px;
}

.flight-option,
.transfer-option {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    background-color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: box-shadow 0.3s;
}

.flight-option:hover,
.transfer-option:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.flight-info,
.transfer-info {
    flex: 1;
    line-height: 1.4;
}

/* Hotel and Activity selection styling */
.hotel-selection,
.activity-selection {
    margin-top: 10px;
}

.hotel-details,
.activity-details {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #ddd;
}

/* Checkbox styling */
.field-box input[type="checkbox"] {
    width: auto;
    margin-right: 8px;
}

.field-box label:has(input[type="checkbox"]) {
    display: flex;
    align-items: center;
    font-weight: normal;
}

/* Return date field animation */
.return-date-field {
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        max-height: 0;
    }
    to {
        opacity: 1;
        max-height: 100px;
    }
}

/* Responsive design */
@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
    }
    
    .field-box {
        min-width: 100%;
    }
    
    .flight-option,
    .transfer-option {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }
}

/* Admin-specific overrides */
.inline-related {
    position: relative;
}

.booking-type-selector {
    font-weight: bold;
    background-color: #fff;
    border: 2px solid #79aec8;
}

/* Inline fieldset styling */
.inline-related fieldset {
    border: 1px solid #ddd;
    border-radius: 8px;
    margin-bottom: 20px;
}

.inline-related h3 {
    background-color: #79aec8;
    color: white;
    padding: 10px 15px;
    margin: 0;
    border-radius: 6px 6px 0 0;
}

/* Basic fields section */
.basic-fields {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 15px;
}

/* Calculated total styling */
.calculated-total {
    font-weight: bold;
    color: #28a745;
    font-size: 16px;
} 