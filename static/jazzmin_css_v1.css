.django-ckeditor-widget img {
    width: 100%;
}
.brand-text {
    font-weight: bold !important;
}

img[src$="icon-yes.svg"],
img[src$="icon-no.svg"] {
    width: 18px !important;
    height: 18px !important;
    vertical-align: middle !important;
    object-fit: contain;
    display: inline-block;
}

.login-box img,
.brand-logo img {
    width: 184px !important;
    height: auto !important;
}
body.jazzmin-login-page .login-logo img {
    width: 184px !important;
    height: auto !important;
    object-fit: contain;
    display: block;
    margin: 0 auto;
}

/* Fix success message link visibility - Updated selectors */
/* Fix success message hyperlink color */
#content .alert-success.alert-dismissible a:not(.dropdown-item):not(.btn-app):not(.nav-link):not(.brand-link):not(.page-link):not(.btn) {
    color: #ffffff !important;
    text-decoration: underline !important;
}

.field-destination .related-widget-wrapper-link {
    display: none !important;
}

/* ====== PACKAGE LOADING GLOBAL STYLES ====== */
/* Base loading overlay styles that can be used across package admin pages */
.package-upload-button-processing {
    opacity: 0.7;
    pointer-events: none;
    position: relative;
}

.package-upload-button-processing::after {
    content: "Processing...";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 123, 186, 0.9);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1;
}

/* Package form processing indicators */
.package-form-processing input,
.package-form-processing textarea,
.package-form-processing select {
    background-color: #f8f9fa !important;
}

.package-form-processing .submit-row {
    opacity: 0.6;
}

/* Loading spinner for inline use */
.package-inline-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007cba;
    border-radius: 50%;
    animation: package-spin-small 1s linear infinite;
    margin-right: 8px;
    vertical-align: middle;
}

@keyframes package-spin-small {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Package status indicators */
.package-status-processing {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 14px;
    margin: 10px 0;
}

.package-status-processing::before {
    content: "🔄 ";
}

/* Enhanced button styles for package actions */
.btn-success.package-action-btn {
    position: relative;
    overflow: hidden;
}

.btn-success.package-action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 123, 186, 0.3);
}

/* File upload field enhancements */
.field-file input[type="file"] {
    padding: 8px;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.field-file input[type="file"]:hover {
    border-color: #007cba;
    background: #e7f3ff;
}

.field-file input[type="file"]:focus {
    border-color: #004d75;
    background: #ffffff;
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 123, 186, 0.1);
}

/* Package form help text styling */
.package-help-collapsible summary {
    cursor: pointer;
    padding: 8px 12px;
    background: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 4px;
    margin: 5px 0;
    font-weight: 500;
}

.package-help-collapsible summary:hover {
    background: #d1ecf1;
}

.package-help-collapsible pre {
    max-height: 300px;
    overflow-y: auto;
}

/* Progress indicators for long-running operations */
.package-operation-progress {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
    text-align: center;
}

.package-operation-progress .progress-text {
    font-size: 16px;
    margin-bottom: 10px;
    color: #495057;
}

.package-operation-progress .progress-bar-container {
    width: 100%;
    height: 6px;
    background: #e9ecef;
    border-radius: 3px;
    overflow: hidden;
}

.package-operation-progress .progress-bar-fill {
    height: 100%;
    background: linear-gradient(90deg, #007cba, #004d75);
    transition: width 0.3s ease;
    border-radius: 3px;
} 