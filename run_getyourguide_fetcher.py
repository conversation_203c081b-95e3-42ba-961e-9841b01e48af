#!/usr/bin/env python3
"""
GetYourGuide Data Fetcher - Main Executable Script
Run this script from the project root to fetch and store GetYourGuide activity data

Usage:
    python run_getyourguide_fetcher.py                    # Run for all destinations
    python run_getyourguide_fetcher.py --destination dubai # Run for specific destination
    python run_getyourguide_fetcher.py --test             # Run in test mode
    python run_getyourguide_fetcher.py --clean            # Clean duplicates first
"""

import os
import sys
import argparse
import django
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'zuumm.settings')
django.setup()

# Import after Django setup
from base.getyourguide_fetcher.main import GetYourGuideDataFetcher
from base.getyourguide_fetcher.config import GetYourGuideConfig


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description='GetYourGuide Data Fetcher - Fetch and store activity data from GetYourGuide API',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_getyourguide_fetcher.py                    # Process all destinations
  python run_getyourguide_fetcher.py --destination dubai # Process only Dubai
  python run_getyourguide_fetcher.py --test             # Run in test mode (5 destinations)
  python run_getyourguide_fetcher.py --clean            # Clean duplicates before running
  python run_getyourguide_fetcher.py --stats            # Show current database statistics
        """
    )
    
    parser.add_argument(
        '--destination',
        type=str,
        help='Process only the specified destination (e.g., "dubai", "paris")'
    )
    
    parser.add_argument(
        '--test',
        action='store_true',
        help='Run in test mode (process limited number of destinations)'
    )
    
    parser.add_argument(
        '--clean',
        action='store_true',
        help='Clean duplicate activities before running the main process'
    )
    
    parser.add_argument(
        '--stats',
        action='store_true',
        help='Show current database statistics and exit'
    )
    
    parser.add_argument(
        '--validate',
        action='store_true',
        help='Only validate prerequisites and configuration, then exit'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose logging output'
    )
    
    return parser.parse_args()


def print_banner():
    """Print script banner"""
    print("=" * 80)
    print("                    GETYOURGUIDE DATA FETCHER")
    print("=" * 80)
    print("Fetches activity data from GetYourGuide API and stores in database")
    print("Based on your existing destinations in the packages app")
    print("=" * 80)


def print_configuration_info():
    """Print current configuration information"""
    print("\nCurrent Configuration:")
    print(f"  Test Mode: {GetYourGuideConfig.TEST_MODE}")
    if GetYourGuideConfig.TEST_MODE:
        print(f"  Test Mode Limit: {GetYourGuideConfig.TEST_MODE_DESTINATION_LIMIT} destinations")
    print(f"  Request Delay: {GetYourGuideConfig.REQUEST_DELAY} seconds")
    print(f"  Max Retries: {GetYourGuideConfig.MAX_RETRIES}")
    print(f"  Batch Size: {GetYourGuideConfig.BULK_CREATE_BATCH_SIZE}")
    print(f"  Log Directory: {GetYourGuideConfig.LOG_DIR}")
    print()


def show_database_statistics():
    """Show current database statistics"""
    try:
        print("\nRetrieving database statistics...")
        fetcher = GetYourGuideDataFetcher()
        stats = fetcher.database_manager.get_activity_statistics()
        
        if stats:
            print("\nCurrent Database Statistics:")
            print("-" * 40)
            print(f"Total Custom Activities: {stats.get('total_activities', 0)}")
            print(f"Total Activity Categories: {stats.get('total_categories', 0)}")
            print(f"Total Activity Locations: {stats.get('total_locations', 0)}")
            
            activities_by_dest = stats.get('activities_by_destination', {})
            if activities_by_dest:
                print("\nActivities by Destination:")
                for dest_name, count in sorted(activities_by_dest.items()):
                    print(f"  {dest_name}: {count} activities")
            else:
                print("\nNo activities found in database.")
        else:
            print("Could not retrieve database statistics.")
            
    except Exception as e:
        print(f"Error retrieving statistics: {str(e)}")


def validate_prerequisites():
    """Validate prerequisites and configuration"""
    try:
        print("\nValidating prerequisites...")
        fetcher = GetYourGuideDataFetcher()
        
        # Test API client
        print("  - Validating API client configuration...")
        if not fetcher.api_client.validate_configuration():
            print("    ❌ API client configuration validation failed")
            return False
        print("    ✅ API client configuration is valid")
        
        # Test API connection
        print("  - Testing API connection...")
        if not fetcher.api_client.test_api_connection():
            print("    ❌ API connection test failed")
            return False
        print("    ✅ API connection test successful")
        
        # Test database connection
        print("  - Validating database connection...")
        if not fetcher.database_manager.validate_database_connection():
            print("    ❌ Database connection validation failed")
            return False
        print("    ✅ Database connection is valid")
        
        print("\n✅ All prerequisites validated successfully!")
        return True
        
    except Exception as e:
        print(f"\n❌ Prerequisite validation failed: {str(e)}")
        return False


def main():
    """Main execution function"""
    try:
        # Parse arguments
        args = parse_arguments()
        
        # Print banner
        print_banner()
        
        # Show current configuration
        print_configuration_info()
        
        # Handle different modes
        if args.stats:
            show_database_statistics()
            return 0
        
        if args.validate:
            if validate_prerequisites():
                return 0
            else:
                return 1
        
        # Override test mode if specified
        if args.test:
            GetYourGuideConfig.TEST_MODE = True
            print("✓ Test mode enabled")
        
        # Create fetcher instance
        fetcher = GetYourGuideDataFetcher()
        
        # Clean duplicates if requested
        if args.clean:
            print("\nCleaning duplicate activities...")
            if fetcher.clean_existing_data():
                print("✓ Cleanup completed successfully")
            else:
                print("❌ Cleanup failed")
                return 1
        
        # Process specific destination or all destinations
        if args.destination:
            print(f"\nProcessing single destination: {args.destination}")
            success = fetcher.process_single_destination(args.destination)
        else:
            print("\nProcessing all destinations...")
            success = fetcher.run()
        
        # Print final status
        if success:
            print("\n" + "="*60)
            print("🎉 GETYOURGUIDE DATA FETCHER COMPLETED SUCCESSFULLY!")
            print("="*60)
            print("\nCheck the log files for detailed information:")
            print(f"  Main Log: {GetYourGuideConfig.MAIN_LOG_FILE}")
            print(f"  Failed Activities: {GetYourGuideConfig.FAILED_ACTIVITIES_LOG}")
            print(f"  Empty Responses: {GetYourGuideConfig.EMPTY_RESPONSE_LOG}")
            print(f"  Missing Destinations: {GetYourGuideConfig.NO_DESTINATION_LOG}")
            return 0
        else:
            print("\n" + "="*60)
            print("❌ GETYOURGUIDE DATA FETCHER COMPLETED WITH ERRORS")
            print("="*60)
            print("\nCheck the log files for error details:")
            print(f"  Main Log: {GetYourGuideConfig.MAIN_LOG_FILE}")
            return 1
    
    except KeyboardInterrupt:
        print("\n\n⚠️  Script interrupted by user")
        return 1
    except Exception as e:
        print(f"\n💥 FATAL ERROR: {str(e)}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 